load("@rules_go//go:def.bzl", "go_library")

go_library(
    name = "wechat",
    srcs = [
        "cache.go",
        "config.go",
        "example.go",
        "http_types.go",
        "models.go",
        "service.go",
        "types.go",
    ],
    importpath = "github.com/Arxtect/CoreXWorkSpace/golangp/common/payment/wechat",
    visibility = ["//visibility:public"],
    deps = [
        "//golangp/common/redis",
        "@com_github_artisancloud_powerwechat//src/kernel",
        "@com_github_artisancloud_powerwechat_v3//src/kernel/response",
        "@com_github_artisancloud_powerwechat_v3//src/payment",
        "@com_github_artisancloud_powerwechat_v3//src/payment/order/request",
        "@com_github_artisancloud_powerwechat_v3//src/payment/refund/request",
        "@com_github_google_uuid//:uuid",
        "@com_github_redis_go_redis_v9//:go-redis",
        "@io_gorm_gorm//:gorm",
    ],
)
