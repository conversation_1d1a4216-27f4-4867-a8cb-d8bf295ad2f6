load("@rules_go//go:def.bzl", "go_library")

go_library(
    name = "alipay",
    srcs = glob(["**/*.go"]),
    importpath = "github.com/Arxtect/CoreXWorkSpace/golangp/common/payment/alipay",
    visibility = ["//visibility:public"],
    deps = [
        "//golangp/common/redis",
        "@com_github_google_uuid//:uuid",
        "@com_github_redis_go_redis_v9//:go-redis",
        "@com_github_smartwalle_alipay_v3//:alipay",
        "@io_gorm_gorm//:gorm",
    ],
)
