load("@rules_go//go:def.bzl", "go_library")

go_library(
    name = "middleware",
    srcs = [
        "auth.go",
        "cors.go",
        "logging.go",
    ],
    importpath = "github.com/Arxtect/CoreXWorkSpace/golangp/common/gin_core/middleware",
    visibility = ["//visibility:public"],
    deps = [
        "//golangp/apps/pointer_center/internal/config",
        "//golangp/common/gin_core/response",
        "//golangp/common/logging:logger",
        "//golangp/common/utils",
        "@com_github_gin_gonic_gin//:gin",
    ],
)
