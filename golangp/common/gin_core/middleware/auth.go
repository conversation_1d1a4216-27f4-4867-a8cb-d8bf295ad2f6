/*
 * @Description: Authentication middleware for Gin applications
 * @Author: AI Assistant
 * @Date: 2025-08-02
 */
package middleware

import (
	"strings"

	"github.com/Arxtect/CoreXWorkSpace/golangp/common/gin_core/response"
	"github.com/Arxtect/CoreXWorkSpace/golangp/common/utils"

	"github.com/gin-gonic/gin"
)

// AuthMiddleware validates JWT tokens and sets user context
func AuthMiddleware(publicKey string) gin.HandlerFunc {
	return func(c *gin.Context) {
		var accessToken string

		// Try to get token from Authorization header first
		authorizationHeader := c.GetHeader("Authorization")
		fields := strings.Fields(authorizationHeader)

		// Try to get token from access_token cookie first
		cookie, err := c.<PERSON>("access_token")

		if err == nil && cookie != "" {
			// Clean the cookie value - remove any trailing semicolon and whitespace
			accessToken = strings.TrimSpace(strings.Split(cookie, ";")[0])
		} else {
			// Fallback to Authorization header
			if len(fields) == 2 && fields[0] == "Bearer" && fields[1] != "undefined" {
				accessToken = fields[1]
			}
		}

		if accessToken == "" {
			// Clear any existing invalid cookies
			c.SetCookie("access_token", "", -1, "/", "", false, true)
			c.SetCookie("refresh_token", "", -1, "/", "", false, true)
			response.Error(c, 401, "You are not logged in")
			c.Abort()
			return
		}

		// Validate JWT token using common utils
		payload, err := utils.ValidateToken(accessToken, publicKey)
		if err != nil {
			// Clear invalid tokens from cookies
			c.SetCookie("access_token", "", -1, "/", "", false, true)
			c.SetCookie("refresh_token", "", -1, "/", "", false, true)
			response.Error(c, 401, "Invalid or expired token"+err.Error())
			c.Abort()
			return
		}

		// Extract user information from token payload
		payloadMap, ok := payload.(map[string]interface{})
		if !ok {
			// Clear invalid tokens from cookies
			c.SetCookie("access_token", "", -1, "/", "", false, true)
			c.SetCookie("refresh_token", "", -1, "/", "", false, true)
			response.Error(c, 401, "Invalid token payload")
			c.Abort()
			return
		}
		// Store the access token in context for handlers that need it (like logout)
		c.Set("access_token", accessToken)

		// Also store refresh token from cookie if available
		refreshToken, err := c.Cookie("refresh_token")
		if err == nil && refreshToken != "" {
			c.Set("refresh_token", refreshToken)
		}

		// Try to extract user object from payload (fund_center format)
		if user, ok := payloadMap["user"]; ok {
			// Set current user context as map (for handlers that expect map)
			c.Set("currentUser", user)

			// Also extract user_id for convenience
			if userMap, ok := user.(map[string]interface{}); ok {
				if userID, ok := userMap["id"].(string); ok {
					c.Set("user_id", userID)
				}
				if role, ok := userMap["role"].(string); ok {
					c.Set("role", role)
				}
			}
		} else if userID, ok := payloadMap["user_id"].(string); ok {
			// GEOK Center format - only user_id in payload
			// Set user_id for handlers that need it
			c.Set("user_id", userID)
			// Note: currentUser would need to be fetched from database by the handler
		} else {
			// Clear invalid tokens from cookies
			c.SetCookie("access_token", "", -1, "/", "", false, true)
			c.SetCookie("refresh_token", "", -1, "/", "", false, true)
			response.Error(c, 401, "Invalid user data in token")
			c.Abort()
			return
		}

		c.Next()
	}
}

// AdminMiddleware ensures the user has admin privileges
func AdminMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Try to get role from context first (set by AuthMiddleware)
		role, exists := c.Get("role")
		if exists {
			userRole, ok := role.(string)
			if ok && userRole == "admin" {
				c.Next()
				return
			}
		}

		// Fallback: try to get user object and check role
		currentUser, exists := c.Get("currentUser")
		if exists {
			// Handle user as map (common format from JWT tokens)
			if userMap, ok := currentUser.(map[string]interface{}); ok {
				if userRole, ok := userMap["role"].(string); ok && userRole == "admin" {
					c.Next()
					return
				}
			}
		}

		response.Error(c, 403, "Admin access required")
		c.Abort()
	}
}
