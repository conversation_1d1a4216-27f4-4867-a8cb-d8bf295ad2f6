/*
 * @Description:
 * @Author: Devin
 * @Date: 2025-07-15 11:05:17
 */
package utils

import (
	"encoding/base64"
	"fmt"
	"time"

	"github.com/golang-jwt/jwt"
)

func CreateToken(ttl time.Duration, payload interface{}, privateKey string) (string, error) {
	decodedPrivateKey, err := base64.StdEncoding.DecodeString(privateKey)
	if err != nil {
		return "", fmt.Errorf("could not decode key: %w", err)
	}
	key, err := jwt.ParseRSAPrivateKeyFromPEM(decodedPrivateKey)

	if err != nil {
		return "", fmt.Errorf("create: parse key: %w", err)
	}

	now := time.Now().UTC()

	claims := make(jwt.MapClaims)

	// Set standard claims
	claims["iat"] = now.Unix()
	claims["nbf"] = now.Unix()

	// Only set exp if ttl is not zero
	if ttl > 0 {
		claims["exp"] = now.Add(ttl).Unix()
	}

	// Handle payload - merge into claims instead of setting as 'sub'
	if payloadMap, ok := payload.(map[string]interface{}); ok {
		// If payload is a map, merge all fields into claims
		for key, value := range payloadMap {
			claims[key] = value
		}
		// Set user_id as 'sub' if it exists
		if userID, exists := payloadMap["user_id"]; exists {
			claims["sub"] = userID
		}
	} else {
		// If payload is not a map, set it as 'sub'
		claims["sub"] = payload
	}

	token, err := jwt.NewWithClaims(jwt.SigningMethodRS256, claims).SignedString(key)

	if err != nil {
		return "", fmt.Errorf("create: sign token: %w", err)
	}

	return token, nil
}

// GenerateAccessToken generates a JWT access token for any user type
func GenerateAccessToken[T any](user T, ttl time.Duration, privateKey string) (string, time.Time, error) {
	// Set token expiry
	expiresAt := time.Now().Add(ttl)

	// Create token payload with user and type
	payload := map[string]interface{}{
		"user": user,
		"type": "access",
	}

	// Generate JWT token using common utils
	token, err := CreateToken(ttl, payload, privateKey)
	if err != nil {
		return "", time.Time{}, fmt.Errorf("failed to create access token: %w", err)
	}

	return token, expiresAt, nil
}

// GenerateRefreshToken generates a JWT refresh token for any user type
func GenerateRefreshToken[T any](user T, ttl time.Duration, privateKey string) (string, time.Time, error) {
	// Set token expiry
	expiresAt := time.Now().Add(ttl)

	// Create token payload with user and type
	payload := map[string]interface{}{
		"user": user,
		"type": "refresh",
	}

	// Generate JWT token using common utils
	token, err := CreateToken(ttl, payload, privateKey)
	if err != nil {
		return "", time.Time{}, fmt.Errorf("failed to create refresh token: %w", err)
	}

	return token, expiresAt, nil
}

func ValidateToken(token string, publicKey string) (interface{}, error) {
	decodedPublicKey, err := base64.StdEncoding.DecodeString(publicKey)
	if err != nil {
		return nil, fmt.Errorf("could not decode: %w", err)
	}

	key, err := jwt.ParseRSAPublicKeyFromPEM(decodedPublicKey)

	if err != nil {
		return nil, fmt.Errorf("validate: parse key: %w", err)
	}

	parsedToken, err := jwt.Parse(token, func(t *jwt.Token) (interface{}, error) {
		if _, ok := t.Method.(*jwt.SigningMethodRSA); !ok {
			return nil, fmt.Errorf("unexpected method: %s", t.Header["alg"])
		}
		return key, nil
	})

	if err != nil {
		return nil, fmt.Errorf("validate: %w", err)
	}

	claims, ok := parsedToken.Claims.(jwt.MapClaims)
	if !ok || !parsedToken.Valid {
		return nil, fmt.Errorf("validate: invalid token")
	}

	// Return the complete claims map instead of just 'sub'
	// Remove standard JWT fields to return clean payload
	payload := make(map[string]interface{})
	for key, value := range claims {
		// Skip standard JWT claims
		if key != "iat" && key != "nbf" && key != "exp" && key != "iss" && key != "aud" {
			payload[key] = value
		}
	}

	// If payload is empty, return sub field for backward compatibility
	if len(payload) == 0 {
		return claims["sub"], nil
	}

	return payload, nil
}
