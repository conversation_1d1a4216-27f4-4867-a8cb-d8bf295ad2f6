/*
 * @Description: JWT utilities for authentication
 * @Author: AI Assistant
 * @Date: 2025-07-16
 */
package utils

import (
	"errors"
	"time"

	"github.com/golang-jwt/jwt"
)

// JWTClaims represents the claims in a JWT token
type JWTClaims struct {
	UserID  string `json:"user_id"`
	Email   string `json:"email"`
	IsAdmin bool   `json:"is_admin"`
	Role    string `json:"role"`
	Type    string `json:"type"`
	jwt.StandardClaims
}

// GenerateJWT generates a JWT token for a user
func GenerateJWT(userID, email string, isAdmin bool) (string, error) {
	// Default secret key - should be configurable in production
	secretKey := []byte("your-secret-key")

	// Create claims
	claims := JWTClaims{
		UserID:  userID,
		Email:   email,
		IsAdmin: isAdmin,
		StandardClaims: jwt.StandardClaims{
			ExpiresAt: time.Now().Add(24 * time.Hour).Unix(), // 24 hours
			IssuedAt:  time.Now().Unix(),
			Issuer:    "pointer_center",
		},
	}

	// Create token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	// Sign token
	tokenString, err := token.SignedString(secretKey)
	if err != nil {
		return "", err
	}

	return tokenString, nil
}

// ValidateJWT validates a JWT token and returns the claims
func ValidateJWT(tokenString string) (*JWTClaims, error) {
	// Default secret key - should be configurable in production
	secretKey := []byte("your-secret-key")

	// Parse token
	token, err := jwt.ParseWithClaims(tokenString, &JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		// Validate signing method
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, errors.New("invalid signing method")
		}
		return secretKey, nil
	})

	if err != nil {
		return nil, err
	}

	// Validate token and extract claims
	if claims, ok := token.Claims.(*JWTClaims); ok && token.Valid {
		return claims, nil
	}

	return nil, errors.New("invalid token")
}

// GenerateJWTWithSecret generates a JWT token with a custom secret
func GenerateJWTWithSecret(userID, email string, isAdmin bool, secret string, expiryHours int) (string, error) {
	secretKey := []byte(secret)

	// Create claims
	claims := JWTClaims{
		UserID:  userID,
		Email:   email,
		IsAdmin: isAdmin,
		StandardClaims: jwt.StandardClaims{
			ExpiresAt: time.Now().Add(time.Duration(expiryHours) * time.Hour).Unix(),
			IssuedAt:  time.Now().Unix(),
			Issuer:    "pointer_center",
		},
	}

	// Create token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	// Sign token
	tokenString, err := token.SignedString(secretKey)
	if err != nil {
		return "", err
	}

	return tokenString, nil
}

// ValidateJWTWithSecret validates a JWT token with a custom secret
func ValidateJWTWithSecret(tokenString, secret string) (*JWTClaims, error) {
	secretKey := []byte(secret)

	// Parse token
	token, err := jwt.ParseWithClaims(tokenString, &JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		// Validate signing method
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, errors.New("invalid signing method")
		}
		return secretKey, nil
	})

	if err != nil {
		return nil, err
	}

	// Validate token and extract claims
	if claims, ok := token.Claims.(*JWTClaims); ok && token.Valid {
		return claims, nil
	}

	return nil, errors.New("invalid token")
}

// GenerateSecureToken generates a secure random token
func GenerateSecureToken(length int) string {
	token, err := GenerateRandomString(length)
	if err != nil {
		// Fallback to a simple token if random generation fails
		return "fallback-token-" + time.Now().Format("20060102150405")
	}
	return token
}
