package mongodb

import (
	"context"
	"fmt"
	"log"
	"time"

	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

var DB *mongo.Database

// ConnectDB connects to MongoDB database
func ConnectDB(config *Config) {
	var err error

	// Build connection URI
	var uri string
	if config.Username != "" && config.Password != "" {
		uri = fmt.Sprintf("mongodb://%s:%s@%s:%s/%s?authSource=%s",
			config.Username, config.Password, config.Host, config.Port, config.Database, config.AuthDB)
	} else {
		uri = fmt.Sprintf("mongodb://%s:%s/%s", config.Host, config.Port, config.Database)
	}

	// Set client options
	clientOptions := options.Client().ApplyURI(uri)

	// Set connection pool settings
	clientOptions.SetMaxPoolSize(100)
	clientOptions.SetMinPoolSize(10)
	clientOptions.SetMaxConnIdleTime(30 * time.Second)
	clientOptions.SetConnectTimeout(10 * time.Second)
	clientOptions.SetServerSelectionTimeout(5 * time.Second)

	// Connect to MongoDB
	client, err := mongo.Connect(context.Background(), clientOptions)
	if err != nil {
		log.Fatal("Failed to connect to MongoDB:", err)
	}

	// Test the connection
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	err = client.Ping(ctx, nil)
	if err != nil {
		log.Fatal("Failed to ping MongoDB:", err)
	}

	// Set global database instance
	DB = client.Database(config.Database)

	fmt.Println("🚀 Connected Successfully to MongoDB")
}

// GetDB returns the MongoDB database instance
func GetDB() *mongo.Database {
	if DB == nil {
		log.Fatal("MongoDB not connected. Call ConnectDB first.")
	}
	return DB
}

// Disconnect closes the MongoDB connection
func Disconnect() error {
	if DB != nil {
		return DB.Client().Disconnect(context.Background())
	}
	return nil
}
