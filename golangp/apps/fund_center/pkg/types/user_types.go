/*
 * @Description: User-related types for Fund Center
 * @Author: AI Assistant
 * @Date: 2025-08-01
 */
package types

// CreateUserRequest represents a user creation request
type CreateUserRequest struct {
	Email            *string `json:"email" binding:"omitempty,email"`
	Phone            *string `json:"phone" binding:"omitempty,min=10,max=20"`
	Username         string  `json:"username" binding:"omitempty,min=3,max=50"`
	Password         string  `json:"password" binding:"required,min=6"`
	FirstName        string  `json:"first_name" binding:"max=100"`
	LastName         string  `json:"last_name" binding:"max=100"`
	VerificationCode *string `json:"verification_code" binding:"omitempty,len=6"` // 验证码，可选
	LoginType        string  `json:"-"`                                           // 内部使用，根据输入字段自动判断
}

// LoginRequest represents a login request
type LoginRequest struct {
	Email            *string `json:"email" binding:"omitempty,email"`
	Phone            *string `json:"phone" binding:"omitempty,min=10,max=20"`
	Password         string  `json:"password" binding:"omitempty"`
	VerificationCode *string `json:"verification_code" binding:"omitempty,len=6"` // 验证码，可选
}

// UpdateUserRequest represents a user update request
type UpdateUserRequest struct {
	Username  *string `json:"username" binding:"omitempty,min=3,max=50"`
	FirstName string  `json:"first_name" binding:"max=100"`
	LastName  string  `json:"last_name" binding:"max=100"`
	Avatar    string  `json:"avatar" binding:"max=500"`
	Bio       string  `json:"bio" binding:"max=500"`
	Company   string  `json:"company" binding:"max=200"`
	Country   string  `json:"country" binding:"max=100"`
}

// ChangePasswordRequest represents a password change request
type ChangePasswordRequest struct {
	CurrentPassword string `json:"current_password" binding:"required"`
	NewPassword     string `json:"new_password" binding:"required,min=6"`
}

// VerifyEmailRequest represents an email verification request
type VerifyEmailRequest struct {
	Token string `json:"token" binding:"required"`
}

// VerifyPhoneRequest represents a phone verification request
type VerifyPhoneRequest struct {
	Phone string `json:"phone" binding:"required,min=10,max=20"`
	Code  string `json:"code" binding:"required,len=6"`
}

// VerifyCodeRequest represents a verification code request
type VerifyCodeRequest struct {
	LoginType string `json:"login_type" binding:"required,oneof=email phone"`
	Contact   string `json:"contact" binding:"required"`
	Code      string `json:"code" binding:"required,len=6"`
}

// SendVerificationCodeRequest represents a request to send verification code
type SendVerificationCodeRequest struct {
	Email *string `json:"email" binding:"omitempty,email"`
	Phone *string `json:"phone" binding:"omitempty,min=10,max=20"`
	Type  string  `json:"type" binding:"omitempty,oneof=register login"` // 验证码类型：register 或 login，默认为 login
}

// UserResponse represents a user response (without sensitive data)
type UserResponse struct {
	ID        string  `json:"id"`
	Email     *string `json:"email"`
	Phone     *string `json:"phone"`
	Username  *string `json:"username"`
	FirstName string  `json:"first_name"`
	LastName  string  `json:"last_name"`
	Avatar    string  `json:"avatar"`
	Bio       string  `json:"bio"`
	Company   string  `json:"company"`
	Country   string  `json:"country"`
	LoginType string  `json:"login_type"`
	Origin    string  `json:"origin"`
	IsActive  bool    `json:"is_active"`
	IsAdmin   bool    `json:"is_admin"`

	// 验证状态
	EmailVerified bool `json:"email_verified"`
	PhoneVerified bool `json:"phone_verified"`

	// 安全相关预留字段
	LoginProtection        bool `json:"login_protection"`
	PasswordChangeRequired bool `json:"password_change_required"`
	SecurityLock           bool `json:"security_lock"`

	CreatedAt string `json:"created_at"`
	UpdatedAt string `json:"updated_at"`
}

// LoginResponse represents a login response
type LoginResponse struct {
	User         UserResponse `json:"user"`
	AccessToken  string       `json:"access_token"`
	RefreshToken string       `json:"refresh_token"`
	ExpiresIn    int          `json:"expires_in"`
}

// RefreshTokenResponse represents a token refresh response
type RefreshTokenResponse struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	ExpiresIn    int    `json:"expires_in"`
}

// UserListRequest represents a user list request
type UserListRequest struct {
	Page     int    `form:"page" binding:"omitempty,min=1"`
	PageSize int    `form:"page_size" binding:"omitempty,min=1,max=100"`
	Search   string `form:"search" binding:"omitempty,max=100"`
	Role     string `form:"role" binding:"omitempty,oneof=user admin"`
	Status   string `form:"status" binding:"omitempty,oneof=active inactive suspended deleted"`
}

// UserListResponse represents a user list response
type UserListResponse struct {
	Users      []UserResponse `json:"users"`
	Total      int64          `json:"total"`
	Page       int            `json:"page"`
	PageSize   int            `json:"page_size"`
	TotalPages int            `json:"total_pages"`
}

// AdminCreateUserRequest represents an admin user creation request
type AdminCreateUserRequest struct {
	Email     *string `json:"email" binding:"omitempty,email"`
	Phone     *string `json:"phone" binding:"omitempty,min=10,max=20"`
	Username  *string `json:"username" binding:"omitempty,min=3,max=50"`
	Password  string  `json:"password" binding:"required,min=6"`
	FirstName string  `json:"first_name" binding:"max=100"`
	LastName  string  `json:"last_name" binding:"max=100"`
	Avatar    string  `json:"avatar" binding:"max=500"`
	Bio       string  `json:"bio" binding:"max=500"`
	Company   string  `json:"company" binding:"max=200"`
	Country   string  `json:"country" binding:"max=100"`
	Role      string  `json:"role" binding:"omitempty,oneof=user admin"`
	Status    string  `json:"status" binding:"omitempty,oneof=active inactive suspended"`
	Origin    string  `json:"origin" binding:"max=50"`
}

// AdminUpdateUserRequest represents an admin user update request
type AdminUpdateUserRequest struct {
	Email     *string `json:"email" binding:"omitempty,email"`
	Phone     *string `json:"phone" binding:"omitempty,min=10,max=20"`
	Username  *string `json:"username" binding:"omitempty,min=3,max=50"`
	FirstName string  `json:"first_name" binding:"max=100"`
	LastName  string  `json:"last_name" binding:"max=100"`
	Avatar    string  `json:"avatar" binding:"max=500"`
	Bio       string  `json:"bio" binding:"max=500"`
	Company   string  `json:"company" binding:"max=200"`
	Country   string  `json:"country" binding:"max=100"`
	Role      string  `json:"role" binding:"omitempty,oneof=user admin"`
	Status    string  `json:"status" binding:"omitempty,oneof=active inactive suspended"`
	Origin    string  `json:"origin" binding:"max=50"`

	// 验证状态
	EmailVerified *bool `json:"email_verified"`
	PhoneVerified *bool `json:"phone_verified"`

	// 安全相关字段
	LoginProtection        *bool `json:"login_protection"`
	PasswordChangeRequired *bool `json:"password_change_required"`
	SecurityLock           *bool `json:"security_lock"`
}

// AdminResetPasswordRequest represents an admin password reset request
type AdminResetPasswordRequest struct {
	NewPassword string `json:"new_password" binding:"required,min=6"`
}

// UserBatchOperationRequest represents a batch operation request
type UserBatchOperationRequest struct {
	UserIDs   []string `json:"user_ids" binding:"required,min=1"`
	Operation string   `json:"operation" binding:"required,oneof=activate deactivate suspend delete"`
}

// UserStatsResponse represents user statistics response
type UserStatsResponse struct {
	TotalUsers     int64 `json:"total_users"`
	ActiveUsers    int64 `json:"active_users"`
	InactiveUsers  int64 `json:"inactive_users"`
	SuspendedUsers int64 `json:"suspended_users"`
	AdminUsers     int64 `json:"admin_users"`
	NewUsersToday  int64 `json:"new_users_today"`
	NewUsersWeek   int64 `json:"new_users_week"`
	NewUsersMonth  int64 `json:"new_users_month"`
}

// AdminStartTrialRequest represents an admin request to start trial for a user
type AdminStartTrialRequest struct {
	TrialDays int `json:"trial_days" binding:"required,min=1,max=365"`
}
