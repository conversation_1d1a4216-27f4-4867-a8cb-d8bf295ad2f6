load("@rules_go//go:def.bzl", "go_library")

go_library(
    name = "middleware",
    srcs = [
        "dify_proxy.go",
        "membership_middleware.go",
    ],
    importpath = "github.com/Arxtect/CoreXWorkSpace/golangp/apps/fund_center/internal/middleware",
    visibility = ["//visibility:public"],
    deps = [
        "//golangp/apps/fund_center/internal/config",
        "//golangp/apps/fund_center/internal/models",
        "//golangp/common/gin_core/response",
        "//golangp/common/logging:logger",
        "@com_github_gin_gonic_gin//:gin",
        "@com_github_google_uuid//:uuid",
        "@io_gorm_gorm//:gorm",
    ],
)
