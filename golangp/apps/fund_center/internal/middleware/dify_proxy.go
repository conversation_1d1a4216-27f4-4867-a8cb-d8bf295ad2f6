/*
 * @Description: DIFY proxy middleware for Fund Center
 * @Author: AI Assistant
 * @Date: 2025-08-04
 */
package middleware

import (
	"net/http"
	"net/http/httputil"
	"net/url"
	"strings"
	"time"

	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/fund_center/internal/config"
	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/fund_center/internal/models"
	"github.com/Arxtect/CoreXWorkSpace/golangp/common/gin_core/response"
	"github.com/Arxtect/CoreXWorkSpace/golangp/common/logging"

	"github.com/gin-gonic/gin"
)

// DifyProxyMiddleware creates a middleware that proxies specific requests to DIFY
func DifyProxyMiddleware(cfg *config.Config, logger *logging.Logger) gin.HandlerFunc {
	// Parse DIFY host URL
	difyURL, err := url.Parse(cfg.DifyHost)
	if err != nil {
		logger.Error("Failed to parse DIFY host URL: %v", err)
		return func(c *gin.Context) {
			response.Error(c, http.StatusInternalServerError, "DIFY proxy configuration error")
			c.Abort()
		}
	}

	return func(c *gin.Context) {
		path := c.Request.URL.Path

		// Check if the path should be proxied to DIFY
		if shouldProxyToDify(path) {
			// Check membership for chat-messages endpoints
			if isChatMessagesPath(path) {
				user, ok := response.GetUserFromContext[models.User](c)
				if !ok {
					response.Error(c, http.StatusUnauthorized, "用户未认证")
					c.Abort()
					return
				}

				if !user.IsValidMember() {
					response.Error(c, http.StatusForbidden, "请先订阅后再访问")
					c.Abort()
					return
				}
			}

			logger.Info("Proxying request to DIFY: %s %s", c.Request.Method, path)

			// Create reverse proxy
			proxy := &httputil.ReverseProxy{
				Director: func(req *http.Request) {
					// Set target URL
					req.URL.Scheme = difyURL.Scheme
					req.URL.Host = difyURL.Host
					req.Host = difyURL.Host

					// Transform path: remove "api" prefix, keep "v1"
					// Example: /api/v1/chat-messages -> /v1/chat-messages
					originalPath := req.URL.Path
					if strings.HasPrefix(originalPath, "/api/v1/") {
						req.URL.Path = strings.Replace(originalPath, "/api/v1/", "/v1/", 1)
					} else if strings.HasPrefix(originalPath, "/api/") {
						req.URL.Path = strings.Replace(originalPath, "/api/", "/", 1)
					}

					// Authorization header from original request is preserved

					// Set proxy identification header
					req.Header.Set("User-Agent", "Fund-Center-Proxy/1.0")

					// Preserve original Content-Type if present
					// Don't override Content-Type as it may vary (application/json, multipart/form-data, etc.)

					// Remove hop-by-hop headers
					req.Header.Del("Connection")
					req.Header.Del("Proxy-Connection")
					req.Header.Del("Te")
					req.Header.Del("Trailer")
					req.Header.Del("Transfer-Encoding")
					req.Header.Del("Upgrade")
				},
				ModifyResponse: func(resp *http.Response) error {
					// Log the response
					logger.Info("DIFY proxy response: %d for %s %s",
						resp.StatusCode, c.Request.Method, c.Request.URL.Path)

					// Add CORS headers if needed
					resp.Header.Set("Access-Control-Allow-Origin", "*")
					resp.Header.Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
					resp.Header.Set("Access-Control-Allow-Headers", "Content-Type, Authorization")

					return nil
				},
				ErrorHandler: func(w http.ResponseWriter, r *http.Request, err error) {
					logger.Error("DIFY proxy error: %v for %s %s", err, r.Method, r.URL.Path)

					w.Header().Set("Content-Type", "application/json")
					w.WriteHeader(http.StatusBadGateway)
					w.Write([]byte(`{"success": false, "message": "DIFY service error", "error": "` + err.Error() + `"}`))
				},
				Transport: &http.Transport{
					MaxIdleConns:        100,
					MaxIdleConnsPerHost: 10,
					IdleConnTimeout:     30 * time.Second,
				},
			}

			// Execute the proxy
			proxy.ServeHTTP(c.Writer, c.Request)
			c.Abort()
			return
		}

		// Continue to next middleware if not a DIFY proxy request
		c.Next()
	}
}

// shouldProxyToDify determines if a request path should be proxied to DIFY
func shouldProxyToDify(path string) bool {
	// Remove leading slash for consistent checking
	path = strings.TrimPrefix(path, "/")

	// List of path prefixes that should be proxied to DIFY
	difyPrefixes := []string{
		"api/v1/messages",
		"api/v1/conversations",
		"api/v1/chat-messages",
		"v1/messages",
		"v1/conversations",
		"v1/chat-messages",
		"messages",
		"conversations",
		"chat-messages",
	}

	for _, prefix := range difyPrefixes {
		if strings.HasPrefix(path, prefix) {
			return true
		}
	}

	return false
}

// isChatMessagesPath determines if a request path is for chat-messages endpoint
func isChatMessagesPath(path string) bool {
	// Remove leading slash for consistent checking
	path = strings.TrimPrefix(path, "/")

	// List of chat-messages path prefixes that require membership check
	chatMessagesPrefixes := []string{
		"api/v1/chat-messages",
		"v1/chat-messages",
		"chat-messages",
	}

	for _, prefix := range chatMessagesPrefixes {
		if strings.HasPrefix(path, prefix) {
			return true
		}
	}

	return false
}
