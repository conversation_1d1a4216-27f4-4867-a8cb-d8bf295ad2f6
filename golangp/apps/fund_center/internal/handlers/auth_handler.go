/*
 * @Description: Authentication handler for Fund Center
 * @Author: AI Assistant
 * @Date: 2025-08-01
 */
package handlers

import (
	"context"
	"fmt"
	"math/rand"
	"net/http"
	"strconv"
	"time"

	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/fund_center/internal/config"
	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/fund_center/internal/models"
	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/fund_center/pkg/types"
	"github.com/Arxtect/CoreXWorkSpace/golangp/common/gin_core/response"
	"github.com/Arxtect/CoreXWorkSpace/golangp/common/logging"
	"github.com/Arxtect/CoreXWorkSpace/golangp/common/redis"
	sms "github.com/Arxtect/CoreXWorkSpace/golangp/common/sms/aliyun"
	"github.com/Arxtect/CoreXWorkSpace/golangp/common/utils"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// AuthHandler handles authentication-related requests
type AuthHandler struct {
	db          *gorm.DB
	config      *config.Config
	logger      *logging.Logger
	smsClient   *sms.Client
	redisClient *redis.Client
}

// NewAuthHandler creates a new authentication handler
func NewAuthHandler(db *gorm.DB, config *config.Config, logger *logging.Logger, smsClient *sms.Client, redisClient *redis.Client) *AuthHandler {
	return &AuthHandler{
		db:          db,
		config:      config,
		logger:      logger,
		smsClient:   smsClient,
		redisClient: redisClient,
	}
}

// generateVerificationCode generates a random verification code
func generateVerificationCode() string {
	source := rand.NewSource(time.Now().UnixNano())
	r := rand.New(source)
	code := r.Intn(900000) + 100000 // 生成6位数字验证码
	return strconv.Itoa(code)
}

// sendVerificationCode sends verification code via email or SMS
func (h *AuthHandler) sendVerificationCode(loginType string, contact string, code string) error {
	if loginType == "email" {
		h.logger.Info("📧 Sending email verification code to %s: %s", contact, code)
		// TODO: 实现邮件服务发送验证码
		return nil
	} else if loginType == "phone" {
		h.logger.Info("📱 Sending SMS verification code to %s: %s", contact, code)

		// 检查 SMS 客户端是否可用
		if h.smsClient == nil {
			h.logger.Error("❌ SMS client not initialized")
			return fmt.Errorf("SMS client not available")
		}

		// 发送验证码
		request := &sms.VerificationCodeRequest{
			PhoneNumber: contact,
			Code:        code,
			Purpose:     "login", // 验证码用途：登录
		}

		response, err := h.smsClient.SendVerificationCode(request)
		if err != nil {
			h.logger.Error("❌ Failed to send SMS verification code: %v", err)
			return fmt.Errorf("failed to send SMS verification code: %w", err)
		}

		if response.Success {
			h.logger.Info("✅ SMS verification code sent successfully to %s, RequestID: %s",
				sms.MaskPhoneNumber(contact), response.RequestID)
		} else {
			h.logger.Error("❌ SMS verification code sending failed - Code: %s, Message: %s, RequestID: %s",
				response.Code, response.Message, response.RequestID)
			return fmt.Errorf("SMS sending failed: %s - %s", response.Code, response.Message)
		}

		return nil
	}

	return fmt.Errorf("unsupported login type: %s", loginType)
}

// SendVerificationCode handles sending verification code
func (h *AuthHandler) SendVerificationCode(c *gin.Context) {
	startTime := time.Now()
	clientIP := c.ClientIP()

	var req types.SendVerificationCodeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warning("❌ Invalid send verification code request - Error: %v, ClientIP: %s", err, clientIP)
		response.Error(c, http.StatusBadRequest, "Invalid request data")
		return
	}

	// 设置默认类型为登录
	codeType := req.Type
	if codeType == "" {
		codeType = "login"
	}

	// 根据输入的字段自动判断登录类型
	var loginType string
	var contact string

	if req.Email != nil && *req.Email != "" {
		if req.Phone != nil && *req.Phone != "" {
			response.Error(c, http.StatusBadRequest, "Please provide either email or phone, not both")
			return
		}
		loginType = "email"
		contact = *req.Email
	} else if req.Phone != nil && *req.Phone != "" {
		loginType = "phone"
		contact = *req.Phone
	} else {
		response.Error(c, http.StatusBadRequest, "Either email or phone is required")
		return
	}

	// 检查用户是否存在
	var existingUser models.User
	var query *gorm.DB

	if loginType == "email" {
		query = h.db.Where("email = ? AND email IS NOT NULL", contact)
	} else {
		query = h.db.Where("phone = ? AND phone IS NOT NULL", contact)
	}

	err := query.First(&existingUser).Error
	userExists := err == nil

	h.logger.Info("🔍 User existence check - Type: %s, %s: %s, Exists: %v, Error: %v, ClientIP: %s",
		codeType, loginType, contact, userExists, err, clientIP)

	// 根据验证码类型和用户存在性进行逻辑判断
	if codeType == "register" {
		if userExists {
			h.logger.Warning("❌ User already exists for registration - %s: %s, ClientIP: %s", loginType, contact, clientIP)
			response.Error(c, http.StatusBadRequest, fmt.Sprintf("User with this %s already exists", loginType))
			return
		}
		h.logger.Info("✅ User does not exist, can proceed with registration - %s: %s, ClientIP: %s", loginType, contact, clientIP)
	} else if codeType == "login" {
		if !userExists {
			h.logger.Warning("❌ User does not exist for login - %s: %s, ClientIP: %s", loginType, contact, clientIP)
			response.Error(c, http.StatusNotFound, "User not found")
			return
		}
		h.logger.Info("✅ User exists, can proceed with login - %s: %s, ClientIP: %s", loginType, contact, clientIP)
	}

	// 生成验证码
	verificationCode := generateVerificationCode()

	// 存储验证码到Redis，10分钟过期，包含类型信息
	ctx := context.Background()
	redisKey := fmt.Sprintf("verification_code:%s:%s:%s", codeType, loginType, contact)
	if err := h.redisClient.Set(ctx, redisKey, verificationCode, 10*time.Minute); err != nil {
		h.logger.Error("❌ Failed to store verification code in Redis - Error: %v, Type: %s, %s: %s, ClientIP: %s", err, codeType, loginType, contact, clientIP)
		response.Error(c, http.StatusInternalServerError, "Failed to generate verification code")
		return
	}

	// 发送验证码
	if err := h.sendVerificationCode(loginType, contact, verificationCode); err != nil {
		h.logger.Error("❌ Failed to send verification code - Error: %v, Type: %s, %s: %s, ClientIP: %s", err, codeType, loginType, contact, clientIP)
		response.Error(c, http.StatusInternalServerError, "Failed to send verification code")
		return
	}

	h.logger.Info("✅ Verification code sent successfully - Type: %s, %s: %s, Duration: %v, ClientIP: %s",
		codeType, loginType, contact, time.Since(startTime), clientIP)

	response.Success(c, "Verification code sent successfully", gin.H{
		"message": fmt.Sprintf("Please check your %s for the verification code", loginType),
		"contact": contact,
		"type":    codeType,
		"code":    verificationCode, // TODO:remove
	})
}

// verifyCodeFromRedis verifies the verification code from Redis
func (h *AuthHandler) verifyCodeFromRedis(codeType, loginType, contact, code string) error {
	ctx := context.Background()
	redisKey := fmt.Sprintf("verification_code:%s:%s:%s", codeType, loginType, contact)

	storedCode, err := h.redisClient.Get(ctx, redisKey)
	if err != nil {
		if err.Error() == "redis: nil" {
			return fmt.Errorf("verification code not found or expired")
		}
		return fmt.Errorf("failed to get verification code from Redis: %w", err)
	}

	if storedCode != code {
		return fmt.Errorf("invalid verification code")
	}

	// 验证成功后删除验证码
	if err := h.redisClient.Del(ctx, redisKey); err != nil {
		h.logger.Warning("⚠️ Failed to delete verification code from Redis - Key: %s, Error: %v", redisKey, err)
	}

	return nil
}

// Register handles user registration
func (h *AuthHandler) Register(c *gin.Context) {
	startTime := time.Now()
	clientIP := c.ClientIP()

	var req types.CreateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warning("❌ Invalid registration request - Error: %v, ClientIP: %s", err, clientIP)
		response.Error(c, http.StatusBadRequest, "Invalid request data")
		return
	}

	// 根据输入的字段自动判断登录类型
	var loginType string
	var contact string

	if req.Email != nil && *req.Email != "" {
		if req.Phone != nil && *req.Phone != "" {
			response.Error(c, http.StatusBadRequest, "Please provide either email or phone, not both")
			return
		}
		loginType = "email"
		contact = *req.Email
		req.Phone = nil // 清除 phone 字段
	} else if req.Phone != nil && *req.Phone != "" {
		loginType = "phone"
		contact = *req.Phone
		req.Email = nil // 清除 email 字段
	} else {
		response.Error(c, http.StatusBadRequest, "Either email or phone is required")
		return
	}

	// 更新请求中的登录类型
	req.LoginType = loginType

	// 如果提供了验证码，验证验证码
	if req.VerificationCode != nil && *req.VerificationCode != "" {
		if err := h.verifyCodeFromRedis("register", loginType, contact, *req.VerificationCode); err != nil {
			h.logger.Warning("❌ Verification code validation failed - %s: %s, Error: %v, ClientIP: %s", loginType, contact, err, clientIP)
			response.Error(c, http.StatusBadRequest, "Invalid or expired verification code")
			return
		}
		h.logger.Info("✅ Verification code validated successfully - %s: %s, ClientIP: %s", loginType, contact, clientIP)
	}

	// Check if user already exists
	var existingUser models.User
	var query *gorm.DB

	if req.LoginType == "email" {
		query = h.db.Where("email = ?", *req.Email)
	} else {
		query = h.db.Where("phone = ?", *req.Phone)
	}

	if err := query.First(&existingUser).Error; err == nil {
		h.logger.Warning("❌ User already exists - %s: %s, ClientIP: %s", req.LoginType, contact, clientIP)
		response.Error(c, http.StatusBadRequest, fmt.Sprintf("User with this %s already exists", req.LoginType))
		return
	}

	// Hash password
	hashedPassword, err := utils.HashPassword(req.Password)
	if err != nil {
		h.logger.Error("❌ Failed to hash password - Error: %v, ClientIP: %s", err, clientIP)
		response.Error(c, http.StatusInternalServerError, "Failed to process password")
		return
	}

	// 根据是否提供验证码决定验证状态
	emailVerified := false
	phoneVerified := false
	if req.VerificationCode != nil && *req.VerificationCode != "" {
		if loginType == "email" {
			emailVerified = true
		} else {
			phoneVerified = true
		}
	}

	// Create new user
	var username *string
	if req.Username != "" {
		username = &req.Username
	}

	newUser := &models.User{
		ID:            uuid.New(),
		Email:         req.Email,
		Phone:         req.Phone,
		Username:      username,
		Password:      hashedPassword,
		FirstName:     req.FirstName,
		LastName:      req.LastName,
		LoginType:     models.LoginType(req.LoginType),
		Role:          models.UserRoleUser,
		Status:        models.UserStatusActive,
		Origin:        "local",
		EmailVerified: emailVerified,
		PhoneVerified: phoneVerified,
	}

	if err := h.db.Create(newUser).Error; err != nil {
		h.logger.Error("❌ Failed to create user - Error: %v, %s: %s, ClientIP: %s", err, req.LoginType, contact, clientIP)
		response.Error(c, http.StatusInternalServerError, "Failed to create user")
		return
	}

	h.logger.Info("✅ User registered successfully - UserID: %s, %s: %s, Verified: %v, Duration: %v, ClientIP: %s",
		newUser.ID, req.LoginType, contact, emailVerified || phoneVerified, time.Since(startTime), clientIP)

	response.Success(c, "User registered successfully", gin.H{
		"message":  "Registration successful",
		"user_id":  newUser.ID.String(),
		"contact":  contact,
		"verified": emailVerified || phoneVerified,
	})
}

// Login handles user login
func (h *AuthHandler) Login(c *gin.Context) {
	startTime := time.Now()
	clientIP := c.ClientIP()

	var req types.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warning("❌ Invalid login request - Error: %v, ClientIP: %s", err, clientIP)
		response.Error(c, http.StatusBadRequest, "Invalid request data")
		return
	}

	// Determine login type and find user
	var user models.User
	var query *gorm.DB
	var contact string
	var loginType string

	if req.Email != nil && *req.Email != "" {
		if req.Phone != nil && *req.Phone != "" {
			response.Error(c, http.StatusBadRequest, "Please provide either email or phone, not both")
			return
		}
		query = h.db.Where("email = ?", *req.Email)
		contact = *req.Email
		loginType = "email"
	} else if req.Phone != nil && *req.Phone != "" {
		query = h.db.Where("phone = ?", *req.Phone)
		contact = *req.Phone
		loginType = "phone"
	} else {
		response.Error(c, http.StatusBadRequest, "Email or phone is required")
		return
	}

	if err := query.First(&user).Error; err != nil {
		h.logger.Warning("❌ User not found - Contact: %s, ClientIP: %s", contact, clientIP)
		response.Error(c, http.StatusUnauthorized, "Invalid credentials")
		return
	}

	// 如果提供了验证码，验证验证码
	if req.VerificationCode != nil && *req.VerificationCode != "" {
		if err := h.verifyCodeFromRedis("login", loginType, contact, *req.VerificationCode); err != nil {
			h.logger.Warning("❌ Verification code validation failed - %s: %s, Error: %v, ClientIP: %s", loginType, contact, err, clientIP)
			response.Error(c, http.StatusBadRequest, "Invalid or expired verification code")
			return
		}
		h.logger.Info("✅ Verification code validated successfully - %s: %s, ClientIP: %s", loginType, contact, clientIP)
	} else {
		// 如果没有提供验证码，验证密码
		if err := utils.VerifyPassword(user.Password, req.Password); err != nil {
			h.logger.Warning("❌ Password verification failed - Contact: %s, ClientIP: %s", contact, clientIP)
			response.Error(c, http.StatusUnauthorized, "Invalid credentials")
			return
		}
	}

	// Check if user is active
	if !user.IsActive() {
		h.logger.Warning("❌ Inactive user login attempt - UserID: %s, Contact: %s, ClientIP: %s", user.ID, contact, clientIP)
		response.Error(c, http.StatusForbidden, "Account is not active")
		return
	}

	// Update last login
	now := time.Now()
	user.LastLogin = &now
	if err := h.db.Save(&user).Error; err != nil {
		response.Error(c, http.StatusInternalServerError, "Failed to update user")
		return
	}

	// Generate tokens
	accessToken, refreshToken, accessExpiresAt, _, err := h.generateTokens(&user)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "Failed to generate tokens")
		return
	}

	// Create session record
	session := &models.UserSession{
		ID:        uuid.New(),
		UserID:    user.ID,
		Token:     accessToken,
		ExpiresAt: accessExpiresAt,
		IPAddress: clientIP,
		UserAgent: c.GetHeader("User-Agent"),
		IsActive:  true,
	}

	if err := h.db.Create(session).Error; err != nil {
		response.Error(c, http.StatusInternalServerError, "Failed to create session")
		return
	}

	// Set HTTP cookies
	c.SetCookie("access_token", accessToken, h.config.AccessTokenMaxAge*60, "/", h.config.Domain, false, true)
	c.SetCookie("refresh_token", refreshToken, h.config.RefreshTokenMaxAge*60, "/", h.config.Domain, false, true)

	h.logger.Info("✅ User login successful - UserID: %s, Contact: %s, Duration: %v, ClientIP: %s",
		user.ID, contact, time.Since(startTime), clientIP)

	// Return success response
	userResponse := h.convertToUserResponse(&user)
	loginResponse := types.LoginResponse{
		User:         userResponse,
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresIn:    h.config.AccessTokenMaxAge * 60,
	}

	response.Success(c, "Login successful", loginResponse)
}

// Logout handles user logout
func (h *AuthHandler) Logout(c *gin.Context) {
	// Always clear cookies first, regardless of authentication status
	// This ensures cleanup even if there are token issues
	defer func() {
		// Clear cookies
		c.SetCookie("access_token", "", -1, "/", h.config.Domain, false, true)
		c.SetCookie("refresh_token", "", -1, "/", h.config.Domain, false, true)
	}()

	currentUser, exists := c.Get("currentUser")
	if !exists {
		response.Error(c, http.StatusUnauthorized, "User not authenticated")
		return
	}

	// Convert the user object from token payload
	userMap, ok := currentUser.(map[string]interface{})
	if !ok {
		response.Error(c, http.StatusBadRequest, "Invalid user data")
		return
	}

	// Extract user ID (note: JSON field is "id" not "ID")
	userID, ok := userMap["id"].(string)
	if !ok {
		// Try alternative field name in case of different token format
		if userIDInterface, exists := userMap["ID"]; exists {
			userID, ok = userIDInterface.(string)
		}
		if !ok {
			h.logger.Error("❌ User ID not found in token payload - Available fields: %+v", userMap)
			response.Error(c, http.StatusBadRequest, "Invalid user ID")
			return
		}
	}

	// Get token from context (set by AuthMiddleware)
	tokenInterface, exists := c.Get("access_token")
	if !exists {
		response.Error(c, http.StatusBadRequest, "Access token not found in context")
		return
	}

	token, ok := tokenInterface.(string)
	if !ok {
		response.Error(c, http.StatusBadRequest, "Invalid access token format")
		return
	}

	// Deactivate session
	if err := h.db.Model(&models.UserSession{}).
		Where("user_id = ? AND token = ?", userID, token).
		Update("is_active", false).Error; err != nil {
		h.logger.Warning("Failed to deactivate session - UserID: %s, Error: %v", userID, err)
	}

	h.logger.Info("✅ User logout successful - UserID: %s, ClientIP: %s", userID, c.ClientIP())
	response.Success(c, "Logout successful", nil)
}

// RefreshToken handles token refresh
func (h *AuthHandler) RefreshToken(c *gin.Context) {
	refreshToken, err := c.Cookie("refresh_token")
	if err != nil {
		response.Error(c, http.StatusUnauthorized, "Invalid refresh token")
		return
	}
	// Validate refresh token
	payload, err := utils.ValidateToken(refreshToken, h.config.RefreshTokenPublicKey)
	if err != nil {
		response.Error(c, http.StatusUnauthorized, "Invalid refresh token")
		return
	}

	payloadMap, ok := payload.(map[string]interface{})
	if !ok {
		response.Error(c, http.StatusBadRequest, "Invalid token payload")
		return
	}

	userMap, ok := payloadMap["user"].(map[string]interface{})
	if !ok {
		response.Error(c, http.StatusBadRequest, "Invalid user in token payload")
		return
	}

	userID, ok := userMap["id"].(string)
	if !ok {
		response.Error(c, http.StatusBadRequest, "Invalid user ID in token")
		return
	}

	user_id, _ := uuid.Parse(userID)
	var user models.User
	if err := h.db.Where("id = ?", user_id).First(&user).Error; err != nil {
		response.Error(c, http.StatusNotFound, "User not found")
		return
	}

	// Check if user is active
	if !user.IsActive() {
		response.Error(c, http.StatusForbidden, "Account is not active")
		return
	}

	// Generate new tokens
	accessToken, refreshToken, _, _, err := h.generateTokens(&user)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "Failed to generate tokens")
		return
	}

	// Set new cookies
	c.SetCookie("access_token", accessToken, h.config.AccessTokenMaxAge*60, "/", h.config.Domain, false, true)
	c.SetCookie("refresh_token", refreshToken, h.config.RefreshTokenMaxAge*60, "/", h.config.Domain, false, true)

	refreshResponse := types.RefreshTokenResponse{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresIn:    h.config.AccessTokenMaxAge * 60,
	}

	response.Success(c, "Token refreshed successfully", refreshResponse)
}

// generateTokens generates access and refresh tokens for a user
func (h *AuthHandler) generateTokens(user *models.User) (string, string, time.Time, time.Time, error) {
	accessToken, accessExpiresAt, err := h.generateAccessToken(user)
	if err != nil {
		return "", "", time.Time{}, time.Time{}, fmt.Errorf("failed to generate access token: %w", err)
	}

	refreshToken, refreshExpiresAt, err := h.generateRefreshToken(user)
	if err != nil {
		return "", "", time.Time{}, time.Time{}, fmt.Errorf("failed to generate refresh token: %w", err)
	}

	return accessToken, refreshToken, accessExpiresAt, refreshExpiresAt, nil
}

// generateAccessToken generates a JWT access token for the user
func (h *AuthHandler) generateAccessToken(user *models.User) (string, time.Time, error) {
	// Set token expiry
	expiresAt := time.Now().Add(time.Duration(h.config.AccessTokenMaxAge) * time.Minute)
	ttl := time.Duration(h.config.AccessTokenMaxAge) * time.Minute

	// Create token payload with full user object
	payload := map[string]interface{}{
		"user": user,
		"type": "access",
	}

	// Generate JWT token using common utils
	token, err := utils.CreateToken(ttl, payload, h.config.AccessTokenPrivateKey)
	if err != nil {
		return "", time.Time{}, fmt.Errorf("failed to create access token: %w", err)
	}

	return token, expiresAt, nil
}

// generateRefreshToken generates a JWT refresh token for the user
func (h *AuthHandler) generateRefreshToken(user *models.User) (string, time.Time, error) {
	// Set token expiry
	expiresAt := time.Now().Add(time.Duration(h.config.RefreshTokenMaxAge) * time.Minute)
	ttl := time.Duration(h.config.RefreshTokenMaxAge) * time.Minute

	// Create token payload with full user object
	payload := map[string]interface{}{
		"user": user,
		"type": "refresh",
	}

	// Generate JWT token using common utils
	token, err := utils.CreateToken(ttl, payload, h.config.RefreshTokenPrivateKey)
	if err != nil {
		return "", time.Time{}, fmt.Errorf("failed to create refresh token: %w", err)
	}

	return token, expiresAt, nil
}

// GetCurrentUser returns current user information from token
func (h *AuthHandler) GetCurrentUser(c *gin.Context) {
	startTime := time.Now()
	clientIP := c.ClientIP()

	h.logger.Info("👤 GetCurrentUser started - ClientIP: %s", clientIP)

	// Get user from middleware context
	currentUser, exists := c.Get("currentUser")
	if !exists {
		h.logger.Warning("❌ User not found in context - ClientIP: %s", clientIP)
		response.Error(c, http.StatusUnauthorized, "User not authenticated")
		return
	}

	// Convert the user object from token payload
	userMap, ok := currentUser.(map[string]interface{})
	if !ok {
		h.logger.Error("❌ Invalid user context type - ClientIP: %s", clientIP)
		response.Error(c, http.StatusBadRequest, "Invalid user context")
		return
	}

	h.logger.Info("✅ User retrieved from context - Duration: %v, ClientIP: %s",
		time.Since(startTime), clientIP)

	response.Success(c, "Current user retrieved successfully", userMap)
}

// convertToUserResponse converts a User model to UserResponse
func (h *AuthHandler) convertToUserResponse(user *models.User) types.UserResponse {
	return types.UserResponse{
		ID:        user.ID.String(),
		Email:     user.Email,
		Phone:     user.Phone,
		Username:  user.Username,
		FirstName: user.FirstName,
		LastName:  user.LastName,
		Avatar:    user.Avatar,
		Bio:       user.Bio,
		Company:   user.Company,
		Country:   user.Country,
		LoginType: string(user.LoginType),
		Origin:    user.Origin,
		IsActive:  user.IsActive(),
		IsAdmin:   user.IsAdmin(),

		EmailVerified: user.EmailVerified,
		PhoneVerified: user.PhoneVerified,

		LoginProtection:        user.LoginProtection,
		PasswordChangeRequired: user.PasswordChangeRequired,
		SecurityLock:           user.SecurityLock,

		CreatedAt: user.CreatedAt.Format(time.RFC3339),
		UpdatedAt: user.UpdatedAt.Format(time.RFC3339),
	}
}
