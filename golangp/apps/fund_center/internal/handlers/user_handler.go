/*
 * @Description: User handler for Fund Center
 * @Author: AI Assistant
 * @Date: 2025-08-01
 */
package handlers

import (
	"fmt"
	"time"

	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/fund_center/internal/config"
	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/fund_center/internal/models"
	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/fund_center/pkg/types"
	"github.com/Arxtect/CoreXWorkSpace/golangp/common/gin_core/response"
	"github.com/Arxtect/CoreXWorkSpace/golangp/common/logging"
	"github.com/Arxtect/CoreXWorkSpace/golangp/common/utils"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// UserHandler handles user-related requests
type UserHandler struct {
	db     *gorm.DB
	config *config.Config
	logger *logging.Logger
}

// NewUserHandler creates a new user handler
func NewUserHandler(db *gorm.DB, config *config.Config, logger *logging.Logger) *UserHandler {
	return &UserHandler{
		db:     db,
		config: config,
		logger: logger,
	}
}

// GetProfile gets the current user's profile
func (h *UserHandler) GetProfile(c *gin.Context) {
	// 获取当前用户
	user, ok := response.GetUserFromContext[models.User](c)
	if !ok {
		response.Error(c, 401, "用户未认证")
		return
	}

	response.Success(c, "User profile retrieved successfully", user)
}

// UpdateProfile updates the current user's profile
func (h *UserHandler) UpdateProfile(c *gin.Context) {
	// 获取当前用户
	user, ok := response.GetUserFromContext[models.User](c)
	if !ok {
		response.Error(c, 401, "用户未认证")
		return
	}

	var req types.UpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, 400, "Invalid request data")
		return
	}

	if err := h.db.Where("id = ?", user.ID).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			response.Error(c, 404, "User not found")
			return
		}
		response.Error(c, 500, "Failed to get user")
		return
	}

	// Update user fields
	if req.Username != nil && *req.Username != "" {
		user.Username = req.Username
	}
	if req.FirstName != "" {
		user.FirstName = req.FirstName
	}
	if req.LastName != "" {
		user.LastName = req.LastName
	}
	if req.Avatar != "" {
		user.Avatar = req.Avatar
	}
	if req.Bio != "" {
		user.Bio = req.Bio
	}
	if req.Company != "" {
		user.Company = req.Company
	}
	if req.Country != "" {
		user.Country = req.Country
	}

	if err := h.db.Save(&user).Error; err != nil {
		response.Error(c, 500, "Failed to update user")
		return
	}

	h.logger.Info("✅ User profile updated - UserID: %s, ClientIP: %s", user.ID, c.ClientIP())

	response.Success(c, "User profile updated successfully", user)
}

// ChangePassword changes the current user's password
func (h *UserHandler) ChangePassword(c *gin.Context) {
	// 获取当前用户
	user, ok := response.GetUserFromContext[models.User](c)
	if !ok {
		response.Error(c, 401, "用户未认证")
		return
	}
	var req types.ChangePasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, 400, "Invalid request data")
		return
	}

	if err := h.db.Where("id = ?", user.ID).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			response.Error(c, 404, "User not found")
			return
		}
		response.Error(c, 500, "Failed to get user")
		return
	}

	// Verify current password
	if err := utils.VerifyPassword(user.Password, req.CurrentPassword); err != nil {
		response.Error(c, 401, "Current password is incorrect")
		return
	}

	// Hash new password
	hashedPassword, err := utils.HashPassword(req.NewPassword)
	if err != nil {
		response.Error(c, 500, "Failed to process new password")
		return
	}

	// Update password
	user.Password = hashedPassword
	if err := h.db.Save(&user).Error; err != nil {
		response.Error(c, 500, "Failed to update password")
		return
	}

	h.logger.Info("✅ Password changed - UserID: %s, ClientIP: %s", user.ID, c.ClientIP())
	response.Success(c, "操作成功", "Password changed successfully")
}

// UpdateUserStatus updates a user's status (admin only)
func (h *UserHandler) UpdateUserStatus(c *gin.Context) {
	idParam := c.Param("id")
	userID, err := uuid.Parse(idParam)
	if err != nil {
		response.Error(c, 400, "Invalid user ID")
		return
	}

	var req struct {
		Status string `json:"status" binding:"required,oneof=active inactive suspended deleted"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, 400, "Invalid request data")
		return
	}

	var user models.User
	if err := h.db.Where("id = ?", userID).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			response.Error(c, 404, "User not found")
			return
		}
		response.Error(c, 500, "Failed to get user")
		return
	}

	user.Status = models.UserStatus(req.Status)
	if err := h.db.Save(&user).Error; err != nil {
		response.Error(c, 500, "Failed to update user status")
		return
	}

	adminID, _ := c.Get("user_id")
	h.logger.Info("✅ User status updated - UserID: %s, NewStatus: %s, AdminID: %s, ClientIP: %s",
		userID, req.Status, adminID, c.ClientIP())

	userResponse := h.convertToUserResponse(&user)
	response.Success(c, "操作成功", userResponse)
}

// CreateUser creates a new user (admin only)
func (h *UserHandler) CreateUser(c *gin.Context) {
	var req types.AdminCreateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, 400, "Invalid request data")
		return
	}

	// 验证邮箱或手机号至少有一个
	if req.Email == nil && req.Phone == nil {
		response.Error(c, 400, "Email or phone number is required")
		return
	}

	// 检查邮箱是否已存在
	if req.Email != nil {
		var existingUser models.User
		if err := h.db.Where("email = ?", *req.Email).First(&existingUser).Error; err == nil {
			response.Error(c, 409, "Email already exists")
			return
		}
	}

	// 检查手机号是否已存在
	if req.Phone != nil {
		var existingUser models.User
		if err := h.db.Where("phone = ?", *req.Phone).First(&existingUser).Error; err == nil {
			response.Error(c, 409, "Phone number already exists")
			return
		}
	}

	// 检查用户名是否已存在
	if req.Username != nil {
		var existingUser models.User
		if err := h.db.Where("username = ?", *req.Username).First(&existingUser).Error; err == nil {
			response.Error(c, 409, "Username already exists")
			return
		}
	}

	// 加密密码
	hashedPassword, err := utils.HashPassword(req.Password)
	if err != nil {
		response.Error(c, 500, "Failed to process password")
		return
	}

	// 设置默认值
	role := models.UserRoleUser
	if req.Role != "" {
		role = models.UserRole(req.Role)
	}

	status := models.UserStatusActive
	if req.Status != "" {
		status = models.UserStatus(req.Status)
	}

	origin := "admin_created"
	if req.Origin != "" {
		origin = req.Origin
	}

	// 创建用户
	user := &models.User{
		Email:     req.Email,
		Phone:     req.Phone,
		Username:  req.Username,
		Password:  hashedPassword,
		FirstName: req.FirstName,
		LastName:  req.LastName,
		Avatar:    req.Avatar,
		Bio:       req.Bio,
		Company:   req.Company,
		Country:   req.Country,
		Role:      role,
		Status:    status,
		Origin:    origin,
		LoginType: models.LoginTypeEmail, // 默认邮箱登录
	}

	// 如果只有手机号，设置为手机登录
	if req.Email == nil && req.Phone != nil {
		user.LoginType = models.LoginTypePhone
		user.PhoneVerified = true
	} else if req.Email != nil {
		user.EmailVerified = true
	}

	if err := h.db.Create(user).Error; err != nil {
		response.Error(c, 500, "Failed to create user")
		return
	}

	adminID, _ := c.Get("user_id")
	h.logger.Info("✅ User created by admin - UserID: %s, AdminID: %s, ClientIP: %s",
		user.ID, adminID, c.ClientIP())

	userResponse := h.convertToUserResponse(user)
	response.Success(c, "User created successfully", userResponse)
}

// UpdateUser updates a user (admin only)
func (h *UserHandler) UpdateUser(c *gin.Context) {
	idParam := c.Param("id")
	userID, err := uuid.Parse(idParam)
	if err != nil {
		response.Error(c, 400, "Invalid user ID")
		return
	}

	var req types.AdminUpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, 400, "Invalid request data")
		return
	}

	var user models.User
	if err := h.db.Where("id = ?", userID).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			response.Error(c, 404, "User not found")
			return
		}
		response.Error(c, 500, "Failed to get user")
		return
	}

	// 检查邮箱唯一性
	if req.Email != nil && (user.Email == nil || *req.Email != *user.Email) {
		var existingUser models.User
		if err := h.db.Where("email = ? AND id != ?", *req.Email, userID).First(&existingUser).Error; err == nil {
			response.Error(c, 409, "Email already exists")
			return
		}
		user.Email = req.Email
	}

	// 检查手机号唯一性
	if req.Phone != nil && (user.Phone == nil || *req.Phone != *user.Phone) {
		var existingUser models.User
		if err := h.db.Where("phone = ? AND id != ?", *req.Phone, userID).First(&existingUser).Error; err == nil {
			response.Error(c, 409, "Phone number already exists")
			return
		}
		user.Phone = req.Phone
	}

	// 检查用户名唯一性
	if req.Username != nil && (user.Username == nil || *req.Username != *user.Username) {
		var existingUser models.User
		if err := h.db.Where("username = ? AND id != ?", *req.Username, userID).First(&existingUser).Error; err == nil {
			response.Error(c, 409, "Username already exists")
			return
		}
		user.Username = req.Username
	}

	// 更新其他字段
	if req.FirstName != "" {
		user.FirstName = req.FirstName
	}
	if req.LastName != "" {
		user.LastName = req.LastName
	}
	if req.Avatar != "" {
		user.Avatar = req.Avatar
	}
	if req.Bio != "" {
		user.Bio = req.Bio
	}
	if req.Company != "" {
		user.Company = req.Company
	}
	if req.Country != "" {
		user.Country = req.Country
	}
	if req.Role != "" {
		user.Role = models.UserRole(req.Role)
	}
	if req.Status != "" {
		user.Status = models.UserStatus(req.Status)
	}
	if req.Origin != "" {
		user.Origin = req.Origin
	}

	// 更新验证状态
	if req.EmailVerified != nil {
		user.EmailVerified = *req.EmailVerified
	}
	if req.PhoneVerified != nil {
		user.PhoneVerified = *req.PhoneVerified
	}

	// 更新安全相关字段
	if req.LoginProtection != nil {
		user.LoginProtection = *req.LoginProtection
	}
	if req.PasswordChangeRequired != nil {
		user.PasswordChangeRequired = *req.PasswordChangeRequired
	}
	if req.SecurityLock != nil {
		user.SecurityLock = *req.SecurityLock
	}

	if err := h.db.Save(&user).Error; err != nil {
		response.Error(c, 500, "Failed to update user")
		return
	}

	adminID, _ := c.Get("user_id")
	h.logger.Info("✅ User updated by admin - UserID: %s, AdminID: %s, ClientIP: %s",
		userID, adminID, c.ClientIP())

	userResponse := h.convertToUserResponse(&user)
	response.Success(c, "User updated successfully", userResponse)
}

// DeleteUser deletes a user (admin only)
func (h *UserHandler) DeleteUser(c *gin.Context) {
	idParam := c.Param("id")
	userID, err := uuid.Parse(idParam)
	if err != nil {
		response.Error(c, 400, "Invalid user ID")
		return
	}

	// 检查用户是否存在
	var user models.User
	if err := h.db.Where("id = ?", userID).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			response.Error(c, 404, "User not found")
			return
		}
		response.Error(c, 500, "Failed to get user")
		return
	}

	// 防止删除管理员账户
	if user.Role == models.UserRoleAdmin {
		response.Error(c, 403, "Cannot delete admin user")
		return
	}

	// 软删除用户
	if err := h.db.Delete(&user).Error; err != nil {
		response.Error(c, 500, "Failed to delete user")
		return
	}

	adminID, _ := c.Get("user_id")
	h.logger.Info("✅ User deleted by admin - UserID: %s, AdminID: %s, ClientIP: %s",
		userID, adminID, c.ClientIP())

	response.Success(c, "User deleted successfully", nil)
}

// GetUser gets a user by ID (admin only)
func (h *UserHandler) GetUser(c *gin.Context) {
	idParam := c.Param("id")
	userID, err := uuid.Parse(idParam)
	if err != nil {
		response.Error(c, 400, "Invalid user ID")
		return
	}

	var user models.User
	if err := h.db.Where("id = ?", userID).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			response.Error(c, 404, "User not found")
			return
		}
		response.Error(c, 500, "Failed to get user")
		return
	}

	userResponse := h.convertToUserResponse(&user)
	response.Success(c, "User retrieved successfully", userResponse)
}

// ListUsers lists users with pagination and filtering (admin only)
func (h *UserHandler) ListUsers(c *gin.Context) {
	var req types.UserListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.Error(c, 400, "Invalid query parameters")
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	if req.PageSize > 100 {
		req.PageSize = 100
	}

	// 构建查询
	query := h.db.Model(&models.User{})

	// 搜索条件
	if req.Search != "" {
		searchPattern := "%" + req.Search + "%"
		query = query.Where("email ILIKE ? OR phone ILIKE ? OR username ILIKE ? OR first_name ILIKE ? OR last_name ILIKE ?",
			searchPattern, searchPattern, searchPattern, searchPattern, searchPattern)
	}

	// 角色过滤
	if req.Role != "" {
		query = query.Where("role = ?", req.Role)
	}

	// 状态过滤
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		response.Error(c, 500, "Failed to count users")
		return
	}

	// 分页查询
	var users []models.User
	offset := (req.Page - 1) * req.PageSize
	if err := query.Offset(offset).Limit(req.PageSize).Order("created_at DESC").Find(&users).Error; err != nil {
		response.Error(c, 500, "Failed to get users")
		return
	}

	// 转换为响应格式
	userResponses := make([]types.UserResponse, len(users))
	for i, user := range users {
		userResponses[i] = h.convertToUserResponse(&user)
	}

	// 计算总页数
	totalPages := int((total + int64(req.PageSize) - 1) / int64(req.PageSize))

	listResponse := types.UserListResponse{
		Users:      userResponses,
		Total:      total,
		Page:       req.Page,
		PageSize:   req.PageSize,
		TotalPages: totalPages,
	}

	response.Success(c, "Users retrieved successfully", listResponse)
}

// ResetUserPassword resets a user's password (admin only)
func (h *UserHandler) ResetUserPassword(c *gin.Context) {
	idParam := c.Param("id")
	userID, err := uuid.Parse(idParam)
	if err != nil {
		response.Error(c, 400, "Invalid user ID")
		return
	}

	var req types.AdminResetPasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, 400, "Invalid request data")
		return
	}

	// 检查用户是否存在
	var user models.User
	if err := h.db.Where("id = ?", userID).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			response.Error(c, 404, "User not found")
			return
		}
		response.Error(c, 500, "Failed to get user")
		return
	}

	// 加密新密码
	hashedPassword, err := utils.HashPassword(req.NewPassword)
	if err != nil {
		response.Error(c, 500, "Failed to process password")
		return
	}

	// 更新密码并设置密码修改要求
	user.Password = hashedPassword
	user.PasswordChangeRequired = true

	if err := h.db.Save(&user).Error; err != nil {
		response.Error(c, 500, "Failed to reset password")
		return
	}

	adminID, _ := c.Get("user_id")
	h.logger.Info("✅ User password reset by admin - UserID: %s, AdminID: %s, ClientIP: %s",
		userID, adminID, c.ClientIP())

	response.Success(c, "Password reset successfully", nil)
}

// BatchOperateUsers performs batch operations on users (admin only)
func (h *UserHandler) BatchOperateUsers(c *gin.Context) {
	var req types.UserBatchOperationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, 400, "Invalid request data")
		return
	}

	// 验证用户ID
	userIDs := make([]uuid.UUID, len(req.UserIDs))
	for i, idStr := range req.UserIDs {
		id, err := uuid.Parse(idStr)
		if err != nil {
			response.Error(c, 400, fmt.Sprintf("Invalid user ID: %s", idStr))
			return
		}
		userIDs[i] = id
	}

	// 检查是否包含管理员用户
	if req.Operation == "delete" || req.Operation == "suspend" {
		var adminCount int64
		if err := h.db.Model(&models.User{}).Where("id IN ? AND role = ?", userIDs, models.UserRoleAdmin).Count(&adminCount).Error; err != nil {
			response.Error(c, 500, "Failed to check admin users")
			return
		}
		if adminCount > 0 {
			response.Error(c, 403, "Cannot perform this operation on admin users")
			return
		}
	}

	// 执行批量操作
	var err error
	switch req.Operation {
	case "activate":
		err = h.db.Model(&models.User{}).Where("id IN ?", userIDs).Update("status", models.UserStatusActive).Error
	case "deactivate":
		err = h.db.Model(&models.User{}).Where("id IN ?", userIDs).Update("status", models.UserStatusInactive).Error
	case "suspend":
		err = h.db.Model(&models.User{}).Where("id IN ?", userIDs).Update("status", models.UserStatusSuspended).Error
	case "delete":
		err = h.db.Where("id IN ?", userIDs).Delete(&models.User{}).Error
	default:
		response.Error(c, 400, "Invalid operation")
		return
	}

	if err != nil {
		response.Error(c, 500, "Failed to perform batch operation")
		return
	}

	adminID, _ := c.Get("user_id")
	h.logger.Info("✅ Batch operation performed by admin - Operation: %s, UserCount: %d, AdminID: %s, ClientIP: %s",
		req.Operation, len(userIDs), adminID, c.ClientIP())

	response.Success(c, fmt.Sprintf("Batch %s operation completed successfully", req.Operation), map[string]interface{}{
		"affected_users": len(userIDs),
		"operation":      req.Operation,
	})
}

// GetUserStats gets user statistics (admin only)
func (h *UserHandler) GetUserStats(c *gin.Context) {
	var stats types.UserStatsResponse

	// 总用户数
	if err := h.db.Model(&models.User{}).Count(&stats.TotalUsers).Error; err != nil {
		response.Error(c, 500, "Failed to get user statistics")
		return
	}

	// 活跃用户数
	if err := h.db.Model(&models.User{}).Where("status = ?", models.UserStatusActive).Count(&stats.ActiveUsers).Error; err != nil {
		response.Error(c, 500, "Failed to get user statistics")
		return
	}

	// 非活跃用户数
	if err := h.db.Model(&models.User{}).Where("status = ?", models.UserStatusInactive).Count(&stats.InactiveUsers).Error; err != nil {
		response.Error(c, 500, "Failed to get user statistics")
		return
	}

	// 被暂停用户数
	if err := h.db.Model(&models.User{}).Where("status = ?", models.UserStatusSuspended).Count(&stats.SuspendedUsers).Error; err != nil {
		response.Error(c, 500, "Failed to get user statistics")
		return
	}

	// 管理员用户数
	if err := h.db.Model(&models.User{}).Where("role = ?", models.UserRoleAdmin).Count(&stats.AdminUsers).Error; err != nil {
		response.Error(c, 500, "Failed to get user statistics")
		return
	}

	// 今日新用户
	today := time.Now().Truncate(24 * time.Hour)
	if err := h.db.Model(&models.User{}).Where("created_at >= ?", today).Count(&stats.NewUsersToday).Error; err != nil {
		response.Error(c, 500, "Failed to get user statistics")
		return
	}

	// 本周新用户
	weekStart := today.AddDate(0, 0, -int(today.Weekday()))
	if err := h.db.Model(&models.User{}).Where("created_at >= ?", weekStart).Count(&stats.NewUsersWeek).Error; err != nil {
		response.Error(c, 500, "Failed to get user statistics")
		return
	}

	// 本月新用户
	monthStart := time.Date(today.Year(), today.Month(), 1, 0, 0, 0, 0, today.Location())
	if err := h.db.Model(&models.User{}).Where("created_at >= ?", monthStart).Count(&stats.NewUsersMonth).Error; err != nil {
		response.Error(c, 500, "Failed to get user statistics")
		return
	}

	response.Success(c, "User statistics retrieved successfully", stats)
}

// convertToUserResponse converts a User model to UserResponse
func (h *UserHandler) convertToUserResponse(user *models.User) types.UserResponse {
	return types.UserResponse{
		ID:        user.ID.String(),
		Email:     user.Email,
		Phone:     user.Phone,
		Username:  user.Username,
		FirstName: user.FirstName,
		LastName:  user.LastName,
		Avatar:    user.Avatar,
		Bio:       user.Bio,
		Company:   user.Company,
		Country:   user.Country,
		LoginType: string(user.LoginType),
		Origin:    user.Origin,
		IsActive:  user.IsActive(),
		IsAdmin:   user.IsAdmin(),

		EmailVerified: user.EmailVerified,
		PhoneVerified: user.PhoneVerified,

		LoginProtection:        user.LoginProtection,
		PasswordChangeRequired: user.PasswordChangeRequired,
		SecurityLock:           user.SecurityLock,

		CreatedAt: user.CreatedAt.Format("2006-01-02T15:04:05Z07:00"),
		UpdatedAt: user.UpdatedAt.Format("2006-01-02T15:04:05Z07:00"),
	}
}
