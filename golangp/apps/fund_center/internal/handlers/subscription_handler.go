/*
 * @Description: Subscription handler for Fund Center membership system
 * @Author: AI Assistant
 * @Date: 2025-08-02
 */
package handlers

import (
	"fmt"
	"strconv"

	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/fund_center/internal/models"
	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/fund_center/internal/services"
	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/fund_center/pkg/types"
	"github.com/Arxtect/CoreXWorkSpace/golangp/common/gin_core/response"
	"github.com/Arxtect/CoreXWorkSpace/golangp/common/logging"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// SubscriptionHandler handles subscription-related HTTP requests
type SubscriptionHandler struct {
	subscriptionService *services.SubscriptionService
	db                  *gorm.DB
	logger              *logging.Logger
}

// NewSubscriptionHandler creates a new subscription handler
func NewSubscriptionHandler(db *gorm.DB, logger *logging.Logger) *SubscriptionHandler {
	return &SubscriptionHandler{
		subscriptionService: services.NewSubscriptionService(db, logger),
		db:                  db,
		logger:              logger,
	}
}

// GetPlans 获取订阅计划列表
func (h *SubscriptionHandler) GetPlans(c *gin.Context) {
	// 从常量获取计划列表（不包含试用计划）
	plans := h.subscriptionService.GetPlans()

	response.Success(c, "获取订阅计划成功", plans)
}

// GetMembershipComparison 获取会员权益对比
func (h *SubscriptionHandler) GetMembershipComparison(c *gin.Context) {
	// 从订阅服务获取计划列表
	plans := h.subscriptionService.GetPlans()

	// 构建计划信息映射
	planMap := make(map[string]models.SubscriptionPlanInfo)
	for _, plan := range plans {
		planMap[plan.PlanType] = plan
	}

	// 添加免费版信息
	freePlan := models.SubscriptionPlanInfo{
		PlanType:       "free",
		Name:           "免费版",
		Price:          0,
		Currency:       "CNY",
		DailyChatLimit: 10,
		Description:    "基础功能体验",
	}

	// 构建动态会员权益对比数据
	comparison := map[string]interface{}{
		"title": "会员权益对比",
		"features": []map[string]interface{}{
			{
				"name":    "每日消息数量",
				"free":    h.formatChatLimit(freePlan.DailyChatLimit),
				"monthly": h.formatChatLimit(h.getChatLimit(planMap, "monthly")),
				"yearly":  h.formatChatLimit(h.getChatLimit(planMap, "yearly")),
			},
			{
				"name":    "GPT-4访问",
				"free":    "✗",
				"monthly": "✓",
				"yearly":  "✓",
			},
			{
				"name":    "响应速度",
				"free":    "普通",
				"monthly": "优先",
				"yearly":  "最快",
			},
			{
				"name":    "专属客服",
				"free":    "✗",
				"monthly": "✓",
				"yearly":  "✓",
			},
			{
				"name":    "会员标识",
				"free":    "✗",
				"monthly": "✗",
				"yearly":  "✓",
			},
		},
		"plans": []map[string]interface{}{
			{
				"type":        freePlan.PlanType,
				"name":        freePlan.Name,
				"price":       freePlan.Price,
				"currency":    freePlan.Currency,
				"description": freePlan.Description,
			},
		},
	}

	// 添加付费计划
	planOrder := []string{"monthly", "yearly"} // 按顺序显示
	for _, planType := range planOrder {
		if plan, exists := planMap[planType]; exists {
			planData := map[string]interface{}{
				"type":        plan.PlanType,
				"name":        plan.Name,
				"price":       plan.Price,
				"currency":    plan.Currency,
				"description": plan.Description,
				"popular":     plan.Popular,
			}
			if plan.Discount != "" {
				planData["discount"] = plan.Discount
			}
			comparison["plans"] = append(comparison["plans"].([]map[string]interface{}), planData)
		}
	}

	response.Success(c, "获取会员权益对比成功", comparison)
}

// getChatLimit 获取指定计划的聊天限制
func (h *SubscriptionHandler) getChatLimit(planMap map[string]models.SubscriptionPlanInfo, planType string) int {
	if plan, exists := planMap[planType]; exists {
		return plan.DailyChatLimit
	}
	return 0
}

// formatChatLimit 格式化聊天限制显示
func (h *SubscriptionHandler) formatChatLimit(limit int) string {
	if limit <= 0 {
		return "无限制"
	}
	return fmt.Sprintf("%d条", limit)
}

// GetCurrentSubscription 获取当前用户订阅信息
func (h *SubscriptionHandler) GetCurrentSubscription(c *gin.Context) {
	// 获取当前用户
	user, ok := response.GetUserFromContext[models.User](c)
	if !ok {
		response.Error(c, 401, "用户未认证")
		return
	}

	// 获取当前订阅
	subscription, err := h.subscriptionService.GetCurrentSubscription(user.ID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			response.Success(c, "用户暂无订阅", nil)
			return
		}
		h.logger.Error("获取当前订阅失败: %v", err)
		response.Error(c, 500, "获取订阅信息失败")
		return
	}

	// 构建响应数据
	responseData := map[string]interface{}{
		"subscription_id":  subscription.ID,
		"plan_type":        subscription.PlanType,
		"status":           subscription.Status,
		"start_date":       subscription.StartDate,
		"end_date":         subscription.EndDate,
		"days_left":        subscription.GetDaysLeft(),
		"auto_renew":       subscription.AutoRenew,
		"membership_type":  user.MembershipType,
		"daily_chat_limit": user.GetDailyChatLimit(),
		"is_valid_member":  user.IsValidMember(),
		"is_trial_member":  user.IsTrialMember(),
		"is_paid_member":   user.IsPaidMember(),
	}

	response.Success(c, "获取当前订阅成功", responseData)
}

// GetSubscriptionHistory 获取用户订阅历史
func (h *SubscriptionHandler) GetSubscriptionHistory(c *gin.Context) {
	// 获取当前用户
	user, ok := response.GetUserFromContext[models.User](c)
	if !ok {
		response.Error(c, 401, "用户未认证")
		return
	}

	// 获取分页参数
	page := 1
	pageSize := 10

	if p := c.Query("page"); p != "" {
		if parsedPage, err := strconv.Atoi(p); err == nil && parsedPage > 0 {
			page = parsedPage
		}
	}

	if ps := c.Query("page_size"); ps != "" {
		if parsedPageSize, err := strconv.Atoi(ps); err == nil && parsedPageSize > 0 && parsedPageSize <= 100 {
			pageSize = parsedPageSize
		}
	}

	// 获取订阅历史
	subscriptions, total, err := h.subscriptionService.GetSubscriptionHistory(user.ID, page, pageSize)
	if err != nil {
		h.logger.Error("获取订阅历史失败: %v", err)
		response.Error(c, 500, "获取订阅历史失败")
		return
	}

	// 构建响应数据
	responseData := map[string]interface{}{
		"subscriptions": subscriptions,
		"pagination": map[string]interface{}{
			"page":       page,
			"page_size":  pageSize,
			"total":      total,
			"total_page": (total + int64(pageSize) - 1) / int64(pageSize),
		},
	}

	response.Success(c, "获取订阅历史成功", responseData)
}

// StartTrial 开始试用（内部方法，不暴露为API）
func (h *SubscriptionHandler) StartTrial(userID uuid.UUID, trialDays int) error {
	return h.subscriptionService.StartTrial(userID, trialDays)
}

// CanStartTrial 检查用户是否可以开始试用（内部方法）
func (h *SubscriptionHandler) CanStartTrial(userID uuid.UUID) (bool, string) {
	return h.subscriptionService.CanStartTrial(userID)
}

// GetUserMembershipInfo 获取用户会员信息
func (h *SubscriptionHandler) GetUserMembershipInfo(c *gin.Context) {
	// 获取当前用户
	user, ok := response.GetUserFromContext[models.User](c)
	if !ok {
		response.Error(c, 401, "用户未认证")
		return
	}

	// 构建会员信息
	membershipInfo := map[string]interface{}{
		"user_id":                 user.ID,
		"membership_type":         user.MembershipType,
		"membership_expiry":       user.MembershipExpiry,
		"days_left":               user.GetMembershipDaysLeft(),
		"is_valid_member":         user.IsValidMember(),
		"is_trial_member":         user.IsTrialMember(),
		"is_paid_member":          user.IsPaidMember(),
		"daily_chat_limit":        user.GetDailyChatLimit(),
		"can_access_advanced":     user.CanAccessAdvancedFeatures(),
		"can_access_professional": user.CanAccessProfessionalFeatures(),
	}

	response.Success(c, "获取会员信息成功", membershipInfo)
}

// AdminStartTrial 管理员为用户开始试用 (admin only)
func (h *SubscriptionHandler) AdminStartTrial(c *gin.Context) {
	// 获取用户ID参数
	idParam := c.Param("id")
	userID, err := uuid.Parse(idParam)
	if err != nil {
		response.Error(c, 400, "Invalid user ID")
		return
	}

	// 绑定请求数据
	var req types.AdminStartTrialRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, 400, "Invalid request data")
		return
	}

	// 检查用户是否存在
	var user models.User
	if err := h.db.First(&user, userID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			response.Error(c, 404, "User not found")
			return
		}
		h.logger.Error("查找用户失败: %v", err)
		response.Error(c, 500, "Failed to find user")
		return
	}

	// 调用订阅服务开始试用
	if err := h.subscriptionService.StartTrial(userID, req.TrialDays); err != nil {
		h.logger.Error("管理员为用户 %s 开始试用失败: %v", userID, err)
		response.Error(c, 400, err.Error())
		return
	}

	h.logger.Info("✅ 管理员为用户 %s 成功开始 %d 天试用", userID, req.TrialDays)

	// 构建响应数据
	responseData := map[string]interface{}{
		"user_id":    userID,
		"trial_days": req.TrialDays,
		"message":    "试用已成功开始",
	}

	response.Success(c, "试用开始成功", responseData)
}
