/*
 * @Description: User model for Fund Center
 * @Author: AI Assistant
 * @Date: 2025-08-01
 */
package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// UserRole represents user role types
type UserRole string

const (
	UserRoleUser  UserRole = "user"
	UserRoleAdmin UserRole = "admin"
)

// UserStatus represents user status types
type UserStatus string

const (
	UserStatusActive    UserStatus = "active"
	UserStatusInactive  UserStatus = "inactive"
	UserStatusSuspended UserStatus = "suspended"
	UserStatusDeleted   UserStatus = "deleted"
)

// LoginType represents login method types
type LoginType string

const (
	LoginTypeEmail LoginType = "email"
	LoginTypePhone LoginType = "phone"
)

// MembershipType represents membership types
type MembershipType string

const (
	MembershipTypeNone     MembershipType = "none"      // 非会员用户
	MembershipTypeTrial    MembershipType = "trial"     // 试用会员
	MembershipTypeMonthly  MembershipType = "monthly"   // 月会员
	MembershipTypeHalfYear MembershipType = "half_year" // 半年会员
	MembershipTypeYearly   MembershipType = "yearly"    // 年会员
)

// User represents a user in the Fund Center system
type User struct {
	ID       uuid.UUID `gorm:"type:uuid;default:uuid_generate_v4();primaryKey;comment:用户唯一标识符，使用UUID格式" json:"id"`
	Username *string   `gorm:"size:100;uniqueIndex:idx_user_username;comment:用户名，可选，唯一" json:"username"`

	// 登录方式相关字段
	Email     *string   `gorm:"size:255;uniqueIndex:idx_user_email;comment:邮箱地址，可选，如果设置则唯一" json:"email"`
	Phone     *string   `gorm:"size:20;uniqueIndex:idx_user_phone;comment:手机号码，可选，如果设置则唯一" json:"phone"`
	Password  string    `gorm:"size:255;not null;comment:加密后的密码，不在JSON中返回" json:"-"`
	LoginType LoginType `gorm:"size:20;not null;default:'email';comment:主要登录方式" json:"login_type"`

	// 验证相关字段
	EmailVerified          bool       `gorm:"default:false;index:idx_user_email_verified;comment:邮箱是否已验证" json:"email_verified"`
	PhoneVerified          bool       `gorm:"default:false;index:idx_user_phone_verified;comment:手机号是否已验证" json:"phone_verified"`
	EmailVerificationToken *string    `gorm:"size:255;comment:邮箱验证令牌，不在JSON中返回" json:"-"`
	PhoneVerificationCode  *string    `gorm:"size:10;comment:手机验证码，不在JSON中返回" json:"-"`
	VerificationCodeExpiry *time.Time `gorm:"comment:验证码过期时间" json:"-"`

	// 用户信息字段
	FirstName string `gorm:"size:100;comment:用户名字，可选" json:"first_name"`
	LastName  string `gorm:"size:100;comment:用户姓氏，可选" json:"last_name"`
	Avatar    string `gorm:"size:500;comment:用户头像URL，可选" json:"avatar"`
	Bio       string `gorm:"size:500;comment:用户个人简介，可选" json:"bio"`
	Company   string `gorm:"size:200;comment:用户所属公司，可选" json:"company"`
	Country   string `gorm:"size:100;comment:用户所在国家，可选" json:"country"`

	// 系统字段
	Role      UserRole   `gorm:"size:50;default:user;index:idx_user_role;comment:用户角色，定义权限级别" json:"role"`
	Status    UserStatus `gorm:"size:50;default:active;index:idx_user_status;comment:用户账户状态" json:"status"`
	Origin    string     `gorm:"size:50;default:local;index:idx_user_origin;comment:用户注册来源（local、google等）" json:"origin"`
	LastLogin *time.Time `gorm:"index:idx_user_last_login;comment:最后登录时间，可为空" json:"last_login"`

	// 会员相关字段
	MembershipType   MembershipType `gorm:"size:50;default:none;index:idx_user_membership_type;comment:会员类型" json:"membership_type"`
	MembershipExpiry *time.Time     `gorm:"index:idx_user_membership_expiry;comment:会员到期时间，非会员为空" json:"membership_expiry"`

	// 安全相关预留字段
	LoginProtection        bool `gorm:"default:false;comment:登录保护开关，预留字段" json:"login_protection"`
	PasswordChangeRequired bool `gorm:"default:false;comment:是否需要定期更改密码，预留字段" json:"password_change_required"`
	SecurityLock           bool `gorm:"default:false;comment:安全锁定状态，预留字段" json:"security_lock"`

	CreatedAt time.Time      `gorm:"autoCreateTime;comment:账户创建时间" json:"created_at"`
	UpdatedAt time.Time      `gorm:"autoUpdateTime;comment:账户最后更新时间" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index:idx_user_deleted_at;comment:软删除时间戳，不在JSON中返回" json:"-"`

	// Relationships
	Sessions []UserSession `gorm:"foreignKey:UserID" json:"sessions,omitempty"` // 用户的所有会话记录
}

// BeforeCreate sets the ID if not provided
func (u *User) BeforeCreate(tx *gorm.DB) error {
	if u.ID == uuid.Nil {
		u.ID = uuid.New()
	}
	return nil
}

// GetFullName returns the user's full name
func (u *User) GetFullName() string {
	if u.FirstName != "" && u.LastName != "" {
		return u.FirstName + " " + u.LastName
	}
	if u.FirstName != "" {
		return u.FirstName
	}
	if u.LastName != "" {
		return u.LastName
	}
	if u.Username != nil && *u.Username != "" {
		return *u.Username
	}
	if u.Email != nil {
		return *u.Email
	}
	if u.Phone != nil {
		return *u.Phone
	}
	return "Unknown User"
}

// IsActive checks if the user account is active
func (u *User) IsActive() bool {
	return u.Status == UserStatusActive
}

// IsAdmin checks if the user has admin privileges
func (u *User) IsAdmin() bool {
	return u.Role == UserRoleAdmin
}

// GetPrimaryContact returns the primary contact method based on login type
func (u *User) GetPrimaryContact() string {
	switch u.LoginType {
	case LoginTypeEmail:
		if u.Email != nil {
			return *u.Email
		}
	case LoginTypePhone:
		if u.Phone != nil {
			return *u.Phone
		}
	}
	return ""
}

// IsVerified checks if the user's primary contact method is verified
func (u *User) IsVerified() bool {
	switch u.LoginType {
	case LoginTypeEmail:
		return u.EmailVerified
	case LoginTypePhone:
		return u.PhoneVerified
	default:
		return false
	}
}

// IsValidMember 检查用户是否为有效会员
func (u *User) IsValidMember() bool {
	if u.MembershipType == MembershipTypeNone {
		return false
	}

	if u.MembershipExpiry == nil {
		return false
	}

	return time.Now().Before(*u.MembershipExpiry)
}

// IsTrialMember 检查用户是否为试用会员
func (u *User) IsTrialMember() bool {
	return u.MembershipType == MembershipTypeTrial && u.IsValidMember()
}

// IsPaidMember 检查用户是否为付费会员
func (u *User) IsPaidMember() bool {
	return u.IsValidMember() && u.MembershipType != MembershipTypeTrial && u.MembershipType != MembershipTypeNone
}

// GetMembershipDaysLeft 获取会员剩余天数
func (u *User) GetMembershipDaysLeft() int {
	if u.MembershipExpiry == nil {
		return 0
	}

	days := int(time.Until(*u.MembershipExpiry).Hours() / 24)
	if days < 0 {
		return 0
	}

	return days
}

// GetDailyChatLimit 获取每日聊天次数限制
func (u *User) GetDailyChatLimit() int {
	// 如果不是有效会员，返回0
	if !u.IsValidMember() {
		return 0
	}

	switch u.MembershipType {
	case MembershipTypeNone:
		return 0 // 非会员无法使用
	case MembershipTypeTrial:
		return 20 // 试用会员每日20次
	case MembershipTypeMonthly:
		return 50 // 月会员每日50次
	case MembershipTypeHalfYear, MembershipTypeYearly:
		return 200 // 半年/年会员每日200次
	default:
		return 0
	}
}

// CanAccessAdvancedFeatures 检查是否可以使用高级金融功能
func (u *User) CanAccessAdvancedFeatures() bool {
	if !u.IsValidMember() {
		return false
	}
	return u.MembershipType == MembershipTypeHalfYear ||
		u.MembershipType == MembershipTypeYearly
}

// CanAccessProfessionalFeatures 检查是否可以使用专业金融功能
func (u *User) CanAccessProfessionalFeatures() bool {
	if !u.IsValidMember() {
		return false
	}
	return u.MembershipType == MembershipTypeYearly
}

// TableName returns the table name for the User model
func (User) TableName() string {
	return "users"
}
