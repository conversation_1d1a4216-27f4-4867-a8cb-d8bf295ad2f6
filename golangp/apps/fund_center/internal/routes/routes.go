/*
 * @Description: Main routes setup for Fund Center
 * @Author: AI Assistant
 * @Date: 2025-08-01
 */
package routes

import (
	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/fund_center/internal/config"
	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/fund_center/internal/handlers"
	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/fund_center/internal/middleware"
	"github.com/Arxtect/CoreXWorkSpace/golangp/common/logging"
	"github.com/Arxtect/CoreXWorkSpace/golangp/common/redis"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// SetupRoutes configures all routes for the Fund Center application
func SetupRoutes(router *gin.Engine, authHandler *handlers.AuthHandler, db *gorm.DB, cfg *config.Config, logger *logging.Logger, redisClient *redis.Client) {
	// Add DIFY proxy middleware globally
	// This will intercept requests to messages, conversations, chat-messages and proxy them to DIFY
	router.Use(middleware.DifyProxyMiddleware(cfg, logger))

	// API v1 group
	v1 := router.Group("/api/v1")

	// Setup authentication routes
	setupAuthRoutes(v1, authHandler, cfg)

	// Setup user routes
	setupUserRoutes(v1, authHandler, db, cfg, logger)

	// Setup payment routes
	setupPaymentRoutes(v1, db, cfg, logger, redisClient)

	// Setup subscription routes
	SetupSubscriptionRoutes(v1, db, cfg, logger)

	// Setup app routes
	SetupAppRoutes(v1, db, cfg, logger)

	// Setup documentation routes
	setupDocRoutes(router)
}

// setupDocRoutes sets up documentation routes
func setupDocRoutes(router *gin.Engine) {
	// API documentation endpoint
	router.GET("/docs", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"message": "Fund Center API Documentation",
			"version": "1.0.0",
			"endpoints": gin.H{
				"auth": gin.H{
					"register": "POST /api/v1/auth/register",
					"login":    "POST /api/v1/auth/login",
					"logout":   "POST /api/v1/auth/logout",
					"refresh":  "POST /api/v1/auth/refresh",
				},
				"users": gin.H{
					"profile": "GET /api/v1/users/profile",
					"update":  "PUT /api/v1/users/profile",
				},
				"payments": gin.H{
					"alipay": gin.H{
						"qrcode": "GET /api/v1/alipay/qrcode?plan_type={plan} (会员订阅二维码)",
						"web":    "GET /api/v1/alipay/web?plan_type={plan} (会员订阅网页支付)",
						"status": "GET /api/v1/alipay/status",
						"notify": "POST /api/v1/alipay/notify (自动激活会员)",
					},
					"wechat": gin.H{
						"qrcode": "GET /api/v1/wechat/qrcode?plan_type={plan} (会员订阅二维码)",
						"jsapi":  "GET /api/v1/wechat/jsapi?plan_type={plan}&open_id={openid} (会员订阅JSAPI)",
						"status": "GET /api/v1/wechat/status",
						"notify": "POST /api/v1/wechat/notify (自动激活会员)",
					},
					"plan_types": "monthly, half_year, yearly",
					"note":       "需要认证，自动从计划配置获取价格和描述，支付成功后自动激活会员",
				},
				"subscriptions": gin.H{
					"plans":      "GET /api/v1/subscriptions/plans (获取订阅计划列表)",
					"comparison": "GET /api/v1/subscriptions/comparison (获取会员权益对比)",
					"current":    "GET /api/v1/subscriptions/current",
					"history":    "GET /api/v1/subscriptions/history",
					"membership": "GET /api/v1/subscriptions/membership",
				},
				"apps": gin.H{
					"show":            "GET /api/v1/app/show (获取展示应用列表)",
					"api_key":         "GET /api/v1/app/api_key/{id} (获取应用API密钥，需要认证)",
					"test_connection": "GET /api/v1/app/test-connection (测试DIFY连接和登录)",
					"note":            "集成DIFY平台，提供AI应用管理功能，使用邮箱密码登录认证",
				},
				"membership_features": gin.H{
					"chat_basic":           "POST /api/v1/chat/basic (需要会员)",
					"analysis_advanced":    "POST /api/v1/analysis/advanced (需要半年/年会员)",
					"professional_advice":  "POST /api/v1/professional/advice (需要年会员)",
					"professional_reports": "GET /api/v1/professional/reports (需要年会员)",
				},
			},
		})
	})
}
