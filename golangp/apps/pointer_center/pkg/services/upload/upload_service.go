/*
 * @Description: Upload service for file upload management
 * @Author: AI Assistant
 * @Date: 2025-07-16
 */
package upload

import (
	"bytes"
	"fmt"
	"io"
	"mime/multipart"
	"path/filepath"
	"time"

	"github.com/Arxtect/CoreXWorkSpace/golangp/common/logging"
	"github.com/Arxtect/CoreXWorkSpace/golangp/common/storage"
)

// Service handles file upload operations
type Service struct {
	storageService storage.StorageService
	bucket         string
	maxUploadSize  int64
	logger         *logging.Logger
}

// NewService creates a new upload service instance
func NewService(storageService storage.StorageService, bucket string, maxUploadSize int64) *Service {
	logger := logging.GetLogger("upload_service")
	return &Service{
		storageService: storageService,
		bucket:         bucket,
		maxUploadSize:  maxUploadSize,
		logger:         logger,
	}
}

// UploadFromMultipart uploads file from multipart form file
func (s *Service) UploadFromMultipart(file *multipart.FileHeader, category string) (string, error) {
	// Open the uploaded file
	src, err := file.Open()
	if err != nil {
		return "", fmt.Errorf("failed to open uploaded file: %w", err)
	}
	defer src.Close()

	// Read file data
	fileData, err := io.ReadAll(src)
	if err != nil {
		return "", fmt.Errorf("failed to read file data: %w", err)
	}

	// Upload using byte data
	return s.Upload(fileData, category, file.Filename)
}

// Upload uploads file from byte data
func (s *Service) Upload(fileData []byte, category string, originalFilename string) (string, error) {
	s.logger.Info("Upload called for category: %s, filename: %s", category, originalFilename)

	// Validate file data
	if len(fileData) == 0 {
		s.logger.Warning("Upload: file data is empty")
		return "", fmt.Errorf("file data is required")
	}

	s.logger.Info("Upload: max upload size: %d bytes", s.maxUploadSize)
	s.logger.Info("Upload: file data size: %d bytes", len(fileData))

	// Validate file size
	if int64(len(fileData)) > s.maxUploadSize {
		s.logger.Warning("Upload: file size %s exceeds limit %s",
			s.formatFileSize(int64(len(fileData))),
			s.formatFileSize(s.maxUploadSize))
		return "", fmt.Errorf("file size must be less than %s", s.formatFileSize(s.maxUploadSize))
	}

	// Detect content type
	contentType, _ := s.detectContentType(fileData)

	// Generate unique filename
	uniqueFilename := s.generateTimestampFilename(originalFilename)

	// Generate storage object key
	objectKey := s.generateObjectKey(uniqueFilename, category)

	// Create file reader
	reader := bytes.NewReader(fileData)
	objectSize := int64(len(fileData))

	// Upload to storage service
	objectKeyPath, uploadedPath, err := s.storageService.Upload(s.bucket, objectKey, reader, objectSize, contentType)
	if err != nil {
		s.logger.Error("Upload: failed to upload file: %v", err)
		return "", fmt.Errorf("failed to upload file: %v", err)
	}

	// Return the uploaded path as the URL
	fileURL := uploadedPath

	s.logger.Info("Upload: successfully uploaded file to %s (objectKey: %s)", fileURL, objectKeyPath)

	return fileURL, nil
}

// detectContentType detects file content type from byte data
func (s *Service) detectContentType(data []byte) (string, error) {
	if len(data) < 4 {
		return "", fmt.Errorf("data too short to detect content type")
	}

	// Check common image format magic numbers
	if len(data) >= 2 && data[0] == 0xFF && data[1] == 0xD8 {
		return "image/jpeg", nil
	}
	if len(data) >= 8 && data[0] == 0x89 && data[1] == 0x50 && data[2] == 0x4E && data[3] == 0x47 {
		return "image/png", nil
	}
	if len(data) >= 6 && data[0] == 0x47 && data[1] == 0x49 && data[2] == 0x46 {
		return "image/gif", nil
	}
	if len(data) >= 4 && data[0] == 0x52 && data[1] == 0x49 && data[2] == 0x46 && data[3] == 0x46 {
		return "image/webp", nil
	}

	return "application/octet-stream", nil
}

// generateTimestampFilename generates timestamp-based filename
func (s *Service) generateTimestampFilename(originalFilename string) string {
	now := time.Now()

	// Extract file extension from original filename
	ext := filepath.Ext(originalFilename)
	if ext == "" {
		ext = ".bin" // Default extension for files without extension
	}

	return fmt.Sprintf("%d%s", now.UnixNano(), ext)
}

// generateObjectKey generates storage object key
func (s *Service) generateObjectKey(filename, category string) string {
	// Organize files by year/month structure
	now := time.Now()
	year := now.Format("2006")
	month := now.Format("01")

	// Generate path: images/category/YYYYMM/filename
	return filepath.Join("images", category, year+month, filename)
}

// formatFileSize formats bytes to readable file size
func (s *Service) formatFileSize(bytes int64) string {
	const unit = 1024
	if bytes < unit {
		return fmt.Sprintf("%d B", bytes)
	}
	div, exp := int64(unit), 0
	for n := bytes / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(bytes)/float64(div), "KMGTPE"[exp])
}
