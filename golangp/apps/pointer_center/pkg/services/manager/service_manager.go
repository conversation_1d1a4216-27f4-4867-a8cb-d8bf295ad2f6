/*
 * @Description: Service manager for GEOK Center - manages global services
 * @Author: AI Assistant
 * @Date: 2025-07-16
 */
package manager

import (
	"fmt"

	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/internal/config"
	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/pkg/services/upload"
	"github.com/Arxtect/CoreXWorkSpace/golangp/common/logging"
	"github.com/Arxtect/CoreXWorkSpace/golangp/common/storage"
)

// ServiceManager manages global services
type ServiceManager struct {
	StorageService storage.StorageService
	UploadService  *upload.Service
	logger         *logging.Logger
}

var globalServiceManager *ServiceManager

// Initialize initializes all global services
func Initialize(cfg *config.Config) error {
	logger := logging.GetLogger("service_manager")

	// Initialize storage service
	storageConfig := storage.Config{
		Provider:        cfg.StorageProvider,
		Endpoint:        cfg.StorageEndpoint,
		AccessKeyID:     cfg.StorageAccessKeyID,
		SecretAccessKey: cfg.StorageSecretKey,
		UseSSL:          cfg.StorageUseSSL,
		Region:          cfg.StorageRegion,
	}

	storageService, err := storage.NewStorageService(storageConfig)
	if err != nil {
		return fmt.Errorf("failed to initialize storage service: %w", err)
	}

	// Initialize upload service
	uploadService := upload.NewService(
		storageService,
		cfg.StorageBucket,
		cfg.MaxUploadSize,
	)

	globalServiceManager = &ServiceManager{
		StorageService: storageService,
		UploadService:  uploadService,
		logger:         logger,
	}

	logger.Info("Services initialized successfully")
	return nil
}

// GetServiceManager returns the global service manager
func GetServiceManager() *ServiceManager {
	if globalServiceManager == nil {
		panic("Service manager not initialized. Call Initialize first.")
	}
	return globalServiceManager
}

// GetStorageService returns the global storage service
func GetStorageService() storage.StorageService {
	return GetServiceManager().StorageService
}

// GetUploadService returns the global upload service
func GetUploadService() *upload.Service {
	return GetServiceManager().UploadService
}
