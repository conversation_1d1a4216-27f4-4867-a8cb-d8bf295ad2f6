load("@rules_go//go:def.bzl", "go_library")

go_library(
    name = "manager",
    srcs = ["service_manager.go"],
    importpath = "github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/pkg/services/manager",
    visibility = ["//visibility:public"],
    deps = [
        "//golangp/apps/pointer_center/internal/config",
        "//golangp/apps/pointer_center/pkg/services/upload",
        "//golangp/common/logging:logger",
        "//golangp/common/storage",
    ],
)
