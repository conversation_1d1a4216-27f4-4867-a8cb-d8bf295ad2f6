/*
 * @Description: AI Agent Tools Implementation
 * @Author: AI Assistant
 * @Date: 2025-08-16
 */
package ai_agents

import (
	"context"
	"fmt"
	"log"

	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/internal/models"
	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/internal/models/student_profile"
	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/internal/repositories"
	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/pkg/utils"
	"github.com/google/uuid"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// AgentTools provides tool functions for AI agents to interact with the data layer
// All operations work on a working copy of the learning path until finalized
type AgentTools struct {
	repos *repositories.Repositories
}

// NewAgentTools creates a new instance of AgentTools
func NewAgentTools(repos *repositories.Repositories) *AgentTools {
	return &AgentTools{
		repos: repos,
	}
}

// StartPersonalizeWork creates a new personalize work session
// This should be called at the beginning of each AI agent conversation
func (at *AgentTools) StartPersonalizeWork(ctx context.Context, conversationID string, userID uuid.UUID, title, description string) (*models.PersonalizeWork, error) {
	// Check if there's already a work session for this conversation
	existingWork, err := at.repos.PersonalizeWork.GetByConversationID(ctx, conversationID)
	if err == nil {
		// Update last operation time and return existing work
		err = at.repos.PersonalizeWork.UpdateLastOperation(ctx, existingWork.ID)
		if err != nil {
			return nil, fmt.Errorf("failed to update last operation: %w", err)
		}
		return existingWork, nil
	}

	// Create new personalize work
	work := &models.PersonalizeWork{
		ConversationID: conversationID,
		UserID:         userID,
		Status:         models.PersonalizeWorkStatusInProgress,
		Title:          title,
		Description:    description,
	}

	err = at.repos.PersonalizeWork.Create(ctx, work)
	if err != nil {
		return nil, fmt.Errorf("failed to create personalize work: %w", err)
	}

	log.Printf("Started personalize work %s for conversation %s", work.ID, conversationID)
	return work, nil
}

// GetPersonalizeWork retrieves a personalize work by conversation ID
func (at *AgentTools) GetPersonalizeWork(ctx context.Context, conversationID string) (*models.PersonalizeWork, error) {
	work, err := at.repos.PersonalizeWork.GetByConversationID(ctx, conversationID)
	if err != nil {
		return nil, fmt.Errorf("failed to get personalize work: %w", err)
	}
	return work, nil
}

// UpdatePersonalizeWorkProgress updates the progress of a personalize work
func (at *AgentTools) UpdatePersonalizeWorkProgress(ctx context.Context, conversationID string, completedSteps, totalSteps int) error {
	work, err := at.repos.PersonalizeWork.GetByConversationID(ctx, conversationID)
	if err != nil {
		return fmt.Errorf("failed to get personalize work: %w", err)
	}

	err = at.repos.PersonalizeWork.UpdateLastOperation(ctx, work.ID)
	if err != nil {
		return fmt.Errorf("failed to update progress: %w", err)
	}

	log.Printf("Updated progress for conversation %s: %d/%d", conversationID, completedSteps, totalSteps)
	return nil
}

// SetPersonalizeWorkPath sets the working path for a personalize work
func (at *AgentTools) SetPersonalizeWorkPath(ctx context.Context, conversationID string, workingPathID, originalPathID uuid.UUID) error {
	work, err := at.repos.PersonalizeWork.GetByConversationID(ctx, conversationID)
	if err != nil {
		return fmt.Errorf("failed to get personalize work: %w", err)
	}

	// Update both working path and original path
	work.WorkingPathID = &workingPathID
	work.OriginalPathID = &originalPathID
	
	err = at.repos.PersonalizeWork.Update(ctx, work)
	if err != nil {
		return fmt.Errorf("failed to update personalize work: %w", err)
	}

	log.Printf("Set working path %s and original path %s for conversation %s", workingPathID, originalPathID, conversationID)
	return nil
}

// CompletePersonalizeWork marks a personalize work as completed
func (at *AgentTools) CompletePersonalizeWork(ctx context.Context, conversationID string) error {
	work, err := at.repos.PersonalizeWork.GetByConversationID(ctx, conversationID)
	if err != nil {
		return fmt.Errorf("failed to get personalize work: %w", err)
	}

	err = at.repos.PersonalizeWork.MarkCompleted(ctx, work.ID)
	if err != nil {
		return fmt.Errorf("failed to mark as completed: %w", err)
	}

	log.Printf("Completed personalize work for conversation %s", conversationID)
	return nil
}

// FailPersonalizeWork marks a personalize work as failed
func (at *AgentTools) FailPersonalizeWork(ctx context.Context, conversationID string, errorMsg string) error {
	work, err := at.repos.PersonalizeWork.GetByConversationID(ctx, conversationID)
	if err != nil {
		return fmt.Errorf("failed to get personalize work: %w", err)
	}

	err = at.repos.PersonalizeWork.MarkFailed(ctx, work.ID, errorMsg)
	if err != nil {
		return fmt.Errorf("failed to mark as failed: %w", err)
	}

	log.Printf("Failed personalize work for conversation %s: %s", conversationID, errorMsg)
	return nil
}

type StudentProfile struct {
	StaticProfile   *student_profile.StaticProfile   `json:"static_profile"`
	TechCompetency  *student_profile.TechCompetencyGraph `json:"tech_competency"`
}

// getStudentProfile retrieves the current student's learning background, skills, goals, and preferences
func (at *AgentTools) GetStudentProfile(ctx context.Context, userID uuid.UUID) (StudentProfile, error) {
	profile, err := at.repos.StaticProfile.GetByUserID(ctx, userID)
	if err != nil {
		return StudentProfile{}, fmt.Errorf("failed to get student profile: %w", err)
	}
	tech_competency, err := at.repos.TechCompetency.GetByUserID(ctx, userID.String())
	if err != nil {
		return StudentProfile{}, fmt.Errorf("failed to get tech competency: %w", err)
	}

	return StudentProfile{StaticProfile: profile, TechCompetency: tech_competency}, nil
}

// getPathNodes retrieves all nodes in the working learning path (with order preserved)
func (at *AgentTools) GetPathNodes(ctx context.Context, workingPathID uuid.UUID) ([]*models.LearningNode, error) {
	nodes, err := at.repos.LearningPathNode.GetNodesForPath(ctx, workingPathID)
	if err != nil {
		return nil, fmt.Errorf("failed to get path nodes: %w", err)
	}
	return nodes, nil
}

// getNodeLessons retrieves all lessons for a specific node (with order preserved)
func (at *AgentTools) GetNodeLessons(ctx context.Context, nodeID uuid.UUID) ([]*models.NodeLesson, error) {
	lessons, err := at.repos.NodeLesson.GetByNodeID(ctx, nodeID)
	if err != nil {
		return nil, fmt.Errorf("failed to get node lessons: %w", err)
	}
	return lessons, nil
}

// getLesson retrieves specific lesson content
func (at *AgentTools) GetLesson(ctx context.Context, lessonID string) (*models.LearningLesson, error) {
	lesson, err := at.repos.LearningLesson.GetByLessonID(ctx, lessonID)
	if err != nil {
		return nil, fmt.Errorf("failed to get lesson: %w", err)
	}
	return lesson, nil
}

// NodeSearchResult 表示节点搜索返回的精简信息
type NodeSearchResult struct {
	ID          uuid.UUID `json:"id"`
	Title       string    `json:"title"`
	Description string    `json:"description"`
}

// SearchNodes 根据关键词分页搜索节点，返回包含节点ID、标题和描述的结构体数组
// 参数：keyword, page, pageSize
func (at *AgentTools) SearchNodes(ctx context.Context, keyword string, page, pageSize int) ([]NodeSearchResult, error) {
	if page < 1 {
		page = 1
	}
	if pageSize < 1 {
		pageSize = 5
	}
	if pageSize > 20 {
		pageSize = 20
	}
	offset := (page - 1) * pageSize

	nodes, err := at.repos.LearningNode.Search(ctx, keyword, pageSize, offset)
	if err != nil {
		return nil, fmt.Errorf("failed to search nodes: %w", err)
	}

	results := make([]NodeSearchResult, 0, len(nodes))
	for _, n := range nodes {
		results = append(results, NodeSearchResult{
			ID:          n.ID,
			Title:       n.Title,
			Description: n.Description,
		})
	}
	return results, nil
}

// NodeLessonInfo 表示节点目录中的一条课程信息（按顺序）
type NodeLessonInfo struct {
	Title       string `json:"title"`
	Description string `json:"description"`
}

// GetNodeDirectory 查看节点目录：根据关联表及其排序键，返回顺序正确的 lesson 标题和描述
func (at *AgentTools) GetNodeDirectory(ctx context.Context, nodeID uuid.UUID) ([]NodeLessonInfo, error) {
	// 从 Postgres 读取节点与课程的关联，已按 order_key ASC 排序
	nodeLessons, err := at.repos.NodeLesson.GetByNodeID(ctx, nodeID)
	if err != nil {
		return nil, fmt.Errorf("failed to get node lessons: %w", err)
	}

	directory := make([]NodeLessonInfo, 0, len(nodeLessons))
	for _, nl := range nodeLessons {
		title := nl.LessonTitle
		desc := nl.LessonDescription
		// 若缓存为空，则回源 Mongo 获取
		if title == "" || desc == "" {
			lesson, lerr := at.repos.LearningLesson.GetByLessonID(ctx, nl.LessonID)
			if lerr == nil && lesson != nil {
				if title == "" {
					title = lesson.Title
				}
				if desc == "" {
					desc = lesson.Description
				}
			}
		}
		directory = append(directory, NodeLessonInfo{Title: title, Description: desc})
	}
	return directory, nil
}

// GetCourseByID 根据课程ID查看课程（与 GetLesson 等价，提供更贴合语义的别名）
func (at *AgentTools) GetCourseByID(ctx context.Context, id string) (*models.LearningLesson, error) {
	return at.GetLesson(ctx, id)
}

// replaceLesson replaces a lesson with new content
func (at *AgentTools) ReplaceLesson(ctx context.Context, lessonID string, newLesson *models.LearningLesson) error {
	// Parse the lessonID to get ObjectID
	oid, err := primitive.ObjectIDFromHex(lessonID)
	if err != nil {
		return fmt.Errorf("invalid lesson ID: %w", err)
	}
	
	// Set the ID and update the lesson
	newLesson.ID = oid
	err = at.repos.LearningLesson.Update(ctx, newLesson)
	if err != nil {
		return fmt.Errorf("failed to replace lesson: %w", err)
	}
	return nil
}

// removeLessonFromNode removes a lesson from a specific node
func (at *AgentTools) RemoveLessonFromNode(ctx context.Context, lessonID string, nodeID uuid.UUID) error {
	// Find the NodeLesson association
	nodeLessons, err := at.repos.NodeLesson.GetByNodeID(ctx, nodeID)
	if err != nil {
		return fmt.Errorf("failed to get node lessons: %w", err)
	}
	
	// Find the specific association and delete it
	for _, nl := range nodeLessons {
		if nl.LessonID == lessonID {
			err = at.repos.NodeLesson.Delete(ctx, nl.ID)
			if err != nil {
				return fmt.Errorf("failed to remove lesson from node: %w", err)
			}
			break
		}
	}
	return nil
}

// removeNodeFromPath removes an entire node from the learning path
func (at *AgentTools) RemoveNodeFromPath(ctx context.Context, workingPathID uuid.UUID, nodeID uuid.UUID) error {
	// Find the LearningPathNode association
	pathNodes, err := at.repos.LearningPathNode.GetByPathID(ctx, workingPathID)
	if err != nil {
		return fmt.Errorf("failed to get path nodes: %w", err)
	}
	
	// Find and delete the specific association
	for _, pn := range pathNodes {
		if pn.NodeID == nodeID {
			err = at.repos.LearningPathNode.Delete(ctx, pn.ID)
			if err != nil {
				return fmt.Errorf("failed to remove node from path: %w", err)
			}
			break
		}
	}
	return nil
}

// appendLessonToNode adds a new lesson to the end of a node with proper ordering
func (at *AgentTools) AppendLessonToNode(ctx context.Context, nodeID uuid.UUID, lessonTemplate *models.LearningLesson) error {
	// Create the lesson in MongoDB first
	err := at.repos.LearningLesson.Create(ctx, lessonTemplate)
	if err != nil {
		return fmt.Errorf("failed to create lesson: %w", err)
	}
	
	// Get existing lessons to determine the order key for the new lesson
	existingLessons, err := at.repos.NodeLesson.GetByNodeID(ctx, nodeID)
	if err != nil {
		return fmt.Errorf("failed to get existing lessons: %w", err)
	}
	
	// Generate order key for the new lesson (append to end)
	var orderKey string
	if len(existingLessons) == 0 {
		orderKey = utils.NextAfter("")
	} else {
		// Find the last order key
		lastOrderKey := ""
		for _, nl := range existingLessons {
			if nl.OrderKey > lastOrderKey {
				lastOrderKey = nl.OrderKey
			}
		}
		orderKey = utils.NextAfter(lastOrderKey)
	}
	
	// Create the NodeLesson association
	nodeLesson := &models.NodeLesson{
		NodeID:            nodeID,
		LessonID:          lessonTemplate.ID.Hex(),
		LessonTitle:       lessonTemplate.Title,
		LessonDescription: lessonTemplate.Description,
		OrderKey:          orderKey,
	}
	
	err = at.repos.NodeLesson.Create(ctx, nodeLesson)
	if err != nil {
		return fmt.Errorf("failed to create node-lesson association: %w", err)
	}
	return nil
}

// reorderNodeLessons adjusts the order of lessons within a node using lexicographic ordering
func (at *AgentTools) ReorderNodeLessons(ctx context.Context, nodeID uuid.UUID, lessonIDOrder []string) error {
	// Get current lessons for the node
	nodeLessons, err := at.repos.NodeLesson.GetByNodeID(ctx, nodeID)
	if err != nil {
		return fmt.Errorf("failed to get node lessons: %w", err)
	}
	
	// Create a map for quick lookup
	lessonMap := make(map[string]*models.NodeLesson)
	for _, nl := range nodeLessons {
		lessonMap[nl.LessonID] = nl
	}
	
	// Generate new order keys based on the provided order using lexicographic ordering
	for i, lessonID := range lessonIDOrder {
		if nodeLesson, exists := lessonMap[lessonID]; exists {
			var orderKey string
			if i == 0 {
				orderKey = utils.NextAfter("")
			} else {
				// Get the order key of the previous item in the new order
				prevLessonID := lessonIDOrder[i-1]
				if prevNodeLesson, prevExists := lessonMap[prevLessonID]; prevExists {
					var nextOrderKey string
					if i+1 < len(lessonIDOrder) {
						// Get the next item's current order key
						nextLessonID := lessonIDOrder[i+1]
						if nextNodeLesson, nextExists := lessonMap[nextLessonID]; nextExists {
							nextOrderKey = nextNodeLesson.OrderKey
						}
					}
					if nextOrderKey != "" {
						orderKey = utils.Between(prevNodeLesson.OrderKey, nextOrderKey)
					} else {
						orderKey = utils.NextAfter(prevNodeLesson.OrderKey)
					}
				} else {
					orderKey = utils.NextAfter("")
				}
			}
			
			// Update the order key
			nodeLesson.OrderKey = orderKey
			err = at.repos.NodeLesson.Update(ctx, nodeLesson)
			if err != nil {
				return fmt.Errorf("failed to update lesson order: %w", err)
			}
		}
	}
	return nil
}

// addNodeToPath inserts a new node into the learning path
func (at *AgentTools) AddNodeToPath(ctx context.Context, workingPathID uuid.UUID, newNode *models.LearningNode) error {
	// Create the node first
	err := at.repos.LearningNode.Create(ctx, newNode)
	if err != nil {
		return fmt.Errorf("failed to create node: %w", err)
	}
	
	// Create the LearningPathNode association
	pathNode := &models.LearningPathNode{
		LearningPathID: workingPathID,
		NodeID:         newNode.ID,
	}
	
	err = at.repos.LearningPathNode.Create(ctx, pathNode)
	if err != nil {
		return fmt.Errorf("failed to create path-node association: %w", err)
	}
	return nil
}

// taskCompleted notifies the system that a task has been completed and finalizes changes
// Can optionally complete a PersonalizeWork session if conversationID is provided
func (at *AgentTools) TaskCompleted(ctx context.Context, userID uuid.UUID, workingPathID uuid.UUID, message string, conversationID ...string) error {
	// If conversation ID is provided, complete the PersonalizeWork
	if len(conversationID) > 0 && conversationID[0] != "" {
		err := at.CompletePersonalizeWork(ctx, conversationID[0])
		if err != nil {
			// Log error but don't fail the operation
			log.Printf("Warning: failed to complete PersonalizeWork: %v", err)
		}
	}
	
	// Log the completion message
	log.Printf("Task completed for path %s: %s", workingPathID.String(), message)
	return nil
}
