package ai_agents

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/internal/repositories"
	"github.com/Arxtect/CoreXWorkSpace/golangp/common/dify"
	"github.com/google/uuid"
)

// AgentType represents different types of AI agents
type AgentType string

const (
	AgentTypeGoalPlanner         AgentType = "goal_planner"
	AgentTypePathGenerator       AgentType = "path_generator"
	AgentTypeCourseGenerator     AgentType = "course_generator"
	AgentTypeLessonGenerator     AgentType = "lesson_generator"
	AgentTypeQualityAssurance    AgentType = "quality_assurance"
	AgentTypeProfileTechAnalyzer AgentType = "profile_analyzer"
	AgentTypePersonalize         AgentType = "personalize"
)

// AgentRequest represents a request to an AI agent
// Note: This generic request might still be used internally or for less structured calls
type AgentRequest struct {
	AgentType AgentType              `json:"agent_type"`
	Inputs    map[string]interface{} `json:"inputs"`
}

// AgentResponse represents a response from an AI agent
type AgentResponse struct {
	Success        bool                   `json:"success"`
	Data           map[string]interface{} `json:"data"`
	Error          string                 `json:"error,omitempty"`
	Metadata       map[string]interface{} `json:"metadata"`
	ProcessingTime time.Duration          `json:"processing_time"`
	TokensUsed     int                    `json:"tokens_used"`
	Cost           float64                `json:"cost"`
}

// Manager manages AI agents and their interactions
type Manager struct {
	difyClient   *dify.Client
	agentAPIKeys map[string]string
	mutex        sync.RWMutex
	maxRetries   int
	timeout      int
	tools        *AgentTools
}

// NewManager creates a new AI agents manager
func NewManager(difyBaseURL string, agentAPIKeys map[string]string, timeout int, repos *repositories.Repositories) *Manager {
	// Create dify client configuration
	difyConfig := dify.Config{
		BaseURL: difyBaseURL,
		Timeout: time.Duration(timeout) * time.Second,
	}

	// Create dify client
	difyClient := dify.NewClient(difyConfig)

	manager := &Manager{
		difyClient:   difyClient,
		agentAPIKeys: agentAPIKeys,
		maxRetries:   3,
		timeout:      timeout,
		tools:        NewAgentTools(repos),
	}

	return manager
}

// GetAgent retrieves an agent's API key
func (m *Manager) GetAgent(agentType AgentType) (string, bool) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	apiKey, exists := m.agentAPIKeys[string(agentType)]
	return apiKey, exists
}

// GetPersonalizeAPIKey returns the API key for personalize agent (convenience accessor)
func (m *Manager) GetPersonalizeAPIKey() (string, bool) {
	return m.GetAgent(AgentTypePersonalize)
}

// Tools exposes a safe read-only handle to agent tools for services
func (m *Manager) Tools() *AgentTools { return m.tools }

// DifyClient exposes the underlying Dify client for services that need direct access
func (m *Manager) DifyClient() *dify.Client { return m.difyClient }

// GoalPlannerChat performs chat with the Goal Planner agent
// conversationID can be empty to start a new conversation
func (m *Manager) GoalPlannerChat(ctx context.Context, conversationID string, query string) (*AgentResponse, error) {
	// Get the goal planner agent API key
	apiKey, exists := m.GetAgent(AgentTypeGoalPlanner)
	if !exists {
		return &AgentResponse{Success: false, Error: "goal planner agent not registered"},
			fmt.Errorf("goal planner agent not registered")
	}
	return m.callChatAgent(ctx, apiKey, conversationID, query)
}

// CallAgent calls an AI agent with the given request
// This can remain as the generic internal call mechanism
func (m *Manager) CallAgent(ctx context.Context, request *AgentRequest) (*AgentResponse, error) {
	startTime := time.Now()

	// Get agent API key
	apiKey, exists := m.GetAgent(request.AgentType)
	if !exists {
		return &AgentResponse{
			Success:        false,
			Error:          fmt.Sprintf("agent %s not registered", request.AgentType),
			ProcessingTime: time.Since(startTime),
		}, nil
	}

	// Create context with timeout
	agentCtx, cancel := context.WithTimeout(ctx, time.Duration(m.timeout)*time.Second)
	defer cancel()

	// Call agent with retries
	var response *AgentResponse
	var err error

	for attempt := 0; attempt <= m.maxRetries; attempt++ {
		response, err = m.callAgentOnce(agentCtx, apiKey, request.Inputs)
		if err == nil && response.Success {
			break
		}

		if attempt < m.maxRetries {
			// Wait before retry
			time.Sleep(time.Duration(attempt+1) * time.Second)
		}
	}

	if response == nil {
		response = &AgentResponse{
			Success:   false,
			Error:     fmt.Sprintf("failed after %d attempts: %v", m.maxRetries+1, err),
		}
	}

	response.ProcessingTime = time.Since(startTime)
	return response, nil
}

// CallAgentAsync calls an AI agent asynchronously
// This can also remain as is, using the generic AgentRequest
func (m *Manager) CallAgentAsync(ctx context.Context, request *AgentRequest, callback func(*AgentResponse, error)) {
	go func() {
		response, err := m.CallAgent(ctx, request)
		callback(response, err)
	}()
}

// GenerateLearningPath generates a personalized learning path
// Modified to use a specific request struct
func (m *Manager) GenerateLearningPath(ctx context.Context, inputs map[string]interface{}) (*AgentResponse, error) {
	// check inputs
	if inputs["goal"] == "" {
		return nil, fmt.Errorf("goal is required")
	}
	if inputs["current_map"] == "" {
		return nil, fmt.Errorf("current_map is required")
	}
	if inputs["current_path"] == "" {
		return nil, fmt.Errorf("current_path is required")
	}
	request := &AgentRequest{
		AgentType: AgentTypePathGenerator,
		Inputs:    inputs,
	}

	return m.CallAgent(ctx, request)
}

// PlanGoal generates a goal plan using AI
func (m *Manager) PlanGoal(ctx context.Context, inputs map[string]interface{}) (*AgentResponse, error) {
	// check inputs
	if inputs["origin_goal"] == nil || inputs["origin_goal"] == "" {
		return nil, fmt.Errorf("origin_goal is required")
	}

	if inputs["current_map"] == nil || inputs["current_map"] == "" {
		return nil, fmt.Errorf("current_map is required")
	}

	request := &AgentRequest{
		AgentType: AgentTypeGoalPlanner,
		Inputs:    inputs,
	}

	return m.CallAgent(ctx, request)
}

// GenerateCourse generates a course using AI
func (m *Manager) GenerateCourse(ctx context.Context, inputs map[string]interface{}) (*AgentResponse, error) {
	// check inputs
	if inputs["node"] == nil || inputs["node"] == "" {
		return nil, fmt.Errorf("node is required")
	}
	if inputs["student_profile"] == nil || inputs["student_profile"] == "" {
		return nil, fmt.Errorf("student_profile is required")
	}

	request := &AgentRequest{
		AgentType: AgentTypeCourseGenerator,
		Inputs:    inputs,
	}

	return m.CallAgent(ctx, request)
}

// GenerateLesson generates a lesson using AI
func (m *Manager) GenerateLesson(ctx context.Context, inputs map[string]interface{}) (*AgentResponse, error) {
	// check inputs
	if inputs["lesson"] == nil || inputs["lesson"] == "" {
		return nil, fmt.Errorf("lesson is required")
	}
	if inputs["student_profile"] == nil || inputs["student_profile"] == "" {
		return nil, fmt.Errorf("student_profile is required")
	}

	request := &AgentRequest{
		AgentType: AgentTypeLessonGenerator,
		Inputs:    inputs,
	}

	return m.CallAgent(ctx, request)
}

// AnalyzeTechProfile analyzes a student profile
func (m *Manager) AnalyzeTechProfile(ctx context.Context, inputs map[string]interface{}) (*AgentResponse, error) {
	// check inputs
	if inputs["current_competency_graph"] == nil {
		return nil, fmt.Errorf("current_competency_graph is required")
	}
	if inputs["student_action"] == nil {
		return nil, fmt.Errorf("student_action is required")
	}
	if inputs["ai_evaluation"] == nil {
		return nil, fmt.Errorf("ai_evaluation is required")
	}

	request := &AgentRequest{
		AgentType: AgentTypeProfileTechAnalyzer,
		Inputs:    inputs,
	}

	return m.CallAgent(ctx, request)
}

// QualityCheck performs quality assurance on generated content
func (m *Manager) QualityCheck(ctx context.Context, inputs map[string]interface{}) (*AgentResponse, error) {
	// check inputs
	if inputs["content_data"] == nil {
		return nil, fmt.Errorf("content_data is required")
	}
	// check_type is optional, so no validation needed

	request := &AgentRequest{
		AgentType: AgentTypeQualityAssurance,
		Inputs:    inputs,
	}

	return m.CallAgent(ctx, request)
}

// callAgentOnce makes a single call to an AI agent
// This internal helper remains unchanged as it works with the generic AgentRequest
func (m *Manager) callAgentOnce(ctx context.Context, apiKey string, inputs map[string]interface{}) (*AgentResponse, error) {
	// Convert struct values to strings in inputs (modify in place)
	for key, value := range inputs {
		if value != nil {
			// Check if value is a struct (not a basic type)
			switch value.(type) {
			case string, int, int8, int16, int32, int64, uint, uint8, uint16, uint32, uint64, float32, float64, bool:
				// Basic types, keep as is
				continue
			default:
				// Convert struct or complex types to JSON string
				jsonStr, err := ToJSONString(value)
				if err != nil {
					return &AgentResponse{
						Success:   false,
						Error:     fmt.Sprintf("failed to convert input %s to string: %v", key, err),
					}, err
				}
				inputs[key] = jsonStr
			}
		}
	}

	// Prepare Dify workflow request
	difyRequest := dify.WorkflowRequest{
		Inputs:       inputs,
		ResponseMode: "blocking",
		User:         "pageflux",
	}

	// Call Dify Workflow API with agent's API key
	difyResponse, err := m.difyClient.RunWorkflow(ctx, difyRequest, apiKey)
	if err != nil {
		return &AgentResponse{
			Success:   false,
			Error:     err.Error(),
		}, err
	}

	// Parse response
	response := &AgentResponse{
		Success:   true,
		Data:      make(map[string]interface{}),
		Metadata:  make(map[string]interface{}),
	}

	// Extract data from workflow response
	if difyResponse.Data.Outputs != nil {
		response.Data = difyResponse.Data.Outputs
	}

	// Add workflow metadata
	response.Metadata["workflow_run_id"] = difyResponse.WorkflowRunID
	response.Metadata["task_id"] = difyResponse.TaskID
	response.Metadata["status"] = difyResponse.Data.Status
	response.Metadata["elapsed_time"] = difyResponse.Data.ElapsedTime

	// Extract token usage
	if difyResponse.Data.TotalTokens > 0 {
		response.TokensUsed = difyResponse.Data.TotalTokens
	}

	// Handle workflow errors
	if difyResponse.Data.Status == "failed" || difyResponse.Data.Error != "" {
		response.Success = false
		response.Error = difyResponse.Data.Error
	}

	return response, nil
}

// PersonalizeWithChat performs personalization using chat-based AI agent
// This method integrates with PersonalizeWork to track the conversation
func (m *Manager) PersonalizeWithChat(ctx context.Context, conversationID string, userID uuid.UUID, query string) (*AgentResponse, error) {
	// Get the personalize agent API key
	apiKey, exists := m.GetAgent(AgentTypePersonalize)
	if !exists {
		return &AgentResponse{
			Success: false,
			Error:   "personalize agent not registered",
		}, fmt.Errorf("personalize agent not registered")
	}

	// Get or create PersonalizeWork session
	work, err := m.tools.GetPersonalizeWork(ctx, conversationID)
	if err != nil {
		// If PersonalizeWork doesn't exist, create it
		work, err = m.tools.StartPersonalizeWork(ctx, conversationID, userID, 
			"Chat-based Personalization", 
			"Personalizing learning experience through conversational AI")
		if err != nil {
			return &AgentResponse{
				Success: false,
				Error:   fmt.Sprintf("failed to create personalize work: %v", err),
			}, err
		}
	}

	// Update last operation time
	err = m.tools.UpdatePersonalizeWorkProgress(ctx, conversationID, 0, 1)
	if err != nil {
		// Log but don't fail
		fmt.Printf("Warning: failed to update progress: %v\n", err)
	}

	// Call the chat agent
	response, err := m.callChatAgent(ctx, apiKey, conversationID, query)
	if err != nil {
		// Mark the work as failed
		m.tools.FailPersonalizeWork(ctx, conversationID, err.Error())
		return response, err
	}

	// If successful and conversation indicates completion, mark as completed
	if response.Success {
		// Update progress to completed
		err = m.tools.UpdatePersonalizeWorkProgress(ctx, conversationID, 1, 1)
		if err != nil {
			fmt.Printf("Warning: failed to update final progress: %v\n", err)
		}

		// Add PersonalizeWork metadata to response
		response.Metadata["personalize_work_id"] = work.ID
		response.Metadata["work_status"] = work.Status
	}

	return response, nil
}

// callChatAgent is the internal method for calling chat-based AI agents
func (m *Manager) callChatAgent(ctx context.Context, apiKey string, conversationID string, query string) (*AgentResponse, error) {
	// Prepare Dify chat request
	difyRequest := dify.ChatRequest{
		User:           "pageflux",
		Query:          query,
	}
	// Only set ConversationID when provided; empty means create new conversation
	if conversationID != "" { difyRequest.ConversationID = conversationID }

	// Call Dify Chat API with agent's API key
	difyResponse, err := m.difyClient.ChatCompletion(ctx, difyRequest, apiKey)
	if err != nil {
		return &AgentResponse{
			Success: false,
			Error:   err.Error(),
		}, err
	}

	// Parse response
	response := &AgentResponse{
		Success:  true,
		Data:     make(map[string]interface{}),
		Metadata: make(map[string]interface{}),
	}

	// Extract data from chat response
	if difyResponse.Answer != "" {
		response.Data["answer"] = difyResponse.Answer
	} else {
		// 如果答案为空，设置错误状态但不返回error，让调用方决定如何处理
		response.Success = false
		response.Error = "empty answer received from AI agent"
	}

	// Add conversation metadata
	response.Metadata["conversation_id"] = difyResponse.ConversationID
	response.Metadata["message_id"] = difyResponse.MessageID
	response.Metadata["task_id"] = difyResponse.TaskID
	response.Metadata["created_at"] = difyResponse.CreatedAt
	response.Metadata["model"] = difyResponse.Model

	// Add usage metadata safely (check for nil)
	if difyResponse.Metadata.Usage.TotalTokens > 0 {
		response.Metadata["usage"] = difyResponse.Metadata.Usage
		response.TokensUsed = difyResponse.Metadata.Usage.TotalTokens
		
		// 如果有价格信息，也添加到响应中
		if difyResponse.Metadata.Usage.TotalPrice != "" {
			response.Metadata["total_price"] = difyResponse.Metadata.Usage.TotalPrice
			response.Metadata["currency"] = difyResponse.Metadata.Usage.Currency
		}
	}

	return response, nil
}

func ToJSONString(v interface{}) (string, error) {
	if v == nil {
		return "", nil
	}

	// 如果已经是 string，直接返回
	if str, ok := v.(string); ok {
		return str, nil
	}

	// 否则尝试序列化为 JSON 字符串
	data, err := json.Marshal(v)
	if err != nil {
		return "", err
	}
	return string(data), nil
}
