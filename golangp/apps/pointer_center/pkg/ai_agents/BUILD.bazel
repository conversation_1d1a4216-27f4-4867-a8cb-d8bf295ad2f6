load("@rules_go//go:def.bzl", "go_library")

# AI Agents Manager
go_library(
    name = "ai_agents",
    srcs = [
        "model_manager.go",
        "agent_tools.go",
    ],
    importpath = "github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/pkg/ai_agents",
    visibility = ["//visibility:public"],
    deps = [
        "//golangp/common/dify",
        "//golangp/apps/pointer_center/internal/models",
        "//golangp/apps/pointer_center/internal/models/student_profile",
        "//golangp/apps/pointer_center/internal/repositories", 
        "//golangp/apps/pointer_center/pkg/utils",
        "@com_github_google_uuid//:uuid",
        "@org_mongodb_go_mongo_driver//bson/primitive",
    ],
)
