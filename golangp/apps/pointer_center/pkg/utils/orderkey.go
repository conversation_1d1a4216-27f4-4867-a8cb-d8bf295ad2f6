package utils

// A minimal lexicographic rank generator for sortable keys.
// It uses a base-36 alphabet to generate keys that sort lexicographically
// in the same order as their numeric values.

const alphabet = "0123456789abcdefghijklmnopqrstuvwxyz"

// NextAfter returns a key that sorts after the given key.
// If key is empty, it returns a sensible starting key.
func NextAfter(key string) string {
    if key == "" {
        return "m" // middle starting key
    }
    // Append the middle character to push it slightly after
    return key + "m"
}

// Between returns a key that sorts lexicographically between a and b.
// If a is empty, it returns a prefix of b. If b is empty, returns NextAfter(a).
// This is a simplified approach sufficient for most insertions; in rare cases
// where no space exists, callers should trigger a rebalance.
func Between(a, b string) string {
    if a == "" && b == "" {
        return "m"
    }
    if a == "" {
        // choose a prefix slightly before b
        return prefixMid(b)
    }
    if b == "" {
        return NextAfter(a)
    }
    // Find first position where a and b differ
    i := 0
    for i < len(a) && i < len(b) && a[i] == b[i] {
        i++
    }
    if i == len(a) && i == len(b) {
        // identical; append mid to a
        return a + "m"
    }
    var left byte = '0'
    if i < len(a) { left = a[i] }
    var right byte = 'z'
    if i < len(b) { right = b[i] }
    if left == right {
        // extend a by a mid char to move between
        return a + "m"
    }
    // choose a char strictly between left and right if possible
    li := indexOf(left)
    ri := indexOf(right)
    if li < 0 { li = 0 }
    if ri < 0 { ri = len(alphabet)-1 }
    if ri-li > 1 {
        mi := (li + ri) / 2
        return a[:i] + string(alphabet[mi])
    }
    // no room; prefer a + mid char
    return a + "m"
}

func prefixMid(b string) string {
    if b == "" { return "m" }
    // pick a char smaller than first char of b if possible
    bi := indexOf(b[0])
    if bi > 0 {
        return string(alphabet[bi-1]) + "m"
    }
    // otherwise, just return "m" to start in the middle
    return "m"
}

func indexOf(ch byte) int {
    for i := 0; i < len(alphabet); i++ {
        if alphabet[i] == ch { return i }
    }
    return -1
}
