package utils

import (
	"encoding/json"
	"fmt"
)

// ParseAIAnswerField is a helper function to parse AI response answer field
// It handles both JSON string and object formats
func ParseAIAnswerField(aiData map[string]interface{}, target interface{}) error {
	answerData, ok := aiData["answer"]
	if !ok {
		return fmt.<PERSON>rrorf("answer field not found in AI response")
	}

	// Handle case where answer is already a string (JSON string)
	if answerStr, isString := answerData.(string); isString {
		// If it's already a JSON string, unmarshal directly
		if err := json.Unmarshal([]byte(answerStr), target); err != nil {
			return fmt.Errorf("failed to unmarshal AI response from JSON string: %w", err)
		}
	} else {
		// If it's an object, marshal first then unmarshal
		answerBytes, err := json.Marshal(answerData)
		if err != nil {
			return fmt.Errorf("failed to marshal answer data: %w", err)
		}

		if err := json.Unmarshal(answerBytes, target); err != nil {
			return fmt.E<PERSON><PERSON>("failed to unmarshal AI response: %w", err)
		}
	}

	return nil
}
