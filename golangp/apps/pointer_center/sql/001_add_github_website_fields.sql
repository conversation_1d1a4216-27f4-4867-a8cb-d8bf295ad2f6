-- PostgreSQL版本：添加GitHub和Website字段到users表
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS github VARCHAR(255) DEFAULT '';

ALTER TABLE users 
ADD COLUMN IF NOT EXISTS website VARCHAR(255) DEFAULT '';

-- 为新字段添加列注释
COMMENT ON COLUMN users.github IS '用户GitHub地址，可选，格式如：https://github.com/username';
COMMENT ON COLUMN users.website IS '用户个人网站地址，可选，格式如：https://example.com';

-- 为新字段添加条件索引以提高查询性能（只为非空值建索引）
CREATE INDEX IF NOT EXISTS idx_users_github ON users(github) WHERE github != '';
CREATE INDEX IF NOT EXISTS idx_users_website ON users(website) WHERE website != '';