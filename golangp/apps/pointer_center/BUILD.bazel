load("@rules_go//go:def.bzl", "go_binary")

exports_files(
    ["app.env"],
    visibility = ["//visibility:public"],
)

go_binary(
    name = "pointer_center",
    data = [
        "app.env",
        "//golangp/apps/pointer_center/certs:apiclient_cert.pem",
        "//golangp/apps/pointer_center/certs:apiclient_key.pem",
    ],
    embed = ["//golangp/apps/pointer_center/cmd"],
    visibility = ["//visibility:public"],
)
