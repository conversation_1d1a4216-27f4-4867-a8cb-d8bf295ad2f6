package handlers

import (
	"context"
	"fmt"
	"time"

	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/internal/models"
	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/internal/services"
	"github.com/Arxtect/CoreXWorkSpace/golangp/common/gin_core/response"
	"github.com/Arxtect/CoreXWorkSpace/golangp/common/logging"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// LearningPathHandler handles HTTP requests for learning paths
type LearningPathHandler struct {
	service *services.LearningPathService
}

// NewLearningPathHandler creates a new learning path handler
func NewLearningPathHandler(service *services.LearningPathService) *LearningPathHandler {
	return &LearningPathHandler{service: service}
}

// CreateLearningPath godoc
// @Summary Create a new learning path
// @Description Creates a new standard learning path. Requires authentication.
// @Tags Learning Paths
// @Accept json
// @Produce json
// @Param request body services.CreateLearningPathRequest true "Create Learning Path Request"
// @Success 201 {object} models.LearningPath
// @Failure 400 {object} response.APIError
// @Failure 401 {object} response.APIError
// @Failure 500 {object} response.APIError
// @Router /learning-paths [post]
// @Security BearerAuth
func (h *LearningPathHandler) CreateLearningPath(c *gin.Context) {
	var req services.CreateLearningPathRequest

	// Bind JSON request
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "Invalid request body", err)
		return
	}

	// Get user ID from context (set by AuthMiddleware)
	currentUser, exists := response.GetUserFromContext[models.User](c)
	if !exists {
		response.Unauthorized(c, "User not authenticated", nil)
		return
	}
	// Assuming user_id is a string, convert it to uuid.UUID if needed
	// For now, we assume the service expects the CreatorID to be set in the request
	// and validated by the service layer. If not, you might need to parse userID here.
	// req.CreatedBy = uuid.MustParse(userID.(string)) // Example if needed

	// Set CreatedBy from authenticated user if not provided in request
	// Or enforce it always comes from context for security
	if req.CreatedBy == uuid.Nil {
		// Prefer setting it from context for security
		parsedUserID := currentUser.ID
		req.CreatedBy = parsedUserID
	}

	// Call service
	ctx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Second)
	defer cancel()

	path, err := h.service.CreateLearningPath(ctx, &req)
	if err != nil {
		logging.Error("Handler: Failed to create learning path: %v", err)
		response.InternalServerError(c, "Failed to create learning path", err)
		return
	}

	response.Created(c, "Learning path created successfully", path)
}

// GenerateLearningPathWithAI godoc
// @Summary Generate a learning path using AI
// @Description Generates a personalized learning path based on user profile and goal. Requires authentication.
// @Tags Learning Paths
// @Accept json
// @Produce json
// @Param request body services.GeneratePathRequest true "Generate Path Request"
// @Success 201 {object} models.LearningPath
// @Failure 400 {object} response.APIError
// @Failure 401 {object} response.APIError
// @Failure 500 {object} response.APIError
// @Router /learning-paths/generate [post]
// @Security BearerAuth
func (h *LearningPathHandler) GenerateLearningPathWithAI(c *gin.Context) {
	var req services.GeneratePathRequest

	// Bind JSON request
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "Invalid request body", err)
		return
	}

	// Get user ID from context (set by AuthMiddleware)
	currentUser, exists := response.GetUserFromContext[models.User](c)
	if !exists {
		response.Unauthorized(c, "User not authenticated", nil)
		return
	}

	req.UserID = currentUser.ID

	// Call service
	ctx, cancel := context.WithTimeout(c.Request.Context(), 300*time.Second) // Longer timeout for AI
	defer cancel()

	path, err := h.service.GenerateLearningPathWithAI(ctx, &req)
	if err != nil {
		logging.Error("Handler: Failed to generate learning path with AI: %v", err)
		response.InternalServerError(c, "Failed to generate learning path", err)
		return
	}

	response.Created(c, "AI-generated learning path created successfully", path)
}

// UpdateLearningPath godoc
// @Summary Update a learning path
// @Description Updates an existing learning path by ID. Requires authentication and ownership or admin rights.
// @Tags Learning Paths
// @Accept json
// @Produce json
// @Param id path string true "Learning Path ID"
// @Param request body services.UpdateLearningPathRequest true "Update Learning Path Request"
// @Success 200 {object} models.LearningPath
// @Failure 400 {object} response.APIError
// @Failure 401 {object} response.APIError
// @Failure 403 {object} response.APIError
// @Failure 404 {object} response.APIError
// @Failure 500 {object} response.APIError
// @Router /learning-paths/{id} [put]
// @Security BearerAuth
func (h *LearningPathHandler) UpdateLearningPath(c *gin.Context) {
	pathIDStr := c.Param("id")
	pathID, err := uuid.Parse(pathIDStr)
	if err != nil {
		response.BadRequest(c, "Invalid path ID", err)
		return
	}

	var req services.UpdateLearningPathRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "Invalid request body", err)
		return
	}

	// Get user ID from context
	_, exists := response.GetUserFromContext[models.User](c)
	if !exists {
		response.Unauthorized(c, "User not authenticated", nil)
		return
	}

	// TODO: Add authorization check here (e.g., is user owner or admin?)
	// For now, we'll assume the service layer or a separate middleware handles this.
	// You might need to fetch the path and check CreatorID against parsedUserID.

	// Call service
	ctx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Second)
	defer cancel()

	updatedPath, err := h.service.UpdateLearningPath(ctx, pathID, &req)
	if err != nil {
		if err.Error() == fmt.Sprintf("learning path not found: %v", pathID) { // Adapt error check based on service implementation
			response.NotFound(c, "Learning path not found", err)
			return
		}
		logging.Error("Handler: Failed to update learning path: %v", err)
		response.InternalServerError(c, "Failed to update learning path", err)
		return
	}

	response.Success(c, "Learning path updated successfully", updatedPath)
}

// DeleteLearningPath godoc
// @Summary Delete a learning path
// @Description Deletes a learning path by ID. Requires authentication and ownership or admin rights.
// @Tags Learning Paths
// @Produce json
// @Param id path string true "Learning Path ID"
// @Success 200 {object} response.APIResponse
// @Failure 400 {object} response.APIError
// @Failure 401 {object} response.APIError
// @Failure 403 {object} response.APIError
// @Failure 404 {object} response.APIError
// @Failure 500 {object} response.APIError
// @Router /learning-paths/{id} [delete]
// @Security BearerAuth
func (h *LearningPathHandler) DeleteLearningPath(c *gin.Context) {
	pathIDStr := c.Param("id")
	pathID, err := uuid.Parse(pathIDStr)
	if err != nil {
		response.BadRequest(c, "Invalid path ID", err)
		return
	}

	// TODO: Add authorization check here

	// Call service
	ctx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Second)
	defer cancel()

	err = h.service.DeleteLearningPath(ctx, pathID)
	if err != nil {
		if err.Error() == fmt.Sprintf("learning path not found: %v", pathID) {
			response.NotFound(c, "Learning path not found", err)
			return
		}
		logging.Error("Handler: Failed to delete learning path: %v", err)
		response.InternalServerError(c, "Failed to delete learning path", err)
		return
	}

	response.Success(c, "Learning path deleted successfully", nil)
}

// GetLearningPath godoc
// @Summary Get a learning path by ID
// @Description Retrieves a learning path by its ID. Public access if path is public, otherwise requires authentication.
// @Tags Learning Paths
// @Produce json
// @Param id path string true "Learning Path ID"
// @Success 200 {object} models.LearningPath
// @Failure 400 {object} response.APIError
// @Failure 404 {object} response.APIError
// @Failure 500 {object} response.APIError
// @Router /learning-paths/{id} [get]
func (h *LearningPathHandler) GetLearningPath(c *gin.Context) {
	pathIDStr := c.Param("id")
	pathID, err := uuid.Parse(pathIDStr)
	if err != nil {
		response.BadRequest(c, "Invalid path ID", err)
		return
	}

	// Call service
	ctx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Second)
	defer cancel()

	path, err := h.service.GetLearningPath(ctx, pathID)
	if err != nil {
		if err.Error() == fmt.Sprintf("failed to get learning path: %v", pathID) {
			response.NotFound(c, "Learning path not found", err)
			return
		}
		logging.Error("Handler: Failed to get learning path: %v", err)
		response.InternalServerError(c, "Failed to get learning path", err)
		return
	}

	// TODO: Check if path is public or user has access (auth context)
	// For now, assuming service handles visibility or it's public endpoint
	response.Success(c, "Learning path retrieved successfully", path)
}

// GetLearningPaths godoc
// @Summary Get learning paths with pagination
// @Description Retrieves a list of learning paths with pagination. Public endpoint.
// @Tags Learning Paths
// @Produce json
// @Param page query int false "Page number (default: 1)"
// @Param page_size query int false "Page size (default: 20)"
// @Param tags[] query string false "Filter by tag names (OR by default, AND when match_all_tags=true)"
// @Param match_all_tags query bool false "When true, a path must contain all provided tags (AND). Default false (OR)."
// @Success 200 {object} response.PaginatedResponse{data=[]models.LearningPath}
// @Failure 400 {object} response.APIError
// @Failure 500 {object} response.APIError
// @Router /learning-paths [get]
func (h *LearningPathHandler) GetLearningPaths(c *gin.Context) {
	var req services.GetLearningPathsRequest

	// Bind query parameters
	if err := c.ShouldBindQuery(&req); err != nil {
		response.BadRequest(c, "Invalid query parameters", err)
		return
	}

	// Call service
	ctx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Second)
	defer cancel()

	// Only return public paths; apply optional tag filters
	var (
		paths []*models.LearningPath
		total int64
		err error
	)
	if len(req.Tags) > 0 {
		paths, total, err = h.service.GetPublicLearningPathsByTags(ctx, req.Page, req.PageSize, req.Tags, req.MatchAllTags)
	} else {
		paths, total, err = h.service.GetPublicLearningPaths(ctx, req.Page, req.PageSize)
	}
	if err != nil {
		logging.Error("Handler: Failed to get learning paths: %v", err)
		response.InternalServerError(c, "Failed to get learning paths", err)
		return
	}

	// 构造分页响应结构体
	resp := struct {
		Data     interface{} `json:"data"`
		Total    int         `json:"total"`
		Page     int         `json:"page"`
		PageSize int         `json:"page_size"`
	}{
		Data:     paths,
		Total:    int(total),
		Page:     req.Page,
		PageSize: req.PageSize,
	}
	response.Success(c, "Learning paths retrieved successfully", resp)
}

// GetUserMasterPath godoc
// @Summary Get user's master learning path
// @Description Retrieves the master learning path for the authenticated user.
// @Tags User Paths
// @Produce json
// @Success 200 {object} models.UserMasterPath
// @Failure 401 {object} response.APIError
// @Failure 404 {object} response.APIError
// @Failure 500 {object} response.APIError
// @Router /user/master-path [get]
// @Security BearerAuth
func (h *LearningPathHandler) GetUserMasterPath(c *gin.Context) {
	// Get user ID from context
	currentUser, exists := response.GetUserFromContext[models.User](c)
	if !exists {
		response.Unauthorized(c, "User not authenticated", nil)
		return
	}
	parsedUserID := currentUser.ID
	// Call service
	ctx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Second)
	defer cancel()

	masterPath, err := h.service.GetUserMasterPath(ctx, parsedUserID)
	if err != nil {
		if err.Error() == fmt.Sprintf("failed to get user master path: %v", parsedUserID) {
			response.NotFound(c, "User master path not found", err)
			return
		}
		logging.Error("Handler: Failed to get user master path: %v", err)
		response.InternalServerError(c, "Failed to get user master path", err)
		return
	}

	response.Success(c, "User master path retrieved successfully", masterPath)
}

// Request structs for handler-specific requests (if not in service)
type CreateUserMasterPathRequest struct {
	CurrentGoalTitle string `json:"current_goal_title" validate:"required"`
}

type UpdateUserMasterPathRequest struct {
	NewPathID uuid.UUID `json:"new_path_id" validate:"required"`
}

type MarkPathCompletedRequest struct {
	PathID uuid.UUID `json:"path_id" validate:"required"`
}

type AdaptUserMasterPathRequest struct {
	NewGoalTitle string `json:"new_goal_title"`
}

// UnsubscribePath godoc
// @Summary Unsubscribe a learning path for the current user
// @Description Removes user's subscription/progress to a learning path and updates node ref counts.
// @Tags User Paths
// @Accept json
// @Produce json
// @Param request body UnsubscribePathRequest true "Unsubscribe Path Request"
// @Success 200 {object} response.APIResponse
// @Failure 400 {object} response.APIError
// @Failure 401 {object} response.APIError
// @Failure 500 {object} response.APIError
// @Router /user/master-path/unsubscribe [post]
// @Security BearerAuth
func (h *LearningPathHandler) UnsubscribePath(c *gin.Context) {
	var req struct {
		PathID uuid.UUID `json:"path_id" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "Invalid request body", err)
		return
	}
	currentUser, exists := response.GetUserFromContext[models.User](c)
	if !exists {
		response.Unauthorized(c, "User not authenticated", nil)
		return
	}
	ctx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Second)
	defer cancel()
	if err := h.service.RemovePathForUser(ctx, currentUser.ID, req.PathID); err != nil {
		logging.Error("Handler: Failed to unsubscribe path: user_id=%s path_id=%s err=%v", currentUser.ID, req.PathID, err)
		response.InternalServerError(c, "Failed to unsubscribe path", err)
		return
	}
	response.Success(c, "Path unsubscribed successfully", nil)
}

type UnsubscribePathRequest struct {
	PathID uuid.UUID `json:"path_id" validate:"required"`
}

// SubscribePath godoc
// @Summary Subscribe (fork) a public learning path to current user
// @Description Clone base info and node associations; keep CreatorID, set UserID & ForkFrom; idempotent.
// @Tags User Paths
// @Accept json
// @Produce json
// @Param request body SubscribePathRequest true "Subscribe Path Request"
// @Success 201 {object} models.LearningPath
// @Failure 400 {object} response.APIError
// @Failure 401 {object} response.APIError
// @Failure 500 {object} response.APIError
// @Router /user/master-path/subscribe [post]
// @Security BearerAuth
func (h *LearningPathHandler) SubscribePath(c *gin.Context) {
	var req SubscribePathRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "Invalid request body", err)
		return
	}
	currentUser, exists := response.GetUserFromContext[models.User](c)
	if !exists {
		response.Unauthorized(c, "User not authenticated", nil)
		return
	}
	ctx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Second)
	defer cancel()
	path, err := h.service.SubscribePath(ctx, currentUser.ID, req.PathID)
	if err != nil {
		logging.Error("Handler: Failed to subscribe path: user_id=%s source_path_id=%s err=%v", currentUser.ID, req.PathID, err)
		response.InternalServerError(c, "Failed to subscribe path", err)
		return
	}
	response.Created(c, "Path subscribed successfully", path)
}

type SubscribePathRequest struct {
	PathID uuid.UUID `json:"path_id" validate:"required"`
}

// GetUserLearningPaths godoc
// @Summary Get current user's learning paths
// @Description Retrieves learning paths owned by the authenticated user (by UserID) with pagination.
// @Tags User Paths
// @Produce json
// @Param page query int false "Page number (default: 1)"
// @Param page_size query int false "Page size (default: 20)"
// @Success 200 {object} response.PaginatedResponse{data=[]models.LearningPath}
// @Failure 400 {object} response.APIError
// @Failure 401 {object} response.APIError
// @Failure 500 {object} response.APIError
// @Router /user/learning-paths [get]
// @Security BearerAuth
func (h *LearningPathHandler) GetUserLearningPaths(c *gin.Context) {
	// auth
	currentUser, exists := response.GetUserFromContext[models.User](c)
	if !exists {
		response.Unauthorized(c, "User not authenticated", nil)
		return
	}
	// bind pagination
	var qp services.GetLearningPathsRequest
	if err := c.ShouldBindQuery(&qp); err != nil {
		response.BadRequest(c, "Invalid query parameters", err)
		return
	}
	ctx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Second)
	defer cancel()
	paths, total, err := h.service.GetLearningPathsByUser(ctx, currentUser.ID, qp.Page, qp.PageSize)
	if err != nil {
		logging.Error("Handler: Failed to get user learning paths: user_id=%s err=%v", currentUser.ID, err)
		response.InternalServerError(c, "Failed to get user learning paths", err)
		return
	}
	resp := struct {
		Data     interface{} `json:"data"`
		Total    int         `json:"total"`
		Page     int         `json:"page"`
		PageSize int         `json:"page_size"`
	}{
		Data:     paths,
		Total:    int(total),
		Page:     qp.Page,
		PageSize: qp.PageSize,
	}
	response.Success(c, "User learning paths retrieved successfully", resp)
}

// RegisterRoutes registers the learning path routes
func (h *LearningPathHandler) RegisterRoutes(r *gin.RouterGroup) {
	r.GET("/learning-paths", h.GetLearningPaths)
	r.GET("/learning-paths/:id", h.GetLearningPath)
	r.POST("/learning-paths", h.CreateLearningPath)
	r.POST("/learning-paths/generate", h.GenerateLearningPathWithAI)
	r.PUT("/learning-paths/:id", h.UpdateLearningPath)
	r.DELETE("/learning-paths/:id", h.DeleteLearningPath)
	r.POST("/user/master-path/subscribe", h.SubscribePath)
	r.POST("/user/master-path/unsubscribe", h.UnsubscribePath)
	r.GET("/user/master-path", h.GetUserMasterPath)
	r.GET("/user/learning-paths", h.GetUserLearningPaths)
}