package handlers

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/internal/models"
	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/internal/services"
	"github.com/Arxtect/CoreXWorkSpace/golangp/common/gin_core/response"
	"github.com/Arxtect/CoreXWorkSpace/golangp/common/logging"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// LearningNodeHandler handles HTTP requests for learning nodes
type LearningNodeHandler struct {
	service *services.LearningNodeService
}

// NewLearningNodeHandler creates a new learning node handler
func NewLearningNodeHandler(service *services.LearningNodeService) *LearningNodeHandler {
	return &LearningNodeHandler{service: service}
}

// CreateLearningNode godoc
// @Summary Create a new learning node
// @Description Creates a new standard learning node. Requires authentication.
// @Tags Learning Nodes
// @Accept json
// @Produce json
// @Param request body services.CreateLearningNodeRequest true "Create Learning Node Request"
// @Success 201 {object} models.LearningNode
// @Failure 400 {object} response.APIError
// @Failure 401 {object} response.APIError
// @Failure 500 {object} response.APIError
// @Router /learning-nodes [post]
// @Security BearerAuth
func (h *LearningNodeHandler) CreateLearningNode(c *gin.Context) {
	var req services.CreateLearningNodeRequest

	// Bind JSON request
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "Invalid request body", err)
		return
	}

	// Get user ID from context (set by AuthMiddleware) and set as CreatedBy if not provided
	// This ensures security by preventing impersonation via the request body
	if req.CreatedBy == uuid.Nil {
		currentUser, exists := response.GetUserFromContext[models.User](c)
		if !exists {
			response.Unauthorized(c, "User not authenticated", nil)
			return
		}
		parsedUserID := currentUser.ID
		req.CreatedBy = parsedUserID
	}

	// Call service
	ctx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Second)
	defer cancel()

	node, err := h.service.CreateLearningNode(ctx, &req)
	if err != nil {
		logging.Error("Handler: Failed to create learning node: %v", err)
		// Check for specific validation errors if needed and return 400
		response.InternalServerError(c, "Failed to create learning node", err)
		return
	}

	response.Created(c, "Learning node created successfully", node)
}

// UpdateLearningNode godoc
// @Summary Update a learning node
// @Description Updates an existing learning node by ID. Requires authentication.
// @Tags Learning Nodes
// @Accept json
// @Produce json
// @Param node_id path string true "Learning Node ID"
// @Param request body services.UpdateLearningNodeRequest true "Update Learning Node Request"
// @Success 200 {object} models.LearningNode
// @Failure 400 {object} response.APIError
// @Failure 401 {object} response.APIError
// @Failure 404 {object} response.APIError
// @Failure 500 {object} response.APIError
// @Router /learning-nodes/{node_id} [put]
// @Security BearerAuth
func (h *LearningNodeHandler) UpdateLearningNode(c *gin.Context) {
	nodeIDStr := c.Param("node_id")
	nodeID, err := uuid.Parse(nodeIDStr)
	if err != nil {
		response.BadRequest(c, "Invalid node ID", err)
		return
	}

	var req services.UpdateLearningNodeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "Invalid request body", err)
		return
	}

	// Get user ID from context (set by AuthMiddleware) and set as UpdatedBy if not provided
	// This ensures security by preventing impersonation via the request body
	if req.UpdatedBy == nil {
		currentUser, exists := response.GetUserFromContext[models.User](c)
		if exists {
			req.UpdatedBy = &currentUser.ID
		}
		// If user is not authenticated, req.UpdatedBy remains nil, which is acceptable.
	}

	// Call service
	ctx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Second)
	defer cancel()

	updatedNode, err := h.service.UpdateLearningNode(ctx, nodeID, &req)
	if err != nil {
		if err.Error() == fmt.Sprintf("failed to get learning node: %v", nodeID) { // Adapt error check
			response.NotFound(c, "Learning node not found", err)
			return
		}
		logging.Error("Handler: Failed to update learning node: %v", err)
		response.InternalServerError(c, "Failed to update learning node", err)
		return
	}

	response.Success(c, "Learning node updated successfully", updatedNode)
}

// DeleteLearningNode godoc
// @Summary Delete a learning node
// @Description Deletes a learning node by ID. Requires authentication.
// @Tags Learning Nodes
// @Produce json
// @Param node_id path string true "Learning Node ID"
// @Success 200 {object} response.APIResponse
// @Failure 400 {object} response.APIError
// @Failure 401 {object} response.APIError
// @Failure 404 {object} response.APIError
// @Failure 500 {object} response.APIError
// @Router /learning-nodes/{node_id} [delete]
// @Security BearerAuth
func (h *LearningNodeHandler) DeleteLearningNode(c *gin.Context) {
	nodeIDStr := c.Param("node_id")
	nodeID, err := uuid.Parse(nodeIDStr)
	if err != nil {
		response.BadRequest(c, "Invalid node ID", err)
		return
	}

	// Call service
	ctx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Second)
	defer cancel()

	err = h.service.DeleteLearningNode(ctx, nodeID)
	if err != nil {
		if err.Error() == fmt.Sprintf("learning node not found: %v", nodeID) { // Adapt error check
			response.NotFound(c, "Learning node not found", err)
			return
		}
		logging.Error("Handler: Failed to delete learning node: %v", err)
		response.InternalServerError(c, "Failed to delete learning node", err)
		return
	}

	response.Success(c, "Learning node deleted successfully", nil)
}

// GetLearningNode godoc
// @Summary Get a learning node by ID
// @Description Retrieves a learning node by its ID.
// @Tags Learning Nodes
// @Produce json
// @Param node_id path string true "Learning Node ID"
// @Success 200 {object} models.LearningNode
// @Failure 400 {object} response.APIError
// @Failure 404 {object} response.APIError
// @Failure 500 {object} response.APIError
// @Router /learning-nodes/{node_id} [get]
func (h *LearningNodeHandler) GetLearningNode(c *gin.Context) {
	nodeIDStr := c.Param("node_id")
	nodeID, err := uuid.Parse(nodeIDStr)
	if err != nil {
		response.BadRequest(c, "Invalid node ID", err)
		return
	}

	// Call service
	ctx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Second)
	defer cancel()

	node, err := h.service.GetLearningNode(ctx, nodeID)
	if err != nil {
		if err.Error() == fmt.Sprintf("failed to get learning node: %v", nodeID) { // Adapt error check
			response.NotFound(c, "Learning node not found", err)
			return
		}
		logging.Error("Handler: Failed to get learning node: %v", err)
		response.InternalServerError(c, "Failed to get learning node", err)
		return
	}

	response.Success(c, "Learning node retrieved successfully", node)
}

// GetLearningNodes godoc
// @Summary Get learning nodes with pagination
// @Description Retrieves a list of learning nodes with pagination.
// @Tags Learning Nodes
// @Produce json
// @Param page query int false "Page number (default: 1)"
// @Param page_size query int false "Page size (default: 20)"
// @Param tags query []string false "Filter by tags (any-match within field)"
// @Param skills query []string false "Filter by skills (any-match within field)"
// @Param learner_tags query []string false "Filter by learner tags (any-match within field)"
// @Param query query string false "Text search over title/description"
// @Param search_mode query string false "Text search mode: exact|fuzzy (default: fuzzy)"
// @Success 200 {object} response.PaginatedResponse{data=[]models.LearningNode}
// @Failure 400 {object} response.APIError
// @Failure 500 {object} response.APIError
// @Router /learning-nodes [get]
func (h *LearningNodeHandler) GetLearningNodes(c *gin.Context) {
	var req services.GetLearningNodesRequest

	// Bind query parameters
	if err := c.ShouldBindQuery(&req); err != nil {
		response.BadRequest(c, "Invalid query parameters", err)
		return
	}

	// Allow comma-separated lists as a convenience
	splitCSV := func(ss []string) []string {
		if len(ss) == 1 && strings.Contains(ss[0], ",") {
			parts := strings.Split(ss[0], ",")
			out := make([]string, 0, len(parts))
			for _, p := range parts {
				if v := strings.TrimSpace(p); v != "" {
					out = append(out, v)
				}
			}
			return out
		}
		// also trim spaces in each value
		out := make([]string, 0, len(ss))
		for _, v := range ss {
			if t := strings.TrimSpace(v); t != "" {
				out = append(out, t)
			}
		}
		return out
	}
	req.Tags = splitCSV(req.Tags)
	req.Skills = splitCSV(req.Skills)
	req.LearnerTags = splitCSV(req.LearnerTags)

	// Call service
	ctx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Second)
	defer cancel()
	fmt.Printf("req: %+v\n", req)
	nodes, total, err := h.service.GetLearningNodes(ctx, &req)
	if err != nil {
		logging.Error("Handler: Failed to get learning nodes: %v", err)
		response.InternalServerError(c, "Failed to get learning nodes", err)
		return
	}

	response.Paginated(c, "Learning nodes retrieved successfully", nodes, int64(total), req.Page, req.PageSize)
}

// UpdateNodeStatus godoc
// @Summary Update a learning node's status
// @Description Updates the status of an existing learning node by ID. Requires authentication.
// @Tags Learning Nodes
// @Accept json
// @Produce json
// @Param node_id path string true "Learning Node ID"
// @Param request body UpdateNodeStatusRequest true "Update Node Status Request"
// @Success 200 {object} response.APIResponse
// @Failure 400 {object} response.APIError
// @Failure 401 {object} response.APIError
// @Failure 404 {object} response.APIError
// @Failure 500 {object} response.APIError
// @Router /learning-nodes/{node_id}/status [put]
// @Security BearerAuth
func (h *LearningNodeHandler) UpdateNodeStatus(c *gin.Context) {
	nodeIDStr := c.Param("node_id")
	nodeID, err := uuid.Parse(nodeIDStr)
	if err != nil {
		response.BadRequest(c, "Invalid node ID", err)
		return
	}

	var req struct {
		Status models.NodeStatus `json:"status" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "Invalid request body", err)
		return
	}

	// Call service
	ctx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Second)
	defer cancel()

	err = h.service.UpdateNodeStatus(ctx, nodeID, req.Status)
	if err != nil {
		if err.Error() == fmt.Sprintf("failed to get learning node: %v", nodeID) { // Adapt error check
			response.NotFound(c, "Learning node not found", err)
			return
		}
		// Check for invalid status error
		if err.Error() == fmt.Sprintf("invalid status: %s", req.Status) {
			response.BadRequest(c, "Invalid node status", err)
			return
		}
		logging.Error("Handler: Failed to update node status: %v", err)
		response.InternalServerError(c, "Failed to update node status", err)
		return
	}

	response.Success(c, "Node status updated successfully", nil)
}

// AddNodeToPath godoc
// @Summary Add a learning node to a learning path
// @Description Associates a learning node with a learning path. Requires authentication.
// @Tags Learning Nodes
// @Accept json
// @Produce json
// @Param request body services.AddNodeToPathRequest true "Add Node To Path Request"
// @Success 201 {object} models.LearningPathNode
// @Failure 400 {object} response.APIError
// @Failure 401 {object} response.APIError
// @Failure 409 {object} response.APIError "Node already exists in this path"
// @Failure 500 {object} response.APIError
// @Router /learning-nodes/add-to-path [post]
// @Security BearerAuth
func (h *LearningNodeHandler) AddNodeToPath(c *gin.Context) {
	var req services.AddNodeToPathRequest

	// Bind JSON request
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "Invalid request body", err)
		return
	}

	// Call service
	ctx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Second)
	defer cancel()

	pathNode, err := h.service.AddNodeToPath(ctx, &req)
	if err != nil {
		// Check for specific conflict error
		if err.Error() == "node already exists in this path" {
			response.Conflict(c, "Node already exists in this path", err)
			return
		}
		logging.Error("Handler: Failed to add node to path: %v", err)
		response.InternalServerError(c, "Failed to add node to path", err)
		return
	}

	response.Created(c, "Node added to path successfully", pathNode)
}

// RemoveNodeFromPath godoc
// @Summary Remove a learning node from a learning path
// @Description Removes the association between a learning node and a learning path. Requires authentication.
// @Tags Learning Nodes
// @Produce json
// @Param path_id query string true "Learning Path ID"
// @Param node_id query string true "Learning Node ID"
// @Success 200 {object} response.APIResponse
// @Failure 400 {object} response.APIError
// @Failure 401 {object} response.APIError
// @Failure 404 {object} response.APIError "Node not found in this path"
// @Failure 500 {object} response.APIError
// @Router /learning-nodes/remove-from-path [delete]
// @Security BearerAuth
func (h *LearningNodeHandler) RemoveNodeFromPath(c *gin.Context) {
	pathIDStr := c.Query("path_id")
	nodeIDStr := c.Query("node_id")

	if pathIDStr == "" || nodeIDStr == "" {
		response.BadRequest(c, "Both path_id and node_id query parameters are required", nil)
		return
	}

	pathID, err := uuid.Parse(pathIDStr)
	if err != nil {
		response.BadRequest(c, "Invalid path ID", err)
		return
	}

	nodeID, err := uuid.Parse(nodeIDStr)
	if err != nil {
		response.BadRequest(c, "Invalid node ID", err)
		return
	}

	// Call service
	ctx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Second)
	defer cancel()

	err = h.service.RemoveNodeFromPath(ctx, pathID, nodeID)
	if err != nil {
		// Check for specific not found error
		if err.Error() == "node not found in this path" {
			response.NotFound(c, "Node not found in this path", err)
			return
		}
		logging.Error("Handler: Failed to remove node from path: %v", err)
		response.InternalServerError(c, "Failed to remove node from path", err)
		return
	}

	response.Success(c, "Node removed from path successfully", nil)
}

// GetPathNodes godoc
// @Summary Get all nodes in a learning path
// @Description Retrieves a list of all nodes associated with a specific learning path.
// @Tags Learning Nodes
// @Produce json
// @Param path_id query string true "Learning Path ID"
// @Success 200 {object} response.APIResponse{data=[]models.LearningNode}
// @Failure 400 {object} response.APIError
// @Failure 500 {object} response.APIError
// @Router /learning-nodes/path-nodes [get]
func (h *LearningNodeHandler) GetPathNodes(c *gin.Context) {
	pathIDStr := c.Query("path_id")
	if pathIDStr == "" {
		response.BadRequest(c, "path_id query parameter is required", nil)
		return
	}

	pathID, err := uuid.Parse(pathIDStr)
	if err != nil {
		response.BadRequest(c, "Invalid path ID", err)
		return
	}

	// Call service
	ctx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Second)
	defer cancel()

	nodes, err := h.service.GetPathNodes(ctx, pathID)
	if err != nil {
		logging.Error("Handler: Failed to get path nodes: %v", err)
		response.InternalServerError(c, "Failed to get path nodes", err)
		return
	}

	response.Success(c, "Path nodes retrieved successfully", nodes)
}

// AssignLessonToNode godoc
// @Summary Assign a lesson to a learning node
// @Description Associates a lesson with a learning node. Requires authentication.
// @Tags Learning Nodes
// @Accept json
// @Produce json
// @Param node_id path string true "Learning Node ID"
// @Param request body AssignLessonToNodeRequest true "Assign Lesson To Node Request"
// @Success 201 {object} models.NodeLesson
// @Failure 400 {object} response.APIError
// @Failure 401 {object} response.APIError
// @Failure 409 {object} response.APIError "Lesson already assigned to this node"
// @Failure 500 {object} response.APIError
// @Router /learning-nodes/{node_id}/assign-lesson [post]
// @Security BearerAuth
func (h *LearningNodeHandler) AssignLessonToNode(c *gin.Context) {
	nodeIDStr := c.Param("node_id")
	nodeID, err := uuid.Parse(nodeIDStr)
	if err != nil {
		response.BadRequest(c, "Invalid node ID", err)
		return
	}

	var req struct {
		LessonID string `json:"lesson_id" binding:"required"`
		// Order is an optional 1-based position to insert at; if 0 or omitted, append to end.
		Order int `json:"order"`
	}

	// Bind JSON request
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "Invalid request body", err)
		return
	}

	// Validate lesson_id format (Mongo ObjectID hex)
	if _, err := primitive.ObjectIDFromHex(req.LessonID); err != nil {
		response.BadRequest(c, "invalid lesson_id (must be Mongo ObjectID)", err)
		return
	}

	// Call service
	ctx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Second)
	defer cancel()

	nodeLesson, err := h.service.AssignLessonToNode(ctx, nodeID, req.LessonID, req.Order)
	if err != nil {
		// Check for specific conflict error
		if err.Error() == "lesson already assigned to this node" {
			response.Conflict(c, "Lesson already assigned to this node", err)
			return
		}
		if err.Error() == "learning lesson not found" {
			response.NotFound(c, "Learning lesson not found", err)
			return
		}
		logging.Error("Handler: Failed to assign lesson to node: %v", err)
		response.InternalServerError(c, "Failed to assign lesson to node", err)
		return
	}

	response.Created(c, "Lesson assigned to node successfully", nodeLesson)
}

// GetNodeLessons godoc
// @Summary Get all lessons assigned to a node with content flow
// @Description Retrieves all lessons assigned to a specific learning node with brief content flow (type, id, order, title, description)
// @Tags Learning Nodes
// @Produce json
// @Param node_id path string true "Learning Node ID"
// @Success 200 {array} services.NodeLessonWithContent
// @Failure 400 {object} response.APIError
// @Failure 500 {object} response.APIError
// @Router /learning-nodes/{node_id}/lessons [get]
func (h *LearningNodeHandler) GetNodeLessons(c *gin.Context) {
	nodeIDStr := c.Param("node_id")
	nodeID, err := uuid.Parse(nodeIDStr)
	if err != nil {
		response.BadRequest(c, "Invalid node ID", err)
		return
	}

	// Call service - always return lessons with content
	ctx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Second)
	defer cancel()

	nodeLessonsWithContent, err := h.service.GetNodeLessonsWithContent(ctx, nodeID)
	if err != nil {
		logging.Error("Handler: Failed to get node lessons with content: %v", err)
		response.InternalServerError(c, "Failed to get node lessons with content", err)
		return
	}

	response.Success(c, "Node lessons with content retrieved successfully", nodeLessonsWithContent)
}

// GetNodeLessonsBasic godoc
// @Summary Get basic lesson associations for a node
// @Description Retrieves only the basic lesson association information without full content (for performance)
// @Tags Learning Nodes
// @Produce json
// @Param node_id path string true "Learning Node ID"
// @Success 200 {array} models.NodeLesson
// @Failure 400 {object} response.APIError
// @Failure 500 {object} response.APIError
// @Router /learning-nodes/{node_id}/lessons-basic [get]
func (h *LearningNodeHandler) GetNodeLessonsBasic(c *gin.Context) {
	nodeIDStr := c.Param("node_id")
	nodeID, err := uuid.Parse(nodeIDStr)
	if err != nil {
		response.BadRequest(c, "Invalid node ID", err)
		return
	}

	// Call service - return basic lesson associations only
	ctx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Second)
	defer cancel()

	nodeLessons, err := h.service.GetNodeLessons(ctx, nodeID)
	if err != nil {
		logging.Error("Handler: Failed to get basic node lessons: %v", err)
		response.InternalServerError(c, "Failed to get basic node lessons", err)
		return
	}

	response.Success(c, "Basic node lessons retrieved successfully", nodeLessons)
}

// Request structs for handler-specific requests (if not in service or for clarity)
// These can also be defined in the service package if preferred.

type UpdateNodeStatusRequest struct {
	Status models.NodeStatus `json:"status" validate:"required"`
}

type AssignLessonToNodeRequest struct {
	LessonID string `json:"lesson_id" validate:"required"`
	// Optional 1-based position; 0 means append
	Order int `json:"order"`
}

type GenerateLessonsFromOutlineRequest struct {
	Lessons []services.OutlineLesson `json:"lessons" binding:"required"`
}

// GenerateNodeAndLessonsRequest mirrors service request for handler binding
type GenerateNodeAndLessonsRequest struct {
	NodeDescription string                   `json:"node_description" binding:"required"`
	Lessons         []services.OutlineLesson `json:"lessons,omitempty"`
}

// GenerateLessonsFromOutline godoc

// MoveLessonToPosition godoc
// @Summary Move a lesson within a node to the specified position
// @Description Move lesson to a 1-based index; 0 or negative becomes 1; too-large becomes tail.
// @Tags Learning Nodes
// @Accept json
// @Produce json
// @Param node_id path string true "Learning Node ID"
// @Param lesson_id path string true "Lesson ID"
// @Param position query int true "1-based position"
// @Success 200 {object} response.APIResponse{data=string}
// @Failure 400 {object} response.APIError
// @Failure 500 {object} response.APIError
// @Router /learning-nodes/{node_id}/lessons/{lesson_id}/move [put]
// @Security BearerAuth
func (h *LearningNodeHandler) MoveLessonToPosition(c *gin.Context) {
	nodeIDStr := c.Param("node_id")
	lessonID := c.Param("lesson_id")
	nodeID, err := uuid.Parse(nodeIDStr)
	if err != nil {
		response.BadRequest(c, "Invalid node ID", err)
		return
	}
	// Validate lesson_id format
	if _, err := primitive.ObjectIDFromHex(lessonID); err != nil {
		response.BadRequest(c, "invalid lesson_id (must be Mongo ObjectID)", err)
		return
	}
	posStr := c.Query("position")
	if posStr == "" {
		response.BadRequest(c, "position is required", fmt.Errorf("missing position"))
		return
	}
	pos, convErr := strconv.Atoi(posStr)
	if convErr != nil {
		response.BadRequest(c, "invalid position", convErr)
		return
	}

	ctx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Second)
	defer cancel()
	if err := h.service.MoveLessonToPosition(ctx, nodeID, lessonID, pos); err != nil {
		logging.Error("Handler: Failed to move lesson: %v", err)
		response.InternalServerError(c, "Failed to move lesson", err)
		return
	}
	response.Success(c, "Lesson moved successfully", "ok")
}

// @Summary Generate lessons from an outline for a node (AI)
// @Description Using a provided outline (list of lessons), generate detailed lessons and attach them to the node in order. Requires authentication.
// @Tags Learning Nodes
// @Accept json
// @Produce json
// @Param node_id path string true "Learning Node ID"
// @Param request body GenerateLessonsFromOutlineRequest true "Lessons Outline"
// @Success 201 {object} response.APIResponse{data=[]models.LearningLesson}
// @Failure 400 {object} response.APIError
// @Failure 401 {object} response.APIError
// @Failure 500 {object} response.APIError
// @Router /learning-nodes/{node_id}/generate-lessons [post]
// @Security BearerAuth
func (h *LearningNodeHandler) GenerateLessonsFromOutline(c *gin.Context) {
	nodeIDStr := c.Param("node_id")
	nodeID, err := uuid.Parse(nodeIDStr)
	if err != nil {
		response.BadRequest(c, "Invalid node ID", err)
		return
	}

	var req GenerateLessonsFromOutlineRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "Invalid request body", err)
		return
	}
	if len(req.Lessons) == 0 {
		response.BadRequest(c, "lessons is required and cannot be empty", nil)
		return
	}

	// Get user ID (optional)
	var userID uuid.UUID
	if currentUser, exists := response.GetUserFromContext[models.User](c); exists {
		userID = currentUser.ID
	}

	ctx, cancel := context.WithTimeout(c.Request.Context(), 180*time.Second)
	defer cancel()

	outline := &services.CourseOutline{NodeID: nodeID, Lessons: req.Lessons}
	lessons, err := h.service.GenerateLessonsFromOutline(ctx, nodeID, outline, userID)
	if err != nil {
		logging.Error("Handler: Failed to generate lessons from outline: %v", err)
		response.InternalServerError(c, "Failed to generate lessons from outline", err)
		return
	}

	response.Created(c, "Lessons generated and assigned successfully", lessons)
}

// RemoveLessonFromNode godoc
// @Summary Remove a lesson from a learning node
// @Description Removes the association between a lesson and a learning node by IDs. Requires authentication.
// @Tags Learning Nodes
// @Produce json
// @Param node_id path string true "Learning Node ID"
// @Param lesson_id path string true "Lesson ID (Mongo ObjectID)"
// @Success 200 {object} response.APIResponse
// @Failure 400 {object} response.APIError
// @Failure 401 {object} response.APIError
// @Failure 404 {object} response.APIError "Lesson not assigned to node"
// @Failure 500 {object} response.APIError
// @Router /learning-nodes/{node_id}/lessons/{lesson_id} [delete]
// @Security BearerAuth
func (h *LearningNodeHandler) RemoveLessonFromNode(c *gin.Context) {
	nodeIDStr := c.Param("node_id")
	lessonID := c.Param("lesson_id")
	nodeID, err := uuid.Parse(nodeIDStr)
	if err != nil {
		response.BadRequest(c, "Invalid node ID", err)
		return
	}
	if _, err := primitive.ObjectIDFromHex(lessonID); err != nil {
		response.BadRequest(c, "invalid lesson_id (must be Mongo ObjectID)", err)
		return
	}

	ctx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Second)
	defer cancel()

	if err := h.service.RemoveLessonFromNode(ctx, nodeID, lessonID); err != nil {
		if err.Error() == "lesson not assigned to node" {
			response.NotFound(c, "Lesson not assigned to node", err)
			return
		}
		logging.Error("Handler: Failed to remove lesson from node: %v", err)
		response.InternalServerError(c, "Failed to remove lesson from node", err)
		return
	}

	response.Success(c, "Lesson removed from node successfully", nil)
}

// GenerateCourseFromNode godoc
// @Summary Generate a full course from an existing node
// @Description Given a node_id, fetch the node, ask AI for a course outline, save the outline to MongoDB, and generate lessons bound to the node. Requires authentication.
// @Tags Learning Nodes
// @Produce json
// @Param node_id path string true "Learning Node ID"
// @Success 201 {object} services.GenerateNodeAndLessonsResult
// @Failure 400 {object} response.APIError
// @Failure 401 {object} response.APIError
// @Failure 404 {object} response.APIError
// @Failure 500 {object} response.APIError
// @Router /learning-nodes/{node_id}/generate-course [post]
// @Security BearerAuth
func (h *LearningNodeHandler) GenerateCourseFromNode(c *gin.Context) {
	nodeIDStr := c.Param("node_id")
	nodeID, err := uuid.Parse(nodeIDStr)
	if err != nil {
		response.BadRequest(c, "Invalid node ID", err)
		return
	}

	var userID uuid.UUID
	if currentUser, exists := response.GetUserFromContext[models.User](c); exists {
		userID = currentUser.ID
	}

	ctx, cancel := context.WithTimeout(c.Request.Context(), 240*time.Second)
	defer cancel()

	svcReq := &services.GenerateCourseFromNodeRequest{NodeID: nodeID, UserID: userID}
	resp, err := h.service.GenerateCourseFromNode(ctx, svcReq)
	if err != nil {
		logging.Error("Handler: Failed to generate course from node: %v", err)
		response.InternalServerError(c, "Failed to generate course from node", err)
		return
	}

	response.Created(c, "Course outline saved and lessons generated successfully", resp)
}

// RegisterRoutes registers the learning node routes
func (h *LearningNodeHandler) RegisterRoutes(r *gin.RouterGroup) {
	r.GET("/learning-nodes/:node_id/lessons", h.GetNodeLessons)

	r.GET("/learning-nodes/:node_id", h.GetLearningNode)

	r.GET("/learning-nodes", h.GetLearningNodes)
	r.GET("/learning-nodes/path-nodes", h.GetPathNodes)

	r.POST("/learning-nodes", h.CreateLearningNode)
	r.POST("/learning-nodes/:node_id/generate-course", h.GenerateCourseFromNode)
	r.POST("/learning-nodes/:node_id/generate-lessons", h.GenerateLessonsFromOutline)
	r.PUT("/learning-nodes/:node_id", h.UpdateLearningNode)
	r.DELETE("/learning-nodes/:node_id", h.DeleteLearningNode)
	r.PUT("/learning-nodes/:node_id/status", h.UpdateNodeStatus)
	r.POST("/learning-nodes/add-to-path", h.AddNodeToPath)
	r.DELETE("/learning-nodes/remove-from-path", h.RemoveNodeFromPath)
	r.POST("/learning-nodes/:node_id/assign-lesson", h.AssignLessonToNode)
	r.DELETE("/learning-nodes/:node_id/lessons/:lesson_id", h.RemoveLessonFromNode)
	r.PUT("/learning-nodes/:node_id/lessons/:lesson_id/move", h.MoveLessonToPosition)
}
