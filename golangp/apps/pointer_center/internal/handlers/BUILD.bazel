load("@rules_go//go:def.bzl", "go_library")

go_library(
    name = "handlers",
    srcs = glob(["**/*.go"]),
    importpath = "github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/internal/handlers",
    visibility = ["//visibility:public"],
    deps = [
        "//golangp/apps/pointer_center/internal/config",
        "//golangp/apps/pointer_center/internal/models",
        "//golangp/apps/pointer_center/internal/models/student_profile",
        "//golangp/apps/pointer_center/internal/repositories",
        "//golangp/apps/pointer_center/internal/services",
        "//golangp/apps/pointer_center/pkg/services/manager",
        "//golangp/common/auth/googleauth",
        "//golangp/common/gin_core/response",
        "//golangp/common/logging:logger",
        "//golangp/common/payment/alipay",
        "//golangp/common/payment/wechat",
        "//golangp/common/utils",
        "@com_github_gin_gonic_gin//:gin",
        "@com_github_google_uuid//:uuid",
        "@io_gorm_gorm//:gorm",
        "@org_mongodb_go_mongo_driver//bson/primitive",
        "@org_mongodb_go_mongo_driver//mongo",
    ],
)
