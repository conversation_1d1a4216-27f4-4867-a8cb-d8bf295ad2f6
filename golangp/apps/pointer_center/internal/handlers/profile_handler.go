package handlers

import (
	"context"
	"fmt"
	"time"

	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/internal/models"
	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/internal/models/student_profile"
	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/internal/services"
	"github.com/Arxtect/CoreXWorkSpace/golangp/common/gin_core/response"
	"github.com/Arxtect/CoreXWorkSpace/golangp/common/logging"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// ProfileHandler handles HTTP requests for student profiles
type ProfileHandler struct {
	staticService         *services.StaticProfileService
	techCompetencyService *services.TechCompetencyService
}

// NewProfileHandler creates a new profile handler
func NewProfileHandler(staticService *services.StaticProfileService, techCompetencyService *services.TechCompetencyService) *<PERSON>Handler {
	return &ProfileHandler{
		staticService:         staticService,
		techCompetencyService: techCompetencyService,
	}
}

// RegisterRoutes registers all profile-related routes
func (h *ProfileHandler) RegisterRoutes(api *gin.RouterGroup) {
	// Static profile routes
	staticGroup := api.Group("/profiles/static")
	{
		staticGroup.POST("", h.CreateStaticProfile)
		staticGroup.PUT("", h.UpdateStaticProfile)
		staticGroup.GET("", h.GetStaticProfile)
		staticGroup.GET("/list", h.GetStaticProfiles)
	}

	// Tech competency routes
	techGroup := api.Group("/profiles/tech-competency")
	{
		techGroup.GET("", h.GetTechCompetencyGraph)
		techGroup.POST("", h.UpdateTechCompetencyWithAction)
		techGroup.GET("/nodes/:level", h.GetNodesByLevel)
		techGroup.GET("/stats", h.GetUserCompetencyStats)
		techGroup.POST("/refresh-stale", h.RefreshStaleGraphs)
	}
}

// getCurrentUserID retrieves the user ID from the Gin context set by the AuthMiddleware
func (h *ProfileHandler) getCurrentUserID(c *gin.Context) (uuid.UUID, error) {
	currentUser, exists := response.GetUserFromContext[models.User](c)
	if !exists {
		return uuid.Nil, fmt.Errorf("user_id not found in context")
	}
	return currentUser.ID, nil
}

// ==================== 静态画像 (Static Profile) Handlers ====================

// CreateStaticProfile creates a new static profile for the authenticated user
// @Summary      创建静态画像
// @Description  为当前登录用户创建静态画像
// @Tags         Profiles
// @Accept       json
// @Produce      json
// @Param        request body services.CreateStaticProfileRequest true "创建请求"
// @Success      201 {object} response.Response{data=student_profile.StaticProfile}
// @Failure      400 {object} response.Response
// @Failure      401 {object} response.Response
// @Failure      409 {object} response.Response
// @Failure      500 {object} response.Response
// @Security     BearerAuth
// @Router       /profiles/static [post]
func (h *ProfileHandler) CreateStaticProfile(c *gin.Context) {
	var req services.CreateStaticProfileRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "Invalid request payload", err)
		return
	}

	// 获取当前认证的用户ID
	userID, err := h.getCurrentUserID(c)
	if err != nil {
		logging.Error("Failed to get user ID from context: %v", err)
		response.Unauthorized(c, "Authentication required", nil)
		return
	}
	req.UserID = userID

	ctx, cancel := context.WithTimeout(c, 10*time.Second)
	defer cancel()

	profile, err := h.staticService.CreateStaticProfile(ctx, &req)
	if err != nil {
		if err.Error() == "static profile already exists for user "+userID.String() {
			response.Conflict(c, "Static profile already exists", nil)
			return
		}
		response.InternalServerError(c, "Failed to create static profile", err)
		return
	}

	response.Created(c, "Static profile created successfully", profile)
}

// UpdateStaticProfile updates the static profile of the authenticated user
// @Summary      更新静态画像
// @Description  更新当前登录用户的静态画像
// @Tags         Profiles
// @Accept       json
// @Produce      json
// @Param        request body services.UpdateStaticProfileRequest true "更新请求"
// @Success      200 {object} response.Response{data=student_profile.StaticProfile}
// @Failure      400 {object} response.Response
// @Failure      401 {object} response.Response
// @Failure      404 {object} response.Response
// @Failure      500 {object} response.Response
// @Security     BearerAuth
// @Router       /profiles/static [put]
func (h *ProfileHandler) UpdateStaticProfile(c *gin.Context) {
	var req services.UpdateStaticProfileRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "Invalid request payload", err)
		return
	}

	userID, err := h.getCurrentUserID(c)
	if err != nil {
		logging.Error("Failed to get user ID from context: %v", err)
		response.Unauthorized(c, "Authentication required", nil)
		return
	}

	ctx, cancel := context.WithTimeout(c, 10*time.Second)
	defer cancel()

	profile, err := h.staticService.UpdateStaticProfile(ctx, userID, &req)
	if err != nil {
		if err.Error() == "failed to get static profile: not found" {
			response.NotFound(c, "Static profile not found", nil)
			return
		}
		response.InternalServerError(c, "Failed to update static profile", err)
		return
	}

	response.Success(c, "Static profile updated successfully", profile)
}

// GetStaticProfile retrieves the static profile of the authenticated user
// @Summary      获取静态画像
// @Description  获取当前登录用户的静态画像
// @Tags         Profiles
// @Produce      json
// @Success      200 {object} response.Response{data=student_profile.StaticProfile}
// @Failure      401 {object} response.Response
// @Failure      404 {object} response.Response
// @Failure      500 {object} response.Response
// @Security     BearerAuth
// @Router       /profiles/static [get]
func (h *ProfileHandler) GetStaticProfile(c *gin.Context) {
	userID, err := h.getCurrentUserID(c)
	if err != nil {
		logging.Error("Failed to get user ID from context: %v", err)
		response.Unauthorized(c, "Authentication required", nil)
		return
	}

	ctx, cancel := context.WithTimeout(c, 10*time.Second)
	defer cancel()

	profile, err := h.staticService.GetStaticProfile(ctx, userID)
	if err != nil {
		if err.Error() == "failed to get static profile: not found" {
			response.NotFound(c, "Static profile not found", nil)
			return
		}
		response.InternalServerError(c, "Failed to get static profile", err)
		return
	}

	response.Success(c, "Static profile retrieved successfully", profile)
}

// GetStaticProfiles retrieves a list of static profiles (Admin only)
// @Summary      获取所有静态画像
// @Description  分页获取所有用户的静态画像（管理员专用）
// @Tags         Profiles
// @Produce      json
// @Param        page query int false "页码"
// @Param        page_size query int false "每页数量"
// @Param        industry query string false "行业"
// @Param        learning_style query string false "学习风格"
// @Param        min_age query int false "最小年龄"
// @Param        max_age query int false "最大年龄"
// @Success      200 {object} response.Response{data=[]student_profile.StaticProfile,meta=gin.H}
// @Failure      401 {object} response.Response
// @Failure      403 {object} response.Response
// @Failure      500 {object} response.Response
// @Security     BearerAuth
// @Router       /profiles/static/list [get]
func (h *ProfileHandler) GetStaticProfiles(c *gin.Context) {
	var req services.GetStaticProfilesRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.BadRequest(c, "Invalid query parameters", err)
		return
	}

	ctx, cancel := context.WithTimeout(c, 10*time.Second)
	defer cancel()

	profiles, total, err := h.staticService.GetStaticProfiles(ctx, &req)
	if err != nil {
		response.InternalServerError(c, "Failed to get static profiles", err)
		return
	}

	meta := gin.H{
		"total":     total,
		"page":      req.Page,
		"page_size": req.PageSize,
	}

	responseData := gin.H{
		"profiles": profiles,
		"meta":     meta,
	}

	response.Success(c, "Static profiles retrieved successfully", responseData)
}

// ==================== 技术能力画像 (Tech Competency) Handlers ====================

// GetTechCompetencyGraph retrieves the technical competency graph for the authenticated user
// @Summary      获取技术能力画像
// @Description  获取当前登录用户的技术能力图谱
// @Tags         Profiles
// @Produce      json
// @Success      200 {object} response.Response{data=student_profile.TechCompetencyGraph}
// @Failure      401 {object} response.Response
// @Failure      404 {object} response.Response
// @Failure      500 {object} response.Response
// @Security     BearerAuth
// @Router       /profiles/tech-competency [get]
func (h *ProfileHandler) GetTechCompetencyGraph(c *gin.Context) {
	userID, err := h.getCurrentUserID(c)
	if err != nil {
		logging.Error("Failed to get user ID from context: %v", err)
		response.Unauthorized(c, "Authentication required", nil)
		return
	}

	ctx, cancel := context.WithTimeout(c, 10*time.Second)
	defer cancel()

	graph, err := h.techCompetencyService.GetTechCompetencyGraph(ctx, userID.String())
	if err != nil {
		if err.Error() == "failed to get tech competency graph: not found" {
			response.NotFound(c, "Tech competency graph not found", nil)
			return
		}
		response.InternalServerError(c, "Failed to get tech competency graph", err)
		return
	}

	response.Success(c, "Tech competency graph retrieved successfully", graph)
}

// UpdateTechCompetencyWithAction updates the technical competency graph based on a student action
// @Summary      更新技术能力画像
// @Description  基于用户行为（如完成课程、项目）更新技术能力画像
// @Tags         Profiles
// @Accept       json
// @Produce      json
// @Param        request body services.UpdateCompetencyActionRequest true "更新请求"
// @Success      200 {object} response.Response{data=student_profile.TechCompetencyGraph}
// @Failure      400 {object} response.Response
// @Failure      401 {object} response.Response
// @Failure      500 {object} response.Response
// @Security     BearerAuth
// @Router       /profiles/tech-competency [post]
func (h *ProfileHandler) UpdateTechCompetencyWithAction(c *gin.Context) {
	var req services.UpdateCompetencyActionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "Invalid request payload", err)
		return
	}

	userID, err := h.getCurrentUserID(c)
	if err != nil {
		logging.Error("Failed to get user ID from context: %v", err)
		response.Unauthorized(c, "Authentication required", nil)
		return
	}
	req.UserID = userID.String() // Convert uuid.UUID to string

	ctx, cancel := context.WithTimeout(c, 30*time.Second) // AI calls might take longer
	defer cancel()

	graph, err := h.techCompetencyService.UpdateTechCompetencyWithAction(ctx, &req)
	if err != nil {
		response.InternalServerError(c, "Failed to update tech competency graph", err)
		return
	}

	response.Success(c, "Tech competency graph updated successfully", graph)
}

// GetNodesByLevel retrieves competency nodes by level for the authenticated user
// @Summary      按等级获取能力节点
// @Description  获取用户在特定能力等级（如初级、中级、高级）的技能节点
// @Tags         Profiles
// @Produce      json
// @Param        level path string true "能力等级 (beginner, intermediate, advanced)" Enums(beginner, intermediate, advanced)
// @Success      200 {object} response.Response{data=[]student_profile.CompetencyNode}
// @Failure      400 {object} response.Response
// @Failure      401 {object} response.Response
// @Failure      500 {object} response.Response
// @Security     BearerAuth
// @Router       /profiles/tech-competency/nodes/{level} [get]
func (h *ProfileHandler) GetNodesByLevel(c *gin.Context) {
	userID, err := h.getCurrentUserID(c)
	if err != nil {
		logging.Error("Failed to get user ID from context: %v", err)
		response.Unauthorized(c, "Authentication required", nil)
		return
	}

	levelStr := c.Param("level")
	var level student_profile.CompetencyLevel
	switch levelStr {
	case "beginner":
		level = student_profile.CompetencyAware
	case "intermediate":
		level = student_profile.CompetencyNovice
	case "advanced":
		level = student_profile.CompetencyPractitioner
	default:
		response.BadRequest(c, "Invalid competency level", nil)
		return
	}

	ctx, cancel := context.WithTimeout(c, 10*time.Second)
	defer cancel()

	nodes, err := h.techCompetencyService.GetNodesByLevel(ctx, userID.String(), level)
	if err != nil {
		response.InternalServerError(c, "Failed to get nodes by level", err)
		return
	}

	response.Success(c, "Nodes retrieved successfully", nodes)
}

// GetUserCompetencyStats retrieves competency statistics for the authenticated user
// @Summary      获取能力统计
// @Description  获取用户在各能力等级上的节点数量统计
// @Tags         Profiles
// @Produce      json
// @Success      200 {object} response.Response{data=map[string]int}
// @Failure      401 {object} response.Response
// @Failure      500 {object} response.Response
// @Security     BearerAuth
// @Router       /profiles/tech-competency/stats [get]
func (h *ProfileHandler) GetUserCompetencyStats(c *gin.Context) {
	userID, err := h.getCurrentUserID(c)
	if err != nil {
		logging.Error("Failed to get user ID from context: %v", err)
		response.Unauthorized(c, "Authentication required", nil)
		return
	}

	ctx, cancel := context.WithTimeout(c, 10*time.Second)
	defer cancel()

	stats, err := h.techCompetencyService.GetUserCompetencyStats(ctx, userID.String())
	if err != nil {
		response.InternalServerError(c, "Failed to get competency stats", err)
		return
	}

	// Convert map[CompetencyLevel]int to map[string]int for JSON serialization
	statsStr := make(map[string]int)
	for k, v := range stats {
		statsStr[k.String()] = v
	}

	response.Success(c, "Competency stats retrieved successfully", statsStr)
}

// RefreshStaleGraphs refreshes outdated competency graphs (Admin only)
// @Summary      刷新陈旧画像
// @Description  定期刷新长时间未更新的技术能力画像（管理员专用）
// @Tags         Profiles
// @Produce      json
// @Success      200 {object} response.Response
// @Failure      401 {object} response.Response
// @Failure      403 {object} response.Response
// @Failure      500 {object} response.Response
// @Security     BearerAuth
// @Router       /profiles/tech-competency/refresh-stale [post]
func (h *ProfileHandler) RefreshStaleGraphs(c *gin.Context) {
	ctx, cancel := context.WithTimeout(c, 5*time.Minute) // This can be a long-running task
	defer cancel()

	err := h.techCompetencyService.RefreshStaleGraphs(ctx)
	if err != nil {
		response.InternalServerError(c, "Failed to refresh stale graphs", err)
		return
	}

	response.Success(c, "Stale competency graphs refresh process completed", nil)
}
