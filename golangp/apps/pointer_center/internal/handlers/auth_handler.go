/*
 * @Description: Authentication handlers for Pointer Center
 * @Author: AI Assistant
 * @Date: 2025-07-16
 */
package handlers

import (
	"context"
	"errors"
	"regexp"
	"strings"
	"time"

	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/internal/config"
	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/internal/models"
	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/internal/models/student_profile"
	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/internal/repositories"
	"github.com/Arxtect/CoreXWorkSpace/golangp/common/auth/googleauth"
	"github.com/Arxtect/CoreXWorkSpace/golangp/common/gin_core/response"
	"github.com/Arxtect/CoreXWorkSpace/golangp/common/logging"
	"github.com/Arxtect/CoreXWorkSpace/golangp/common/utils"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"go.mongodb.org/mongo-driver/mongo"
	"gorm.io/gorm"
)

// AuthHandler handles authentication-related requests
type AuthHandler struct {
	db                 *gorm.DB
	mongoDB            *mongo.Database
	googleService      *googleauth.Service
	logger             *logging.Logger
	techCompetencyRepo repositories.TechCompetencyRepository
	staticProfileRepo  repositories.StaticProfileRepository
	config             *config.Config
}

// NewAuthHandler creates a new authentication handler
func NewAuthHandler(db *gorm.DB, mongoDB *mongo.Database, googleClientID, googleClientSecret string) *AuthHandler {
	// Mask sensitive information for logging
	maskedClientID := googleClientID
	if len(googleClientID) > 10 {
		maskedClientID = googleClientID[:10] + "..."
	}
	maskedClientSecret := googleClientSecret
	if len(googleClientSecret) > 10 {
		maskedClientSecret = googleClientSecret[:10] + "..."
	}

	logger := logging.GetLogger("auth_handler")
	logger.Info("🔧 Initializing Google OAuth - ClientID: %s, ClientSecret: %s", maskedClientID, maskedClientSecret)

	googleConfig := googleauth.NewConfig(googleClientID, googleClientSecret)
	googleService := googleauth.NewService(googleConfig)

	// Initialize repositories
	techCompetencyRepo := repositories.NewTechCompetencyRepository(mongoDB)
	staticProfileRepo := repositories.NewStaticProfileRepository(db)

	cfg := config.Load()

	return &AuthHandler{
		db:                 db,
		mongoDB:            mongoDB,
		googleService:      googleService,
		logger:             logger,
		techCompetencyRepo: techCompetencyRepo,
		staticProfileRepo:  staticProfileRepo,
		config:             cfg,
	}
}

// LoginRequest represents the unified login request
type LoginRequest struct {
	// For normal login
	Email    string `json:"email"`
	Password string `json:"password"`

	// For Google login with ID Token (frontend flow)
	IDToken string `json:"id_token"`

	// For Google login with authorization code (server flow)
	AuthCode    string `json:"auth_code"`
	RedirectURI string `json:"redirect_uri"`
}

// RegisterRequest represents a user registration request
type RegisterRequest struct {
	Username  string `json:"username" binding:"required"`
	Email     string `json:"email" binding:"required,email"`
	Password  string `json:"password" binding:"required,min=8"`
	FirstName string `json:"first_name,omitempty"`
	LastName  string `json:"last_name,omitempty"`
}

// LoginResponse represents the login response
type LoginResponse struct {
	AccessToken  string      `json:"access_token"`
	RefreshToken string      `json:"refresh_token"`
	User         models.User `json:"user"`
}

// HandleRegister handles user registration
func (h *AuthHandler) HandleRegister(c *gin.Context) {
	startTime := time.Now()
	clientIP := c.ClientIP()
	h.logger.Info("🔄 Starting user registration - ClientIP: %s", clientIP)

	var req RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warning("❌ Invalid registration request - Error: %v, ClientIP: %s", err, clientIP)
		response.BadRequest(c, "Invalid request format", err)
		return
	}

	// Validate email format
	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	if !emailRegex.MatchString(req.Email) {
		h.logger.Warning("❌ Invalid email format - Email: %s, ClientIP: %s", req.Email, clientIP)
		response.BadRequest(c, "Invalid email format", nil)
		return
	}

	// Check if user already exists
	var existingUser models.User
	if err := h.db.Where("email = ? OR username = ?", req.Email, req.Username).First(&existingUser).Error; err == nil {
		h.logger.Warning("❌ User already exists - Email: %s, Username: %s, ClientIP: %s", req.Email, req.Username, clientIP)
		response.Conflict(c, "User with this email or username already exists", nil)
		return
	}

	// Hash password
	hashedPassword, err := utils.HashPassword(req.Password)
	if err != nil {
		h.logger.Error("❌ Failed to hash password - Error: %v, ClientIP: %s", err, clientIP)
		response.InternalServerError(c, "Failed to process password", err)
		return
	}

	// Create new user
	newUser := &models.User{
		ID:        uuid.New(),
		Username:  req.Username,
		Email:     req.Email,
		Password:  hashedPassword,
		FirstName: req.FirstName,
		LastName:  req.LastName,
		Role:      models.UserRoleUser,
		Status:    models.UserStatusActive,
		Origin:    "local",
	}

	// Start database transaction for atomic operations
	tx := h.db.Begin()
	if tx.Error != nil {
		h.logger.Error("❌ Failed to start transaction - Error: %v, Email: %s, ClientIP: %s", tx.Error, req.Email, clientIP)
		response.InternalServerError(c, "Failed to start transaction", tx.Error)
		return
	}

	// Create user within transaction
	if err := tx.Create(newUser).Error; err != nil {
		tx.Rollback()
		h.logger.Error("❌ Failed to create user - Error: %v, Email: %s, ClientIP: %s", err, req.Email, clientIP)
		response.InternalServerError(c, "Failed to create user", err)
		return
	}

	h.logger.Info("✅ User created successfully - UserID: %s, Email: %s, Duration: %v, ClientIP: %s",
		newUser.ID, newUser.Email, time.Since(startTime), clientIP)

	// Create empty static profile
	staticProfile := &student_profile.StaticProfile{
		UserID:              newUser.ID,
		Age:                 0,
		Gender:              "",
		PreferredLanguage:   "zh-CN",
		EducationExperience: "",
		Major:               "",
		GraduationYear:      0,
		CurrentRole:         "",
		Industry:            "",
		WorkExperience:      0,
		LearningStyle:       "visual",
		StudyTimePerWeek:    10,
		PreferredStudyTime:  "",
		LearningPace:        "moderate",
	}

	ctx := context.Background()
	if err := h.staticProfileRepo.Create(ctx, staticProfile); err != nil {
		tx.Rollback()
		h.logger.Error("❌ Failed to create static profile - Error: %v, UserID: %s, ClientIP: %s", err, newUser.ID, clientIP)
		response.InternalServerError(c, "Failed to create user profile", err)
		return
	}

	h.logger.Info("✅ Static profile created successfully - UserID: %s, ClientIP: %s", newUser.ID, clientIP)

	// Create empty tech competency graph
	competencyGraph := &student_profile.TechCompetencyGraph{
		UserID:           newUser.ID.String(),
		ProfileVersion:   1,
		Nodes:            make([]student_profile.CompetencyNode, 0),
		OverallScore:     "",
		StrengthAreas:    make([]string, 0),
		ImprovementAreas: make([]string, 0),
		LastUpdated:      time.Now(),
	}

	if err := h.techCompetencyRepo.Create(ctx, competencyGraph); err != nil {
		tx.Rollback()
		h.logger.Error("❌ Failed to create competency graph - Error: %v, UserID: %s, ClientIP: %s", err, newUser.ID, clientIP)
		response.InternalServerError(c, "Failed to create competency graph", err)
		return
	}

	h.logger.Info("✅ Competency graph created successfully - UserID: %s, ClientIP: %s", newUser.ID, clientIP)

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		h.logger.Error("❌ Failed to commit transaction - Error: %v, UserID: %s, ClientIP: %s", err, newUser.ID, clientIP)
		response.InternalServerError(c, "Failed to complete user registration", err)
		return
	}

	h.logger.Info("✅ User registration completed successfully - UserID: %s, Email: %s, Duration: %v, ClientIP: %s",
		newUser.ID, newUser.Email, time.Since(startTime), clientIP)

	response.Success(c, "Registration successful", newUser)
}

// HandleLogin handles unified login (normal + Google)
func (h *AuthHandler) HandleLogin(c *gin.Context) {
	startTime := time.Now()
	clientIP := c.ClientIP()
	h.logger.Info("🔄 Starting login process - ClientIP: %s", clientIP)

	var req LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warning("❌ Invalid login request - Error: %v, ClientIP: %s", err, clientIP)
		response.BadRequest(c, "Invalid request format", err)
		return
	}

	// Check if this is a Google login or normal login
	if req.IDToken != "" {
		// Google login with ID Token (frontend flow)
		h.logger.Info("🔄 Processing Google ID token login - Email: %s, ClientIP: %s", req.Email, clientIP)
		h.handleGoogleLogin(c, req.IDToken)
		h.logger.Info("✅ HandleLogin completed - Duration: %v, ClientIP: %s", time.Since(startTime), clientIP)
		return
	} else if req.AuthCode != "" && req.RedirectURI != "" {
		// Google login with authorization code (server flow)
		h.logger.Info("🔄 Processing Google auth code login - Email: %s, ClientIP: %s", req.Email, clientIP)
		h.handleGoogleAuthCode(c, req.AuthCode, req.RedirectURI)
		h.logger.Info("✅ HandleLogin completed - Duration: %v, ClientIP: %s", time.Since(startTime), clientIP)
		return
	} else if req.Email != "" && req.Password != "" {
		// Normal login
		h.logger.Info("🔄 Processing normal email/password login - Email: %s, ClientIP: %s", req.Email, clientIP)
		h.handleNormalLogin(c, req.Email, req.Password)
		h.logger.Info("✅ HandleLogin completed - Duration: %v, ClientIP: %s", time.Since(startTime), clientIP)
		return
	} else {
		h.logger.Warning("❌ Invalid login request - Missing required fields, ClientIP: %s", clientIP)
		response.BadRequest(c, "Either ID token, auth code with redirect URI, or email/password must be provided", nil)
		h.logger.Info("❌ HandleLogin failed - Duration: %v, ClientIP: %s", time.Since(startTime), clientIP)
		return
	}
}

// handleGoogleAuthCode handles Google OAuth login with authorization code
func (h *AuthHandler) handleGoogleAuthCode(c *gin.Context, authCode string, redirectURI string) {
	ctx := context.Background()

	// Mask auth code for logging
	maskedCode := authCode
	if len(authCode) > 10 {
		maskedCode = authCode[:10] + "..."
	}
	h.logger.Info("🔄 Exchanging Google auth code - Code: %s, RedirectURI: %s", maskedCode, redirectURI)

	// Exchange authorization code for ID token
	idToken, err := h.googleService.ExchangeAuthCode(ctx, authCode, redirectURI)
	if err != nil {
		h.logger.Error("❌ Failed to exchange auth code: %v", err)
		response.Unauthorized(c, "Failed to exchange authorization code", err)
		return
	}

	h.logger.Info("✅ Successfully exchanged auth code for ID token")

	// Now that we have the ID token, proceed with normal Google login flow
	h.handleGoogleLogin(c, idToken)
}

// handleGoogleLogin handles Google OAuth login with ID token
func (h *AuthHandler) handleGoogleLogin(c *gin.Context, idToken string) {
	ctx := context.Background()

	// Verify Google ID Token
	googleUser, err := h.googleService.VerifyIDToken(ctx, idToken)
	if err != nil {
		response.Unauthorized(c, "Invalid Google ID token", err)
		return
	}

	// Check if user exists
	var existingUser models.User
	err = h.db.Where("email = ?", googleUser.Email).First(&existingUser).Error
	isNewUser := false

	if errors.Is(err, gorm.ErrRecordNotFound) {
		// User doesn't exist, create new user
		newUser := &models.User{
			ID:        uuid.New(),
			Username:  generateUsernameFromEmail(googleUser.Email),
			Email:     googleUser.Email,
			FirstName: googleUser.GivenName,
			LastName:  googleUser.FamilyName,
			Role:      models.UserRoleUser,
			Status:    models.UserStatusActive,
			Origin:    "google",
			LastLogin: &time.Time{},
		}

		// Set current time as last login
		now := time.Now()
		newUser.LastLogin = &now

		if err := h.db.Create(newUser).Error; err != nil {
			response.InternalServerError(c, "Failed to create user", err)
			return
		}

		existingUser = *newUser
		isNewUser = true
		h.logger.Info("✅ Created new Google user - UserID: %s, Email: %s", existingUser.ID, existingUser.Email)
	} else if err != nil {
		response.InternalServerError(c, "Database error", err)
		return
	} else {
		// Update existing user's last login
		now := time.Now()
		existingUser.LastLogin = &now
		if err := h.db.Save(&existingUser).Error; err != nil {
			response.InternalServerError(c, "Failed to update user", err)
			return
		}
		h.logger.Info("✅ Existing Google user login - UserID: %s, Email: %s", existingUser.ID, existingUser.Email)
	}

	// Generate tokens
	accessTTL := time.Duration(h.config.AccessTokenMaxAge) * time.Minute
	refreshTTL := time.Duration(h.config.RefreshTokenMaxAge) * time.Minute

	accessToken, _, err := utils.GenerateAccessToken(&existingUser, accessTTL, h.config.AccessTokenPrivateKey)
	if err != nil {
		response.InternalServerError(c, "Failed to generate access token", err)
		return
	}

	refreshToken, _, err := utils.GenerateRefreshToken(&existingUser, refreshTTL, h.config.RefreshTokenPrivateKey)
	if err != nil {
		response.InternalServerError(c, "Failed to generate refresh token", err)
		return
	}

	// Set HTTP cookies
	c.SetCookie("access_token", accessToken, h.config.AccessTokenMaxAge*60, "/", h.config.Domain, false, true)
	c.SetCookie("refresh_token", refreshToken, h.config.RefreshTokenMaxAge*60, "/", h.config.Domain, false, true)

	// Return success response
	message := "Login successful"
	if isNewUser {
		message = "Account created and login successful"
	}

	response.Success(c, message, LoginResponse{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
	})
}

// handleNormalLogin handles normal login with email and password
func (h *AuthHandler) handleNormalLogin(c *gin.Context, email, password string) {
	// Get user by email
	var user models.User
	if err := h.db.Where("email = ?", email).First(&user).Error; err != nil {
		h.logger.Warning("❌ User not found - Email: %s, ClientIP: %s", email, c.ClientIP())
		response.Unauthorized(c, "User not found", err)
		return
	}

	// Verify password
	if err := utils.VerifyPassword(user.Password, password); err != nil {
		h.logger.Warning("❌ Password verification failed - Email: %s, ClientIP: %s", email, c.ClientIP())
		response.Unauthorized(c, "Password incorrect", err)
		return
	}

	// Update last login
	now := time.Now()
	user.LastLogin = &now
	if err := h.db.Save(&user).Error; err != nil {
		response.InternalServerError(c, "Failed to update user", err)
		return
	}

	// Generate tokens
	accessTTL := time.Duration(h.config.AccessTokenMaxAge) * time.Minute
	refreshTTL := time.Duration(h.config.RefreshTokenMaxAge) * time.Minute

	accessToken, _, err := utils.GenerateAccessToken(&user, accessTTL, h.config.AccessTokenPrivateKey)
	if err != nil {
		response.InternalServerError(c, "Failed to generate access token", err)
		return
	}

	refreshToken, _, err := utils.GenerateRefreshToken(&user, refreshTTL, h.config.RefreshTokenPrivateKey)
	if err != nil {
		response.InternalServerError(c, "Failed to generate refresh token", err)
		return
	}

	// Set HTTP cookies
	c.SetCookie("access_token", accessToken, h.config.AccessTokenMaxAge*60, "/", h.config.Domain, false, true)
	c.SetCookie("refresh_token", refreshToken, h.config.RefreshTokenMaxAge*60, "/", h.config.Domain, false, true)

	response.Success(c, "Login successful", LoginResponse{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		User:         user,
	})
}

// HandleLogout handles user logout
func (h *AuthHandler) HandleLogout(c *gin.Context) {
	clientIP := c.ClientIP()
	h.logger.Info("🔄 Processing logout - ClientIP: %s", clientIP)
	currentUser, ok := response.GetUserFromContext[models.User](c)
	if !ok {
		response.Unauthorized(c, "User not authenticated", nil)
		return
	}
	// Clear cookies
	c.SetCookie("access_token", "", -1, "/", h.config.Domain, false, true)
	c.SetCookie("refresh_token", "", -1, "/", h.config.Domain, false, true)

	h.logger.Info("✅ User logged out successfully - UserID: %s, ClientIP: %s", currentUser.ID, clientIP)
	response.Success(c, "Logout successful", nil)
}

// RefreshToken handles token refresh
func (h *AuthHandler) RefreshToken(c *gin.Context) {
	clientIP := c.ClientIP()
	h.logger.Info("🔄 Processing token refresh - ClientIP: %s", clientIP)

	// Get refresh token from context (set by AuthMiddleware)
	refreshTokenValue, exists := c.Get("refresh_token")
	if !exists {
		h.logger.Warning("❌ No refresh token in context - ClientIP: %s", clientIP)
		response.Unauthorized(c, "No refresh token provided", nil)
		return
	}

	refreshToken, ok := refreshTokenValue.(string)
	if !ok {
		h.logger.Warning("❌ Invalid refresh token type in context - ClientIP: %s", clientIP)
		response.Unauthorized(c, "Invalid refresh token format", nil)
		return
	}

	// Validate refresh token
	_, err := utils.ValidateToken(refreshToken, h.config.RefreshTokenPublicKey)
	if err != nil {
		h.logger.Warning("❌ Invalid refresh token - Error: %v, ClientIP: %s", err, clientIP)
		response.Unauthorized(c, "Invalid refresh token", err)
		return
	}

	currentUser, ok := response.GetUserFromContext[models.User](c)

	// Get user from database
	var user models.User
	if err := h.db.First(&user, "id = ?", currentUser.ID).Error; err != nil {
		response.Unauthorized(c, "User not found", err)
		return
	}

	// Generate new tokens
	accessTTL := time.Duration(h.config.AccessTokenMaxAge) * time.Minute
	refreshTTL := time.Duration(h.config.RefreshTokenMaxAge) * time.Minute

	newAccessToken, _, err := utils.GenerateAccessToken(&user, accessTTL, h.config.AccessTokenPrivateKey)
	if err != nil {
		response.InternalServerError(c, "Failed to generate new access token", err)
		return
	}

	newRefreshToken, _, err := utils.GenerateRefreshToken(&user, refreshTTL, h.config.RefreshTokenPrivateKey)
	if err != nil {
		response.InternalServerError(c, "Failed to generate new refresh token", err)
		return
	}

	// Set new cookies
	c.SetCookie("access_token", newAccessToken, h.config.AccessTokenMaxAge*60, "/", h.config.Domain, false, true)
	c.SetCookie("refresh_token", newRefreshToken, h.config.RefreshTokenMaxAge*60, "/", h.config.Domain, false, true)

	h.logger.Info("✅ Token refreshed successfully - UserID: %s, ClientIP: %s", currentUser.ID, clientIP)
	response.Success(c, "Token refreshed successfully",
		LoginResponse{
			AccessToken:  newAccessToken,
			RefreshToken: newRefreshToken,
		})
}

// GetCurrentUser returns current user information
func (h *AuthHandler) GetCurrentUser(c *gin.Context) {
	clientIP := c.ClientIP()
	CurrentUser, ok := response.GetUserFromContext[models.User](c)
	if !ok {
		h.logger.Warning("❌ User not found in context - ClientIP: %s", clientIP)
		response.Unauthorized(c, "User not authenticated", nil)
		return
	}
	// Get user from database
	var user models.User
	if err := h.db.First(&user, "id = ?", CurrentUser.ID).Error; err != nil {
		h.logger.Error("❌ User not found - UserID: %s, Error: %v, ClientIP: %s", CurrentUser.ID, err, clientIP)
		response.NotFound(c, "User not found", err)
		return
	}

	h.logger.Info("✅ Current user retrieved - UserID: %s, Email: %s, ClientIP: %s", user.ID, user.Email, clientIP)
	response.Success(c, "User information retrieved successfully", user)
}

// UserUpdateProfileRequest represents a user profile update request
type UserUpdateProfileRequest struct {
	Username  string `json:"username"`
	FirstName string `json:"first_name"`
	LastName  string `json:"last_name"`
	Bio       string `json:"bio"`
	Phone     string `json:"phone"`
	Company   string `json:"company"`
	Country   string `json:"country"`
	Github    string `json:"github"`
	Website   string `json:"website"`
}

// UpdateProfile updates user profile information
func (h *AuthHandler) UpdateProfile(c *gin.Context) {
	clientIP := c.ClientIP()
	currentUser, ok := response.GetUserFromContext[models.User](c)
	if !ok {
		h.logger.Warning("❌ User not found in context - ClientIP: %s", clientIP)
		response.Unauthorized(c, "User not authenticated", nil)
		return
	}

	var req UserUpdateProfileRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warning("❌ Invalid profile update request - Error: %v, ClientIP: %s", err, clientIP)
		response.BadRequest(c, "Invalid request format", err)
		return
	}

	// Get current user
	var user models.User
	if err := h.db.First(&user, "id = ?", currentUser.ID).Error; err != nil {
		h.logger.Error("❌ User not found - UserID: %s, Error: %v, ClientIP: %s", currentUser.ID, err, clientIP)
		response.NotFound(c, "User not found", err)
		return
	}

	// Check if username is being changed and if it's already taken
	if req.Username != "" && req.Username != user.Username {
		var existingUser models.User
		if err := h.db.Where("username = ? AND id != ?", req.Username, currentUser.ID).First(&existingUser).Error; err == nil {
			h.logger.Warning("❌ Username already taken - Username: %s, UserID: %s, ClientIP: %s", req.Username, currentUser.ID, clientIP)
			response.Conflict(c, "Username already taken", nil)
			return
		}
	}

	// Update user fields
	if req.Username != "" {
		user.Username = req.Username
	}
	if req.FirstName != "" {
		user.FirstName = req.FirstName
	}
	if req.LastName != "" {
		user.LastName = req.LastName
	}
	if req.Bio != "" {
		user.Bio = req.Bio
	}
	if req.Phone != "" {
		user.Phone = req.Phone
	}
	if req.Company != "" {
		user.Company = req.Company
	}
	if req.Country != "" {
		user.Country = req.Country
	}
	if req.Github != "" {
		user.Github = req.Github
	}
	if req.Website != "" {
		user.Website = req.Website
	}

	// Save updated user
	if err := h.db.Save(&user).Error; err != nil {
		h.logger.Error("❌ Failed to update user profile - UserID: %s, Error: %v, ClientIP: %s", currentUser.ID, err, clientIP)
		response.InternalServerError(c, "Failed to update profile", err)
		return
	}

	h.logger.Info("✅ User profile updated successfully - UserID: %s, ClientIP: %s", currentUser.ID, clientIP)
	response.Success(c, "Profile updated successfully", user)
}

// ChangePasswordRequest represents a password change request
type ChangePasswordRequest struct {
	CurrentPassword string `json:"current_password" binding:"required"`
	NewPassword     string `json:"new_password" binding:"required,min=8"`
}

// ChangePassword handles password change
func (h *AuthHandler) ChangePassword(c *gin.Context) {
	clientIP := c.ClientIP()
	currentUser, ok := response.GetUserFromContext[models.User](c)
	if !ok {
		h.logger.Warning("❌ User not found in context - ClientIP: %s", clientIP)
		response.Unauthorized(c, "User not authenticated", nil)
		return
	}

	var req ChangePasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warning("❌ Invalid password change request - Error: %v, ClientIP: %s", err, clientIP)
		response.BadRequest(c, "Invalid request format", err)
		return
	}

	// Get current user
	var user models.User
	if err := h.db.First(&user, "id = ?", currentUser.ID).Error; err != nil {
		h.logger.Error("❌ User not found - UserID: %s, Error: %v, ClientIP: %s", currentUser.ID, err, clientIP)
		response.NotFound(c, "User not found", err)
		return
	}

	// Verify current password
	if err := utils.VerifyPassword(user.Password, req.CurrentPassword); err != nil {
		h.logger.Warning("❌ Current password verification failed - UserID: %s, ClientIP: %s", currentUser.ID, clientIP)
		response.Error(c, 422, "Current password is incorrect")
		return
	}

	// Hash new password
	hashedPassword, err := utils.HashPassword(req.NewPassword)
	if err != nil {
		h.logger.Error("❌ Failed to hash new password - UserID: %s, Error: %v, ClientIP: %s", currentUser.ID, err, clientIP)
		response.InternalServerError(c, "Failed to process new password", err)
		return
	}

	// Update password
	user.Password = hashedPassword
	if err := h.db.Save(&user).Error; err != nil {
		h.logger.Error("❌ Failed to update password - UserID: %s, Error: %v, ClientIP: %s", currentUser.ID, err, clientIP)
		response.InternalServerError(c, "Failed to update password", err)
		return
	}

	h.logger.Info("✅ Password changed successfully - UserID: %s, ClientIP: %s", currentUser.ID, clientIP)
	response.Success(c, "Password changed successfully", nil)
}

// RequestEmailChangeRequest represents an email change request
type RequestEmailChangeRequest struct {
	NewEmail string `json:"new_email" binding:"required,email"`
	Password string `json:"password" binding:"required"`
}

// generateUsernameFromEmail generates a username from email
func generateUsernameFromEmail(email string) string {
	parts := strings.Split(email, "@")
	if len(parts) > 0 {
		return parts[0]
	}
	return email
}
