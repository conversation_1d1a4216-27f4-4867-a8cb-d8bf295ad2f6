package handlers

import (
	"context"
	"fmt"
	"time"

	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/internal/models"
	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/internal/services"
	"github.com/Arxtect/CoreXWorkSpace/golangp/common/gin_core/response"
	"github.com/Arxtect/CoreXWorkSpace/golangp/common/logging"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// LearningLessonHandler handles HTTP requests for learning lessons
type LearningLessonHandler struct {
	service *services.LearningLessonService
}

// NewLearningLessonHandler creates a new learning lesson handler
func NewLearningLessonHandler(service *services.LearningLessonService) *LearningLessonHandler {
	return &LearningLessonHandler{service: service}
}

// CreateLearningLesson godoc
// @Summary Create a new learning lesson
// @Description Creates a new standard learning lesson. Requires authentication.
// @Description Optional fields: node_id (UUID) and order (int). If node_id is provided, the lesson will be automatically associated to the node on creation.
// @Description If order is omitted or 0, the lesson is appended to the end (max order + 1).
// @Tags Learning Lessons
// @Accept json
// @Produce json
// @Param request body services.CreateLearningLessonRequest true "Create Learning Lesson Request (server generates lesson_id)"
// @Success 201 {object} models.LearningLesson
// @Failure 400 {object} response.APIError
// @Failure 401 {object} response.APIError
// @Failure 500 {object} response.APIError
// @Router /learning-lessons [post]
// @Security BearerAuth
func (h *LearningLessonHandler) CreateLearningLesson(c *gin.Context) {
	var req services.CreateLearningLessonRequest

	// Bind JSON request
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "Invalid request body", err)
		return
	}

	// Get user ID from context (set by AuthMiddleware) and set as CreatedBy if not provided or empty
	// This ensures security by preventing impersonation via the request body
	if req.CreatedBy == "" {
		currentUser, exists := response.GetUserFromContext[models.User](c)
		if !exists {
			response.Unauthorized(c, "User not authenticated", nil)
			return
		}
		// Assuming user_id in context is a string that can be used directly
		// If it's a uuid.UUID, convert it to string: userIDStr := userID.(uuid.UUID).String()
		userIDStr := currentUser.ID.String()
		req.CreatedBy = userIDStr
	}

	// Call service
	ctx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Second)
	defer cancel()

	lesson, err := h.service.CreateLearningLesson(ctx, &req)
	if err != nil {
		logging.Error("Handler: Failed to create learning lesson: %v", err)
		// Check for specific validation errors if needed and return 400
		response.InternalServerError(c, "Failed to create learning lesson", err)
		return
	}

	response.Created(c, "Learning lesson created successfully", lesson)
}

// GenerateLearningLessonWithAI godoc
// @Summary Generate a learning lesson using AI
// @Description Generates a personalized learning lesson based on description and user profile. Requires authentication.
// @Tags Learning Lessons
// @Accept json
// @Produce json
// @Param request body services.GenerateLessonRequest true "Generate Lesson Request"
// @Success 201 {object} models.LearningLesson
// @Failure 400 {object} response.APIError
// @Failure 401 {object} response.APIError
// @Failure 500 {object} response.APIError
// @Router /learning-lessons/generate [post]
// @Security BearerAuth
func (h *LearningLessonHandler) GenerateLearningLessonWithAI(c *gin.Context) {
	var req services.GenerateLessonRequest

	// Bind JSON request
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "Invalid request body", err)
		return
	}

	// Get user ID from context (set by AuthMiddleware) and set UserID
	// UserID is optional in the service but useful for context
	currentUser, ok := response.GetUserFromContext[models.User](c)
	if !ok {
		response.Unauthorized(c, "用户未认证", nil)
		return
	}
	req.UserID = currentUser.ID.String()

	// Call service
	ctx, cancel := context.WithTimeout(c.Request.Context(), 60*time.Second) // Longer timeout for AI
	defer cancel()

	lesson, err := h.service.GenerateLearningLessonWithAI(ctx, &req)
	if err != nil {
		logging.Error("Handler: Failed to generate learning lesson with AI: %v", err)
		response.InternalServerError(c, "Failed to generate learning lesson", err)
		return
	}

	response.Created(c, "AI-generated learning lesson created successfully", lesson)
}

// UpdateLearningLesson godoc
// @Summary Update a learning lesson
// @Description Updates an existing learning lesson by ID. Requires authentication.
// @Tags Learning Lessons
// @Accept json
// @Produce json
// @Param id path string true "Learning Lesson ID (ObjectID)"
// @Param request body services.UpdateLearningLessonRequest true "Update Learning Lesson Request"
// @Success 200 {object} models.LearningLesson
// @Failure 400 {object} response.APIError
// @Failure 401 {object} response.APIError
// @Failure 404 {object} response.APIError
// @Failure 500 {object} response.APIError
// @Router /learning-lessons/{id} [put]
// @Security BearerAuth
func (h *LearningLessonHandler) UpdateLearningLesson(c *gin.Context) {
	lessonIDStr := c.Param("id")
	lessonID, err := primitive.ObjectIDFromHex(lessonIDStr)
	if err != nil {
		response.BadRequest(c, "Invalid lesson ID format", err)
		return
	}

	var req services.UpdateLearningLessonRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "Invalid request body", err)
		return
	}

	// Get user ID from context (set by AuthMiddleware) and set as UpdatedBy if not provided
	// This ensures security by preventing impersonation via the request body
	if req.UpdatedBy == nil || *req.UpdatedBy == "" {
		user, exists := response.GetUserFromContext[models.User](c)
		if exists {
			userIDStr := user.ID.String()
			req.UpdatedBy = &userIDStr
		}
		// If user is not authenticated or type is wrong, req.UpdatedBy might remain nil/empty, which is acceptable.
	}

	// Call service
	ctx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Second)
	defer cancel()

	updatedLesson, err := h.service.UpdateLearningLesson(ctx, lessonID, &req)
	if err != nil {
		if err.Error() == fmt.Sprintf("failed to get learning lesson: %v", lessonID) { // Adapt error check
			response.NotFound(c, "Learning lesson not found", err)
			return
		}
		logging.Error("Handler: Failed to update learning lesson: %v", err)
		response.InternalServerError(c, "Failed to update learning lesson", err)
		return
	}

	response.Success(c, "Learning lesson updated successfully", updatedLesson)
}

// DeleteLearningLesson godoc
// @Summary Delete a learning lesson
// @Description Deletes a learning lesson by ID. Requires authentication.
// @Tags Learning Lessons
// @Produce json
// @Param id path string true "Learning Lesson ID (ObjectID)"
// @Success 200 {object} response.APIResponse
// @Failure 400 {object} response.APIError
// @Failure 401 {object} response.APIError
// @Failure 404 {object} response.APIError
// @Failure 500 {object} response.APIError
// @Router /learning-lessons/{id} [delete]
// @Security BearerAuth
func (h *LearningLessonHandler) DeleteLearningLesson(c *gin.Context) {
	lessonIDStr := c.Param("id")
	lessonID, err := primitive.ObjectIDFromHex(lessonIDStr)
	if err != nil {
		response.BadRequest(c, "Invalid lesson ID format", err)
		return
	}

	// Call service
	ctx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Second)
	defer cancel()

	err = h.service.DeleteLearningLesson(ctx, lessonID)
	if err != nil {
		if err.Error() == fmt.Sprintf("learning lesson not found: %v", lessonID) { // Adapt error check
			response.NotFound(c, "Learning lesson not found", err)
			return
		}
		logging.Error("Handler: Failed to delete learning lesson: %v", err)
		response.InternalServerError(c, "Failed to delete learning lesson", err)
		return
	}

	response.Success(c, "Learning lesson deleted successfully", nil)
}

// GetLearningLesson godoc
// @Summary Get a learning lesson by ID
// @Description Retrieves a learning lesson by its ID.
// @Tags Learning Lessons
// @Produce json
// @Param id path string true "Learning Lesson ID (ObjectID)"
// @Success 200 {object} models.LearningLesson
// @Failure 400 {object} response.APIError
// @Failure 404 {object} response.APIError
// @Failure 500 {object} response.APIError
// @Router /learning-lessons/{id} [get]
func (h *LearningLessonHandler) GetLearningLesson(c *gin.Context) {
	lessonIDStr := c.Param("id")
	lessonID, err := primitive.ObjectIDFromHex(lessonIDStr)
	if err != nil {
		response.BadRequest(c, "Invalid lesson ID format", err)
		return
	}

	// Call service
	ctx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Second)
	defer cancel()

	lesson, err := h.service.GetLearningLesson(ctx, lessonID)
	if err != nil {
		if err.Error() == fmt.Sprintf("failed to get learning lesson: %v", lessonID) { // Adapt error check
			response.NotFound(c, "Learning lesson not found", err)
			return
		}
		logging.Error("Handler: Failed to get learning lesson: %v", err)
		response.InternalServerError(c, "Failed to get learning lesson", err)
		return
	}

	response.Success(c, "Learning lesson retrieved successfully", lesson)
}

// GetLearningLessons godoc
// @Summary Get learning lessons with pagination
// @Description Retrieves a list of learning lessons with pagination.
// @Tags Learning Lessons
// @Produce json
// @Param page query int false "Page number (default: 1)"
// @Param page_size query int false "Page size (default: 20)"
// @Success 200 {object} response.PaginatedResponse{data=[]models.LearningLesson}
// @Failure 400 {object} response.APIError
// @Failure 500 {object} response.APIError
// @Router /learning-lessons [get]
func (h *LearningLessonHandler) GetLearningLessons(c *gin.Context) {
	var req services.GetLearningLessonsRequest
	// 统一绑定：支持 page、page_size、q、type
	if err := c.ShouldBindQuery(&req); err != nil {
		response.BadRequest(c, "Invalid query parameters", err)
		return
	}
	ctx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Second)
	defer cancel()
	lessons, total, err := h.service.GetLearningLessons(ctx, &req)
	if err != nil {
		logging.Error("Handler: Failed to get learning lessons: %v", err)
		response.InternalServerError(c, "Failed to get learning lessons", err)
		return
	}
	response.Paginated(c, "Learning lessons retrieved successfully", lessons, int64(total), req.Page, req.PageSize)
}

// GetLessonsByType godoc
// @Summary Get learning lessons by type
// @Description Retrieves a list of learning lessons filtered by type with pagination.
// @Tags Learning Lessons
// @Produce json
// @Param type query string true "Lesson Type"
// @Param page query int false "Page number (default: 1)"
// @Param page_size query int false "Page size (default: 20)"
// @Success 200 {object} response.PaginatedResponse{data=[]models.LearningLesson}
// @Failure 400 {object} response.APIError
// @Failure 500 {object} response.APIError
// @Router /learning-lessons/type [get]
// removed GetLessonsByType; use GetLearningLessons with type query param

// UpdateLessonStatus godoc
// @Summary Update a learning lesson's status
// @Description Updates the status of an existing learning lesson by ID. Requires authentication.
// @Tags Learning Lessons
// @Accept json
// @Produce json
// @Param id path string true "Learning Lesson ID (ObjectID)"
// @Param request body UpdateLessonStatusRequest true "Update Lesson Status Request"
// @Success 200 {object} response.APIResponse
// @Failure 400 {object} response.APIError
// @Failure 401 {object} response.APIError
// @Failure 404 {object} response.APIError
// @Failure 500 {object} response.APIError
// @Router /learning-lessons/{id}/status [put]
// @Security BearerAuth
func (h *LearningLessonHandler) UpdateLessonStatus(c *gin.Context) {
	lessonIDStr := c.Param("id")
	lessonID, err := primitive.ObjectIDFromHex(lessonIDStr)
	if err != nil {
		response.BadRequest(c, "Invalid lesson ID format", err)
		return
	}

	var req struct {
		Status models.LessonStatus `json:"status" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "Invalid request body", err)
		return
	}

	// Call service
	ctx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Second)
	defer cancel()

	err = h.service.UpdateLessonStatus(ctx, lessonID, req.Status)
	if err != nil {
		if err.Error() == fmt.Sprintf("failed to get learning lesson: %v", lessonID) { // Adapt error check
			response.NotFound(c, "Learning lesson not found", err)
			return
		}
		// Check for invalid status error
		if err.Error() == fmt.Sprintf("invalid status: %s", req.Status) {
			response.BadRequest(c, "Invalid lesson status", err)
			return
		}
		logging.Error("Handler: Failed to update lesson status: %v", err)
		response.InternalServerError(c, "Failed to update lesson status", err)
		return
	}

	response.Success(c, "Lesson status updated successfully", nil)
}

// SearchLessons godoc
// @Summary Search learning lessons
// @Description Searches learning lessons by content or title with pagination.
// @Tags Learning Lessons
// @Produce json
// @Param q query string true "Search query"
// @Param page query int false "Page number (default: 1)"
// @Param page_size query int false "Page size (default: 20)"
// @Success 200 {object} response.PaginatedResponse{data=[]models.LearningLesson}
// @Failure 400 {object} response.APIError
// @Failure 500 {object} response.APIError
// @Router /learning-lessons/search [get]
// removed SearchLessons; use GetLearningLessons with q query param

// ArchiveLesson godoc
// @Summary Archive a learning lesson
// @Description Archives a learning lesson (sets status to Archived). Requires authentication.
// @Tags Learning Lessons
// @Produce json
// @Param id path string true "Learning Lesson ID (ObjectID)"
// @Success 200 {object} response.APIResponse
// @Failure 400 {object} response.APIError
// @Failure 401 {object} response.APIError
// @Failure 404 {object} response.APIError
// @Failure 500 {object} response.APIError
// @Router /learning-lessons/{id}/archive [post]
// @Security BearerAuth
func (h *LearningLessonHandler) ArchiveLesson(c *gin.Context) {
	lessonIDStr := c.Param("id")
	lessonID, err := primitive.ObjectIDFromHex(lessonIDStr)
	if err != nil {
		response.BadRequest(c, "Invalid lesson ID format", err)
		return
	}

	// Call service
	ctx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Second)
	defer cancel()

	err = h.service.ArchiveLesson(ctx, lessonID)
	if err != nil {
		if err.Error() == fmt.Sprintf("failed to get learning lesson: %v", lessonID) { // Adapt error check
			response.NotFound(c, "Learning lesson not found", err)
			return
		}
		if err.Error() == fmt.Sprintf("invalid status: %s", models.LessonStatusArchived) { // Adapt error check
			response.BadRequest(c, "Cannot archive lesson", err)
			return
		}
		logging.Error("Handler: Failed to archive learning lesson: %v", err)
		response.InternalServerError(c, "Failed to archive learning lesson", err)
		return
	}

	response.Success(c, "Learning lesson archived successfully", nil)
}

// PublishLesson godoc
// @Summary Publish a learning lesson
// @Description Publishes a learning lesson (sets status to Active). Requires authentication.
// @Tags Learning Lessons
// @Produce json
// @Param id path string true "Learning Lesson ID (ObjectID)"
// @Success 200 {object} response.APIResponse
// @Failure 400 {object} response.APIError
// @Failure 401 {object} response.APIError
// @Failure 404 {object} response.APIError
// @Failure 500 {object} response.APIError
// @Router /learning-lessons/{id}/publish [post]
// @Security BearerAuth
func (h *LearningLessonHandler) PublishLesson(c *gin.Context) {
	lessonIDStr := c.Param("id")
	lessonID, err := primitive.ObjectIDFromHex(lessonIDStr)
	if err != nil {
		response.BadRequest(c, "Invalid lesson ID format", err)
		return
	}

	// Call service
	ctx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Second)
	defer cancel()

	err = h.service.PublishLesson(ctx, lessonID)
	if err != nil {
		if err.Error() == fmt.Sprintf("failed to get learning lesson: %v", lessonID) { // Adapt error check
			response.NotFound(c, "Learning lesson not found", err)
			return
		}
		if err.Error() == fmt.Sprintf("invalid status: %s", models.LessonStatusActive) { // Adapt error check
			response.BadRequest(c, "Cannot publish lesson", err)
			return
		}
		logging.Error("Handler: Failed to publish learning lesson: %v", err)
		response.InternalServerError(c, "Failed to publish learning lesson", err)
		return
	}

	response.Success(c, "Learning lesson published successfully", nil)
}

// RegisterRoutes registers the learning lesson routes
func (h *LearningLessonHandler) RegisterRoutes(r *gin.RouterGroup) {
	// Public routes
	r.GET("/learning-lessons", h.GetLearningLessons)
	r.GET("/learning-lessons/:id", h.GetLearningLesson)

	// Authenticated routes
	r.POST("/learning-lessons", h.CreateLearningLesson)
	r.POST("/learning-lessons/generate", h.GenerateLearningLessonWithAI)
	r.PUT("/learning-lessons/:id", h.UpdateLearningLesson)
	r.DELETE("/learning-lessons/:id", h.DeleteLearningLesson)
	r.PUT("/learning-lessons/:id/status", h.UpdateLessonStatus)
	r.POST("/learning-lessons/:id/archive", h.ArchiveLesson)
	r.POST("/learning-lessons/:id/publish", h.PublishLesson)
}
