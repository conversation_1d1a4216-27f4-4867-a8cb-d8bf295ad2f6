package handlers

import (
	"context"
	"net/http"
	"time"

	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/internal/models"
	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/internal/services"
	"github.com/Arxtect/CoreXWorkSpace/golangp/common/gin_core/response"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

type PersonalizeHandler struct {
    svc *services.PersonalizeService
}

func NewPersonalizeHandler(svc *services.PersonalizeService) *PersonalizeHandler {
    return &PersonalizeHandler{svc: svc}
}

// POST /personalize/start
func (h *PersonalizeHandler) Start(c *gin.Context) {
    var req services.StartRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        response.BadRequest(c, "invalid request body", err)
        return
    }
    currentUser, exists := response.GetUserFromContext[models.User](c)
	if !exists {
		response.Unauthorized(c, "User not authenticated", nil)
		return
	}
    req.UserID = currentUser.ID
    ctx, cancel := context.WithTimeout(c.Request.Context(), 60*time.Second)
    defer cancel()
    workID, err := h.svc.Start(ctx, req)
    if err != nil {
        response.InternalServerError(c, "start personalize failed", err)
        return
    }
    response.Success(c, "started", gin.H{"success": true, "work_id": workID})
}

// POST /personalize/reply
func (h *PersonalizeHandler) Reply(c *gin.Context) {
    var req struct {
    WorkID string `json:"work_id" binding:"required"`
    Reply  string `json:"reply" binding:"required"`
    }
    if err := c.ShouldBindJSON(&req); err != nil {
        response.BadRequest(c, "invalid request body", err)
        return
    }
    currentUser, exists := response.GetUserFromContext[models.User](c)
	if !exists {
		response.Unauthorized(c, "User not authenticated", nil)
		return
	}
    ctx, cancel := context.WithTimeout(c.Request.Context(), 60*time.Second)
    defer cancel()
    workUUID, err := uuid.Parse(req.WorkID)
    if err != nil {
        response.BadRequest(c, "invalid work_id", err)
        return
    }
    im, err := h.svc.Reply(ctx, workUUID, currentUser.ID, req.Reply)
    if err != nil {
        response.InternalServerError(c, "reply failed", err)
        return
    }
    response.Success(c, "ok", im)
}

// GET /personalize/status/:work_id
func (h *PersonalizeHandler) Status(c *gin.Context) {
    workIDStr := c.Param("work_id")
    if workIDStr == "" {
        response.BadRequest(c, "missing work_id", nil)
        return
    }
    workID, err := uuid.Parse(workIDStr)
    if err != nil {
        response.BadRequest(c, "invalid work_id", err)
        return
    }
    ctx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Second)
    defer cancel()
    st, err := h.svc.Status(ctx, workID)
    if err != nil {
        response.Error(c, http.StatusBadRequest, "status failed")
        return
    }
    response.Success(c, "ok", st)
}

// RegisterRoutes registers personalize routes
func (h *PersonalizeHandler) RegisterRoutes(r *gin.RouterGroup) {
    grp := r.Group("/personalize")
    grp.POST("/start", h.Start)
    grp.POST("/reply", h.Reply)
    grp.GET("/status/:work_id", h.Status)
}
