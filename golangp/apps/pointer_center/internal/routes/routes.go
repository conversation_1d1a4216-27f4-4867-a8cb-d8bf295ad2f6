/*
 * @Description: Main routes configuration for Pointer Center
 * @Author: AI Assistant
 * @Date: 2025-07-16
 */
package routes

import (
	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/internal/config"
	localMiddleware "github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/internal/middleware"
	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/internal/repositories"
	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/pkg/ai_agents"
	"github.com/Arxtect/CoreXWorkSpace/golangp/common/database/mongodb"
	"github.com/Arxtect/CoreXWorkSpace/golangp/common/gin_core/middleware"
	"github.com/Arxtect/CoreXWorkSpace/golangp/common/logging"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// SetupRoutes configures all routes for the application
func SetupRoutes(router *gin.Engine, cfg *config.Config, db *gorm.DB, googleClientID string, googleClientSecret string) {
	// Initialize logger
	loggerConfig := &logging.LoggerConfig{
		Level:    logging.INFO,
		LogPath:  "logs",
		FileName: "pointer_center",
		Env:      "dev",
	}
	logging.InitLogger(loggerConfig)
	logger := logging.GetLogger("pointer_center")

	// Add global middleware
	router.Use(gin.Recovery())
	router.Use(middleware.RequestIDMiddleware())
	router.Use(middleware.LoggingMiddleware(cfg.LogPath, cfg.Environment))
	router.Use(middleware.CORSMiddleware())

	// Add PFChat proxy middleware globally
	// This will intercept requests to /api/v1/pfchat and proxy them to external service
	router.Use(localMiddleware.PFChatProxyMiddleware(cfg, logger))

	// Setup health check routes
	SetupHealthRoutes(router)

	// Initialize repositories
	mongoDB := mongodb.GetDB()
	repos := repositories.NewRepositories(db, mongoDB)

	// Initialize AI agents manager
	agent_apikeys := cfg.GetAgentAPIKeys()
	ai_agents := ai_agents.NewManager(cfg.DifyBaseURL, agent_apikeys, cfg.AIAgentTimeout, repos)

	// API routes group
	api := router.Group("/api/v1")
	{
		// Setup authentication routes (includes user management)
		SetupAuthRoutes(api, cfg, googleClientID, googleClientSecret, logger)

		protected := api.Group("")
		protected.Use(middleware.AuthMiddleware(cfg.AccessTokenPublicKey))

		// Setup learning-related routes
		SetupLearningRoutes(protected, logger, ai_agents, repos)

		// Setup personalize routes
		SetupPersonalizeRoutes(protected, logger, ai_agents, repos)

		// Setup static profile routes
		SetupProfileRoutes(protected, logger, ai_agents, repos)

		SetupUploadRoutes(protected)

		// Setup Alipay payment routes
		err := SetupAlipayRoutes(router, api, cfg, db, logger)
		if err != nil {
			logger.Error("Failed to setup Alipay routes: %v", err)
		}

		// Setup WeChat payment routes
		err = SetupWeChatRoutes(router, api, cfg, db, logger)
		if err != nil {
			logger.Error("Failed to setup WeChat routes: %v", err)
		}
	}
}
