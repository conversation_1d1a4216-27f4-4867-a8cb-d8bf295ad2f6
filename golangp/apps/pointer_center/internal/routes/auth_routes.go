/*
 * @Description: Authentication routes for Pointer Center
 * @Author: AI Assistant
 * @Date: 2025-07-16
 */
package routes

import (
	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/internal/config"
	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/internal/handlers"
	"github.com/Arxtect/CoreXWorkSpace/golangp/common/database/mongodb"
	"github.com/Arxtect/CoreXWorkSpace/golangp/common/database/postgres"
	"github.com/Arxtect/CoreXWorkSpace/golangp/common/gin_core/middleware"
	"github.com/Arxtect/CoreXWorkSpace/golangp/common/logging"

	"github.com/gin-gonic/gin"
)

// SetupAuthRoutes configures authentication-related routes
func SetupAuthRoutes(api *gin.RouterGroup, cfg *config.Config, googleClientID string, googleClientSecret string, logger *logging.Logger) {
	// Initialize auth handler
	authHandler := handlers.NewAuthHandler(postgres.DB, mongodb.GetDB(), googleClientID, googleClientSecret)

	auth := api.Group("/auth")
	{
		// Public routes (no authentication required)
		auth.POST("/register", authHandler.HandleRegister)
		auth.POST("/login", authHandler.HandleLogin)

		// Protected routes (authentication required)
		protected := auth.Group("")
		protected.Use(middleware.AuthMiddleware(cfg.AccessTokenPublicKey))
		{
			// Authentication management
			protected.POST("/logout", authHandler.HandleLogout)
			protected.GET("/refresh", authHandler.RefreshToken)

			// User profile management
			protected.GET("/me", authHandler.GetCurrentUser)
			protected.PUT("/profile", authHandler.UpdateProfile)

			// Password management
			protected.PUT("/password", authHandler.ChangePassword)
		}
	}
}
