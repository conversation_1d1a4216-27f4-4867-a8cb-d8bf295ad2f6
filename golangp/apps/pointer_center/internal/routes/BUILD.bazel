load("@rules_go//go:def.bzl", "go_library")

go_library(
    name = "routes",
    srcs = glob(["**/*.go"]),
    importpath = "github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/internal/routes",
    visibility = ["//visibility:public"],
    deps = [
        "//golangp/apps/pointer_center/internal/config",
        "//golangp/apps/pointer_center/internal/handlers",
        "//golangp/apps/pointer_center/internal/middleware",
        "//golangp/apps/pointer_center/internal/repositories",
        "//golangp/apps/pointer_center/internal/services",
        "//golangp/apps/pointer_center/pkg/ai_agents",
        "//golangp/common/database/mongodb",
        "//golangp/common/database/postgres",
        "//golangp/common/gin_core/middleware",
        "//golangp/common/logging:logger",
        "@com_github_gin_gonic_gin//:gin",
        "@io_gorm_gorm//:gorm",
    ],
)
