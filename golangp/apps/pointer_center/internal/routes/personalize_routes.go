package routes

import (
	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/internal/handlers"
	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/internal/repositories"
	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/internal/services"
	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/pkg/ai_agents"
	"github.com/Arxtect/CoreXWorkSpace/golangp/common/logging"
	"github.com/gin-gonic/gin"
)

func SetupPersonalizeRoutes(api *gin.RouterGroup, logger *logging.Logger, ai *ai_agents.Manager, repos *repositories.Repositories) {
    // build service using the shared dify client from manager
    svc := services.NewPersonalizeService(ai, repos, ai.DifyClient())
    h := handlers.NewPersonalizeHandler(svc)
    h.RegisterRoutes(api)
    logger.Info("✅ Personalize routes configured successfully")
}
