/*
 * @Description: User lesson progress routes
 * @Author: <PERSON>
 * @Date: 2025-08-17 10:45:57
 */
package routes

import (
	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/internal/handlers"
	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/internal/services"
	"github.com/Arxtect/CoreXWorkSpace/golangp/common/logging"
	"github.com/gin-gonic/gin"
)

// SetupProgressRoutes configures all user lesson progress related routes
func SetupProgressRoutes(api *gin.RouterGroup, services *services.Services) {

	// Initialize progress handler
	progressHandler := handlers.NewUserLessonProgressHandler(services.UserLessonProgress)

	// User lesson progress routes
	progressGroup := api.Group("/user-lesson-progress")
	{
		// ContentFlow progress management
		progressGroup.POST("/content-flow/upsert", progressHandler.UpsertCompletedContentFlow) // 统一的添加/更新接口
		progressGroup.POST("/content-flow/remove", progressHandler.RemoveCompletedContentFlow)

		// Get progress information (unified routes that auto-detect path_id vs node_id)
		progressGroup.GET("/all", progressHandler.GetAllProgress)                                                            // Get all progress across all paths and nodes
		progressGroup.GET("/:node_id/all", progressHandler.GetAllProgressByNodeOrPath)                                       // Get all progress for path or node (auto-detect)
		progressGroup.GET("/:node_id/lesson/:lesson_id", progressHandler.GetProgressByNodeOrPath)                            // Get single lesson progress by path or node (auto-detect)
		progressGroup.GET("/:node_id/lesson/:lesson_id/content-flows", progressHandler.GetCompletedContentFlowsByNodeOrPath) // Get completed content flows by path or node (auto-detect)
	}

	logging.Info("✅ User lesson progress routes configured successfully")
}
