/*
 * @Description:
 * @Author: Devin
 * @Date: 2025-08-15 10:45:57
 */
package routes

import (
	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/internal/handlers"
	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/internal/repositories"
	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/internal/services"
	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/pkg/ai_agents"
	"github.com/Arxtect/CoreXWorkSpace/golangp/common/logging"

	"github.com/gin-gonic/gin"
)

// SetupLearningRoutes configures learning-related routes
func SetupLearningRoutes(api *gin.RouterGroup, logger *logging.Logger, ai_agents *ai_agents.Manager, repos *repositories.Repositories) {
	// Initialize services
	services := services.NewServices(repos, ai_agents)

	// Initialize handlers
	pathHandler := handlers.NewLearningPathHandler(services.LearningPath)
	nodeHander := handlers.NewLearningNodeHandler(services.LearningNode)
	lessonHander := handlers.NewLearningLessonHandler(services.LearningLesson)
	goalHandler := handlers.NewGoalPlanningHandler(services.GoalPlanning)

	// Learning lessons routes (MongoDB)
	lessonHander.RegisterRoutes(api)

	// Learning paths routes (PostgreSQL)
	pathHandler.RegisterRoutes(api)

	// Learning nodes routes (PostgreSQL)
	nodeHander.RegisterRoutes(api)

	// Goal planning routes
	goalHandler.RegisterRoutes(api)

	// User lesson progress routes (moved to separate file)
	SetupProgressRoutes(api, services)

	logger.Info("✅ Learning routes configured successfully")
}
