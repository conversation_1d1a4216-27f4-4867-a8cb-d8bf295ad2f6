package services

import (
	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/internal/repositories"
	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/pkg/ai_agents"
	"github.com/google/uuid"
)

// Services contains all business logic services
type Services struct {
	LearningPath       *LearningPathService
	LearningNode       *LearningNodeService
	LearningLesson     *LearningLessonService
	UserLessonProgress *UserLessonProgressService
	StaticProfile      *StaticProfileService
	TechCompetency     *TechCompetencyService
	GoalPlanning       *GoalPlanningService
}

var AI_ID = uuid.MustParse("00000000-0000-0000-0000-000000000000")

// NewServices creates a new Services instance with all initialized services
func NewServices(repos *repositories.Repositories, aiManager *ai_agents.Manager) *Services {
	return &Services{
		LearningPath:       NewLearningPathService(repos, aiManager),
		LearningNode:       NewLearningNodeService(repos, aiManager),
		LearningLesson:     NewLearningLessonService(repos, aiManager),
		UserLessonProgress: NewUserLessonProgressService(repos),
		StaticProfile:      NewStaticProfileService(repos),
		TechCompetency:     NewTechCompetencyService(repos, aiManager),
		GoalPlanning:       NewGoalPlanningService(repos, aiManager),
	}
}
