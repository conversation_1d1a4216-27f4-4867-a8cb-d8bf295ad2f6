package services

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/internal/models"
	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/internal/repositories"
	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/pkg/ai_agents"
	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/pkg/utils"
	"github.com/Arxtect/CoreXWorkSpace/golangp/common/logging"

	"github.com/google/uuid"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// LearningNodeService handles business logic for learning nodes
type LearningNodeService struct {
	repos     *repositories.Repositories
	aiManager *ai_agents.Manager
}

// NewLearningNodeService creates a new learning node service
func NewLearningNodeService(repos *repositories.Repositories, aiManager *ai_agents.Manager) *LearningNodeService {
	return &LearningNodeService{
		repos:     repos,
		aiManager: aiManager,
	}
}

// CreateLearningNode creates a new learning node
func (s *LearningNodeService) CreateLearningNode(ctx context.Context, req *CreateLearningNodeRequest) (*models.LearningNode, error) {
	// Validate request
	if err := s.validateCreateNodeRequest(req); err != nil {
		return nil, fmt.Errorf("validation failed: %w", err)
	}
	// Create learning node model
	node := &models.LearningNode{
		ID:               uuid.New(),
		Title:            req.Title,
		Description:      req.Description,
		EstimatedTimes:   req.EstimatedTimes,
		Difficulty:       models.DifficultyLevel(req.Difficulty),
		Price:            req.Price,
		CertificatePrice: req.CertificatePrice,
		CoverImage:       req.CoverImage,
		Skills:           req.Skills,
		Tags:             req.Tags,
		LearnerTags:      req.LearnerTags,
		WhatYouWillLearn: req.WhatYouWillLearn,
		Prerequisites:    req.Prerequisites,
		Status:           models.NodeStatusDraft,
		CreatedBy:        req.CreatedBy,
		CreatedAt:        time.Now(),
		UpdatedAt:        time.Now(),
	}

	// Initialize empty slices if nil
	if node.Skills == nil {
		node.Skills = []string{}
	}
	if node.Tags == nil {
		node.Tags = []string{}
	}
	if node.LearnerTags == nil {
		node.LearnerTags = []string{}
	}
	if node.WhatYouWillLearn == nil {
		node.WhatYouWillLearn = []string{}
	}
	// Save to database
	if err := s.repos.LearningNode.Create(ctx, node); err != nil {
		logging.Error("Failed to create learning node: node_id=%s, error=%v", node.ID, err)
		return nil, fmt.Errorf("failed to create learning node: %w", err)
	}
	logging.Info("Learning node created successfully: node_id=%s, title=%s", node.ID, node.Title)
	return node, nil
}

// UpdateLearningNode updates an existing learning node
func (s *LearningNodeService) UpdateLearningNode(ctx context.Context, nodeID uuid.UUID, req *UpdateLearningNodeRequest) (*models.LearningNode, error) {
	// Get existing node
	existingNode, err := s.repos.LearningNode.GetByID(ctx, nodeID)
	if err != nil {
		return nil, fmt.Errorf("failed to get learning node: %w", err)
	}
	// Update fields
	if req.Title != nil {
		existingNode.Title = *req.Title
	}
	if req.Description != nil {
		existingNode.Description = *req.Description
	}
	if req.EstimatedTimes != nil {
		existingNode.EstimatedTimes = *req.EstimatedTimes
	}
	if req.Difficulty != nil {
		existingNode.Difficulty = models.DifficultyLevel(*req.Difficulty)
	}
	if req.Prerequisites != nil {
		existingNode.Prerequisites = req.Prerequisites
	}
	if req.Price != nil {
		existingNode.Price = req.Price
	}
	if req.CertificatePrice != nil {
		existingNode.CertificatePrice = req.CertificatePrice
	}
	if req.Skills != nil {
		existingNode.Skills = req.Skills
	}
	if req.Tags != nil {
		existingNode.Tags = req.Tags
	}
	if req.LearnerTags != nil {
		existingNode.LearnerTags = req.LearnerTags
	}
	if req.WhatYouWillLearn != nil {
		existingNode.WhatYouWillLearn = req.WhatYouWillLearn
	}
	if req.UpdatedBy != nil {
		existingNode.UpdatedBy = *req.UpdatedBy
	}
	existingNode.UpdatedAt = time.Now()
	// Save updates
	if err := s.repos.LearningNode.Update(ctx, existingNode); err != nil {
		logging.Error("Failed to update learning node: node_id=%s, error=%v", nodeID, err)
		return nil, fmt.Errorf("failed to update learning node: %w", err)
	}
	logging.Info("Learning node updated successfully: node_id=%s", nodeID)
	return existingNode, nil
}

// DeleteLearningNode soft deletes a learning node
func (s *LearningNodeService) DeleteLearningNode(ctx context.Context, nodeID uuid.UUID) error {
	// Check if node exists
	_, err := s.repos.LearningNode.GetByID(ctx, nodeID)
	if err != nil {
		return fmt.Errorf("learning node not found: %w", err)
	}
	// Use regular delete since repository doesn't have SoftDelete method
	if err := s.repos.LearningNode.Delete(ctx, nodeID); err != nil {
		logging.Error("Failed to delete learning node: node_id=%s, error=%v", nodeID, err)
		return fmt.Errorf("failed to delete learning node: %w", err)
	}
	logging.Info("Learning node deleted successfully: node_id=%s", nodeID)
	return nil
}

// GetLearningNode retrieves a learning node by ID
func (s *LearningNodeService) GetLearningNode(ctx context.Context, nodeID uuid.UUID) (*models.LearningNode, error) {
	node, err := s.repos.LearningNode.GetByID(ctx, nodeID)
	if err != nil {
		return nil, fmt.Errorf("failed to get learning node: %w", err)
	}
	return node, nil
}

// GetLearningNodes retrieves learning nodes with pagination
func (s *LearningNodeService) GetLearningNodes(ctx context.Context, req *GetLearningNodesRequest) ([]*models.LearningNode, int64, error) {
	// Set default pagination
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	// Calculate offset
	offset := (req.Page - 1) * req.PageSize
	// Normalize search mode
	mode := strings.ToLower(strings.TrimSpace(req.SearchMode))
	if mode != "exact" {
		mode = "fuzzy"
	}

	hasFilter := (len(req.Tags) > 0) || (len(req.Skills) > 0) || (len(req.LearnerTags) > 0)
	hasQuery := strings.TrimSpace(req.Query) != ""

	switch {
	case hasFilter && hasQuery:
		nodes, err := s.repos.LearningNode.FilterAndSearch(ctx, req.Tags, req.Skills, req.LearnerTags, req.Query, mode, req.PageSize, offset)
		if err != nil {
			return nil, 0, fmt.Errorf("failed to get learning nodes: %w", err)
		}
		total, err := s.repos.LearningNode.CountFilterAndSearch(ctx, req.Tags, req.Skills, req.LearnerTags, req.Query, mode)
		if err != nil {
			return nil, 0, fmt.Errorf("failed to get total count: %w", err)
		}
		return nodes, total, nil
	case hasFilter:
		nodes, err := s.repos.LearningNode.Filter(ctx, req.Tags, req.Skills, req.LearnerTags, req.PageSize, offset)
		if err != nil {
			return nil, 0, fmt.Errorf("failed to get learning nodes: %w", err)
		}
		total, err := s.repos.LearningNode.CountFilter(ctx, req.Tags, req.Skills, req.LearnerTags)
		if err != nil {
			return nil, 0, fmt.Errorf("failed to get total count: %w", err)
		}
		return nodes, total, nil
	case hasQuery:
		nodes, err := s.repos.LearningNode.SearchWithMode(ctx, req.Query, mode, req.PageSize, offset)
		if err != nil {
			return nil, 0, fmt.Errorf("failed to get learning nodes: %w", err)
		}
		total, err := s.repos.LearningNode.CountSearch(ctx, req.Query, mode)
		if err != nil {
			return nil, 0, fmt.Errorf("failed to get total count: %w", err)
		}
		return nodes, total, nil
	default:
		nodes, err := s.repos.LearningNode.List(ctx, req.PageSize, offset)
		if err != nil {
			return nil, 0, fmt.Errorf("failed to get learning nodes: %w", err)
		}
		total, err := s.repos.LearningNode.Count(ctx)
		if err != nil {
			return nil, 0, fmt.Errorf("failed to get total count: %w", err)
		}
		return nodes, total, nil
	}

	// unreachable
}

// UpdateNodeStatus updates the status of a learning node
func (s *LearningNodeService) UpdateNodeStatus(ctx context.Context, nodeID uuid.UUID, status models.NodeStatus) error {
	// Validate status
	if !s.isValidNodeStatus(status) {
		return fmt.Errorf("invalid status: %s", status)
	}
	// Get existing node
	node, err := s.repos.LearningNode.GetByID(ctx, nodeID)
	if err != nil {
		return fmt.Errorf("failed to get learning node: %w", err)
	}
	// Update status
	node.Status = status
	node.UpdatedAt = time.Now()
	if err := s.repos.LearningNode.Update(ctx, node); err != nil {
		logging.Error("Failed to update node status: node_id=%s, status=%s, error=%v", nodeID, status, err)
		return fmt.Errorf("failed to update node status: %w", err)
	}
	logging.Info("Node status updated: node_id=%s, status=%s", nodeID, status)
	return nil
}

// AddNodeToPath adds a learning node to a learning path
func (s *LearningNodeService) AddNodeToPath(ctx context.Context, req *AddNodeToPathRequest) (*models.LearningPathNode, error) {
	// Validate request
	if err := s.validateAddNodeToPathRequest(req); err != nil {
		return nil, fmt.Errorf("validation failed: %w", err)
	}
	// Check if association already exists
	exists, err := s.repos.LearningPathNode.ExistsByPathAndNode(ctx, req.PathID, req.NodeID)
	if err != nil {
		return nil, fmt.Errorf("failed to check node-path association: %w", err)
	}
	if exists {
		return nil, fmt.Errorf("node already exists in this path")
	}
	// Create path-node association
	pathNode := &models.LearningPathNode{
		ID:             uuid.New(),
		LearningPathID: req.PathID,
		NodeID:         req.NodeID,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}
	if err := s.repos.LearningPathNode.Create(ctx, pathNode); err != nil {
		logging.Error("Failed to add node to path: path_id=%s, node_id=%s, error=%v", req.PathID, req.NodeID, err)
		return nil, fmt.Errorf("failed to add node to path: %w", err)
	}
	logging.Info("Node added to path successfully: path_id=%s, node_id=%s", req.PathID, req.NodeID)
	return pathNode, nil
}

// RemoveNodeFromPath removes a learning node from a learning path
func (s *LearningNodeService) RemoveNodeFromPath(ctx context.Context, pathID, nodeID uuid.UUID) error {
	// Check if association exists
	exists, err := s.repos.LearningPathNode.ExistsByPathAndNode(ctx, pathID, nodeID)
	if err != nil {
		return fmt.Errorf("failed to check node-path association: %w", err)
	}
	if !exists {
		return fmt.Errorf("node not found in this path")
	}
	// Get all path-node associations and delete them
	pathNodes, err := s.repos.LearningPathNode.GetByPathID(ctx, pathID)
	if err != nil {
		return fmt.Errorf("failed to get path nodes: %w", err)
	}
	for _, pathNode := range pathNodes {
		if pathNode.NodeID == nodeID {
			if err := s.repos.LearningPathNode.Delete(ctx, pathNode.ID); err != nil {
				logging.Error("Failed to remove node from path: path_id=%s, node_id=%s, error=%v", pathID, nodeID, err)
				return fmt.Errorf("failed to remove node from path: %w", err)
			}
			break
		}
	}
	logging.Info("Node removed from path successfully: path_id=%s, node_id=%s", pathID, nodeID)
	return nil
}

// GetPathNodes retrieves all nodes in a learning path
func (s *LearningNodeService) GetPathNodes(ctx context.Context, pathID uuid.UUID) ([]*models.LearningNode, error) {
	// Optimized: return only the node entities for the path
	nodes, err := s.repos.LearningPathNode.GetNodesForPath(ctx, pathID)
	if err != nil {
		return nil, fmt.Errorf("failed to get nodes for path: %w", err)
	}
	return nodes, nil
}

// -----------------------------------------------------------------------------
// Outline -> Lessons generation pipeline
// -----------------------------------------------------------------------------

// OutlineLesson represents a single lesson item from the outline agent
type OutlineLesson struct {
	Title          string `json:"title"`
	Description    string `json:"description"`
	EstimatedTimes int    `json:"estimated_times"`
	Difficulty     int    `json:"difficulty"`
	Type           string `json:"type"`
}

// CourseOutline represents the outline for a node
type CourseOutline struct {
	NodeID  uuid.UUID       `json:"node_id"`
	Title   string          `json:"title"`
	Lessons []OutlineLesson `json:"lessons"`
}

// GenerateNodeAndLessonsRequest represents request to create node and immediate lessons
type GenerateNodeAndLessonsRequest struct {
	NodeDescription string          `json:"node_description" validate:"required"`
	UserID          uuid.UUID       `json:"user_id"`
	Lessons         []OutlineLesson `json:"lessons,omitempty"`
}

// GenerateNodeAndLessonsResponse wraps the created node and generated lessons
// GenerateNodeAndLessonsResult returns minimal info only (no heavy content)
type GenerateNodeAndLessonsResult struct {
	Success          bool      `json:"success"`
	NodeID           uuid.UUID `json:"node_id"`
	GeneratedLessons int       `json:"generated_lessons"`
}

// parseOutlineLessonsFromAIData extracts outline lessons from AI response data
func parseOutlineLessonsFromAIData(data map[string]interface{}) []OutlineLesson {
	if data == nil {
		return nil
	}
	// Unwrap Data["answer"] first
	if rawAns, ok := data["answer"]; ok {
		switch v := rawAns.(type) {
		case map[string]interface{}:
			data = v
		case string:
			// try object first
			var m map[string]interface{}
			if err := json.Unmarshal([]byte(v), &m); err == nil {
				data = m
			} else {
				var arr []interface{}
				if err2 := json.Unmarshal([]byte(v), &arr); err2 == nil {
					return parseOutlineArray(arr)
				}
			}
		case []interface{}:
			return parseOutlineArray(v)
		}
	}
	// Try data["lessons"]
	if raw, ok := data["lessons"]; ok {
		switch v := raw.(type) {
		case []interface{}:
			return parseOutlineArray(v)
		case string:
			// try to unmarshal stringified JSON array
			var tmp []interface{}
			if err := json.Unmarshal([]byte(v), &tmp); err == nil {
				return parseOutlineArray(tmp)
			}
		}
	}
	// Try data["outline"].lessons or outline as array
	if raw, ok := data["outline"]; ok {
		switch v := raw.(type) {
		case map[string]interface{}:
			if arr, ok := v["lessons"].([]interface{}); ok {
				return parseOutlineArray(arr)
			}
			if s, ok := v["lessons"].(string); ok {
				var tmp []interface{}
				if err := json.Unmarshal([]byte(s), &tmp); err == nil {
					return parseOutlineArray(tmp)
				}
			}
		case []interface{}:
			return parseOutlineArray(v)
		case string:
			// outline may itself be a JSON string
			// it could be an array or an object with lessons
			var tmpArr []interface{}
			if err := json.Unmarshal([]byte(v), &tmpArr); err == nil {
				return parseOutlineArray(tmpArr)
			}
			var tmpObj map[string]interface{}
			if err := json.Unmarshal([]byte(v), &tmpObj); err == nil {
				if arr, ok := tmpObj["lessons"].([]interface{}); ok {
					return parseOutlineArray(arr)
				}
			}
		}
	}
	return nil
}

func parseOutlineArray(arr []interface{}) []OutlineLesson {
	lessons := make([]OutlineLesson, 0, len(arr))
	for _, it := range arr {
		if m, ok := it.(map[string]interface{}); ok {
			ol := OutlineLesson{}
			// title variations
			if v, ok := m["title"].(string); ok {
				ol.Title = v
			}
			if v, ok := m["lessonTitle"].(string); ok && ol.Title == "" {
				ol.Title = v
			}
			// description/content variations
			if v, ok := m["description"].(string); ok {
				ol.Description = v
			}
			if v, ok := m["lessonContent"].(string); ok && ol.Description == "" {
				ol.Description = v
			}
			// estimated times variations
			if v, ok := m["estimated_times"].(float64); ok {
				ol.EstimatedTimes = int(v)
			}
			if v, ok := m["estimated_minutes"].(float64); ok && ol.EstimatedTimes == 0 { // backward compatibility
				ol.EstimatedTimes = int(v * 60)
			}
			if v, ok := m["EstimatedTimes"].(float64); ok && ol.EstimatedTimes == 0 {
				ol.EstimatedTimes = int(v)
			}
			if v, ok := m["estimated_times"].(string); ok && ol.EstimatedTimes == 0 {
				if iv, err := strconv.Atoi(v); err == nil {
					ol.EstimatedTimes = iv
				}
			}
			if v, ok := m["estimated_minutes"].(string); ok && ol.EstimatedTimes == 0 { // backward compatibility
				if iv, err := strconv.Atoi(v); err == nil {
					ol.EstimatedTimes = iv * 60
				}
			}
			if v, ok := m["EstimatedTimes"].(string); ok && ol.EstimatedTimes == 0 {
				if iv, err := strconv.Atoi(v); err == nil {
					ol.EstimatedTimes = iv
				}
			}
			// difficulty variations
			if v, ok := m["difficulty"].(float64); ok {
				ol.Difficulty = int(v)
			}
			if v, ok := m["difficulty"].(string); ok && ol.Difficulty == 0 {
				if iv, err := strconv.Atoi(v); err == nil {
					ol.Difficulty = iv
				}
			}
			// type variations
			if v, ok := m["type"].(string); ok {
				ol.Type = v
			}
			if v, ok := m["lessonType"].(string); ok && ol.Type == "" {
				ol.Type = v
			}
			lessons = append(lessons, ol)
		}
	}
	return lessons
}

// extractAnswerMap unwraps the AI response when the actual payload is inside Data["answer"].
// It tolerates the answer being a map, a JSON string (map or array), or a direct array
// by normalizing arrays into a map with key "lessons".
func extractAnswerMap(data map[string]interface{}) map[string]interface{} {
	if data == nil {
		return map[string]interface{}{}
	}
	if ans, ok := data["answer"]; ok {
		switch v := ans.(type) {
		case map[string]interface{}:
			return v
		case string:
			// try object first
			var m map[string]interface{}
			if err := json.Unmarshal([]byte(v), &m); err == nil {
				return m
			}
			var arr []interface{}
			if err := json.Unmarshal([]byte(v), &arr); err == nil {
				return map[string]interface{}{"lessons": arr}
			}
			return map[string]interface{}{}
		case []interface{}:
			return map[string]interface{}{"lessons": v}
		default:
			return map[string]interface{}{}
		}
	}
	return data
}

// Note: Outline generation is covered by GenerateLearningNodeWithAI and downstream processing.

// GenerateCourseFromNodeRequest triggers course generation from an existing node
type GenerateCourseFromNodeRequest struct {
	NodeID uuid.UUID `json:"node_id" validate:"required"`
	UserID uuid.UUID `json:"user_id"`
}

// GenerateCourseFromNodeResponse contains outline id and generated lessons
// GenerateCourseFromNodeResponse kept for backward-compatibility (not used)
// Using GenerateNodeAndLessonsResult as the unified minimal response structure.
type GenerateCourseFromNodeResponse = GenerateNodeAndLessonsResult

// GenerateCourseFromNode reads node description, asks AI for outline, saves outline to MongoDB, and generates lessons
func (s *LearningNodeService) GenerateCourseFromNode(ctx context.Context, req *GenerateCourseFromNodeRequest) (*GenerateNodeAndLessonsResult, error) {
	if req == nil || req.NodeID == uuid.Nil {
		return nil, fmt.Errorf("node_id is required")
	}
	node, err := s.repos.LearningNode.GetByID(ctx, req.NodeID)
	if err != nil {
		return nil, fmt.Errorf("failed to get learning node: %w", err)
	}
	if node == nil {
		return nil, fmt.Errorf("learning node not found")
	}

	// 1) 将 node 状态设为 generating（立即）
	if node.Status != models.NodeStatusGenerating {
		node.Status = models.NodeStatusGenerating
		node.UpdatedAt = time.Now()
		if uerr := s.repos.LearningNode.Update(ctx, node); uerr != nil {
			logging.Warning("set node generating failed (continue): node=%s err=%v", node.ID, uerr)
		}
	}

	// 2) 后台异步：调用 AI 生成大纲 -> 保存大纲 -> 预创建占位 lessons 并按顺序建立关联 -> 置 node active -> 异步逐课生成并激活
	go func(nodeID, userID uuid.UUID) {
		bctx := context.Background()
		defer func() {
			if r := recover(); r != nil {
				logging.Error("panic in GenerateCourseFromNode async: %v", r)
			}
		}()

		n, err := s.repos.LearningNode.GetByID(bctx, nodeID)
		if err != nil || n == nil {
			return
		}

		// Build AI inputs
		studentProfile := "{}"
		if userID != uuid.Nil {
			if profileData, perr := s.aiManager.Tools().GetStudentProfile(bctx, userID); perr == nil {
				if b, mErr := json.Marshal(profileData); mErr == nil {
					studentProfile = string(b)
				}
			}
		}
		inputs := map[string]interface{}{
			"node": map[string]interface{}{
				"description": n.Description,
				"title":       n.Title,
			},
			"student_profile": studentProfile,
		}

		aiResp, err := s.aiManager.GenerateCourse(bctx, inputs)
		if err != nil || aiResp == nil || !aiResp.Success {
			logging.Error("AI outline generation failed: node=%s err=%v aiErr=%v", nodeID, err, func() interface{} {
				if aiResp != nil {
					return aiResp.Error
				}
				return nil
			}())
			// 标记节点失败
			if nn, e := s.repos.LearningNode.GetByID(bctx, nodeID); e == nil && nn != nil {
				nn.Status = models.NodeStatusFailed
				nn.UpdatedAt = time.Now()
				_ = s.repos.LearningNode.Update(bctx, nn)
			}
			return
		}

		// 解析并保存大纲
		ansMap := extractAnswerMap(aiResp.Data)
		ols := parseOutlineLessonsFromAIData(aiResp.Data)
		outlineDoc := &models.CourseOutlineDoc{NodeID: nodeID.String(), Title: n.Title, Lessons: make([]models.OutlineLessonDoc, 0, len(ols)), Raw: aiResp.Data, CreatedAt: time.Now()}
		outlineDoc.CreatedBy = AI_ID.String()
		if desc, ok := ansMap["description"].(string); ok {
			outlineDoc.Description = desc
		}
		for _, l := range ols {
			outlineDoc.Lessons = append(outlineDoc.Lessons, models.OutlineLessonDoc{Title: l.Title, Description: l.Description, EstimatedTimes: l.EstimatedTimes, Difficulty: l.Difficulty, Type: l.Type})
		}
		if err := s.repos.CourseOutline.Create(bctx, outlineDoc); err != nil {
			logging.Error("save course outline failed: node=%s err=%v", nodeID, err)
		}

		// 预创建占位 lessons（状态=generating），并建立 node-lesson 关联（顺序按大纲）
		lastKey := ""
		if assocs, e := s.repos.NodeLesson.GetByNodeID(bctx, nodeID); e == nil {
			for i := len(assocs) - 1; i >= 0; i-- {
				if k := assocs[i].OrderKey; k != "" {
					lastKey = k
					break
				}
			}
		}
		createdLessonIDs := make([]string, 0, len(ols))
		for _, ol := range ols {
			// create placeholder lesson
			lesson := &models.LearningLesson{
				ID:             primitive.NewObjectID(),
				Title:          ol.Title,
				Description:    ol.Description,
				Type:           models.LessonType(ol.Type),
				EstimatedTimes: ol.EstimatedTimes,
				Difficulty:     models.DifficultyLevel(ol.Difficulty),
				Status:         models.LessonStatusGenerating,
				CreatedBy:      AI_ID.String(),
				CreatedAt:      time.Now(),
				UpdatedAt:      time.Now(),
			}
			if err := s.repos.LearningLesson.Create(bctx, lesson); err != nil {
				logging.Warning("create placeholder lesson failed: node=%s title=%s err=%v", nodeID, ol.Title, err)
				continue
			}
			createdLessonIDs = append(createdLessonIDs, lesson.ID.Hex())

			// association with sparse order
			orderKey := utils.NextAfter(lastKey)
			nl := &models.NodeLesson{ID: uuid.New(), NodeID: nodeID, LessonID: lesson.ID.Hex(), OrderKey: orderKey, LessonTitle: lesson.Title, LessonDescription: lesson.Description, CreatedAt: time.Now(), UpdatedAt: time.Now()}
			if err := s.repos.NodeLesson.Create(bctx, nl); err != nil {
				logging.Warning("associate placeholder lesson failed: node=%s lesson=%s err=%v", nodeID, lesson.ID.Hex(), err)
			} else {
				lastKey = orderKey
				// 初始化用户在该课时上的进度（幂等）
				if userID != uuid.Nil {
					s.ensureUserLessonProgress(bctx, userID, nodeID, lesson.ID.Hex())
				}
			}
		}

		// 大纲就绪即可将节点置为 active
		if nn, e := s.repos.LearningNode.GetByID(bctx, nodeID); e == nil && nn != nil {
			nn.Status = models.NodeStatusActive
			nn.UpdatedAt = time.Now()
			_ = s.repos.LearningNode.Update(bctx, nn)
		}

		// 逐个 lesson 异步生成内容并激活（串行或可控并发，这里使用小并发）
		sem := make(chan struct{}, 5)
		var wg sync.WaitGroup
		userIDStr := ""
		if userID != uuid.Nil {
			userIDStr = userID.String()
		}
		studentProfileLocal := studentProfile // reuse

		// 本地校验重试次数（当 content_flow 为空时触发重试）
		validateMaxAttempts := 2
		for _, lessonID := range createdLessonIDs {
			wg.Add(1)
			sem <- struct{}{}
			lid := lessonID
			go func() {
				defer wg.Done()
				defer func() { <-sem }()
				// 加载占位 lesson
				lesson, err := s.repos.LearningLesson.GetByLessonID(bctx, lid)
				if err != nil || lesson == nil {
					return
				}

				// 带本地校验的循环：若解析后 content_flow 为空，则重试请求
				activated := false
				for attempt := 0; attempt < validateMaxAttempts && !activated; attempt++ {
					payload := map[string]interface{}{
						"lesson":          map[string]interface{}{"title": lesson.Title, "description": lesson.Description, "estimated_times": lesson.EstimatedTimes, "difficulty": int(lesson.Difficulty), "type": string(lesson.Type)},
						"student_profile": studentProfileLocal,
					}
					resp, err := s.aiManager.GenerateLesson(bctx, payload)
					if err != nil || resp == nil || !resp.Success {
						continue
					}

					// 解析 AI 响应
					ansMap := extractAnswerMap(resp.Data)
					if v, ok := ansMap["lesson_title"].(string); ok && strings.TrimSpace(v) != "" {
						lesson.Title = strings.TrimSpace(v)
					}
					if v, ok := ansMap["lesson_type"].(string); ok && strings.TrimSpace(v) != "" {
						lesson.Type = models.LessonType(strings.TrimSpace(v))
					}
					if v, ok := ansMap["estimated_times"]; ok {
						switch t := v.(type) {
						case float64:
							if t > 0 {
								lesson.EstimatedTimes = int(t)
							}
						case int:
							if t > 0 {
								lesson.EstimatedTimes = t
							}
						case json.Number:
							if n, e := t.Int64(); e == nil && n > 0 {
								lesson.EstimatedTimes = int(n)
							}
						case string:
							if n, e := strconv.Atoi(t); e == nil && n > 0 {
								lesson.EstimatedTimes = n
							}
						}
					}
					if arr, ok := ansMap["learning_objectives"].([]interface{}); ok {
						out := make([]string, 0, len(arr))
						for _, it := range arr {
							if s, ok := it.(string); ok && strings.TrimSpace(s) != "" {
								out = append(out, strings.TrimSpace(s))
							}
						}
						if len(out) > 0 {
							lesson.LearningObjectives = out
						}
					}
					if ssa, ok := ansMap["student_profile_association"].(string); ok {
						lesson.StudentProfileAssociation = ssa
					}
					if arr, ok := ansMap["common_misconceptions"].([]interface{}); ok {
						out := make([]string, 0, len(arr))
						for _, it := range arr {
							if s, ok := it.(string); ok && strings.TrimSpace(s) != "" {
								out = append(out, strings.TrimSpace(s))
							}
						}
						if len(out) > 0 {
							lesson.CommonMisconceptions = out
						}
					}
					if sExt, ok := ansMap["extension_idea"].(string); ok {
						lesson.ExtensionIdea = sExt
					}
					if sDesc, ok := ansMap["summary"].(string); ok && strings.TrimSpace(sDesc) != "" {
						lesson.Description = strings.TrimSpace(sDesc)
					}

					// content_flow（先收集，确保非空再落库）
					toMap := func(v interface{}) map[string]interface{} {
						if v == nil {
							return map[string]interface{}{}
						}
						if m, ok := v.(map[string]interface{}); ok {
							return m
						}
						b, e := json.Marshal(v)
						if e == nil {
							var m2 map[string]interface{}
							if json.Unmarshal(b, &m2) == nil && m2 != nil {
								return m2
							}
						}
						return map[string]interface{}{"value": v}
					}
					newFlow := make([]models.AtomicContent, 0)
					if raw, ok := ansMap["content_flow"].([]interface{}); ok {
						for _, item := range raw {
							m, ok := item.(map[string]interface{})
							if !ok {
								continue
							}
							typeStr, _ := m["type"].(string)
							typeStr = strings.TrimSpace(strings.ToLower(typeStr))
							data := toMap(m["data"])
							if typeStr == "text_explanation" {
								if _, has := data["formatting"]; !has {
									data["formatting"] = "markdown"
								}
							}
							var acType models.AtomicContentType
							switch typeStr {
							case "text_explanation":
								acType = models.AtomicTypeTextExplanation
							case "code_snippet":
								acType = models.AtomicTypeCodeSnippet
							case "diagram_description":
								acType = models.AtomicTypeDiagramDescription
							case "flowchart_description":
								acType = models.AtomicTypeFlowchartDescription
							case "interactive_quiz":
								qt, _ := data["quiz_type"].(string)
								qt = strings.TrimSpace(strings.ToLower(qt))
								if qt == "fill_in_blank" {
									acType = models.AtomicTypeFillInBlankQuiz
								} else {
									acType = models.AtomicTypeMultipleChoiceQuiz
								}
							case "multiple_choice_quiz":
								acType = models.AtomicTypeMultipleChoiceQuiz
							case "fill_in_blank_quiz":
								acType = models.AtomicTypeFillInBlankQuiz
							case "practice_exercise":
								acType = models.AtomicTypePracticeExercise
							case "math_formula":
								acType = models.AtomicTypeMathFormula
							default:
								continue
							}
							ac := models.AtomicContent{Type: acType, Data: data}
							newFlow = append(newFlow, ac)
						}
					}

					if len(newFlow) == 0 {
						// 解析成功但结构为空，进行下一次尝试
						continue
					}

					// 写回 lesson
					lesson.ContentFlow = newFlow
					lesson.Status = models.LessonStatusActive
					if userIDStr != "" {
						lesson.UpdatedBy = userIDStr
					}
					lesson.UpdatedAt = time.Now()
					if uerr := s.repos.LearningLesson.Update(bctx, lesson); uerr != nil {
						logging.Warning("update lesson after gen failed: lid=%s err=%v", lid, uerr)
					} else {
						activated = true
					}
				}

				if !activated {
					// 失败：标记 lesson 为 failed
					if l2, e := s.repos.LearningLesson.GetByLessonID(bctx, lid); e == nil && l2 != nil {
						l2.Status = models.LessonStatusFiled
						l2.UpdatedAt = time.Now()
						_ = s.repos.LearningLesson.Update(bctx, l2)
					}
				}
			}()
		}
		wg.Wait()
	}(node.ID, req.UserID)

	// 3) 立即返回成功（不等待 AI 完成）
	return &GenerateNodeAndLessonsResult{Success: true, NodeID: node.ID, GeneratedLessons: 0}, nil
}

// GenerateLessonsFromOutline creates concrete lessons via AI from an outline and assigns them to the node
func (s *LearningNodeService) GenerateLessonsFromOutline(ctx context.Context, nodeID uuid.UUID, outline *CourseOutline, userID uuid.UUID) ([]*models.LearningLesson, error) {
	if outline == nil {
		return nil, fmt.Errorf("outline is required")
	}
	// Profile
	studentProfile := "{}"
	userIDStr := ""
	if userID != uuid.Nil {
		userIDStr = userID.String()
		if profileData, perr := s.aiManager.Tools().GetStudentProfile(ctx, userID); perr == nil {
			if b, mErr := json.Marshal(profileData); mErr == nil {
				studentProfile = string(b)
			}
		}
	}

	// Concurrency setup
	total := len(outline.Lessons)
	results := make([]*models.LearningLesson, total)
	errs := make([]error, total)
	maxConcurrent := 6
	sem := make(chan struct{}, maxConcurrent)
	var wg sync.WaitGroup

	// Preload existing associations once for later positioning (we'll link after generation)
	existing, _ := s.repos.NodeLesson.GetByNodeID(ctx, nodeID)

	for idx, ol := range outline.Lessons {
		if ctx.Err() != nil {
			break
		}
		wg.Add(1)
		sem <- struct{}{}
		i := idx
		lessonOutline := ol
		go func() {
			defer wg.Done()
			defer func() { <-sem }()

			// Ask AI to generate a detailed lesson
			lessonPayload := map[string]interface{}{
				"title":           lessonOutline.Title,
				"description":     lessonOutline.Description,
				"estimated_times": lessonOutline.EstimatedTimes,
				"difficulty":      lessonOutline.Difficulty,
				"type":            lessonOutline.Type,
			}
			inputs := map[string]interface{}{
				"lesson":          lessonPayload,
				"student_profile": studentProfile,
				"course":          outline,
			}

			aiResp, err := s.aiManager.GenerateLesson(ctx, inputs)
			if err != nil || (aiResp != nil && !aiResp.Success) {
				logging.Info("AI lesson generation fallback: node=%s, idx=%d, err=%v, aiErr=%v", nodeID, i, err, func() interface{} {
					if aiResp != nil {
						return aiResp.Error
					}
					return nil
				}())
			}

			// Build lesson model (try to parse AI response first)
			lesson := models.NewLearningLesson(lessonOutline.Title, models.LessonType(lessonOutline.Type))
			lesson.Description = lessonOutline.Description
			if lessonOutline.EstimatedTimes > 0 {
				lesson.EstimatedTimes = lessonOutline.EstimatedTimes
			}
			if lessonOutline.Difficulty > 0 {
				lesson.Difficulty = models.DifficultyLevel(lessonOutline.Difficulty)
			}
			if userIDStr != "" {
				lesson.CreatedBy = userIDStr
			}

			if aiResp != nil && aiResp.Success && aiResp.Data["answer"] != nil {
				// Parse strict JSON from AI answer, map into lesson fields and content_flow
				ansMap := extractAnswerMap(aiResp.Data)
				// title override
				if v, ok := ansMap["lesson_title"].(string); ok && strings.TrimSpace(v) != "" {
					lesson.Title = strings.TrimSpace(v)
				}
				// type override if valid
				if v, ok := ansMap["lesson_type"].(string); ok {
					lt := models.LessonType(strings.TrimSpace(v))
					if lt.IsValid() {
						lesson.Type = lt
					}
				}
				// duration -> EstimatedTimes
				if v, ok := ansMap["estimated_times"]; ok {
					switch t := v.(type) {
					case float64:
						if t > 0 {
							lesson.EstimatedTimes = int(t)
						}
					case int:
						if t > 0 {
							lesson.EstimatedTimes = t
						}
					case json.Number:
						if n, err := t.Int64(); err == nil && n > 0 {
							lesson.EstimatedTimes = int(n)
						}
					case string:
						if n, err := strconv.Atoi(t); err == nil && n > 0 {
							lesson.EstimatedTimes = n
						}
					}
				}
				// learning_objectives
				if arr, ok := ansMap["learning_objectives"].([]interface{}); ok {
					lesson.LearningObjectives = make([]string, 0, len(arr))
					for _, it := range arr {
						if s, ok := it.(string); ok && strings.TrimSpace(s) != "" {
							lesson.LearningObjectives = append(lesson.LearningObjectives, strings.TrimSpace(s))
						}
					}
				}
				// student_profile_association
				if s, ok := ansMap["student_profile_association"].(string); ok {
					lesson.StudentProfileAssociation = s
				}
				// common_misconceptions
				if arr, ok := ansMap["common_misconceptions"].([]interface{}); ok {
					lesson.CommonMisconceptions = make([]string, 0, len(arr))
					for _, it := range arr {
						if s, ok := it.(string); ok && strings.TrimSpace(s) != "" {
							lesson.CommonMisconceptions = append(lesson.CommonMisconceptions, strings.TrimSpace(s))
						}
					}
				}
				// extension_idea
				if s, ok := ansMap["extension_idea"].(string); ok {
					lesson.ExtensionIdea = s
				}
				// summary -> use as description if provided
				if s, ok := ansMap["summary"].(string); ok && strings.TrimSpace(s) != "" {
					lesson.Description = strings.TrimSpace(s)
				}

				// Helper: ensure a plain map[string]interface{} for data
				toMap := func(v interface{}) map[string]interface{} {
					if v == nil {
						return map[string]interface{}{}
					}
					if m, ok := v.(map[string]interface{}); ok {
						return m
					}
					b, err := json.Marshal(v)
					if err == nil {
						var m2 map[string]interface{}
						if json.Unmarshal(b, &m2) == nil && m2 != nil {
							return m2
						}
					}
					return map[string]interface{}{"value": v}
				}

				// content_flow parsing with type normalization
				raw, ok := ansMap["content_flow"].([]interface{})
				if !ok || len(raw) == 0 {
					errs[i] = fmt.Errorf("content_flow is empty or invalid")
					return
				}
				for _, item := range raw {
					m, ok := item.(map[string]interface{})
					if !ok {
						continue
					}
					typeStr, _ := m["type"].(string)
					typeStr = strings.TrimSpace(strings.ToLower(typeStr))
					data := toMap(m["data"])
					// default formatting for text
					if typeStr == "text_explanation" {
						if _, has := data["formatting"]; !has {
							data["formatting"] = "markdown"
						}
					}
					var acType models.AtomicContentType
					switch typeStr {
					case "text_explanation":
						acType = models.AtomicTypeTextExplanation
					case "code_snippet":
						acType = models.AtomicTypeCodeSnippet
					case "diagram_description":
						acType = models.AtomicTypeDiagramDescription
					case "flowchart_description":
						acType = models.AtomicTypeFlowchartDescription
					case "interactive_quiz":
						qt, _ := data["quiz_type"].(string)
						qt = strings.TrimSpace(strings.ToLower(qt))
						if qt == "fill_in_blank" {
							acType = models.AtomicTypeFillInBlankQuiz
						} else {
							// default to multiple choice
							acType = models.AtomicTypeMultipleChoiceQuiz
						}
					case "multiple_choice_quiz":
						acType = models.AtomicTypeMultipleChoiceQuiz
					case "fill_in_blank_quiz":
						acType = models.AtomicTypeFillInBlankQuiz
					case "practice_exercise":
						acType = models.AtomicTypePracticeExercise
					case "math_formula":
						acType = models.AtomicTypeMathFormula
					default:
						continue
					}
					lesson.AddContentFlow(acType, data)
				}
				// Mark AI generated lessons as active
				lesson.Activate()
			} else {
				// Fallback: build a minimal content flow using outline info
				lesson.AddContentFlow(models.AtomicTypeTextExplanation, map[string]interface{}{
					"title":      lesson.Title,
					"body":       lesson.Description,
					"formatting": "markdown",
				})
				lesson.AddContentFlow(models.AtomicTypePracticeExercise, map[string]interface{}{
					"title":             "巩固练习",
					"difficulty":        "easy",
					"description":       "请用你自己的话总结本节课的要点，并给出一个简单的示例。",
					"starter_code":      "# 在此处写下你的总结或示例\n",
					"test_cases":        []map[string]interface{}{},
					"evaluation_method": "output_match",
				})
			}

			// Persist lesson (Mongo)
			if err := s.repos.LearningLesson.Create(ctx, lesson); err != nil {
				errs[i] = fmt.Errorf("failed to save lesson: %w", err)
				return
			}

			results[i] = lesson
		}()
	}

	wg.Wait()

	// Return first error if any
	for _, e := range errs {
		if e != nil {
			return nil, e
		}
	}

	// Compact results preserving outline order
	created := make([]*models.LearningLesson, 0, total)
	for _, l := range results {
		if l != nil {
			created = append(created, l)
		}
	}

	// Link lessons to node sequentially with increasing order_key based on outline order
	// Refresh last existing key
	lastKey := ""
	if len(existing) > 0 {
		// find last non-empty key from existing associations
		for i := len(existing) - 1; i >= 0; i-- {
			if k := existing[i].OrderKey; k != "" {
				lastKey = k
				break
			}
		}
	}
	for _, lesson := range created {
		ok := utils.NextAfter(lastKey)
		nl := &models.NodeLesson{
			ID: uuid.New(), NodeID: nodeID, LessonID: lesson.ID.Hex(), OrderKey: ok,
			LessonTitle: lesson.Title, LessonDescription: lesson.Description,
			CreatedAt: time.Now(), UpdatedAt: time.Now(),
		}
		if err := s.repos.NodeLesson.Create(ctx, nl); err != nil {
			return nil, fmt.Errorf("failed to assign lesson to node: %w", err)
		}
		lastKey = ok
		// 初始化用户在该课时上的进度（幂等）
		if userID != uuid.Nil {
			s.ensureUserLessonProgress(ctx, userID, nodeID, lesson.ID.Hex())
		}
	}
	return created, nil
}

// AssignLessonToNode assigns a lesson to a learning node
func (s *LearningNodeService) AssignLessonToNode(ctx context.Context, nodeID uuid.UUID, lessonID string, order int) (*models.NodeLesson, error) {
	// Validate lessonID format and existence in Mongo
	if _, err := primitive.ObjectIDFromHex(lessonID); err != nil {
		return nil, fmt.Errorf("invalid lesson_id")
	}
	existsInMongo, err := s.repos.LearningLesson.ExistsByLessonID(ctx, lessonID)
	if err != nil {
		return nil, fmt.Errorf("failed to verify lesson existence: %w", err)
	}
	if !existsInMongo {
		return nil, fmt.Errorf("learning lesson not found")
	}
	// Check if assignment already exists
	exists, err := s.repos.NodeLesson.ExistsByNodeAndLesson(ctx, nodeID, lessonID)
	if err != nil {
		return nil, fmt.Errorf("failed to check node-lesson assignment: %w", err)
	}
	if exists {
		return nil, fmt.Errorf("lesson already assigned to this node")
	}
	// Generate order_key by intended 1-based position (default: append)
	assocs, _ := s.repos.NodeLesson.GetByNodeID(ctx, nodeID)
	pos := order
	if pos <= 0 {
		pos = len(assocs) + 1
	}
	if pos > len(assocs)+1 {
		pos = len(assocs) + 1
	}

	// Compute neighbor keys
	var prevKey, nextKey string
	if pos-2 >= 0 && pos-2 < len(assocs) {
		prevKey = assocs[pos-2].OrderKey
	}
	if pos-1 >= 0 && pos-1 < len(assocs) {
		nextKey = assocs[pos-1].OrderKey
	}
	ok := utils.Between(prevKey, nextKey)

	// If between returned same as prev (no room), fallback to append after last non-empty key
	if ok == prevKey {
		prevKey = ""
		for i := len(assocs) - 1; i >= 0; i-- {
			if k := assocs[i].OrderKey; k != "" {
				prevKey = k
				break
			}
		}
		ok = utils.NextAfter(prevKey)
	}

	// Create node-lesson assignment
	// Try to fetch lesson to cache title/description; best-effort
	var lessonTitle, lessonDesc string
	if l, _ := s.repos.LearningLesson.GetByLessonID(ctx, lessonID); l != nil {
		lessonTitle = l.Title
		lessonDesc = l.Description
	}
	nodeLesson := &models.NodeLesson{
		ID:                uuid.New(),
		NodeID:            nodeID,
		LessonID:          lessonID,
		OrderKey:          ok,
		LessonTitle:       lessonTitle,
		LessonDescription: lessonDesc,
		CreatedAt:         time.Now(),
		UpdatedAt:         time.Now(),
	}
	if err := s.repos.NodeLesson.Create(ctx, nodeLesson); err != nil {
		logging.Error("Failed to assign lesson to node: node_id=%s, lesson_id=%s, error=%v", nodeID, lessonID, err)
		return nil, fmt.Errorf("failed to assign lesson to node: %w", err)
	}
	logging.Info("Lesson assigned to node successfully: node_id=%s, lesson_id=%s, order_key=%s", nodeID, lessonID, ok)
	return nodeLesson, nil
}

// ContentFlowBrief represents a simplified version of content flow without body content
type ContentFlowBrief struct {
	ID          string                   `json:"id"`
	Type        models.AtomicContentType `json:"type"`
	Order       int                      `json:"order"`
	Title       string                   `json:"title,omitempty"`
	Description string                   `json:"description,omitempty"`
}

// NodeLessonWithContent represents a node lesson with content flow
type NodeLessonWithContent struct {
	*models.NodeLesson                    // Embedded NodeLesson (association info)
	ContentFlow        []ContentFlowBrief `json:"content_flow,omitempty"` // Brief content flow only
}

// GetNodeLessons retrieves all lessons assigned to a node
func (s *LearningNodeService) GetNodeLessons(ctx context.Context, nodeID uuid.UUID) ([]*models.NodeLesson, error) {
	lessons, err := s.repos.NodeLesson.GetByNodeID(ctx, nodeID)
	if err != nil {
		return nil, fmt.Errorf("failed to get node lessons: %w", err)
	}
	return lessons, nil
}

// GetNodeLessonsWithContent retrieves all lessons assigned to a node with full content
func (s *LearningNodeService) GetNodeLessonsWithContent(ctx context.Context, nodeID uuid.UUID) ([]*NodeLessonWithContent, error) {
	// Get node lesson associations
	nodeLessons, err := s.repos.NodeLesson.GetByNodeID(ctx, nodeID)
	if err != nil {
		return nil, fmt.Errorf("failed to get node lessons: %w", err)
	}

	if len(nodeLessons) == 0 {
		return []*NodeLessonWithContent{}, nil
	}

	// Extract lesson IDs for batch query
	lessonIDs := make([]string, len(nodeLessons))
	for i, nodeLesson := range nodeLessons {
		lessonIDs[i] = nodeLesson.LessonID
	}

	// Batch fetch all lesson content from MongoDB
	start := time.Now()
	lessonsMap, err := s.repos.LearningLesson.GetByLessonIDs(ctx, lessonIDs)
	duration := time.Since(start)
	logging.Info("Batch query GetByLessonIDs took %v for %d lessons", duration, len(lessonIDs))
	if err != nil {
		return nil, fmt.Errorf("failed to batch get lesson content: %w", err)
	}

	// Build result maintaining the original order
	result := make([]*NodeLessonWithContent, 0, len(nodeLessons))
	for _, nodeLesson := range nodeLessons {
		lessonContent, exists := lessonsMap[nodeLesson.LessonID]
		if !exists {
			// Log error but continue with other lessons
			logging.Error("Lesson content not found for lesson_id=%s", nodeLesson.LessonID)
			// Include the node lesson without content
			result = append(result, &NodeLessonWithContent{
				NodeLesson:  nodeLesson,
				ContentFlow: []ContentFlowBrief{},
			})
			continue
		}

		// Create brief content flow directly
		contentFlowBrief := make([]ContentFlowBrief, 0, len(lessonContent.ContentFlow))
		for _, content := range lessonContent.ContentFlow {
			brief := ContentFlowBrief{
				ID:    content.ID,
				Type:  content.Type,
				Order: content.Order,
			}

			// Extract title and description from data if available
			if content.Data != nil {
				if title, ok := content.Data["title"].(string); ok {
					brief.Title = title
				}
				if description, ok := content.Data["description"].(string); ok {
					brief.Description = description
				}
			}

			contentFlowBrief = append(contentFlowBrief, brief)
		}

		// Combine node lesson association with content flow brief
		result = append(result, &NodeLessonWithContent{
			NodeLesson:  nodeLesson,
			ContentFlow: contentFlowBrief,
		})
	}

	return result, nil
}

// ensureUserLessonProgress creates a user lesson progress record if missing; idempotent and non-blocking on failure
func (s *LearningNodeService) ensureUserLessonProgress(ctx context.Context, userID uuid.UUID, nodeID uuid.UUID, lessonID string) {
	if userID == uuid.Nil || nodeID == uuid.Nil || strings.TrimSpace(lessonID) == "" {
		return
	}
	// Try to get, if not found then create minimal record
	if _, err := s.repos.UserLessonProgress.GetByUserPathNodeLesson(ctx, userID, nodeID, lessonID); err == nil {
		return
	}

	now := time.Now()
	rec := &models.UserLessonProgress{
		ID:             uuid.New(),
		UserID:         userID,
		LessonID:       lessonID,
		NodeID:         nodeID,
		Progress:       0,
		IsCompleted:    false,
		Score:          0,
		TimeSpent:      0,
		Attempts:       0,
		LastAccessedAt: &now,
		CreatedAt:      now,
		UpdatedAt:      now,
	}
	// initialize empty completed content flow ids
	if err := rec.SetCompletedContentFlowIDs([]string{}); err != nil {
		logging.Warning("ensureUserLessonProgress init JSON failed: user=%s node=%s lesson=%s err=%v", userID, nodeID, lessonID, err)
		return
	}
	if err := s.repos.UserLessonProgress.Create(ctx, rec); err != nil {
		// ignore create errors (e.g., duplicate) to keep flow non-blocking
		logging.Warning("ensureUserLessonProgress create failed (ignored): user=%s node=%s lesson=%s err=%v", userID, nodeID, lessonID, err)
	}
}

// RemoveLessonFromNode removes a specific lesson from a node (by association)
func (s *LearningNodeService) RemoveLessonFromNode(ctx context.Context, nodeID uuid.UUID, lessonID string) error {
	if lessonID == "" {
		return fmt.Errorf("lesson_id is required")
	}
	if _, err := primitive.ObjectIDFromHex(lessonID); err != nil {
		return fmt.Errorf("invalid lesson_id")
	}
	// Ensure the association exists
	exists, err := s.repos.NodeLesson.ExistsByNodeAndLesson(ctx, nodeID, lessonID)
	if err != nil {
		return fmt.Errorf("failed to check node-lesson association: %w", err)
	}
	if !exists {
		return fmt.Errorf("lesson not assigned to node")
	}

	// Load associations and delete matching ones (defensive against duplicates)
	assocs, err := s.repos.NodeLesson.GetByNodeID(ctx, nodeID)
	if err != nil {
		return fmt.Errorf("failed to load node lessons: %w", err)
	}
	removed := 0
	for _, nl := range assocs {
		if nl.LessonID == lessonID {
			if delErr := s.repos.NodeLesson.Delete(ctx, nl.ID); delErr != nil {
				logging.Error("Failed to remove lesson from node: node_id=%s, lesson_id=%s, error=%v", nodeID, lessonID, delErr)
				return fmt.Errorf("failed to remove lesson from node: %w", delErr)
			}
			removed++
		}
	}
	if removed == 0 {
		return fmt.Errorf("lesson not assigned to node")
	}
	logging.Info("Lesson removed from node: node_id=%s, lesson_id=%s, removed=%d", nodeID, lessonID, removed)
	return nil
}

// MoveLessonToPosition moves a lesson within a node to the specified 1-based position.
// If position <= 0, it will be set to 1; if position > len+1, it will be clamped to tail.
func (s *LearningNodeService) MoveLessonToPosition(ctx context.Context, nodeID uuid.UUID, lessonID string, position int) error {
	if lessonID == "" {
		return fmt.Errorf("lesson_id is required")
	}
	if _, err := primitive.ObjectIDFromHex(lessonID); err != nil {
		return fmt.Errorf("invalid lesson_id")
	}
	// Load current list
	assocs, err := s.repos.NodeLesson.GetByNodeID(ctx, nodeID)
	if err != nil {
		return fmt.Errorf("failed to load node lessons: %w", err)
	}
	// Find current index
	curIdx := -1
	for i, nl := range assocs {
		if nl.LessonID == lessonID {
			curIdx = i
			break
		}
	}
	if curIdx == -1 {
		return fmt.Errorf("lesson not assigned to node")
	}

	// Remove current item from slice view to compute neighbors at new position
	assocsReduced := make([]*models.NodeLesson, 0, len(assocs)-1)
	assocsReduced = append(assocsReduced, assocs[:curIdx]...)
	assocsReduced = append(assocsReduced, assocs[curIdx+1:]...)

	// Clamp target position
	if position <= 0 {
		position = 1
	}
	if position > len(assocsReduced)+1 {
		position = len(assocsReduced) + 1
	}

	// Determine neighbor keys for Between
	var prevKey, nextKey string
	if position-2 >= 0 && position-2 < len(assocsReduced) {
		prevKey = assocsReduced[position-2].OrderKey
	}
	if position-1 >= 0 && position-1 < len(assocsReduced) {
		nextKey = assocsReduced[position-1].OrderKey
	}
	newKey := utils.Between(prevKey, nextKey)
	if newKey == prevKey {
		// fallback append after last
		for i := len(assocsReduced) - 1; i >= 0; i-- {
			if k := assocsReduced[i].OrderKey; k != "" {
				prevKey = k
				break
			}
		}
		newKey = utils.NextAfter(prevKey)
	}

	// Persist update
	if err := s.repos.NodeLesson.UpdateOrderKey(ctx, nodeID, lessonID, newKey); err != nil {
		return fmt.Errorf("failed to move lesson: %w", err)
	}
	return nil
}

// Helper methods
func (s *LearningNodeService) validateCreateNodeRequest(req *CreateLearningNodeRequest) error {
	if req.Title == "" {
		return fmt.Errorf("title is required")
	}
	if req.Difficulty < 1 || req.Difficulty > 10 {
		return fmt.Errorf("difficulty must be between 1 and 10")
	}
	if req.CreatedBy == uuid.Nil {
		return fmt.Errorf("created_by is required")
	}
	return nil
}

func (s *LearningNodeService) validateAddNodeToPathRequest(req *AddNodeToPathRequest) error {
	if req.PathID == uuid.Nil {
		return fmt.Errorf("path_id is required")
	}
	if req.NodeID == uuid.Nil {
		return fmt.Errorf("node_id is required")
	}
	return nil
}

func (s *LearningNodeService) isValidNodeStatus(status models.NodeStatus) bool {
	validStatuses := []models.NodeStatus{
		models.NodeStatusDraft,
		models.NodeStatusActive,
		models.NodeStatusArchived,
		models.NodeStatusDeprecated,
	}
	for _, validStatus := range validStatuses {
		if status == validStatus {
			return true
		}
	}
	return false
}

// Request/Response types
type CreateLearningNodeRequest struct {
	Title            string    `json:"title" validate:"required"`
	Description      string    `json:"description"`
	EstimatedTimes   int       `json:"estimated_times"`
	Difficulty       int       `json:"difficulty" validate:"min=1,max=10"`
	Prerequisites    []string  `json:"prerequisites"`
	Price            *int      `json:"price"`
	CertificatePrice *int      `json:"certificate_price"`
	CoverImage       string    `json:"cover_image"`
	Skills           []string  `json:"skills"`
	Tags             []string  `json:"tags"`
	LearnerTags      []string  `json:"learner_tags"`
	WhatYouWillLearn []string  `json:"what_you_will_learn"`
	CreatedBy        uuid.UUID `json:"created_by" validate:"required"`
}

type UpdateLearningNodeRequest struct {
	Title            *string    `json:"title,omitempty"`
	Description      *string    `json:"description,omitempty"`
	EstimatedTimes   *int       `json:"estimated_times,omitempty"`
	Difficulty       *int       `json:"difficulty,omitempty"`
	Prerequisites    []string   `json:"prerequisites,omitempty"`
	Price            *int       `json:"price,omitempty"`
	CertificatePrice *int       `json:"certificate_price,omitempty"`
	CoverImage       *string    `json:"cover_image,omitempty"`
	Skills           []string   `json:"skills,omitempty"`
	Tags             []string   `json:"tags,omitempty"`
	LearnerTags      []string   `json:"learner_tags,omitempty"`
	WhatYouWillLearn []string   `json:"what_you_will_learn,omitempty"`
	UpdatedBy        *uuid.UUID `json:"updated_by,omitempty"`
}

type GenerateNodeRequest struct {
	NodeDescription string    `json:"node_description" validate:"required"`
	UserID          uuid.UUID `json:"user_id"`
}

type GetLearningNodesRequest struct {
	Page        int      `form:"page" json:"page"`
	PageSize    int      `form:"page_size" json:"page_size"`
	Tags        []string `form:"tags" json:"tags"`
	Skills      []string `form:"skills" json:"skills"`
	LearnerTags []string `form:"learner_tags" json:"learner_tags"`
	Query       string   `form:"query" json:"query"`
	SearchMode  string   `form:"search_mode" json:"search_mode"` // "exact" or "fuzzy" (default)
}

type AddNodeToPathRequest struct {
	PathID uuid.UUID `json:"path_id" validate:"required"`
	NodeID uuid.UUID `json:"node_id" validate:"required"`
}
