load("@rules_go//go:def.bzl", "go_library")

go_library(
    name = "services",
    srcs = glob(["**/*.go"]),
    importpath = "github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/internal/services",
    visibility = ["//visibility:public"],
    deps = [
        "//golangp/apps/pointer_center/internal/models",
        "//golangp/apps/pointer_center/internal/models/student_profile",
        "//golangp/apps/pointer_center/internal/repositories",
        "//golangp/apps/pointer_center/pkg/ai_agents",
        "//golangp/apps/pointer_center/pkg/utils",
        "//golangp/common/logging:logger",
        "//golangp/common/dify",
        "@com_github_google_uuid//:uuid",
        "@io_gorm_datatypes//:datatypes",
        "@org_mongodb_go_mongo_driver//bson/primitive",
    ],
)
