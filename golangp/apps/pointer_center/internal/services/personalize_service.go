package services

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/internal/models"
	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/internal/repositories"
	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/pkg/ai_agents"
	"github.com/Arxtect/CoreXWorkSpace/golangp/common/dify"
	"github.com/Arxtect/CoreXWorkSpace/golangp/common/logging"
	"github.com/google/uuid"
)

type AdjustScope string

const (
	ScopePath   AdjustScope = "path"
	ScopeNode   AdjustScope = "node"
	ScopeLesson AdjustScope = "lesson"
)

// StartRequest is the payload to start personalization
type StartRequest struct {
	StudentNeed string      `json:"student_need"`
	UserID      uuid.UUID   `json:"user_id"`
	TargetID    *uuid.UUID  `json:"target_id,omitempty"`
	PathID      uuid.UUID   `json:"path_id"`
	AdjustScope AdjustScope `json:"adjust_scope"`
}

// IterationMessage represents the JSON exchange with AI
type IterationMessage struct {
	Tool      string                 `json:"tool"`
	Arguments map[string]interface{} `json:"arguments"`
	Thought   string                 `json:"thought"`
}

type PersonalizeService struct {
	aiAgents *ai_agents.Manager
	repos    *repositories.Repositories
	dify     *dify.Client
}

var psLogger = logging.GetLogger("personalize_service")

func NewPersonalizeService(aiAgents *ai_agents.Manager, repos *repositories.Repositories, difyClient *dify.Client) *PersonalizeService {
	return &PersonalizeService{aiAgents: aiAgents, repos: repos, dify: difyClient}
}

// Start launches a personalize workflow quickly: create work first and return workID, then async init conversation and iterate
func (s *PersonalizeService) Start(ctx context.Context, req StartRequest) (uuid.UUID, error) {
	if req.AdjustScope == ScopePath && req.TargetID != nil {
		return uuid.Nil, fmt.Errorf("target_id should be nil when scope is path")
	}

	// 1) 使用原路径作为工作路径
	workingPathID := req.PathID
	var workingTitle string
	if lp, err := s.repos.LearningPath.GetByID(ctx, workingPathID); err == nil && lp != nil {
		workingTitle = lp.Title
	} else {
		workingTitle = "Personalization"
	}

	// 2) 在 LearningPath 上就地标记/切换进度，并设置 ForkFrom
	if lp, err := s.repos.LearningPath.GetByID(ctx, workingPathID); err == nil && lp != nil {
		_ = s.repos.LearningPath.UpdateProgressStatus(ctx, workingPathID, models.StatusActive)
		if lp.ForkFrom == uuid.Nil { lp.ForkFrom = workingPathID }
		now := time.Now()
		lp.StartedAt = &now
		lp.LastAccessedAt = &now
		lp.UpdatedAt = now
		if err := s.repos.LearningPath.Update(ctx, lp); err != nil {
			psLogger.Warning("update learning path progress failed: path=%s err=%v", workingPathID, err)
		}
	} else {
		psLogger.Warning("learning path not found for personalization: path=%s err=%v", workingPathID, err)
	}

	// 3) 先创建个性化工作（此时无 conversation_id），立即返回 workID
	work := &models.PersonalizeWork{
		ConversationID: "",
		UserID:         req.UserID,
		WorkingPathID:  &workingPathID,
		OriginalPathID: &req.PathID,
		Status:         models.PersonalizeWorkStatusInProgress,
		Title:          "Personalization-" + workingTitle,
		Description:    req.StudentNeed,
	}
	if err := s.repos.PersonalizeWork.Create(ctx, work); err != nil {
		return uuid.Nil, fmt.Errorf("create work failed: %w", err)
	}
	_ = s.repos.PersonalizeWork.UpdateLastOperation(ctx, work.ID)

	// 4) 异步创建会话并触发首次迭代
	go func(workID uuid.UUID, userID uuid.UUID) {
		// 构造初始负载
		payload := map[string]interface{}{
			"student_need": req.StudentNeed,
			"adjust_scope": string(req.AdjustScope),
		}
		if req.TargetID != nil { payload["target_id"] = req.TargetID.String() }
		body, _ := json.Marshal(payload)

		resp, err := s.aiAgents.PersonalizeWithChat(context.Background(), "", userID, string(body))
		if err != nil {
			psLogger.Error("send initial chat failed work=%s: %v", workID, err)
			return
		}
		convID, _ := resp.Metadata["conversation_id"].(string)
		if convID == "" {
			psLogger.Warning("conversation_id missing from AI response work=%s", workID)
			return
		}

		// 更新 work 的 conversation_id
		// 读取并更新（假定仓储支持 Update 或按需更新）
		// 忽略读取错误时的更新尝试，直接构造对象更新
		if fetched, err := s.repos.PersonalizeWork.GetByID(context.Background(), workID); err == nil && fetched != nil {
			fetched.ConversationID = convID
			if err := s.repos.PersonalizeWork.Update(context.Background(), fetched); err != nil {
				psLogger.Warning("update work conversation_id failed work=%s err=%v", workID, err)
			}
		} else {
			// 回退：尝试最小更新
			tmp := &models.PersonalizeWork{ID: workID}
			tmp.ConversationID = convID
			if err := s.repos.PersonalizeWork.Update(context.Background(), tmp); err != nil {
				psLogger.Warning("fallback update work conversation_id failed work=%s err=%v", workID, err)
			}
		}
		_ = s.repos.PersonalizeWork.UpdateLastOperation(context.Background(), workID)

		// 若有初答，继续后台迭代
		if resp != nil && resp.Data != nil {
			if firstAnswer, ok := resp.Data["answer"].(string); ok && firstAnswer != "" {
				if _, err := s.Iterate(context.Background(), convID, firstAnswer); err != nil {
					psLogger.Error("iterate error conv=%s: %v", convID, err)
				}
			}
		}
	}(work.ID, req.UserID)

	return work.ID, nil
}

// Iterate processes a given AI answer, executes tools, feeds back tool_result, and continues using immediate responses
func (s *PersonalizeService) Iterate(ctx context.Context, conversationID string, aiAnswer string) (*IterationMessage, error) {
	// Fetch work
	work, err := s.repos.PersonalizeWork.GetByConversationID(ctx, conversationID)
	if err != nil {
		return nil, fmt.Errorf("work not found")
	}

	const maxSteps = 50
	var im IterationMessage
	currentAnswer := aiAnswer

	for stepCount := 0; stepCount < maxSteps; stepCount++ {
		if currentAnswer == "" {
			return &im, nil
		}

		// 尝试解析 AI JSON，如失败则引导 AI 重新以严格 JSON 返回并最多重试 2 次
		parseOK := false
		for attempt := 0; attempt < 3; attempt++ {
			if err := json.Unmarshal([]byte(currentAnswer), &im); err == nil {
				parseOK = true
				break
			}
			if attempt == 2 { // 最后一次失败后放弃
				psLogger.Warning("iterate: parse ai json failed after retries conv=%s", conversationID)
				return &im, nil
			}
			guide := "上条输出不是合法 JSON，请仅返回严格 JSON 对象：{\"tool\":\"...\",\"arguments\":{...},\"thought\":\"...\"}。不要包含 Markdown 或解释，所有换行必须用 \\n 转义。"
			resp2, err2 := s.aiAgents.PersonalizeWithChat(ctx, conversationID, work.UserID, guide)
			if err2 != nil {
				psLogger.Error("iterate: request reformat failed conv=%s: %v", conversationID, err2)
				return &im, nil
			}
			next2, _ := resp2.Data["answer"].(string)
			if next2 == "" {
				return &im, nil
			}
			currentAnswer = next2
		}
		if !parseOK {
			return &im, nil
		}

		// Tool dispatch; collect a result object for feedback
		var resultObj interface{}
		switch im.Tool {
		case "ask_student_for_clarification":
			// questions: string
			questions, _ := im.Arguments["questions"].(string)
			resultObj = map[string]interface{}{"status": "asked", "questions": questions}
			_ = s.repos.PersonalizeWork.UpdateLastOperation(ctx, work.ID)
			return &im, nil

		case "get_student_profile":
			prof, err := s.aiAgents.Tools().GetStudentProfile(ctx, work.UserID)
			if err == nil { resultObj = prof }

		case "get_path_nodes":
			if work.WorkingPathID != nil {
				nodes, err := s.aiAgents.Tools().GetPathNodes(ctx, *work.WorkingPathID)
				if err == nil { resultObj = nodes }
			}

		case "get_node_lessons":
			nodeIDStr, _ := im.Arguments["node_id"].(string)
			if nodeIDStr != "" {
				nodeID, _ := uuid.Parse(nodeIDStr)
				lessons, err := s.aiAgents.Tools().GetNodeLessons(ctx, nodeID)
				if err == nil { resultObj = lessons }
			}

		case "get_lesson":
			lessonID, _ := im.Arguments["lesson_id"].(string)
			if lessonID != "" {
				lesson, err := s.aiAgents.Tools().GetLesson(ctx, lessonID)
				if err == nil && lesson != nil { resultObj = lesson }
			}

		case "replace_lesson":
			lessonID, _ := im.Arguments["lesson_id"].(string)
			if raw, ok := im.Arguments["new_lesson"]; ok {
				tmp, _ := json.Marshal(raw)
				var newLesson models.LearningLesson
				if err := json.Unmarshal(tmp, &newLesson); err == nil && lessonID != "" {
					_ = s.aiAgents.Tools().ReplaceLesson(ctx, lessonID, &newLesson)
					resultObj = map[string]interface{}{"replaced_lesson_id": lessonID}
				}
			}

		case "remove_lesson_from_node":
			nodeIDStr, _ := im.Arguments["node_id"].(string)
			lessonID, _ := im.Arguments["lesson_id"].(string)
			if nodeIDStr != "" && lessonID != "" {
				nodeID, _ := uuid.Parse(nodeIDStr)
				_ = s.aiAgents.Tools().RemoveLessonFromNode(ctx, lessonID, nodeID)
				resultObj = map[string]interface{}{"removed_lesson_id": lessonID, "node_id": nodeIDStr}
			}

		case "remove_node_from_path":
			nodeIDStr, _ := im.Arguments["node_id"].(string)
			if nodeIDStr != "" && work.WorkingPathID != nil {
				nodeID, _ := uuid.Parse(nodeIDStr)
				_ = s.aiAgents.Tools().RemoveNodeFromPath(ctx, *work.WorkingPathID, nodeID)
				resultObj = map[string]interface{}{"removed_node_id": nodeIDStr}
			}

		case "append_lesson_to_node":
			nodeIDStr, _ := im.Arguments["node_id"].(string)
			if nodeIDStr != "" {
				nodeID, _ := uuid.Parse(nodeIDStr)
				if raw, ok := im.Arguments["lesson_template"]; ok {
					tmp, _ := json.Marshal(raw)
					var tpl models.LearningLesson
					if err := json.Unmarshal(tmp, &tpl); err == nil {
						_ = s.aiAgents.Tools().AppendLessonToNode(ctx, nodeID, &tpl)
						resultObj = map[string]interface{}{"appended_to_node_id": nodeIDStr, "lesson_id": tpl.ID.Hex(), "lesson_title": tpl.Title}
					}
				}
			}

		case "reorder_node_lessons":
			nodeIDStr, _ := im.Arguments["node_id"].(string)
			var order []string
			if arr, ok := im.Arguments["lesson_id_order"].([]interface{}); ok {
				for _, v := range arr {
					if s, ok := v.(string); ok {
						order = append(order, s)
					}
				}
			}
			if nodeIDStr != "" && len(order) > 0 {
				nodeID, _ := uuid.Parse(nodeIDStr)
				_ = s.aiAgents.Tools().ReorderNodeLessons(ctx, nodeID, order)
				resultObj = map[string]interface{}{"node_id": nodeIDStr, "lesson_id_order": order}
			}

		case "add_node_to_path":
			if work.WorkingPathID != nil {
				if raw, ok := im.Arguments["new_node"]; ok {
					tmp, _ := json.Marshal(raw)
					var node models.LearningNode
					if err := json.Unmarshal(tmp, &node); err == nil {
						_ = s.aiAgents.Tools().AddNodeToPath(ctx, *work.WorkingPathID, &node)
						resultObj = map[string]interface{}{"added_node_id": node.ID.String(), "node_title": node.Title}
					}
				}
			}

		case "task_completed":
			message, _ := im.Arguments["message"].(string)
			if work.WorkingPathID != nil {
				_ = s.aiAgents.Tools().TaskCompleted(ctx, work.UserID, *work.WorkingPathID, message, conversationID)
			} else {
				_ = s.repos.PersonalizeWork.MarkCompleted(ctx, work.ID)
			}
			resultObj = map[string]interface{}{"status": "completed", "message": message}
			_ = s.repos.PersonalizeWork.UpdateLastOperation(ctx, work.ID)
			return &im, nil

		default:
			// Unknown tool; keep thought log only
		}
		// Update last op and log
		_ = s.repos.PersonalizeWork.UpdateLastOperation(ctx, work.ID)
		psLogger.Info("iterate step conv=%s tool=%s args=%v result=%v", conversationID, im.Tool, im.Arguments, resultObj)

		// Feed back tool result to AI to continue the loop and capture response
		if resultObj == nil {
			resultObj = map[string]interface{}{"status": "ok"}
		}
		feedback := map[string]interface{}{
			"tool_result": map[string]interface{}{
				"tool":   im.Tool,
				"result": resultObj,
			},
		}
		fbBytes, _ := json.Marshal(feedback)
		resp, err := s.aiAgents.PersonalizeWithChat(ctx, conversationID, work.UserID, string(fbBytes))
		if err != nil {
			psLogger.Error("send tool_result failed conv=%s: %v", conversationID, err)
			return &im, nil
		}
		nextAns, _ := resp.Data["answer"].(string)
		if nextAns == "" {
			return &im, nil
		}
		currentAnswer = nextAns
	}

	// Safety return; no terminal condition reached
	return &im, nil
}

// StatusResponse is returned by Status API to present current state + latest AI thought/question/error
type StatusResponse struct {
	Status      string `json:"status"`
	LastThought string `json:"last_thought,omitempty"`
	AskQuestion string `json:"ask_question,omitempty"`
	Error       string `json:"error,omitempty"`
}

// Status returns: current status, last AI thought, question when waiting for student reply, and error message when failed
func (s *PersonalizeService) Status(ctx context.Context, workID uuid.UUID) (*StatusResponse, error) {
	work, err := s.repos.PersonalizeWork.GetByID(ctx, workID)
	if err != nil { return nil, err }

	res := &StatusResponse{ Status: string(work.Status) }
	if work.ErrorMessage != "" { res.Error = work.ErrorMessage }

	// 若会话尚未建立，直接返回当前状态
	if work.ConversationID == "" { return res, nil }

	// Try fetching latest AI message to extract thought and potential clarification question
	apiKey, ok := s.aiAgents.GetPersonalizeAPIKey()
	if !ok { return res, nil }

	msgs, err := s.dify.GetMessages(ctx, work.ConversationID, "pageflux", 20, "", apiKey)
	if err != nil {
		psLogger.Warning("status: get messages failed conv=%s: %v", work.ConversationID, err)
		return res, nil
	}
	if len(msgs) == 0 { return res, nil }

	var lastAnswer string
	for i := len(msgs) - 1; i >= 0; i-- {
		if msgs[i].Answer != "" { lastAnswer = msgs[i].Answer; break }
	}
	if lastAnswer == "" { return res, nil }

	var im IterationMessage
	if err := json.Unmarshal([]byte(lastAnswer), &im); err != nil {
		psLogger.Warning("status: parse last answer failed conv=%s: %v", work.ConversationID, err)
		return res, nil
	}

	res.LastThought = im.Thought
	if im.Tool == "ask_student_for_clarification" {
		if q, ok := im.Arguments["questions"].(string); ok { res.AskQuestion = q }
	}
	return res, nil
}

// Reply resumes iteration when AI asked for clarification
func (s *PersonalizeService) Reply(ctx context.Context, workID uuid.UUID, userID uuid.UUID, reply string) (*IterationMessage, error) {
	work, err := s.repos.PersonalizeWork.GetByID(ctx, workID)
	if err != nil { return nil, err }

	if work.ConversationID == "" {
		// 会话尚未就绪，返回初始化中的占位状态
		return &IterationMessage{Tool: "", Arguments: map[string]interface{}{"status": "initializing"}, Thought: "initializing"}, nil
	}

	// 异步发送用户回复并继续迭代
	conv := work.ConversationID
	go func(convID string) {
		resp, err := s.aiAgents.PersonalizeWithChat(context.Background(), convID, userID, reply)
		if err != nil {
			psLogger.Error("reply send failed conv=%s: %v", convID, err)
			return
		}
		if resp != nil {
			if ans, ok := resp.Data["answer"].(string); ok && ans != "" {
				if _, err := s.Iterate(context.Background(), convID, ans); err != nil {
					psLogger.Error("iterate error conv=%s: %v", convID, err)
				}
			}
		}
	}(conv)

	// 立即返回占位应答，提示前端使用 Status 轮询
	return &IterationMessage{Tool: "", Arguments: map[string]interface{}{"status": "processing"}, Thought: "processing"}, nil
}