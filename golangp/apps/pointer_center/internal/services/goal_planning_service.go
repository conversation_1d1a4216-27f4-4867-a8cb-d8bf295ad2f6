package services

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/internal/models"
	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/internal/repositories"
	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/pkg/ai_agents"
	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/pkg/utils"
	"github.com/Arxtect/CoreXWorkSpace/golangp/common/logging"

	"github.com/google/uuid"
	"gorm.io/datatypes"
)

// SuggestedGoal represents one suggested goal item from AI
type SuggestedGoal struct {
    FinalGoalDescription string   `json:"final_goal_description"`
    FocusDirection       string   `json:"focus_direction"`
    TechStack            []string `json:"tech_stack"`
}

// Chat answer schema for goal planner
type goalPlannerChatAnswer struct {
    Message            string          `json:"message,omitempty"`
    SuggestedGoals     []SuggestedGoal `json:"suggested_goals,omitempty"`
}

// Requests
type StartChatRequest struct {
    OriginGoal     string    `json:"origin_goal"`
    ConversationID string    `json:"conversation_id,omitempty"`
    UserID         uuid.UUID `json:"user_id"`
    Message        string    `json:"message,omitempty"`
}

type ChooseGoalRequest struct {
    ConversationID string    `json:"conversation_id" validate:"required"`
    Index          int       `json:"index" validate:"min=0"`
    UserID         uuid.UUID `json:"user_id"`
}

// Chat response
type ChatResponse struct {
    ConversationID string          `json:"conversation_id"`
    Message        string          `json:"message"`
    SuggestedGoals []SuggestedGoal `json:"suggested_goals,omitempty"`
    Status         string          `json:"status"`
}

// GoalPlanningService handles goal planning via AI (chat-based)
type GoalPlanningService struct {
    repos     *repositories.Repositories
    aiManager *ai_agents.Manager
}

func NewGoalPlanningService(repos *repositories.Repositories, aiManager *ai_agents.Manager) *GoalPlanningService {
    return &GoalPlanningService{repos: repos, aiManager: aiManager}
}

// Removed legacy single-shot PlanGoal API per new design

// StartChat starts a goal planning conversation or continues if ConversationID provided
func (s *GoalPlanningService) StartChat(ctx context.Context, req *StartChatRequest) (*ChatResponse, error) {
    if req.UserID == uuid.Nil { return nil, fmt.Errorf("user_id is required") }
    if req.ConversationID == "" && req.OriginGoal == "" {
        return nil, fmt.Errorf("origin_goal is required to start a chat")
    }

    // Send message to Goal Planner chat agent
    // decide the first query content
    query := req.OriginGoal
    if req.ConversationID != "" && req.Message != "" { query = req.Message }

    // When starting a new conversation, include user context (static profile + competency graph)
    if req.ConversationID == "" {
        // fetch competency graph (Mongo)
        graph, gerr := s.repos.TechCompetency.GetByUserID(ctx, req.UserID.String())
        // fetch static profile (Postgres)
        profile, perr := s.repos.StaticProfile.GetByUserID(ctx, req.UserID)
        // Build a context envelope; tolerate missing pieces
        ctxPayload := map[string]interface{}{
            "static_profile":   profile,
            "competency_graph": graph,
        }
        if b, jerr := json.Marshal(ctxPayload); jerr == nil {
            query = fmt.Sprintf("[CONTEXT]%s\n[GOAL]%s", string(b), req.OriginGoal)
        } else {
            _ = gerr; _ = perr // quiet lints; we still proceed with plain goal
        }
    }
    aiResp, err := s.aiManager.GoalPlannerChat(ctx, req.ConversationID, query)
    if err != nil || aiResp == nil || !aiResp.Success {
        return nil, fmt.Errorf("goal planner chat failed: %v %v", err, func() interface{} { if aiResp!=nil {return aiResp.Error}; return nil }())
    }

    convID, _ := aiResp.Metadata["conversation_id"].(string)

    // Parse answer JSON to get message and suggested goals
    var parsed goalPlannerChatAnswer
    suggested := []SuggestedGoal{}
    status := string(models.GoalPlanningStatusPending)
    if err := utils.ParseAIAnswerField(aiResp.Data, &parsed); err == nil {
        if len(parsed.SuggestedGoals) > 0 {
            suggested = parsed.SuggestedGoals
            status = string(models.GoalPlanningStatusReady)
        }
    }
    // Prefer parsed message from JSON; fallback to raw answer string
    messageText := parsed.Message

    // Upsert task
    task := &models.GoalPlanningTask{
        UserID:         req.UserID,
        ConversationID: convID,
        LastMessage:    messageText,
        Status:         models.GoalPlanningStatus(status),
    }
    if len(suggested) > 0 {
        b, _ := json.Marshal(suggested)
        task.SuggestedGoals = datatypes.JSON(b)
    }
    if existing, err := s.repos.GoalPlanning.GetByConversationID(ctx, convID); err == nil && existing != nil {
        // update existing
        task.ID = existing.ID
        if task.SuggestedGoals != nil { existing.SuggestedGoals = task.SuggestedGoals }
        existing.LastMessage = task.LastMessage
        existing.Status = task.Status
        if uerr := s.repos.GoalPlanning.Update(ctx, existing); uerr != nil {
            logging.Warning("update goal planning task failed: %v", uerr)
        }
        task = existing
    } else {
        if cerr := s.repos.GoalPlanning.Create(ctx, task); cerr != nil {
            return nil, fmt.Errorf("failed to save goal planning task: %w", cerr)
        }
    }

    return &ChatResponse{
        ConversationID: convID,
        Message:        messageText,
        SuggestedGoals: suggested,
        Status:         string(task.Status),
    }, nil
}

// ChooseSuggestedGoal selects one suggestion and triggers internal path generation, returning only the generated path ID
func (s *GoalPlanningService) ChooseSuggestedGoal(ctx context.Context, req *ChooseGoalRequest) (uuid.UUID, error) {
    if req.UserID == uuid.Nil { return uuid.Nil, fmt.Errorf("user_id is required") }
    if req.ConversationID == "" { return uuid.Nil, fmt.Errorf("conversation_id is required") }

    // Load task by conversation ID
    task, err := s.repos.GoalPlanning.GetByConversationID(ctx, req.ConversationID)
    if err != nil || task == nil {
        // Backward compatibility: if client still sends a task UUID, try lookup by ID
        if uid, perr := uuid.Parse(req.ConversationID); perr == nil {
            if byID, e2 := s.repos.GoalPlanning.GetByID(ctx, uid); e2 == nil && byID != nil {
                task = byID
            } else {
                return uuid.Nil, fmt.Errorf("goal planning task not found: %w", err)
            }
        } else {
            return uuid.Nil, fmt.Errorf("goal planning task not found: %w", err)
        }
    }

    // Parse suggestions
    var suggestions []SuggestedGoal
    if len(task.SuggestedGoals) == 0 { return uuid.Nil, fmt.Errorf("no suggestions available to choose") }
    if err := json.Unmarshal([]byte(task.SuggestedGoals), &suggestions); err != nil {
        return uuid.Nil, fmt.Errorf("failed to parse suggestions: %w", err)
    }
    if req.Index < 0 || req.Index >= len(suggestions) { return uuid.Nil, fmt.Errorf("index out of range") }
    chosen := suggestions[req.Index]

    // Save selection (only index per requirement)
    task.SelectedIndex = &req.Index
    task.Status = models.GoalPlanningStatusCompleted
    if uerr := s.repos.GoalPlanning.Update(ctx, task); uerr != nil {
        logging.Warning("update selection failed: %v", uerr)
    }

    // Trigger internal path generation (no public API)
    // Construct a concise goal string from selection
    goalText := chosen.FinalGoalDescription
    if goalText == "" { goalText = chosen.FocusDirection }
    // Reuse existing learning path generation service
    lpService := NewLearningPathService(s.repos, s.aiManager)
    placeholder, err := lpService.GenerateLearningPathWithAI(ctx, &GeneratePathRequest{Goal: goalText, UserID: req.UserID})
    if err != nil { return uuid.Nil, fmt.Errorf("failed to trigger path generation: %w", err) }
    return placeholder.ID, nil
}
