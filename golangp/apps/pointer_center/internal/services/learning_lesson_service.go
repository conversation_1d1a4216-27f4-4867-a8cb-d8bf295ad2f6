package services

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/internal/models"
	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/internal/repositories"
	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/pkg/ai_agents"
	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/pkg/utils"
	"github.com/Arxtect/CoreXWorkSpace/golangp/common/logging"
	"github.com/google/uuid"

	"strings"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// LearningLessonService handles business logic for learning lessons
type LearningLessonService struct {
	repos     *repositories.Repositories
	aiManager *ai_agents.Manager
}

// NewLearningLessonService creates a new learning lesson service
func NewLearningLessonService(repos *repositories.Repositories, aiManager *ai_agents.Manager) *LearningLessonService {
	return &LearningLessonService{
		repos:     repos,
		aiManager: aiManager,
	}
}

// CreateLearningLesson creates a new learning lesson
func (s *LearningLessonService) CreateLearningLesson(ctx context.Context, req *CreateLearningLessonRequest) (*models.LearningLesson, error) {
	// Validate request
	if err := s.validateCreateLessonRequest(req); err != nil {
		return nil, fmt.Errorf("validation failed: %w", err)
	}
	// Create learning lesson model
	lesson := &models.LearningLesson{
		ID:                        primitive.NewObjectID(),
		Title:                     req.Title,
		Description:               req.Description,
		Type:                      models.LessonType(req.Type),
		EstimatedTimes:            req.EstimatedTimes,
		Difficulty:                models.DifficultyLevel(req.Difficulty),
		ContentFlow:               req.ContentFlow,
		LearningObjectives:        req.LearningObjectives,
		CommonMisconceptions:      req.CommonMisconceptions,
		ExtensionIdea:             req.ExtensionIdea,
		StudentProfileAssociation: req.StudentProfileAssociation,
		Status:                    models.LessonStatusDraft,
		CreatedBy:                 req.CreatedBy,
		CreatedAt:                 time.Now(),
		UpdatedAt:                 time.Now(),
	}

	// Ensure all content blocks have UUIDs
	lesson.EnsureContentFlowIDs()

	// Save to MongoDB
	if err := s.repos.LearningLesson.Create(ctx, lesson); err != nil {
		logging.Error("Failed to create learning lesson: lesson_id=%v, error=%v", lesson.ID, err)
		return nil, fmt.Errorf("failed to create learning lesson: %w", err)
	}
	logging.Info("Learning lesson created successfully: lesson_id=%v, title=%s", lesson.ID, lesson.Title)

	// If NodeID provided, create NodeLesson association with sparse ordering
	if req.NodeID != "" {
		nodeUUID, err := uuid.Parse(req.NodeID)
		if err != nil {
			logging.Error("Invalid node_id when associating lesson: node_id=%s, error=%v", req.NodeID, err)
		} else {
			// Skip if already associated
			if exists, err := s.repos.NodeLesson.ExistsByNodeAndLesson(ctx, nodeUUID, lesson.ID.Hex()); err == nil && exists {
				logging.Info("Lesson already associated to node: node_id=%s, lesson_id=%s", req.NodeID, lesson.ID.Hex())
			} else {
				// Determine a sparse order value
				// Determine intended position (1-based). If not provided, append.
				assocs, _ := s.repos.NodeLesson.GetByNodeID(ctx, nodeUUID)
				pos := req.Order
				if pos <= 0 {
					pos = len(assocs) + 1
				}
				if pos > len(assocs)+1 {
					pos = len(assocs) + 1
				}

				// Compute order_key between neighbors
				var prevKey, nextKey string
				if pos-2 >= 0 && pos-2 < len(assocs) {
					prevKey = assocs[pos-2].OrderKey
				}
				if pos-1 >= 0 && pos-1 < len(assocs) {
					nextKey = assocs[pos-1].OrderKey
				}
				ok := utils.Between(prevKey, nextKey)

				// If between returned same as prev (no room), fallback to append after last
				if ok == prevKey {
					// try append after actual last non-empty
					prevKey = ""
					for i := len(assocs) - 1; i >= 0; i-- {
						if k := assocs[i].OrderKey; k != "" {
							prevKey = k
							break
						}
					}
					ok = utils.NextAfter(prevKey)
				}

				nl := &models.NodeLesson{NodeID: nodeUUID, LessonID: lesson.ID.Hex(), OrderKey: ok, CreatedAt: time.Now(), UpdatedAt: time.Now(),
					LessonTitle: lesson.Title, LessonDescription: lesson.Description,
				}
				if err := s.repos.NodeLesson.Create(ctx, nl); err != nil {
					logging.Error("Failed to associate lesson to node: node_id=%s, lesson_id=%s, error=%v", req.NodeID, lesson.ID.Hex(), err)
				} else {
					logging.Info("Associated lesson to node on create: node_id=%s, lesson_id=%s, order_key=%s", req.NodeID, lesson.ID.Hex(), ok)
				}
			}
		}
	}
	return lesson, nil
}

// LessonBrief represents minimal lesson info for listing under a node
type LessonBrief struct {
	LessonID    string `json:"lesson_id"`
	Title       string `json:"title"`
	Description string `json:"description"`
	OrderKey    string `json:"order_key,omitempty"`
}

// GenerateLearningLessonWithAI generates a learning lesson using AI
func (s *LearningLessonService) GenerateLearningLessonWithAI(ctx context.Context, req *GenerateLessonRequest) (*models.LearningLesson, error) {
	// 1) 校验并创建占位 lesson（状态=generating），立即返回
	if err := s.validateGenerateLessonRequest(req); err != nil {
		return nil, fmt.Errorf("validation failed: %w", err)
	}
	placeholder := &models.LearningLesson{
		ID:                        primitive.NewObjectID(),
		Title:                     "AI 生成的课程",
		Description:               req.LessonDescription,
		Type:                      models.LessonTypeText,
		EstimatedTimes:            30,
		Difficulty:                models.DifficultyIntermediate5,
		LearningObjectives:        nil,
		CommonMisconceptions:      nil,
		ExtensionIdea:             "",
		StudentProfileAssociation: "",
		Status:                    models.LessonStatusGenerating,
		CreatedBy:                 req.UserID,
		CreatedAt:                 time.Now(),
		UpdatedAt:                 time.Now(),
	}
	if err := s.repos.LearningLesson.Create(ctx, placeholder); err != nil {
		logging.Error("Failed to create placeholder lesson: err=%v", err)
		return nil, fmt.Errorf("failed to create placeholder lesson: %w", err)
	}

	// 2) 后台启动 AI 生成并落库，成功则置 active，失败置 failed
	go func(lessonID primitive.ObjectID, userID string, lessonDesc string) {
		bctx := context.Background()
		defer func(){ if r := recover(); r != nil { logging.Error("panic in GenerateLearningLessonWithAI async: %v", r) } }()

		// Get profile
		studentProfile := "{}"
		if userID != "" {
			if uid, err := uuid.Parse(userID); err == nil {
				if profileData, err := s.aiManager.Tools().GetStudentProfile(bctx, uid); err == nil {
					if b, mErr := json.Marshal(profileData); mErr == nil { studentProfile = string(b) }
				}
			}
		}
		inputs := map[string]interface{}{
			"lesson": map[string]interface{}{ "description": lessonDesc },
			"student_profile": studentProfile,
		}
		// load the placeholder
		lesson, gerr := s.repos.LearningLesson.GetByID(bctx, lessonID)
		if gerr != nil || lesson == nil { return }

		// 本地校验重试，当 content_flow 为空时重试
		validateMaxAttempts := 2
		activated := false
		for attempt := 0; attempt < validateMaxAttempts && !activated; attempt++ {
			aiResponse, err := s.aiManager.GenerateLesson(bctx, inputs)
			if err != nil || aiResponse == nil || !aiResponse.Success { continue }

			// 解析 answer 为 map
			var ans map[string]interface{}
			if perr := utils.ParseAIAnswerField(aiResponse.Data, &ans); perr != nil {
				continue
			}

			// 映射基础字段
			if v, ok := ans["lesson_title"].(string); ok && strings.TrimSpace(v) != "" { lesson.Title = strings.TrimSpace(v) }
			if v, ok := ans["lesson_type"].(string); ok && strings.TrimSpace(v) != "" { lesson.Type = models.LessonType(strings.TrimSpace(v)) }
			if v, ok := ans["estimated_times"]; ok {
				switch t := v.(type) {
				case float64: if t > 0 { lesson.EstimatedTimes = int(t) }
				case int: if t > 0 { lesson.EstimatedTimes = t }
				case json.Number: if n, e := t.Int64(); e == nil && n > 0 { lesson.EstimatedTimes = int(n) }
				case string: if n, e := strconv.Atoi(t); e == nil && n > 0 { lesson.EstimatedTimes = n }
				}
			}
			if arr, ok := ans["learning_objectives"].([]interface{}); ok {
				out := make([]string, 0, len(arr))
				for _, it := range arr { if s, ok := it.(string); ok && strings.TrimSpace(s) != "" { out = append(out, strings.TrimSpace(s)) } }
				if len(out) > 0 { lesson.LearningObjectives = out }
			}
			if ssa, ok := ans["student_profile_association"].(string); ok { lesson.StudentProfileAssociation = ssa }
			if arr, ok := ans["common_misconceptions"].([]interface{}); ok {
				out := make([]string, 0, len(arr))
				for _, it := range arr { if s, ok := it.(string); ok && strings.TrimSpace(s) != "" { out = append(out, strings.TrimSpace(s)) } }
				if len(out) > 0 { lesson.CommonMisconceptions = out }
			}
			if sExt, ok := ans["extension_idea"].(string); ok { lesson.ExtensionIdea = sExt }
			if sDesc, ok := ans["summary"].(string); ok && strings.TrimSpace(sDesc) != "" { lesson.Description = strings.TrimSpace(sDesc) }

			// content_flow 解析
			toMap := func(v interface{}) map[string]interface{} {
				if v == nil { return map[string]interface{}{} }
				if m, ok := v.(map[string]interface{}); ok { return m }
				b, e := json.Marshal(v)
				if e == nil { var m2 map[string]interface{}; if json.Unmarshal(b, &m2) == nil && m2 != nil { return m2 } }
				return map[string]interface{}{ "value": v }
			}
			newFlow := make([]models.AtomicContent, 0)
			if raw, ok := ans["content_flow"].([]interface{}); ok {
				for _, item := range raw {
					m, ok := item.(map[string]interface{}); if !ok { continue }
					typeStr, _ := m["type"].(string)
					typeStr = strings.TrimSpace(strings.ToLower(typeStr))
					data := toMap(m["data"])
					if typeStr == "text_explanation" { if _, has := data["formatting"]; !has { data["formatting"] = "markdown" } }
					var acType models.AtomicContentType
					switch typeStr {
					case "text_explanation": acType = models.AtomicTypeTextExplanation
					case "code_snippet": acType = models.AtomicTypeCodeSnippet
					case "diagram_description": acType = models.AtomicTypeDiagramDescription
					case "flowchart_description": acType = models.AtomicTypeFlowchartDescription
					case "interactive_quiz":
						qt, _ := data["quiz_type"].(string)
						qt = strings.TrimSpace(strings.ToLower(qt))
						if qt == "fill_in_blank" { acType = models.AtomicTypeFillInBlankQuiz } else { acType = models.AtomicTypeMultipleChoiceQuiz }
					case "multiple_choice_quiz": acType = models.AtomicTypeMultipleChoiceQuiz
					case "fill_in_blank_quiz": acType = models.AtomicTypeFillInBlankQuiz
					case "practice_exercise": acType = models.AtomicTypePracticeExercise
					case "math_formula": acType = models.AtomicTypeMathFormula
					default: continue
					}
					ac := models.AtomicContent{ Type: acType, Data: data }
					newFlow = append(newFlow, ac)
				}
			}

			if len(newFlow) == 0 {
				continue
			}

			// 成功：写回并激活
			lesson.ContentFlow = newFlow
			lesson.Status = models.LessonStatusActive
			if userID != "" { lesson.UpdatedBy = userID }
			lesson.UpdatedAt = time.Now()
			if uerr := s.repos.LearningLesson.Update(bctx, lesson); uerr == nil {
				activated = true
			}
		}

		if !activated {
			lesson.Status = models.LessonStatusFiled
			lesson.UpdatedAt = time.Now()
			_ = s.repos.LearningLesson.Update(bctx, lesson)
		}
	}(placeholder.ID, req.UserID, req.LessonDescription)

	return placeholder, nil
}

// UpdateLearningLesson updates an existing learning lesson
func (s *LearningLessonService) UpdateLearningLesson(ctx context.Context, lessonID primitive.ObjectID, req *UpdateLearningLessonRequest) (*models.LearningLesson, error) {
	// Get existing lesson
	existingLesson, err := s.repos.LearningLesson.GetByID(ctx, lessonID)
	if err != nil {
		return nil, fmt.Errorf("failed to get learning lesson: %w", err)
	}
	// Update fields
	// lesson_id field removed; ID (ObjectID) is the canonical identifier
	if req.Title != nil {
		existingLesson.Title = *req.Title
	}
	if req.Description != nil {
		existingLesson.Description = *req.Description
	}
	if req.Type != nil {
		existingLesson.Type = models.LessonType(*req.Type)
	}
	if req.EstimatedTimes != nil {
		existingLesson.EstimatedTimes = *req.EstimatedTimes
	}
	if req.Difficulty != nil {
		existingLesson.Difficulty = models.DifficultyLevel(*req.Difficulty)
	}
	if req.ContentFlow != nil {
		existingLesson.ContentFlow = req.ContentFlow
		existingLesson.EnsureContentFlowIDs() // Ensure all content blocks have UUIDs
	}
	if req.LearningObjectives != nil {
		existingLesson.LearningObjectives = req.LearningObjectives
	}
	if req.CommonMisconceptions != nil {
		existingLesson.CommonMisconceptions = req.CommonMisconceptions
	}
	if req.ExtensionIdea != nil {
		existingLesson.ExtensionIdea = *req.ExtensionIdea
	}
	if req.StudentProfileAssociation != nil {
		existingLesson.StudentProfileAssociation = *req.StudentProfileAssociation
	}
	if req.UpdatedBy != nil {
		existingLesson.UpdatedBy = *req.UpdatedBy
	}
	existingLesson.UpdatedAt = time.Now()
	// Save updates
	if err := s.repos.LearningLesson.Update(ctx, existingLesson); err != nil {
		logging.Error("Failed to update learning lesson: lesson_id=%v, error=%v", lessonID, err)
		return nil, fmt.Errorf("failed to update learning lesson: %w", err)
	}
	logging.Info("Learning lesson updated successfully: lesson_id=%v", lessonID)
	return existingLesson, nil
}

// DeleteLearningLesson soft deletes a learning lesson
func (s *LearningLessonService) DeleteLearningLesson(ctx context.Context, lessonID primitive.ObjectID) error {
	// Check if lesson exists
	_, err := s.repos.LearningLesson.GetByID(ctx, lessonID)
	if err != nil {
		return fmt.Errorf("learning lesson not found: %w", err)
	}
	// Use regular delete since repository doesn't have SoftDelete method
	if err := s.repos.LearningLesson.Delete(ctx, lessonID); err != nil {
		logging.Error("Failed to delete learning lesson: lesson_id=%v, error=%v", lessonID, err)
		return fmt.Errorf("failed to delete learning lesson: %w", err)
	}
	logging.Info("Learning lesson deleted successfully: lesson_id=%v", lessonID)
	return nil
}

// GetLearningLesson retrieves a learning lesson by ID
func (s *LearningLessonService) GetLearningLesson(ctx context.Context, lessonID primitive.ObjectID) (*models.LearningLesson, error) {
	lesson, err := s.repos.LearningLesson.GetByID(ctx, lessonID)
	if err != nil {
		return nil, fmt.Errorf("failed to get learning lesson: %w", err)
	}
	return lesson, nil
}

// GetLearningLessons 统一的课程查询：支持分页，关键字搜索与类型过滤
func (s *LearningLessonService) GetLearningLessons(ctx context.Context, req *GetLearningLessonsRequest) ([]*models.LearningLesson, int64, error) {
	// 默认分页
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	// 限制 pageSize 上限
	if req.PageSize > 200 {
		req.PageSize = 200
	}
	skip := (req.Page - 1) * req.PageSize

	// 优先关键字搜索
	if req.Query != "" {
		lessons, err := s.repos.LearningLesson.Search(ctx, req.Query, req.PageSize, skip)
		if err != nil {
			return nil, 0, fmt.Errorf("failed to search lessons: %w", err)
		}
		// 仓库未提供 CountSearch，采用简化 total（可后续扩展为精准统计）
		total := int64(len(lessons))
		return lessons, total, nil
	}

	// 类型过滤
	if req.Type != "" {
		lessons, err := s.repos.LearningLesson.ListByType(ctx, models.LessonType(req.Type), req.PageSize, skip)
		if err != nil {
			return nil, 0, fmt.Errorf("failed to get lessons by type: %w", err)
		}
		total, err := s.repos.LearningLesson.CountByType(ctx, models.LessonType(req.Type))
		if err != nil {
			return nil, 0, fmt.Errorf("failed to get total count by type: %w", err)
		}
		return lessons, total, nil
	}

	// 默认列表
	lessons, err := s.repos.LearningLesson.List(ctx, req.PageSize, skip)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get learning lessons: %w", err)
	}
	total, err := s.repos.LearningLesson.Count(ctx)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get total count: %w", err)
	}
	return lessons, total, nil
}

// UpdateLessonStatus updates the status of a learning lesson
func (s *LearningLessonService) UpdateLessonStatus(ctx context.Context, lessonID primitive.ObjectID, status models.LessonStatus) error {
	// Validate status
	if !s.isValidLessonStatus(status) {
		return fmt.Errorf("invalid status: %s", status)
	}
	// Get existing lesson
	lesson, err := s.repos.LearningLesson.GetByID(ctx, lessonID)
	if err != nil {
		return fmt.Errorf("failed to get learning lesson: %w", err)
	}
	// Update status
	lesson.Status = status
	lesson.UpdatedAt = time.Now()
	if err := s.repos.LearningLesson.Update(ctx, lesson); err != nil {
		logging.Error("Failed to update lesson status: lesson_id=%v, status=%v, error=%v", lessonID, status, err)
		return fmt.Errorf("failed to update lesson status: %w", err)
	}
	logging.Info("Lesson status updated: lesson_id=%v, status=%v", lessonID, status)
	return nil
}

// ArchiveLesson archives a lesson (soft delete)
func (s *LearningLessonService) ArchiveLesson(ctx context.Context, lessonID primitive.ObjectID) error {
	return s.UpdateLessonStatus(ctx, lessonID, models.LessonStatusArchived)
}

// PublishLesson publishes a lesson
func (s *LearningLessonService) PublishLesson(ctx context.Context, lessonID primitive.ObjectID) error {
	return s.UpdateLessonStatus(ctx, lessonID, models.LessonStatusActive)
}

// Helper methods
func (s *LearningLessonService) validateCreateLessonRequest(req *CreateLearningLessonRequest) error {
	if req.Title == "" {
		return fmt.Errorf("title is required")
	}
	if req.Type == "" {
		return fmt.Errorf("type is required")
	}
	if req.CreatedBy == "" {
		return fmt.Errorf("created_by is required")
	}
	if len(req.ContentFlow) == 0 {
		return fmt.Errorf("content_flow is required")
	}
	return nil
}

func (s *LearningLessonService) validateGenerateLessonRequest(req *GenerateLessonRequest) error {
	if req.LessonDescription == "" {
		return fmt.Errorf("lesson_description is required")
	}
	return nil
}

func (s *LearningLessonService) isValidLessonStatus(status models.LessonStatus) bool {
	validStatuses := []models.LessonStatus{
		models.LessonStatusDraft,
		models.LessonStatusActive,
		models.LessonStatusArchived,
		models.LessonStatusDeprecated,
	}
	for _, validStatus := range validStatuses {
		if status == validStatus {
			return true
		}
	}
	return false
}

// Request/Response types
type CreateLearningLessonRequest struct {
	Title                     string                 `json:"title" validate:"required"`
	Description               string                 `json:"description"`
	Type                      string                 `json:"type" validate:"required"`
	EstimatedTimes            int                    `json:"estimated_times"`
	Difficulty                int                    `json:"difficulty"`
	ContentFlow               []models.AtomicContent `json:"content_flow"`
	LearningObjectives        []string               `json:"learning_objectives"`
	CommonMisconceptions      []string               `json:"common_misconceptions"`
	ExtensionIdea             string                 `json:"extension_idea"`
	StudentProfileAssociation string                 `json:"student_profile_association"`
	CreatedBy                 string                 `json:"created_by" validate:"required"`

	// Optional: immediately associate to a node
	NodeID string `json:"node_id,omitempty"`
	Order  int    `json:"order,omitempty"`
}

type UpdateLearningLessonRequest struct {
	Title                     *string                `json:"title,omitempty"`
	Description               *string                `json:"description,omitempty"`
	Type                      *string                `json:"type,omitempty"`
	EstimatedTimes            *int                   `json:"estimated_times,omitempty"`
	Difficulty                *int                   `json:"difficulty,omitempty"`
	ContentFlow               []models.AtomicContent `json:"content_flow,omitempty"`
	LearningObjectives        []string               `json:"learning_objectives,omitempty"`
	CommonMisconceptions      []string               `json:"common_misconceptions,omitempty"`
	ExtensionIdea             *string                `json:"extension_idea,omitempty"`
	StudentProfileAssociation *string                `json:"student_profile_association,omitempty"`
	UpdatedBy                 *string                `json:"updated_by,omitempty"`
}

type GenerateLessonRequest struct {
	LessonDescription string `json:"lesson_description" validate:"required"`
	UserID            string `json:"user_id"`
}

type GetLearningLessonsRequest struct {
	Page     int    `json:"page" form:"page"`
	PageSize int    `json:"page_size" form:"page_size"`
	// 可选过滤条件
	Query string `json:"q,omitempty" form:"q"`
	Type  string `json:"type,omitempty" form:"type"`
}