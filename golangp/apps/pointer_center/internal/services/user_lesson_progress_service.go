package services

import (
	"context"
	"fmt"
	"time"

	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/internal/models"
	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/internal/repositories"
	"github.com/Arxtect/CoreXWorkSpace/golangp/common/logging"
	"github.com/google/uuid"
)

// Request and Response types for UserLessonProgressService

// UpsertCompletedContentFlowRequest represents a request to add/update completed content flow
// 不存在则新增，存在则增量更新
type UpsertCompletedContentFlowRequest struct {
	UserID        uuid.UUID `json:"user_id" validate:"required"`
	NodeID        uuid.UUID `json:"node_id,omitempty"` // 可选，与 PathID 互斥
	LessonID      string    `json:"lesson_id" validate:"required"`
	ContentFlowID string    `json:"content_flow_id" validate:"required"`
	Progress      float64   `json:"progress" validate:"min=0,max=1"` // 进度值，0-1之间
}

// RemoveCompletedContentFlowRequest represents a request to remove a completed content flow
type RemoveCompletedContentFlowRequest struct {
	UserID        uuid.UUID `json:"user_id" validate:"required"`
	NodeID        uuid.UUID `json:"node_id,omitempty"`
	LessonID      string    `json:"lesson_id" validate:"required"`
	ContentFlowID string    `json:"content_flow_id" validate:"required"`
	Progress      float64   `json:"progress" validate:"min=0,max=1"` // 进度值，0-1之间
}

// GetCompletedContentFlowsRequest represents a request to get completed content flows
type GetCompletedContentFlowsRequest struct {
	UserID   uuid.UUID `json:"user_id" validate:"required"`
	NodeID   uuid.UUID `json:"node_id,omitempty"`
	LessonID string    `json:"lesson_id" validate:"required"`
}

// GetProgressRequest represents a request to get user lesson progress
type GetProgressRequest struct {
	UserID   uuid.UUID `json:"user_id" validate:"required"`
	NodeID   uuid.UUID `json:"node_id,omitempty"` // 可选，与 PathID 互斥
	LessonID string    `json:"lesson_id" validate:"required"`
}

// GetAllProgressRequest represents a request to get all lesson progress for a path or node
type GetAllProgressRequest struct {
	UserID uuid.UUID `json:"user_id" validate:"required"`
	NodeID uuid.UUID `json:"node_id,omitempty"` // 可选，与 PathID 互斥
}

// GetAllUserProgressRequest represents a request to get all lesson progress for a user
type GetAllUserProgressRequest struct {
	UserID uuid.UUID `json:"user_id" validate:"required"`
}

// UserLessonProgressResponse represents the response for user lesson progress
type UserLessonProgressResponse struct {
	ID                      uuid.UUID  `json:"id"`
	UserID                  uuid.UUID  `json:"user_id"`
	LessonID                string     `json:"lesson_id"`
	NodeID                  uuid.UUID  `json:"node_id"`
	LearningPathID          uuid.UUID  `json:"learning_path_id"`
	Progress                float64    `json:"progress"`
	IsCompleted             bool       `json:"is_completed"`
	CompletedAt             *time.Time `json:"completed_at"`
	Score                   float64    `json:"score"`
	TimeSpent               int        `json:"time_spent"`
	Attempts                int        `json:"attempts"`
	LastAccessedAt          *time.Time `json:"last_accessed_at"`
	CompletedContentFlowIDs []string   `json:"completed_content_flow_ids"`
	CreatedAt               time.Time  `json:"created_at"`
	UpdatedAt               time.Time  `json:"updated_at"`
}

// GetAllProgressResponse represents the response for all lesson progress
// Key is lesson_id, value is the complete progress data
type GetAllProgressResponse map[string]*UserLessonProgressResponse

// UserLessonProgressService handles user lesson progress operations
type UserLessonProgressService struct {
	repos *repositories.Repositories
}

// NewUserLessonProgressService creates a new user lesson progress service
func NewUserLessonProgressService(repos *repositories.Repositories) *UserLessonProgressService {
	return &UserLessonProgressService{
		repos: repos,
	}
}

// CreateOrGetProgress creates or retrieves user lesson progress
func (s *UserLessonProgressService) CreateOrGetProgress(ctx context.Context, userID uuid.UUID, nodeID uuid.UUID, lessonID string) (*models.UserLessonProgress, error) {

	// Try to get existing progress
	progress, err := s.repos.UserLessonProgress.GetByUserPathNodeLesson(ctx, userID, nodeID, lessonID)
	if err == nil {
		return progress, nil
	}

	// Create new progress if not found
	now := time.Now()
	progress = &models.UserLessonProgress{
		ID:             uuid.New(),
		UserID:         userID,
		LessonID:       lessonID,
		NodeID:         nodeID,
		IsCompleted:    false,
		Score:          0,
		TimeSpent:      0,
		Attempts:       0,
		LastAccessedAt: &now,
		CreatedAt:      now,
		UpdatedAt:      now,
	}

	// Initialize empty completed content flow IDs
	if err := progress.SetCompletedContentFlowIDs([]string{}); err != nil {
		return nil, fmt.Errorf("failed to initialize completed content flow IDs: %w", err)
	}

	if err := s.repos.UserLessonProgress.Create(ctx, progress); err != nil {
		return nil, fmt.Errorf("failed to create user lesson progress: %w", err)
	}

	logging.Info("Created new user lesson progress: user_id=%s, lesson_id=%s, node_id=%s",
		userID, lessonID, nodeID)

	return progress, nil
}

// UpsertCompletedContentFlow adds or updates a ContentFlow item as completed
// 不存在则新增，存在则增量更新，并根据进度更新完成状态
func (s *UserLessonProgressService) UpsertCompletedContentFlow(ctx context.Context, req *UpsertCompletedContentFlowRequest) error {
	if err := s.validateUpsertRequest(req); err != nil {
		return err
	}

	// Add completed content flow (增量更新，如果已存在则不重复添加)
	if err := s.repos.UserLessonProgress.AddCompletedContentFlow(ctx, req.UserID, req.NodeID, req.LessonID, req.ContentFlowID, req.Progress); err != nil {
		return fmt.Errorf("failed to upsert completed content flow: %w", err)
	}

	logging.Info("Upserted completed content flow: user_id=%s, lesson_id=%s, content_flow_id=%s, progress=%.2f",
		req.UserID, req.LessonID, req.ContentFlowID, req.Progress)

	return nil
}

// RemoveCompletedContentFlow marks a ContentFlow item as not completed
// 并根据进度更新完成状态
func (s *UserLessonProgressService) RemoveCompletedContentFlow(ctx context.Context, req *RemoveCompletedContentFlowRequest) error {
	if err := s.validateRemoveRequest(req); err != nil {
		return err
	}

	if err := s.repos.UserLessonProgress.RemoveCompletedContentFlow(ctx, req.UserID, req.NodeID, req.LessonID, req.ContentFlowID, req.Progress); err != nil {
		return fmt.Errorf("failed to remove completed content flow: %w", err)
	}

	logging.Info("Removed completed content flow: user_id=%s, lesson_id=%s, content_flow_id=%s, progress=%.2f",
		req.UserID, req.LessonID, req.ContentFlowID, req.Progress)

	return nil
}

// GetCompletedContentFlows returns the list of completed ContentFlow IDs
func (s *UserLessonProgressService) GetCompletedContentFlows(ctx context.Context, req *GetCompletedContentFlowsRequest) ([]string, error) {
	if err := s.validateGetRequest(req); err != nil {
		return nil, err
	}

	ids, err := s.repos.UserLessonProgress.GetCompletedContentFlows(ctx, req.UserID, req.NodeID, req.LessonID)
	if err != nil {
		return nil, fmt.Errorf("failed to get completed content flows: %w", err)
	}

	return ids, nil
}

// GetProgress returns the user's progress for a specific lesson
func (s *UserLessonProgressService) GetProgress(ctx context.Context, req *GetProgressRequest) (*UserLessonProgressResponse, error) {
	if err := s.validateGetProgressRequest(req); err != nil {
		return nil, err
	}

	progress, err := s.repos.UserLessonProgress.GetByUserPathNodeLesson(ctx, req.UserID, req.NodeID, req.LessonID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user lesson progress: %w", err)
	}

	return &UserLessonProgressResponse{
		ID:                      progress.ID,
		UserID:                  progress.UserID,
		LessonID:                progress.LessonID,
		NodeID:                  progress.NodeID,
		Progress:                progress.Progress,
		IsCompleted:             progress.IsCompleted,
		CompletedAt:             progress.CompletedAt,
		Score:                   progress.Score,
		TimeSpent:               progress.TimeSpent,
		Attempts:                progress.Attempts,
		LastAccessedAt:          progress.LastAccessedAt,
		CompletedContentFlowIDs: progress.GetCompletedContentFlowIDs(),
		CreatedAt:               progress.CreatedAt,
		UpdatedAt:               progress.UpdatedAt,
	}, nil
}

// GetAllProgress returns all lesson progress for a user within a path or node
func (s *UserLessonProgressService) GetAllProgress(ctx context.Context, req *GetAllProgressRequest) (GetAllProgressResponse, error) {
	if err := s.validateGetAllProgressRequest(req); err != nil {
		return nil, err
	}

	// Get all progress records
	records, err := s.repos.UserLessonProgress.GetAllByUserPathNode(ctx, req.UserID, req.NodeID)
	if err != nil {
		return nil, fmt.Errorf("failed to get all user lesson progress: %w", err)
	}

	// Convert to response format: map[lesson_id]*UserLessonProgressResponse
	response := make(GetAllProgressResponse)
	for _, record := range records {
		response[record.LessonID] = &UserLessonProgressResponse{
			ID:                      record.ID,
			UserID:                  record.UserID,
			LessonID:                record.LessonID,
			NodeID:                  record.NodeID,
			Progress:                record.Progress,
			IsCompleted:             record.IsCompleted,
			CompletedAt:             record.CompletedAt,
			Score:                   record.Score,
			TimeSpent:               record.TimeSpent,
			Attempts:                record.Attempts,
			LastAccessedAt:          record.LastAccessedAt,
			CompletedContentFlowIDs: record.GetCompletedContentFlowIDs(),
			CreatedAt:               record.CreatedAt,
			UpdatedAt:               record.UpdatedAt,
		}
	}

	return response, nil
}

// GetAllUserProgress returns all lesson progress for a user across all paths and nodes
func (s *UserLessonProgressService) GetAllUserProgress(ctx context.Context, req *GetAllUserProgressRequest) (GetAllProgressResponse, error) {
	if err := s.validateGetAllUserProgressRequest(req); err != nil {
		return nil, err
	}

	// Get all progress records for the user
	records, err := s.repos.UserLessonProgress.GetAllByUser(ctx, req.UserID)
	if err != nil {
		return nil, fmt.Errorf("failed to get all user lesson progress: %w", err)
	}

	// Convert to response format: map[lesson_id]*UserLessonProgressResponse
	response := make(GetAllProgressResponse)
	for _, record := range records {
		response[record.LessonID] = &UserLessonProgressResponse{
			ID:                      record.ID,
			UserID:                  record.UserID,
			LessonID:                record.LessonID,
			NodeID:                  record.NodeID,
			Progress:                record.Progress,
			IsCompleted:             record.IsCompleted,
			CompletedAt:             record.CompletedAt,
			Score:                   record.Score,
			TimeSpent:               record.TimeSpent,
			Attempts:                record.Attempts,
			LastAccessedAt:          record.LastAccessedAt,
			CompletedContentFlowIDs: record.GetCompletedContentFlowIDs(),
			CreatedAt:               record.CreatedAt,
			UpdatedAt:               record.UpdatedAt,
		}
	}

	return response, nil
}

// validateUpsertRequest validates the upsert completed content flow request
func (s *UserLessonProgressService) validateUpsertRequest(req *UpsertCompletedContentFlowRequest) error {
	if req == nil {
		return fmt.Errorf("request cannot be nil")
	}
	if req.UserID == uuid.Nil {
		return fmt.Errorf("user_id is required")
	}
	if req.NodeID == uuid.Nil {
		return fmt.Errorf("at least one of path_id or node_id must be provided")
	}
	if req.LessonID == "" {
		return fmt.Errorf("lesson_id is required")
	}
	if req.ContentFlowID == "" {
		return fmt.Errorf("content_flow_id is required")
	}
	if req.Progress < 0 || req.Progress > 1 {
		return fmt.Errorf("progress must be between 0 and 1")
	}
	return nil
}

// validateRemoveRequest validates the remove completed content flow request
func (s *UserLessonProgressService) validateRemoveRequest(req *RemoveCompletedContentFlowRequest) error {
	if req == nil {
		return fmt.Errorf("request cannot be nil")
	}
	if req.UserID == uuid.Nil {
		return fmt.Errorf("user_id is required")
	}
	if req.NodeID == uuid.Nil {
		return fmt.Errorf("at least one of path_id or node_id must be provided")
	}
	if req.LessonID == "" {
		return fmt.Errorf("lesson_id is required")
	}
	if req.ContentFlowID == "" {
		return fmt.Errorf("content_flow_id is required")
	}
	if req.Progress < 0 || req.Progress > 1 {
		return fmt.Errorf("progress must be between 0 and 1")
	}
	return nil
}

// validateGetRequest validates the get completed content flows request
func (s *UserLessonProgressService) validateGetRequest(req *GetCompletedContentFlowsRequest) error {
	if req == nil {
		return fmt.Errorf("request cannot be nil")
	}
	if req.UserID == uuid.Nil {
		return fmt.Errorf("user_id is required")
	}
	if req.NodeID == uuid.Nil {
		return fmt.Errorf("at least one of path_id or node_id must be provided")
	}
	if req.LessonID == "" {
		return fmt.Errorf("lesson_id is required")
	}
	return nil
}

// validateGetProgressRequest validates the get progress request
func (s *UserLessonProgressService) validateGetProgressRequest(req *GetProgressRequest) error {
	if req == nil {
		return fmt.Errorf("request cannot be nil")
	}
	if req.UserID == uuid.Nil {
		return fmt.Errorf("user_id is required")
	}
	if req.NodeID == uuid.Nil {
		return fmt.Errorf("at least one of path_id or node_id must be provided")
	}
	if req.LessonID == "" {
		return fmt.Errorf("lesson_id is required")
	}
	return nil
}

// validateGetAllProgressRequest validates the get all progress request
func (s *UserLessonProgressService) validateGetAllProgressRequest(req *GetAllProgressRequest) error {
	if req == nil {
		return fmt.Errorf("request cannot be nil")
	}
	if req.UserID == uuid.Nil {
		return fmt.Errorf("user_id is required")
	}
	if req.NodeID == uuid.Nil {
		return fmt.Errorf("at least one of path_id or node_id must be provided")
	}
	return nil
}

// validateGetAllUserProgressRequest validates the get all user progress request
func (s *UserLessonProgressService) validateGetAllUserProgressRequest(req *GetAllUserProgressRequest) error {
	if req == nil {
		return fmt.Errorf("request cannot be nil")
	}
	if req.UserID == uuid.Nil {
		return fmt.Errorf("user_id is required")
	}
	return nil
}
