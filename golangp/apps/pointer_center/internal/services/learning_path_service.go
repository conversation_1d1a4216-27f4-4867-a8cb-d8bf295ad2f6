package services

import (
	"context"
	"encoding/json"
	"fmt"
	"regexp"
	"strings"
	"time"

	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/internal/models"
	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/internal/repositories"
	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/pkg/ai_agents"
	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/pkg/utils"
	"github.com/Arxtect/CoreXWorkSpace/golangp/common/logging"

	"github.com/google/uuid"
	"gorm.io/datatypes"
)

// LearningPathService handles business logic for learning paths
type LearningPathService struct {
	repos     *repositories.Repositories
	aiManager *ai_agents.Manager
}

// NewLearningPathService creates a new learning path service
func NewLearningPathService(repos *repositories.Repositories, aiManager *ai_agents.Manager) *LearningPathService {
	return &LearningPathService{
		repos:     repos,
		aiManager: aiManager,
	}
}

// CreateLearningPath creates a new learning path
func (s *LearningPathService) CreateLearningPath(ctx context.Context, req *CreateLearningPathRequest) (*models.LearningPath, error) {
	// Validate request
	if err := s.validateCreateRequest(req); err != nil {
		return nil, fmt.Errorf("validation failed: %w", err)
	}

	// Convert arrays to JSON strings
	suitableForJSON, _ := json.Marshal(req.SuitableFor)

	// Create learning path model
	path := &models.LearningPath{
		ID:               uuid.New(),
		CreatorID:        req.CreatedBy,
		UserID:           req.CreatedBy,
		Title:            req.Title,
		Description:      req.Description,
		Goal:             req.LearningGoal,
		GoalCategory:     req.GoalCategory,
		PathType:         "custom",
		Difficulty:       models.DifficultyLevel(req.DifficultyLevel),
		EstimatedTimes:   req.EstimatedTimes,
		SuitableFor:      datatypes.JSON(suitableForJSON),
		LearningOutcomes: datatypes.JSON(mustJSON(req.LearningOutcomes)),
		IsPublic:         req.IsPublic,
		CreatedAt:        time.Now(),
		UpdatedAt:        time.Now(),
	}

	// Save to database
	if err := s.repos.LearningPath.Create(ctx, path); err != nil {
		logging.Error("Failed to create learning path: %v, path_id: %v", err, path.ID)
		return nil, fmt.Errorf("failed to create learning path: %w", err)
	}

	// If tags provided, ensure tags exist and associate to the path (idempotent)
	if len(req.Tags) > 0 {
		if err := s.ensureTagsForPath(ctx, path.ID, req.CreatedBy, req.Tags); err != nil {
			logging.Warning("ensureTagsForPath on create failed: path=%s err=%v", path.ID, err)
		}
	}

	logging.Info("Learning path created successfully, path_id: %v, title: %s", path.ID, path.Title)
	return path, nil
}

// GenerateLearningPathWithAI generates a learning path using AI
func (s *LearningPathService) GenerateLearningPathWithAI(ctx context.Context, req *GeneratePathRequest) (*models.LearningPath, error) {
	// 1) 参数校验
	if err := s.validateGenerateRequest(req); err != nil {
		return nil, fmt.Errorf("validation failed: %w", err)
	}

	// 2) 先创建一个占位的路径（立即返回给前端用）
	now := time.Now()
	placeholder := &models.LearningPath{
		ID:             uuid.New(),
		CreatorID:      AI_ID,
		UserID:         req.UserID,
		Goal:           req.Goal,
		GoalCategory:   "",
		Title:          "正在生成的学习路径",
		Description:    "",
		PathType:       "personalized",
		Difficulty:     models.DifficultyLevel(5),
		EstimatedTimes: 0,
		SuitableFor:    datatypes.JSON(mustJSON([]string{})),
		LearningOutcomes: datatypes.JSON(mustJSON([]string{})),
		IsPublic:       false,
		CreatedAt:      now,
		UpdatedAt:      now,
		ForkFrom:         uuid.Nil,
		ProgressStatus:   models.StatusGenerating,
		StartedAt:        &now,
		LastAccessedAt:   &now,
	}
	// fork_from set to self for native created path
	placeholder.ForkFrom = placeholder.ID
	if err := s.repos.LearningPath.Create(ctx, placeholder); err != nil {
		logging.Error("Failed to create placeholder path: %v, user_id: %v", err, req.UserID)
		return nil, fmt.Errorf("failed to create placeholder path: %w", err)
	}

	// 4) 后台异步运行 AI 生成流程（manager 内部有重试机制）
	go func(pathID, userID uuid.UUID, goal string) {
		// 使用独立上下文，避免 HTTP 超时取消
		bctx := context.Background()
		defer func() {
			if r := recover(); r != nil {
				logging.Error("panic in GenerateLearningPathWithAI async: %v", r)
			}
		}()

		// 拉取用户画像
		current_map, err := s.aiManager.Tools().GetStudentProfile(bctx, userID)
		if err != nil {
			logging.Error("Failed to get user tech competency: %v, user_id: %v", err, userID)
			// 标记失败
			_ = s.repos.LearningPath.UpdateProgressStatus(bctx, pathID, models.StatusFailed)
			return
		}
		cureent_path, err := s.GetUserMasterPath(bctx, userID)
		if err != nil {
			logging.Error("Failed to get user master path: %v, user_id: %v", err, userID)
			// 不中断，继续
		}
		inputs := map[string]interface{}{
			"goal":         goal,
			"current_map":  current_map,
			"current_path": cureent_path,
		}

		aiResponse, err := s.aiManager.GenerateLearningPath(bctx, inputs)
		if err != nil || aiResponse == nil || !aiResponse.Success {
			logging.Error("AI path generation failed: err=%v, aiErr=%v, user_id=%v", err, func() interface{} { if aiResponse!=nil {return aiResponse.Error}; return nil }(), userID)
			_ = s.repos.LearningPath.UpdateProgressStatus(bctx, pathID, models.StatusFailed)
			return
		}

		// 解析 AI 返回并更新占位路径
		parsed, err := s.parseAIResponseToPath(aiResponse.Data, &GeneratePathRequest{Goal: goal, UserID: userID})
		if err != nil {
			logging.Error("Failed to parse AI response: %v, user_id: %v", err, userID)
			_ = s.repos.LearningPath.UpdateProgressStatus(bctx, pathID, models.StatusFailed)
			return
		}
		// 加载占位路径并覆盖字段
		path, err := s.repos.LearningPath.GetByID(bctx, pathID)
		if err == nil && path != nil {
			path.Title = parsed.Title
			path.Description = parsed.Description
			path.PathType = parsed.PathType
			path.Difficulty = parsed.Difficulty
			path.EstimatedTimes = parsed.EstimatedTimes
			path.SuitableFor = parsed.SuitableFor
			path.LearningOutcomes = parsed.LearningOutcomes
			path.UpdatedAt = time.Now()
			if uerr := s.repos.LearningPath.Update(bctx, path); uerr != nil {
				logging.Warning("update path fields failed: %v", uerr)
			}
		}

		// 接收 AI 返回的 tags，并建立标签与路径的关联；若标签不存在则自动创建
		var parsedForTags AILearningPathResponse
		if err := utils.ParseAIAnswerField(aiResponse.Data, &parsedForTags); err == nil {
			if len(parsedForTags.Tags) > 0 {
				if err := s.ensureTagsForPath(bctx, pathID, userID, parsedForTags.Tags); err != nil {
					logging.Warning("ensure tags for path failed: path=%s err=%v", pathID, err)
				}
			}
		}

		// 处理节点并建立关联/进度脚手架
		if err := s.processAIResponseNodes(bctx, aiResponse.Data, pathID, userID); err != nil {
			logging.Error("Failed to process AI response nodes: %v, path_id: %v", err, pathID)
			// 标记路径进度失败（进度已合并到 LearningPath 上）
			_ = s.repos.LearningPath.UpdateProgressStatus(bctx, pathID, models.StatusFailed)
			return
		}

		// 生成完成，进度状态=active
	_ = s.repos.LearningPath.UpdateProgressStatus(bctx, pathID, models.StatusActive)
	}(placeholder.ID, req.UserID, req.Goal)

	// 5) 立即返回占位路径
	return placeholder, nil
}

// UpdateLearningPath updates an existing learning path
func (s *LearningPathService) UpdateLearningPath(ctx context.Context, pathID uuid.UUID, req *UpdateLearningPathRequest) (*models.LearningPath, error) {
	// Get existing path
	existingPath, err := s.repos.LearningPath.GetByID(ctx, pathID)
	if err != nil {
		return nil, fmt.Errorf("failed to get learning path: %w", err)
	}

	// Update fields
	if req.Title != nil {
		existingPath.Title = *req.Title
	}
	if req.Description != nil {
		existingPath.Description = *req.Description
	}
	if req.Goal != nil {
		existingPath.Goal = *req.Goal
	}
	if req.GoalCategory != nil {
		existingPath.GoalCategory = *req.GoalCategory
	}
	if req.Difficulty != nil {
		existingPath.Difficulty = models.DifficultyLevel(*req.Difficulty)
	}
	if req.EstimatedTimes != nil {
		existingPath.EstimatedTimes = *req.EstimatedTimes
	}
	if req.SuitableFor != nil {
	suitableForJSON, _ := json.Marshal(req.SuitableFor)
	existingPath.SuitableFor = datatypes.JSON(suitableForJSON)
	}
	if req.LearningOutcomes != nil {
	existingPath.LearningOutcomes = datatypes.JSON(mustJSON(req.LearningOutcomes))
	}
	if req.IsPublic != nil {
		existingPath.IsPublic = *req.IsPublic
	}

	existingPath.UpdatedAt = time.Now()

	// Save updates
	if err := s.repos.LearningPath.Update(ctx, existingPath); err != nil {
		logging.Error("Failed to update learning path: %v, path_id: %v", err, pathID)
		return nil, fmt.Errorf("failed to update learning path: %w", err)
	}

	// Append/associate provided tags to this path if any (idempotent; does not remove existing)
	if len(req.Tags) > 0 {
		addedBy := existingPath.UserID
		if addedBy == uuid.Nil {
			addedBy = existingPath.CreatorID
		}
		if err := s.ensureTagsForPath(ctx, pathID, addedBy, req.Tags); err != nil {
			logging.Warning("ensureTagsForPath on update failed: path=%s err=%v", pathID, err)
		}
	}

	logging.Info("Learning path updated successfully, path_id: %v", pathID)
	return existingPath, nil
}

// DeleteLearningPath soft deletes a learning path
func (s *LearningPathService) DeleteLearningPath(ctx context.Context, pathID uuid.UUID) error {
	// Check if path exists
	_, err := s.repos.LearningPath.GetByID(ctx, pathID)
	if err != nil {
		return fmt.Errorf("learning path not found: %w", err)
	}

	// Use regular delete since repository doesn't have SoftDelete method
	if err := s.repos.LearningPath.Delete(ctx, pathID); err != nil {
		logging.Error("Failed to delete learning path: %v, path_id: %v", err, pathID)
		return fmt.Errorf("failed to delete learning path: %w", err)
	}

	logging.Info("Learning path deleted successfully, path_id: %v", pathID)
	return nil
}

// GetLearningPath retrieves a learning path by ID
func (s *LearningPathService) GetLearningPath(ctx context.Context, pathID uuid.UUID) (*models.LearningPath, error) {
	path, err := s.repos.LearningPath.GetByID(ctx, pathID)
	if err != nil {
		return nil, fmt.Errorf("failed to get learning path: %w", err)
	}
	return path, nil
}

// GetLearningPaths retrieves learning paths with pagination
func (s *LearningPathService) GetLearningPaths(ctx context.Context, req *GetLearningPathsRequest) ([]*models.LearningPath, int64, error) {
	// Set default pagination
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	// Calculate offset
	offset := (req.Page - 1) * req.PageSize

	// Get paths
	paths, err := s.repos.LearningPath.List(ctx, req.PageSize, offset)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get learning paths: %w", err)
	}

	// Get total count
	total, err := s.repos.LearningPath.Count(ctx)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get total count: %w", err)
	}

	return paths, total, nil
}

// GetPublicLearningPaths returns only public learning paths with pagination
func (s *LearningPathService) GetPublicLearningPaths(ctx context.Context, page, pageSize int) ([]*models.LearningPath, int64, error) {
	if page <= 0 { page = 1 }
	if pageSize <= 0 { pageSize = 20 }
	offset := (page - 1) * pageSize
	paths, err := s.repos.LearningPath.GetPublicPaths(ctx, pageSize, offset)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get public learning paths: %w", err)
	}
	total, err := s.repos.LearningPath.CountPublic(ctx)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count public learning paths: %w", err)
	}
	return paths, total, nil
}

// GetPublicLearningPathsByTags returns public paths filtered by tag names with AND/OR semantics
func (s *LearningPathService) GetPublicLearningPathsByTags(ctx context.Context, page, pageSize int, tagNames []string, matchAll bool) ([]*models.LearningPath, int64, error) {
	if page <= 0 { page = 1 }
	if pageSize <= 0 { pageSize = 20 }
	offset := (page - 1) * pageSize
	paths, err := s.repos.LearningPath.GetPublicPathsByTags(ctx, tagNames, matchAll, pageSize, offset)
	if err != nil { return nil, 0, fmt.Errorf("failed to get public learning paths by tags: %w", err) }
	total, err := s.repos.LearningPath.CountPublicByTags(ctx, tagNames, matchAll)
	if err != nil { return nil, 0, fmt.Errorf("failed to count public learning paths by tags: %w", err) }
	return paths, total, nil
}

// GetLearningPathsByUser retrieves learning paths for a specific user (assignee/owner)
func (s *LearningPathService) GetLearningPathsByUser(ctx context.Context, userID uuid.UUID, page, pageSize int) ([]*models.LearningPath, int64, error) {
	if page <= 0 { page = 1 }
	if pageSize <= 0 { pageSize = 20 }
	offset := (page - 1) * pageSize
	paths, err := s.repos.LearningPath.ListByUser(ctx, userID, pageSize, offset)
	if err != nil { return nil, 0, fmt.Errorf("failed to get learning paths by user: %w", err) }
	// total count for this user
	all, err := s.repos.LearningPath.ListByUser(ctx, userID, 1_000_000, 0)
	if err != nil { return nil, 0, fmt.Errorf("failed to count learning paths by user: %w", err) }
	return paths, int64(len(all)), nil
}

// GetLearningPathsByCreator retrieves learning paths by creator (author)
func (s *LearningPathService) GetLearningPathsByCreator(ctx context.Context, creatorID uuid.UUID, page, pageSize int) ([]*models.LearningPath, int64, error) {
	if page <= 0 { page = 1 }
	if pageSize <= 0 { pageSize = 20 }
	offset := (page - 1) * pageSize
	paths, err := s.repos.LearningPath.ListByCreator(ctx, creatorID, pageSize, offset)
	if err != nil { return nil, 0, fmt.Errorf("failed to get learning paths by creator: %w", err) }
	all, err := s.repos.LearningPath.ListByCreator(ctx, creatorID, 1_000_000, 0)
	if err != nil { return nil, 0, fmt.Errorf("failed to count learning paths by creator: %w", err) }
	return paths, int64(len(all)), nil
}

// ensureTagsForPath ensures tags exist and associates them with a path (idempotent)
func (s *LearningPathService) ensureTagsForPath(ctx context.Context, pathID uuid.UUID, addedBy uuid.UUID, tagNames []string) error {
	if pathID == uuid.Nil || len(tagNames) == 0 {
		return nil
	}
	// normalize and dedupe
	seen := make(map[string]struct{})
	names := make([]string, 0, len(tagNames))
	for _, t := range tagNames {
		name := strings.TrimSpace(t)
		if name == "" {
			continue
		}
		key := strings.ToLower(name)
		if _, ok := seen[key]; ok {
			continue
		}
		seen[key] = struct{}{}
		names = append(names, name)
	}
	if len(names) == 0 {
		return nil
	}

	creator := addedBy
	if creator == uuid.Nil {
		creator = AI_ID
	}

	tagIDs := make([]uuid.UUID, 0, len(names))
	for _, name := range names {
		// Check existence by name
		exists, err := s.repos.Tag.ExistsByName(ctx, name)
		if err != nil {
			return err
		}
		if !exists {
			// Create new tag
			tag := &models.Tag{
				ID:          uuid.New(),
				Name:        name,
				DisplayName: name,
				Description: "",
				TagType:     models.TagTypeGeneral,
				Color:       "",
				Icon:        "",
				UsageCount:  0,
				IsSystem:    false,
				IsActive:    true,
				ParentID:    nil,
				Level:       0,
				SortOrder:   0,
				CreatedBy:   creator,
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			}
			if err := s.repos.Tag.Create(ctx, tag); err != nil {
				// If creation fails due to race/unique, try to load by name
				if found, gerr := s.repos.Tag.GetByName(ctx, name); gerr == nil && found != nil {
					tagIDs = append(tagIDs, found.ID)
					continue
				}
				return err
			}
			tagIDs = append(tagIDs, tag.ID)
		} else {
			if tag, err := s.repos.Tag.GetByName(ctx, name); err == nil && tag != nil {
				tagIDs = append(tagIDs, tag.ID)
			}
		}
	}
	if len(tagIDs) == 0 {
		return nil
	}
	// Associate to path (idempotent at row level)
	if err := s.repos.LearningPathTag.AddTagsToPath(ctx, pathID, tagIDs, creator); err != nil {
		return err
	}
	// Increment usage for tags
	for _, id := range tagIDs {
		_ = s.repos.Tag.IncrementUsage(ctx, id)
	}
	return nil
}

// SubscribePath clones a public path for a user: copy base fields, keep CreatorID, set UserID and ForkFrom,
// increment source UsageCount, reuse nodes via LearningPathNode, and create per-node UserNodeProgress for the user.
func (s *LearningPathService) SubscribePath(ctx context.Context, userID uuid.UUID, sourcePathID uuid.UUID) (*models.LearningPath, error) {
	if userID == uuid.Nil { return nil, fmt.Errorf("user_id is required") }

	var result *models.LearningPath
	err := s.repos.InTransaction(ctx, func(txRepos *repositories.Repositories) error {
		// 1) Load source path
		src, err := txRepos.LearningPath.GetByID(ctx, sourcePathID)
		if err != nil { return fmt.Errorf("source path not found: %w", err) }

		// 2) Idempotency: if user already has a path forked from this source, return it directly
		existing, err := txRepos.LearningPath.ListByUser(ctx, userID, 1000, 0)
		if err == nil {
			for _, p := range existing {
				if p != nil && p.ForkFrom == src.ID {
					result = p
					return nil
				}
			}
		}

		// 3) Create new path copying base info; keep CreatorID, set UserID and ForkFrom
		now := time.Now()
		newPath := &models.LearningPath{
			ID:               uuid.New(),
			CreatorID:        src.CreatorID,
			UserID:           userID,
			ForkFrom:         src.ID,
			Goal:             src.Goal,
			GoalCategory:     src.GoalCategory,
			Title:            src.Title,
			Description:      src.Description,
			PathType:         src.PathType,
			Difficulty:       src.Difficulty,
			EstimatedTimes:   src.EstimatedTimes,
			TotalNodes:       src.TotalNodes,
			SuitableFor:      src.SuitableFor,
			LearningOutcomes: src.LearningOutcomes,
			ProgressStatus:   models.StatusActive,
			IsPublic:         false,
			UsageCount:       0,
			Rating:           0,
			CreatedAt:        now,
			UpdatedAt:        now,
		}
		if err := txRepos.LearningPath.Create(ctx, newPath); err != nil {
			return fmt.Errorf("failed to create subscribed path: %w", err)
		}
		// 4) Increment source path usage count
		if err := txRepos.LearningPath.IncrementUsageCount(ctx, src.ID); err != nil {
			return err
		}
		// 5) Copy associations: for each node in source path, create LearningPathNode for the new path (reuse node IDs)
		srcNodes, err := txRepos.LearningPathNode.GetByPathID(ctx, src.ID)
		if err != nil { return fmt.Errorf("failed to list source path nodes: %w", err) }
		for _, pn := range srcNodes {
			assoc := &models.LearningPathNode{
				ID:             uuid.New(),
				LearningPathID: newPath.ID,
				NodeID:         pn.NodeID,
				CreatedAt:      now,
				UpdatedAt:      now,
			}
			if err := txRepos.LearningPathNode.Create(ctx, assoc); err != nil {
				return fmt.Errorf("failed to create path-node association: %w", err)
			}
			// For each node, create or increment user node progress
			if _, err := txRepos.UserNodeProgress.IncrementRef(ctx, userID, pn.NodeID, 1); err != nil {
				return fmt.Errorf("failed to init user node progress: %w", err)
			}
		}
		result = newPath
		return nil
	})
	if err != nil { return nil, err }
	return result, nil
}

// RemovePathForUser removes a learning path association for a user and updates progress/ref-count
func (s *LearningPathService) RemovePathForUser(ctx context.Context, userID, pathID uuid.UUID) error {
	// 1) Decrement ref_count for all nodes in this path for the user
	nodes, err := s.repos.LearningPathNode.GetByPathID(ctx, pathID)
	if err != nil {
		return fmt.Errorf("failed to list path nodes: %w", err)
	}
	for _, pn := range nodes {
		// Decrement ref_count by 1 for this user+node
		newCount, err := s.repos.UserNodeProgress.IncrementRef(ctx, userID, pn.NodeID, -1)
		if err != nil {
			logging.Warning("decrement ref_count failed: user=%s path=%s node=%s err=%v", userID, pathID, pn.NodeID, err)
		} else if newCount <= 0 {
			// Delete the user-node record only if ref_count <= 0
			_ = s.repos.UserNodeProgress.DeleteByUserNode(ctx, userID, pn.NodeID)
		}
		// Clean lesson progresses under this node for the path
		_ = s.repos.UserLessonProgress.DeleteByUserPathNode(ctx, userID, pathID, pn.NodeID)
	}
	// 2) 标记该路径进度为取消
	if err := s.repos.LearningPath.UpdateProgressStatus(ctx, pathID, models.StatusCancelled); err != nil {
		logging.Warning("cancel path progress failed: path=%s err=%v", pathID, err)
	}
	return nil
}

func (s *LearningPathService) GetUserMasterPath(ctx context.Context, userID uuid.UUID) (*models.MasterPath, error) {
	if userID == uuid.Nil {
		return nil, fmt.Errorf("user_id is required")
	}

	// 1) 获取该用户归属的所有学习路径（排除已取消），区别于创建者ID查询
	paths, err := s.repos.LearningPath.ListByUser(ctx, userID, 1000, 0)
	if err != nil {
		return nil, fmt.Errorf("failed to list user paths: %w", err)
	}

	// 2) 汇总这些路径关联的节点，去重
	nodeMap := make(map[string]models.PureNode)
	for _, p := range paths {
		if p == nil || p.ProgressStatus == models.StatusCancelled {
			continue
		}
		nodes, err := s.repos.LearningPathNode.GetNodesForPath(ctx, p.ID)
		if err != nil {
			logging.Warning("GetNodesForPath failed: path=%s err=%v", p.ID, err)
			continue
		}
		for _, n := range nodes {
			if n == nil { continue }
			idStr := n.ID.String()
			if _, ok := nodeMap[idStr]; ok {
				continue
			}
			nodeMap[idStr] = models.PureNode{
				ID:            idStr,
				Title:         n.Title,
				Description:   n.Description,
				Prerequisites: n.Prerequisites,
			}
		}
	}

	// 3) 构建 MasterPath 返回
	out := make([]models.PureNode, 0, len(nodeMap))
	for _, v := range nodeMap { out = append(out, v) }
	now := time.Now()
	return &models.MasterPath{ Nodes: out, CreatedAt: now, UpdatedAt: now }, nil
}

// GetUserNodesByPath returns the user's node list for a path via UserNodeProgress; falls back to path-node when missing
func (s *LearningPathService) GetUserNodesByPath(ctx context.Context, userID, pathID uuid.UUID) ([]*models.LearningNode, error) {
	// Use path-node associations to get node list for a path
	return s.repos.LearningPathNode.GetNodesForPath(ctx, pathID)
}

// MarkPathCompleted marks a learning path as completed for a user
func (s *LearningPathService) MarkPathCompleted(ctx context.Context, userID uuid.UUID, pathID uuid.UUID) error {
	// TODO: 标记路径状态为完成
	return nil
}

// Helper methods
func (s *LearningPathService) validateCreateRequest(req *CreateLearningPathRequest) error {
	if req.Title == "" {
		return fmt.Errorf("title is required")
	}
	if req.CreatedBy == uuid.Nil {
		return fmt.Errorf("created_by is required")
	}
	if req.DifficultyLevel < 1 || req.DifficultyLevel > 10 {
		return fmt.Errorf("difficulty_level must be between 1 and 10")
	}
	return nil
}

func (s *LearningPathService) validateGenerateRequest(req *GeneratePathRequest) error {
	if req.Goal == "" {
		return fmt.Errorf("goal is required")
	}
	if req.UserID == uuid.Nil {
		return fmt.Errorf("user_id is required")
	}
	return nil
}

func (s *LearningPathService) parseAIResponseToPath(aiData map[string]interface{}, req *GeneratePathRequest) (*models.LearningPath, error) {
	// Extract answer field from AI response
	var aiResponse AILearningPathResponse
	if err := utils.ParseAIAnswerField(aiData, &aiResponse); err != nil {
		return nil, err
	}

	path := &models.LearningPath{
		ID:        uuid.New(),
		CreatorID: AI_ID,
		PathType:  "personalized",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// Parse AI response fields
	if aiResponse.Title != "" {
		path.Title = aiResponse.Title
	} else {
		path.Title = "AI Generated Learning Path"
	}

	path.Description = aiResponse.Description
	path.Goal = req.Goal
	path.EstimatedTimes = aiResponse.EstimatedTimes
	// SuitableFor: allow AI to return string or array; wrap as JSON
	if aiResponse.SuitableFor != "" {
		path.SuitableFor = datatypes.JSON(mustJSON(aiResponse.SuitableFor))
	}
	// LearningOutcomes -> JSON array
	if len(aiResponse.LearningOutcomes) > 0 {
		path.LearningOutcomes = datatypes.JSON(mustJSON([]string(aiResponse.LearningOutcomes)))
	}

	// Handle path_type array - use it to set difficulty or other properties
	if len(aiResponse.PathType) > 0 {
		// Could use path_type to determine difficulty level
		path.Difficulty = models.DifficultyLevel(5) // Default
	} else {
		path.Difficulty = models.DifficultyLevel(5)
	}

	// AI-generated paths are initially private
	path.IsPublic = false

	return path, nil
}

// mustJSON marshals v to JSON; returns empty JSON null on error.
func mustJSON(v interface{}) []byte {
	b, err := json.Marshal(v)
	if err != nil {
		return []byte("null")
	}
	return b
}

// processAIResponseNodes processes nodes from AI response
func (s *LearningPathService) processAIResponseNodes(ctx context.Context, aiData map[string]interface{}, pathID uuid.UUID, userID uuid.UUID) error {
	// Extract answer field from AI response
	var aiResponse AILearningPathResponse
	if err := utils.ParseAIAnswerField(aiData, &aiResponse); err != nil {
		return err
	}

	// First pass: create/update/reuse nodes without prerequisites, build externalID -> UUID map, and create path associations
	extToUUID := make(map[string]uuid.UUID)
	nodeOrder := 0
	for _, aiNode := range aiResponse.Nodes {
		nodeOrder++

		var nodeID uuid.UUID
		var node *models.LearningNode
		// If AI provided a valid UUID that exists, prefer to use it directly
		var providedUUID uuid.UUID
		hasProvidedUUID := false
		if parsed, err := uuid.Parse(aiNode.NodeID); err == nil {
			providedUUID = parsed
			hasProvidedUUID = true
		}
		existsByUUID := false
		if hasProvidedUUID {
			if ok, err := s.repos.LearningNode.ExistsByID(ctx, providedUUID); err == nil && ok {
				existsByUUID = true
			} else if err != nil {
				return fmt.Errorf("failed to check node existence for UUID %s: %w", providedUUID, err)
			}
		}

		switch aiNode.Status {
		case "新增":
			if existsByUUID {
				// Treat as update to the existing node referenced by UUID
				found, err := s.repos.LearningNode.GetByID(ctx, providedUUID)
				if err == nil && found != nil {
					found.Title = aiNode.NodeName
					found.Description = aiNode.CoreObjective
					found.EstimatedTimes = aiNode.EstimatedTimes
					found.UpdatedAt = time.Now()
					_ = s.repos.LearningNode.Update(ctx, found)
					nodeID = found.ID
				} else {
					nodeID = providedUUID
				}
			} else {
				nodeID = uuid.New()
				node = &models.LearningNode{
					ID:             nodeID,
					Title:          aiNode.NodeName,
					Description:    aiNode.CoreObjective,
					EstimatedTimes: aiNode.EstimatedTimes,
					Prerequisites:  []string{},
					CreatedBy:      AI_ID,
					UpdatedBy:      AI_ID,
					CreatedAt:      time.Now(),
					UpdatedAt:      time.Now(),
				}
				if err := s.repos.LearningNode.Create(ctx, node); err != nil {
					logging.Error("Failed to create new node: %v, nodeId: %s", err, aiNode.NodeID)
					continue
				}
			}
		case "调整":
			if hasProvidedUUID && !existsByUUID {
				return fmt.Errorf("AI provided nodeId UUID not found: %s (status=调整, name=%s)", providedUUID, aiNode.NodeName)
			}
			if existsByUUID {
				// Update the existing node referenced by UUID
				found, err := s.repos.LearningNode.GetByID(ctx, providedUUID)
				if err == nil && found != nil {
					found.Title = aiNode.NodeName
					found.Description = aiNode.CoreObjective
					found.EstimatedTimes = aiNode.EstimatedTimes
					found.UpdatedAt = time.Now()
					if err := s.repos.LearningNode.Update(ctx, found); err != nil {
						logging.Error("Failed to update existing node by UUID: %v, nodeUUID: %s", err, providedUUID)
					}
					nodeID = found.ID
				} else {
					// If cannot fetch, fallback to name matching then create
					foundByName, err2 := s.findNodeByNameExact(ctx, aiNode.NodeName)
					if err2 == nil && foundByName != nil {
						foundByName.Title = aiNode.NodeName
						foundByName.Description = aiNode.CoreObjective
						foundByName.EstimatedTimes = aiNode.EstimatedTimes
						foundByName.UpdatedAt = time.Now()
						_ = s.repos.LearningNode.Update(ctx, foundByName)
						nodeID = foundByName.ID
					} else {
						nodeID = uuid.New()
						node = &models.LearningNode{
							ID:             nodeID,
							Title:          aiNode.NodeName,
							Description:    aiNode.CoreObjective,
							EstimatedTimes: aiNode.EstimatedTimes,
							Prerequisites:  []string{},
							UpdatedBy:      AI_ID,
							CreatedAt:      time.Now(),
							UpdatedAt:      time.Now(),
						}
						if err := s.repos.LearningNode.Create(ctx, node); err != nil {
							logging.Error("Failed to create node for 调整: %v, nodeId: %s", err, aiNode.NodeID)
							continue
						}
					}
				}
			} else {
				// Try find existing node by exact name
				found, err := s.findNodeByNameExact(ctx, aiNode.NodeName)
				if err == nil && found != nil {
					found.Title = aiNode.NodeName
					found.Description = aiNode.CoreObjective
					found.EstimatedTimes = aiNode.EstimatedTimes
					found.UpdatedAt = time.Now()
					if err := s.repos.LearningNode.Update(ctx, found); err != nil {
						logging.Error("Failed to update existing node: %v, nodeName: %s", err, aiNode.NodeName)
					}
					nodeID = found.ID
				} else {
					// Fallback: create new if not found
					nodeID = uuid.New()
					node = &models.LearningNode{
						ID:             nodeID,
						Title:          aiNode.NodeName,
						Description:    aiNode.CoreObjective,
						EstimatedTimes: aiNode.EstimatedTimes,
						Prerequisites:  []string{},
						UpdatedBy:      AI_ID,
						CreatedAt:      time.Now(),
						UpdatedAt:      time.Now(),
					}
					if err := s.repos.LearningNode.Create(ctx, node); err != nil {
						logging.Error("Failed to create node for 调整: %v, nodeId: %s", err, aiNode.NodeID)
						continue
					}
				}
			}
		case "重用":
			if hasProvidedUUID && !existsByUUID {
				return fmt.Errorf("AI provided nodeId UUID not found: %s (status=重用, name=%s)", providedUUID, aiNode.NodeName)
			}
			if existsByUUID {
				nodeID = providedUUID
			} else {
				// Try find existing node by exact name, do not modify fields
				found, err := s.findNodeByNameExact(ctx, aiNode.NodeName)
				if err == nil && found != nil {
					nodeID = found.ID
				} else {
					// Fallback: create new for association
					nodeID = uuid.New()
					node = &models.LearningNode{
						ID:             nodeID,
						Title:          aiNode.NodeName,
						Description:    aiNode.CoreObjective,
						EstimatedTimes: aiNode.EstimatedTimes,
						Prerequisites:  []string{},
						UpdatedBy:      AI_ID,
						CreatedAt:      time.Now(),
						UpdatedAt:      time.Now(),
					}
					if err := s.repos.LearningNode.Create(ctx, node); err != nil {
						logging.Error("Failed to create node for 重用: %v, nodeId: %s", err, aiNode.NodeID)
						continue
					}
				}
			}
		default:
			logging.Error("Unknown node status: %s for nodeId: %s", aiNode.Status, aiNode.NodeID)
			nodeID = uuid.New()
			node = &models.LearningNode{
				ID:             nodeID,
				Title:          aiNode.NodeName,
				Description:    aiNode.CoreObjective,
				EstimatedTimes: aiNode.EstimatedTimes,
				Prerequisites:  []string{},
				UpdatedBy:      AI_ID,
				CreatedAt:      time.Now(),
				UpdatedAt:      time.Now(),
			}
			if err := s.repos.LearningNode.Create(ctx, node); err != nil {
				logging.Error("Failed to create new node (default): %v, nodeId: %s", err, aiNode.NodeID)
				continue
			}
		}

		// Path association
		pathNode := &models.LearningPathNode{
			ID:             uuid.New(),
			LearningPathID: pathID,
			NodeID:         nodeID,
			CreatedAt:      time.Now(),
			UpdatedAt:      time.Now(),
		}
		if err := s.repos.LearningPathNode.Create(ctx, pathNode); err != nil {
			logging.Error("Failed to create learning path node association: %v, nodeId: %s", err, aiNode.NodeID)
			// continue even if association fails
		}

		if userID != uuid.Nil {
			// Increase user node ref_count for this node under the user context
			_, _ = s.repos.UserNodeProgress.IncrementRef(ctx, userID, nodeID, 1)
		}

		// Build mapping
		extToUUID[aiNode.NodeID] = nodeID
	}

	// Second pass: update prerequisites using mapped UUIDs
	for _, aiNode := range aiResponse.Nodes {
		nodeUUID, ok := extToUUID[aiNode.NodeID]
		if !ok {
			continue
		}
		mapped := make([]string, 0, len(aiNode.PrerequisiteNodeIds))
		for _, ext := range aiNode.PrerequisiteNodeIds {
			if uid, ok := extToUUID[ext]; ok {
				mapped = append(mapped, uid.String())
				continue
			}
			// Accept direct UUIDs when valid and existing
			if parsed, err := uuid.Parse(ext); err == nil {
				if ok, err := s.repos.LearningNode.ExistsByID(ctx, parsed); err == nil && ok {
					mapped = append(mapped, parsed.String())
				} else if err == nil && !ok {
					return fmt.Errorf("AI provided prerequisite UUID not found: %s for nodeId=%s", parsed, aiNode.NodeID)
				} else if err != nil {
					return fmt.Errorf("failed to check prerequisite UUID %s existence: %w", parsed, err)
				}
			}
		}
		if err := s.updateNodePrereqs(ctx, nodeUUID, mapped); err != nil {
			logging.Error("Failed to update node prerequisites: %v, nodeId: %s", err, aiNode.NodeID)
			continue
		}
	}

	return nil
}

// findNodeByNameExact tries to find an existing node by exact title
func (s *LearningPathService) findNodeByNameExact(ctx context.Context, name string) (*models.LearningNode, error) {
	nodes, err := s.repos.LearningNode.Search(ctx, name, 10, 0)
	if err != nil {
		return nil, err
	}
	for _, n := range nodes {
		if n.Title == name {
			return n, nil
		}
	}
	return nil, fmt.Errorf("node not found by name: %s", name)
}

// updateNodePrereqs updates the prerequisites for a node by UUID
func (s *LearningPathService) updateNodePrereqs(ctx context.Context, nodeID uuid.UUID, prereqUUIDStrings []string) error {
	node, err := s.repos.LearningNode.GetByID(ctx, nodeID)
	if err != nil {
		return err
	}
	node.Prerequisites = prereqUUIDStrings
	node.UpdatedAt = time.Now()
	return s.repos.LearningNode.Update(ctx, node)
}

// createNewNode creates a new learning node

// AI Response structures
type AILearningPathResponse struct {
	Title            string   `json:"title"`
	Description      string   `json:"description"`
	Tags             []string `json:"tags"`
	EstimatedTimes   int      `json:"estimated_times"`
	SuitableFor      string   `json:"suitable_for"`
	PathType         []string `json:"path_type"`
	LearningOutcomes FlexibleStringSlice `json:"learning_outcomes"`
	Nodes            []AINode `json:"nodes"`
}

// FlexibleStringSlice unmarshals from either a JSON array of strings or a single string.
// If a single string is provided, it will be split into multiple outcomes by newlines
// or common bullet/list prefixes when possible. Fallback is a single-element slice.
type FlexibleStringSlice []string

func (f *FlexibleStringSlice) UnmarshalJSON(data []byte) error {
	// Try array of strings first
	var arr []string
	if err := json.Unmarshal(data, &arr); err == nil {
		*f = FlexibleStringSlice(arr)
		return nil
	}

	// Try single string
	var s string
	if err := json.Unmarshal(data, &s); err == nil {
		// Split by newlines first
		lines := strings.Split(s, "\n")
		// Regex to trim common bullets like: 1., 1)、1), -, *, •, 1、
		bulletRe := regexp.MustCompile(`^\s*(?:[-*•]|\d+[.)、])\s*`)
		out := make([]string, 0, len(lines))
		for _, line := range lines {
			t := strings.TrimSpace(line)
			if t == "" {
				continue
			}
			t = bulletRe.ReplaceAllString(t, "")
			if tt := strings.TrimSpace(t); tt != "" {
				out = append(out, tt)
			}
		}
		if len(out) == 0 && s != "" {
			out = []string{strings.TrimSpace(s)}
		}
		*f = FlexibleStringSlice(out)
		return nil
	}

	// Unknown type
	return fmt.Errorf("learning_outcomes must be string or []string")
}

type AINode struct {
	NodeID                    string   `json:"nodeId"`
	NodeName                  string   `json:"nodeName"`
	NodeType                  string   `json:"nodeType"`
	CoreObjective             string   `json:"coreObjective"`
	PrerequisiteNodeIds       []string `json:"prerequisiteNodeIds"`
	EstimatedTimes            int      `json:"estimated_times"`
	PotentialDifficulties     string   `json:"potentialDifficulties"`
	StudentProfileAssociation string   `json:"studentProfileAssociation"`
	Status                    string   `json:"status"` // "重用|调整|新增"
}

// Request/Response types

type CreateLearningPathRequest struct {
	Title            string    `json:"title" validate:"required"`
	Description      string    `json:"description"`
	LearningGoal     string    `json:"learning_goal"`
	GoalCategory     string    `json:"goal_category"`
	DifficultyLevel  int       `json:"difficulty_level" validate:"min=1,max=10"`
	EstimatedTimes   int       `json:"estimated_times"`
	SuitableFor      []string  `json:"suitable_for"`
	LearningOutcomes []string  `json:"learning_outcomes"`
	IsPublic         bool      `json:"is_public"`
	Tags             []string  `json:"tags"`
	CreatedBy        uuid.UUID `json:"created_by" validate:"required"`
}

type UpdateLearningPathRequest struct {
	Title            *string  `json:"title,omitempty"`
	Description      *string  `json:"description,omitempty"`
	Goal             *string  `json:"goal,omitempty"`
	GoalCategory     *string  `json:"goal_category,omitempty"`
	Difficulty       *int     `json:"difficulty,omitempty"`
	EstimatedTimes   *int     `json:"estimated_times,omitempty"`
	SuitableFor      []string `json:"suitable_for,omitempty"`
	LearningOutcomes []string `json:"learning_outcomes,omitempty"`
	IsPublic         *bool    `json:"is_public,omitempty"`
	Tags             []string `json:"tags,omitempty"`
}

type GeneratePathRequest struct {
	Goal   string    `json:"goal" validate:"required"`
	UserID uuid.UUID `json:"user_id"`
}

type GetLearningPathsRequest struct {
	Page     int `json:"page"`
	PageSize int `json:"page_size"`
	// Optional tag filter: names, semantics controlled by MatchAllTags
	Tags         []string `json:"tags" form:"tags[]"`
	// MatchAllTags=true means AND, false means OR
	MatchAllTags bool     `json:"match_all_tags" form:"match_all_tags"`
}
