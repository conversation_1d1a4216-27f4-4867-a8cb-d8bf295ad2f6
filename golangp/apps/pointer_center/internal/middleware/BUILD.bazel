load("@rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "middleware",
    srcs = [
        "pfchat_proxy.go",
    ],
    importpath = "github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/internal/middleware",
    visibility = ["//visibility:public"],
    deps = [
        "//golangp/apps/pointer_center/internal/config",
        "//golangp/common/gin_core/response",
        "//golangp/common/logging:logger",
        "@com_github_gin_gonic_gin//:gin",
    ],
)

go_test(
    name = "middleware_test",
    srcs = [
        "pfchat_proxy_test.go",
    ],
    deps = [
        ":middleware",
        "//golangp/apps/pointer_center/internal/config",
        "//golangp/common/logging:logger",
        "@com_github_gin_gonic_gin//:gin",
        "@com_github_stretchr_testify//assert",
    ],
)
