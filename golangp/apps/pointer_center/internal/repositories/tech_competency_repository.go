package repositories

import (
	"context"
	"fmt"
	"time"

	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/internal/models/student_profile"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// TechCompetencyRepository interface defines methods for tech competency operations
type TechCompetencyRepository interface {
	Create(ctx context.Context, graph *student_profile.TechCompetencyGraph) error
	GetByID(ctx context.Context, id primitive.ObjectID) (*student_profile.TechCompetencyGraph, error)
	GetByUserID(ctx context.Context, userID string) (*student_profile.TechCompetencyGraph, error)
	Update(ctx context.Context, graph *student_profile.TechCompetencyGraph) error
	Delete(ctx context.Context, id primitive.ObjectID) error
	List(ctx context.Context, limit, offset int) ([]*student_profile.TechCompetencyGraph, error)
	Count(ctx context.Context) (int64, error)
	GetLatestByUserID(ctx context.Context, userID string) (*student_profile.TechCompetencyGraph, error)
	GetByUserAndVersion(ctx context.Context, userID string, version int) (*student_profile.TechCompetencyGraph, error)
	GetStaleGraphs(ctx context.Context, threshold time.Duration) ([]*student_profile.TechCompetencyGraph, error)
	AddUpdateRecord(ctx context.Context, userID string, record student_profile.CompetencyUpdateRecord) error
	GetNodesByLevel(ctx context.Context, userID string, level student_profile.CompetencyLevel) ([]student_profile.CompetencyNode, error)
	UpdateNodeCompetency(ctx context.Context, userID, nodeID string, level student_profile.CompetencyLevel) error
	GetGraphsByStrengthArea(ctx context.Context, strengthArea string) ([]*student_profile.TechCompetencyGraph, error)
	IncrementVersion(ctx context.Context, userID string) error
	ExistsByUserID(ctx context.Context, userID string) (bool, error)
	GetUserCompetencyStats(ctx context.Context, userID string) (map[student_profile.CompetencyLevel]int, error)
	UpdateFromAIOutput(ctx context.Context, userID string, output student_profile.CompetencyGraphOutput, action student_profile.StudentAction, evaluation string) error
}

// techCompetencyRepository implements TechCompetencyRepository interface
type techCompetencyRepository struct {
	collection *mongo.Collection
}

// NewTechCompetencyRepository creates a new tech competency repository
func NewTechCompetencyRepository(db *mongo.Database) TechCompetencyRepository {
	return &techCompetencyRepository{
		collection: db.Collection("student_tech_competency_graphs"),
	}
}

// Create creates a new tech competency graph
func (r *techCompetencyRepository) Create(ctx context.Context, graph *student_profile.TechCompetencyGraph) error {
	if graph.ID.IsZero() {
		graph.ID = primitive.NewObjectID()
	}
	graph.LastUpdated = time.Now()

	_, err := r.collection.InsertOne(ctx, graph)
	if err != nil {
		return fmt.Errorf("failed to create tech competency graph: %w", err)
	}
	return nil
}

// GetByID retrieves a tech competency graph by ID
func (r *techCompetencyRepository) GetByID(ctx context.Context, id primitive.ObjectID) (*student_profile.TechCompetencyGraph, error) {
	var graph student_profile.TechCompetencyGraph
	err := r.collection.FindOne(ctx, bson.M{"_id": id}).Decode(&graph)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, fmt.Errorf("tech competency graph not found with id %s", id.Hex())
		}
		return nil, fmt.Errorf("failed to get tech competency graph: %w", err)
	}
	return &graph, nil
}

// GetByUserID retrieves the latest tech competency graph by user ID, creates one if not exists
func (r *techCompetencyRepository) GetByUserID(ctx context.Context, userID string) (*student_profile.TechCompetencyGraph, error) {
	var graph student_profile.TechCompetencyGraph
	opts := options.FindOne().SetSort(bson.D{{Key: "profile_version", Value: -1}})
	err := r.collection.FindOne(ctx, bson.M{"user_id": userID}, opts).Decode(&graph)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			// Create a new empty graph if none exists
			newGraph := student_profile.NewTechCompetencyGraph(userID)
			if createErr := r.Create(ctx, newGraph); createErr != nil {
				return nil, fmt.Errorf("failed to create new tech competency graph: %w", createErr)
			}
			return newGraph, nil
		}
		return nil, fmt.Errorf("failed to get tech competency graph for user: %w", err)
	}
	return &graph, nil
}

// Update updates an existing tech competency graph
func (r *techCompetencyRepository) Update(ctx context.Context, graph *student_profile.TechCompetencyGraph) error {
	graph.LastUpdated = time.Now()
	filter := bson.M{"_id": graph.ID}
	_, err := r.collection.ReplaceOne(ctx, filter, graph)
	if err != nil {
		return fmt.Errorf("failed to update tech competency graph: %w", err)
	}
	return nil
}

// Delete deletes a tech competency graph by ID
func (r *techCompetencyRepository) Delete(ctx context.Context, id primitive.ObjectID) error {
	_, err := r.collection.DeleteOne(ctx, bson.M{"_id": id})
	if err != nil {
		return fmt.Errorf("failed to delete tech competency graph: %w", err)
	}
	return nil
}

// List retrieves a list of tech competency graphs with pagination
func (r *techCompetencyRepository) List(ctx context.Context, limit, offset int) ([]*student_profile.TechCompetencyGraph, error) {
	opts := options.Find().SetLimit(int64(limit)).SetSkip(int64(offset)).SetSort(bson.D{{Key: "last_updated", Value: -1}})
	cursor, err := r.collection.Find(ctx, bson.M{}, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to list tech competency graphs: %w", err)
	}
	defer cursor.Close(ctx)

	var graphs []*student_profile.TechCompetencyGraph
	for cursor.Next(ctx) {
		var graph student_profile.TechCompetencyGraph
		if err := cursor.Decode(&graph); err != nil {
			return nil, fmt.Errorf("failed to decode tech competency graph: %w", err)
		}
		graphs = append(graphs, &graph)
	}

	if err := cursor.Err(); err != nil {
		return nil, fmt.Errorf("cursor error: %w", err)
	}

	return graphs, nil
}

// Count returns the total number of tech competency graphs
func (r *techCompetencyRepository) Count(ctx context.Context) (int64, error) {
	count, err := r.collection.CountDocuments(ctx, bson.M{})
	if err != nil {
		return 0, fmt.Errorf("failed to count tech competency graphs: %w", err)
	}
	return count, nil
}

// GetLatestByUserID retrieves the latest version of a user's tech competency graph
func (r *techCompetencyRepository) GetLatestByUserID(ctx context.Context, userID string) (*student_profile.TechCompetencyGraph, error) {
	return r.GetByUserID(ctx, userID)
}

// GetByUserAndVersion retrieves a specific version of a user's tech competency graph
func (r *techCompetencyRepository) GetByUserAndVersion(ctx context.Context, userID string, version int) (*student_profile.TechCompetencyGraph, error) {
	var graph student_profile.TechCompetencyGraph
	filter := bson.M{"user_id": userID, "profile_version": version}
	err := r.collection.FindOne(ctx, filter).Decode(&graph)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, fmt.Errorf("tech competency graph not found for user %s version %d", userID, version)
		}
		return nil, fmt.Errorf("failed to get tech competency graph: %w", err)
	}
	return &graph, nil
}

// GetStaleGraphs retrieves graphs that haven't been updated within the threshold
func (r *techCompetencyRepository) GetStaleGraphs(ctx context.Context, threshold time.Duration) ([]*student_profile.TechCompetencyGraph, error) {
	staleTime := time.Now().Add(-threshold)
	filter := bson.M{"last_updated": bson.M{"$lt": staleTime}}

	cursor, err := r.collection.Find(ctx, filter)
	if err != nil {
		return nil, fmt.Errorf("failed to find stale graphs: %w", err)
	}
	defer cursor.Close(ctx)

	var graphs []*student_profile.TechCompetencyGraph
	for cursor.Next(ctx) {
		var graph student_profile.TechCompetencyGraph
		if err := cursor.Decode(&graph); err != nil {
			return nil, fmt.Errorf("failed to decode stale graph: %w", err)
		}
		graphs = append(graphs, &graph)
	}

	return graphs, nil
}

// AddUpdateRecord adds an update record to a user's competency graph
func (r *techCompetencyRepository) AddUpdateRecord(ctx context.Context, userID string, record student_profile.CompetencyUpdateRecord) error {
	record.UpdatedAt = time.Now()
	if record.UpdateID.IsZero() {
		record.UpdateID = primitive.NewObjectID()
	}

	filter := bson.M{"user_id": userID}
	update := bson.M{
		"$push": bson.M{"update_history": record},
		"$set":  bson.M{"last_updated": time.Now()},
	}

	_, err := r.collection.UpdateOne(ctx, filter, update)
	if err != nil {
		return fmt.Errorf("failed to add update record: %w", err)
	}
	return nil
}

// GetNodesByLevel retrieves nodes with a specific competency level for a user
func (r *techCompetencyRepository) GetNodesByLevel(ctx context.Context, userID string, level student_profile.CompetencyLevel) ([]student_profile.CompetencyNode, error) {
	graph, err := r.GetByUserID(ctx, userID)
	if err != nil {
		return nil, err
	}

	return graph.GetNodesByLevel(level), nil
}

// UpdateNodeCompetency updates the competency level of a specific node
func (r *techCompetencyRepository) UpdateNodeCompetency(ctx context.Context, userID, nodeID string, level student_profile.CompetencyLevel) error {
	filter := bson.M{
		"user_id":  userID,
		"nodes.id": nodeID,
	}
	update := bson.M{
		"$set": bson.M{
			"nodes.$.level":        level,
			"nodes.$.last_updated": time.Now(),
			"last_updated":         time.Now(),
		},
	}

	_, err := r.collection.UpdateOne(ctx, filter, update)
	if err != nil {
		return fmt.Errorf("failed to update node competency: %w", err)
	}
	return nil
}

// GetGraphsByStrengthArea retrieves graphs that have a specific strength area
func (r *techCompetencyRepository) GetGraphsByStrengthArea(ctx context.Context, strengthArea string) ([]*student_profile.TechCompetencyGraph, error) {
	filter := bson.M{"strength_areas": bson.M{"$in": []string{strengthArea}}}

	cursor, err := r.collection.Find(ctx, filter)
	if err != nil {
		return nil, fmt.Errorf("failed to find graphs by strength area: %w", err)
	}
	defer cursor.Close(ctx)

	var graphs []*student_profile.TechCompetencyGraph
	for cursor.Next(ctx) {
		var graph student_profile.TechCompetencyGraph
		if err := cursor.Decode(&graph); err != nil {
			return nil, fmt.Errorf("failed to decode graph: %w", err)
		}
		graphs = append(graphs, &graph)
	}

	return graphs, nil
}

// IncrementVersion increments the profile version for a user
func (r *techCompetencyRepository) IncrementVersion(ctx context.Context, userID string) error {
	filter := bson.M{"user_id": userID}
	update := bson.M{
		"$inc": bson.M{"profile_version": 1},
		"$set": bson.M{"last_updated": time.Now()},
	}

	_, err := r.collection.UpdateOne(ctx, filter, update)
	if err != nil {
		return fmt.Errorf("failed to increment version: %w", err)
	}
	return nil
}

// ExistsByUserID checks if a tech competency graph exists for a user
func (r *techCompetencyRepository) ExistsByUserID(ctx context.Context, userID string) (bool, error) {
	count, err := r.collection.CountDocuments(ctx, bson.M{"user_id": userID})
	if err != nil {
		return false, fmt.Errorf("failed to check if graph exists: %w", err)
	}
	return count > 0, nil
}

// GetUserCompetencyStats returns competency level statistics for a user
func (r *techCompetencyRepository) GetUserCompetencyStats(ctx context.Context, userID string) (map[student_profile.CompetencyLevel]int, error) {
	graph, err := r.GetByUserID(ctx, userID)
	if err != nil {
		return nil, err
	}

	stats := make(map[student_profile.CompetencyLevel]int)
	for _, node := range graph.Nodes {
		stats[node.Level]++
	}

	return stats, nil
}

// UpdateFromAIOutput updates the graph from AI agent output
func (r *techCompetencyRepository) UpdateFromAIOutput(ctx context.Context, userID string, output student_profile.CompetencyGraphOutput, action student_profile.StudentAction, evaluation string) error {
	graph, err := r.GetByUserID(ctx, userID)
	if err != nil {
		// If no graph exists, create a new one
		if err.Error() == fmt.Sprintf("tech competency graph not found for user %s", userID) {
			graph = student_profile.NewTechCompetencyGraph(userID)
		} else {
			return err
		}
	}

	// Update graph using the model's method
	graph.UpdateFromAIOutput(output, action, evaluation)

	// Save the updated graph
	if graph.ID.IsZero() {
		return r.Create(ctx, graph)
	} else {
		return r.Update(ctx, graph)
	}
}
