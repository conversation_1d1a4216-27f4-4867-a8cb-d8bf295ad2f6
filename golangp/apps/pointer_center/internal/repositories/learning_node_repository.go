package repositories

import (
	"context"
	"fmt"
	"strings"

	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/internal/models"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// LearningNodeRepository interface defines methods for learning node operations
type LearningNodeRepository interface {
	Create(ctx context.Context, node *models.LearningNode) error
	GetByID(ctx context.Context, id uuid.UUID) (*models.LearningNode, error)
	Update(ctx context.Context, node *models.LearningNode) error
	Delete(ctx context.Context, id uuid.UUID) error
	List(ctx context.Context, limit, offset int) ([]*models.LearningNode, error)
	ListByStatus(ctx context.Context, status models.NodeStatus, limit, offset int) ([]*models.LearningNode, error)
	ListByDifficulty(ctx context.Context, difficulty models.DifficultyLevel, limit, offset int) ([]*models.LearningNode, error)
	Count(ctx context.Context) (int64, error)
	CountByStatus(ctx context.Context, status models.NodeStatus) (int64, error)
	Search(ctx context.Context, query string, limit, offset int) ([]*models.LearningNode, error)
	// Search with mode: mode == "exact" for case-insensitive exact match; otherwise fuzzy (ILIKE %%q%%)
	SearchWithMode(ctx context.Context, query, mode string, limit, offset int) ([]*models.LearningNode, error)
	CountSearch(ctx context.Context, query, mode string) (int64, error)
	GetByCreator(ctx context.Context, creatorID uuid.UUID, limit, offset int) ([]*models.LearningNode, error)
	GetActiveNodes(ctx context.Context, limit, offset int) ([]*models.LearningNode, error)
	GetNodesByEstimatedTime(ctx context.Context, minHours, maxHours int) ([]*models.LearningNode, error)
	UpdateNodeStatus(ctx context.Context, nodeID uuid.UUID, status models.NodeStatus) error
	ExistsByID(ctx context.Context, id uuid.UUID) (bool, error)
	// Filter by tags/skills/learner_tags (any-match per field). Combine different fields with AND.
	Filter(ctx context.Context, tags, skills, learnerTags []string, limit, offset int) ([]*models.LearningNode, error)
	CountFilter(ctx context.Context, tags, skills, learnerTags []string) (int64, error)
	// Combined filter and search
	FilterAndSearch(ctx context.Context, tags, skills, learnerTags []string, query, mode string, limit, offset int) ([]*models.LearningNode, error)
	CountFilterAndSearch(ctx context.Context, tags, skills, learnerTags []string, query, mode string) (int64, error)
}

// LearningPathNodeRepository interface defines methods for learning path node associations
type LearningPathNodeRepository interface {
	Create(ctx context.Context, pathNode *models.LearningPathNode) error
	GetByID(ctx context.Context, id uuid.UUID) (*models.LearningPathNode, error)
	Delete(ctx context.Context, id uuid.UUID) error
	GetByPathID(ctx context.Context, pathID uuid.UUID) ([]*models.LearningPathNode, error)
	GetByNodeID(ctx context.Context, nodeID uuid.UUID) ([]*models.LearningPathNode, error)
	DeleteByPathID(ctx context.Context, pathID uuid.UUID) error
	DeleteByNodeID(ctx context.Context, nodeID uuid.UUID) error
	ExistsByPathAndNode(ctx context.Context, pathID, nodeID uuid.UUID) (bool, error)
	GetNodesForPath(ctx context.Context, pathID uuid.UUID) ([]*models.LearningNode, error)
	GetPathsForNode(ctx context.Context, nodeID uuid.UUID) ([]*models.LearningPath, error)
}

// NodeLessonRepository interface defines methods for node lesson associations
type NodeLessonRepository interface {
	Create(ctx context.Context, nodeLesson *models.NodeLesson) error
	GetByID(ctx context.Context, id uuid.UUID) (*models.NodeLesson, error)
	Update(ctx context.Context, nodeLesson *models.NodeLesson) error
	Delete(ctx context.Context, id uuid.UUID) error
	GetByNodeID(ctx context.Context, nodeID uuid.UUID) ([]*models.NodeLesson, error)
	GetByLessonID(ctx context.Context, lessonID string) ([]*models.NodeLesson, error)
	DeleteByNodeID(ctx context.Context, nodeID uuid.UUID) error
	DeleteByLessonID(ctx context.Context, lessonID string) error
	ExistsByNodeAndLesson(ctx context.Context, nodeID uuid.UUID, lessonID string) (bool, error)
	GetLessonsForNode(ctx context.Context, nodeID uuid.UUID) ([]string, error)
	GetNodesForLesson(ctx context.Context, lessonID string) ([]*models.LearningNode, error)
	UpdateOrderKey(ctx context.Context, nodeID uuid.UUID, lessonID string, newKey string) error
	// numeric order removed; using only order_key
}

// learningNodeRepository implements LearningNodeRepository interface
type learningNodeRepository struct {
	db *gorm.DB
}

// learningPathNodeRepository implements LearningPathNodeRepository interface
type learningPathNodeRepository struct {
	db *gorm.DB
}

// nodeLessonRepository implements NodeLessonRepository interface
type nodeLessonRepository struct {
	db *gorm.DB
}

// NewLearningNodeRepository creates a new learning node repository
func NewLearningNodeRepository(db *gorm.DB) LearningNodeRepository {
	return &learningNodeRepository{db: db}
}

// NewLearningPathNodeRepository creates a new learning path node repository
func NewLearningPathNodeRepository(db *gorm.DB) LearningPathNodeRepository {
	return &learningPathNodeRepository{db: db}
}

// NewNodeLessonRepository creates a new node lesson repository
func NewNodeLessonRepository(db *gorm.DB) NodeLessonRepository {
	return &nodeLessonRepository{db: db}
}

// ============================================================================
// LearningNodeRepository Implementation
// ============================================================================

// Create creates a new learning node
func (r *learningNodeRepository) Create(ctx context.Context, node *models.LearningNode) error {
	if err := r.db.WithContext(ctx).Create(node).Error; err != nil {
		return fmt.Errorf("failed to create learning node: %w", err)
	}
	return nil
}

// GetByID retrieves a learning node by ID
func (r *learningNodeRepository) GetByID(ctx context.Context, id uuid.UUID) (*models.LearningNode, error) {
	var node models.LearningNode
	if err := r.db.WithContext(ctx).Where("id = ?", id).First(&node).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("learning node not found with id %s", id.String())
		}
		return nil, fmt.Errorf("failed to get learning node: %w", err)
	}
	return &node, nil
}

// Update updates an existing learning node
func (r *learningNodeRepository) Update(ctx context.Context, node *models.LearningNode) error {
	if err := r.db.WithContext(ctx).Save(node).Error; err != nil {
		return fmt.Errorf("failed to update learning node: %w", err)
	}
	return nil
}

// Delete deletes a learning node by ID (soft delete)
func (r *learningNodeRepository) Delete(ctx context.Context, id uuid.UUID) error {
	if err := r.db.WithContext(ctx).Delete(&models.LearningNode{}, "id = ?", id).Error; err != nil {
		return fmt.Errorf("failed to delete learning node: %w", err)
	}
	return nil
}

// List retrieves a list of learning nodes with pagination
func (r *learningNodeRepository) List(ctx context.Context, limit, offset int) ([]*models.LearningNode, error) {
	var nodes []*models.LearningNode
	if err := r.db.WithContext(ctx).Limit(limit).Offset(offset).Order("created_at DESC").Find(&nodes).Error; err != nil {
		return nil, fmt.Errorf("failed to list learning nodes: %w", err)
	}
	return nodes, nil
}

// ListByStatus retrieves learning nodes by status
func (r *learningNodeRepository) ListByStatus(ctx context.Context, status models.NodeStatus, limit, offset int) ([]*models.LearningNode, error) {
	var nodes []*models.LearningNode
	if err := r.db.WithContext(ctx).Where("status = ?", status).Limit(limit).Offset(offset).Order("created_at DESC").Find(&nodes).Error; err != nil {
		return nil, fmt.Errorf("failed to list nodes by status: %w", err)
	}
	return nodes, nil
}

// ListByDifficulty retrieves learning nodes by difficulty level
func (r *learningNodeRepository) ListByDifficulty(ctx context.Context, difficulty models.DifficultyLevel, limit, offset int) ([]*models.LearningNode, error) {
	var nodes []*models.LearningNode
	if err := r.db.WithContext(ctx).Where("difficulty = ?", difficulty).Limit(limit).Offset(offset).Order("created_at DESC").Find(&nodes).Error; err != nil {
		return nil, fmt.Errorf("failed to list nodes by difficulty: %w", err)
	}
	return nodes, nil
}

// Count returns the total number of learning nodes
func (r *learningNodeRepository) Count(ctx context.Context) (int64, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&models.LearningNode{}).Count(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to count learning nodes: %w", err)
	}
	return count, nil
}

// CountByStatus returns the number of nodes by status
func (r *learningNodeRepository) CountByStatus(ctx context.Context, status models.NodeStatus) (int64, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&models.LearningNode{}).Where("status = ?", status).Count(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to count nodes by status: %w", err)
	}
	return count, nil
}

// Search searches for nodes by title or description
func (r *learningNodeRepository) Search(ctx context.Context, query string, limit, offset int) ([]*models.LearningNode, error) {
	var nodes []*models.LearningNode
	searchQuery := "%" + query + "%"
	if err := r.db.WithContext(ctx).Where("title ILIKE ? OR description ILIKE ?", searchQuery, searchQuery).
		Limit(limit).Offset(offset).Order("created_at DESC").Find(&nodes).Error; err != nil {
		return nil, fmt.Errorf("failed to search learning nodes: %w", err)
	}
	return nodes, nil
}

// SearchWithMode supports fuzzy (ILIKE) and exact (case-insensitive equality) search
func (r *learningNodeRepository) SearchWithMode(ctx context.Context, query, mode string, limit, offset int) ([]*models.LearningNode, error) {
	var nodes []*models.LearningNode
	db := r.applySearch(r.db.WithContext(ctx).Model(&models.LearningNode{}), query, mode)
	if err := db.Limit(limit).Offset(offset).Order("created_at DESC").Find(&nodes).Error; err != nil {
		return nil, fmt.Errorf("failed to search learning nodes: %w", err)
	}
	return nodes, nil
}

func (r *learningNodeRepository) CountSearch(ctx context.Context, query, mode string) (int64, error) {
	var count int64
	db := r.applySearch(r.db.WithContext(ctx).Model(&models.LearningNode{}), query, mode)
	if err := db.Count(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to count searched learning nodes: %w", err)
	}
	return count, nil
}

// GetByCreator retrieves nodes created by a specific user
func (r *learningNodeRepository) GetByCreator(ctx context.Context, creatorID uuid.UUID, limit, offset int) ([]*models.LearningNode, error) {
	var nodes []*models.LearningNode
	if err := r.db.WithContext(ctx).Where("created_by = ?", creatorID).
		Limit(limit).Offset(offset).Order("created_at DESC").Find(&nodes).Error; err != nil {
		return nil, fmt.Errorf("failed to get nodes by creator: %w", err)
	}
	return nodes, nil
}

// GetActiveNodes retrieves only active nodes
func (r *learningNodeRepository) GetActiveNodes(ctx context.Context, limit, offset int) ([]*models.LearningNode, error) {
	return r.ListByStatus(ctx, models.NodeStatusActive, limit, offset)
}

// GetNodesByEstimatedTime retrieves nodes by estimated time range
func (r *learningNodeRepository) GetNodesByEstimatedTime(ctx context.Context, minHours, maxHours int) ([]*models.LearningNode, error) {
	var nodes []*models.LearningNode
	if err := r.db.WithContext(ctx).Where("estimated_times BETWEEN ? AND ?", minHours, maxHours).Find(&nodes).Error; err != nil {
		return nil, fmt.Errorf("failed to get nodes by estimated time: %w", err)
	}
	return nodes, nil
}

// UpdateNodeStatus updates the status of a node
func (r *learningNodeRepository) UpdateNodeStatus(ctx context.Context, nodeID uuid.UUID, status models.NodeStatus) error {
	if err := r.db.WithContext(ctx).Model(&models.LearningNode{}).
		Where("id = ?", nodeID).Update("status", status).Error; err != nil {
		return fmt.Errorf("failed to update node status: %w", err)
	}
	return nil
}

// ExistsByID checks if a node exists by ID
func (r *learningNodeRepository) ExistsByID(ctx context.Context, id uuid.UUID) (bool, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&models.LearningNode{}).Where("id = ?", id).Count(&count).Error; err != nil {
		return false, fmt.Errorf("failed to check if node exists: %w", err)
	}
	return count > 0, nil
}

// buildFilterQuery applies OR semantics within each array field (tags/skills/learner_tags)
// and AND semantics across different fields.
func (r *learningNodeRepository) buildFilterQuery(ctx context.Context, tags, skills, learnerTags []string) *gorm.DB {
	q := r.db.WithContext(ctx).Model(&models.LearningNode{})

	// helper to apply OR list for a given column
	applyOrList := func(db *gorm.DB, column string, items []string) *gorm.DB {
		// clean items (drop empty)
		cleaned := make([]string, 0, len(items))
		for _, it := range items {
			if it != "" {
				cleaned = append(cleaned, it)
			}
		}
		if len(cleaned) == 0 {
			return db
		}

		// Build OR conditions manually instead of using closure
		var conditions []string
		var args []interface{}

		for _, v := range cleaned {
			// Use case-insensitive matching with JSONB functions
			// Check if any element in the JSONB array matches case-insensitively
			condition := fmt.Sprintf("EXISTS (SELECT 1 FROM jsonb_array_elements_text(%s) AS elem WHERE LOWER(elem) = LOWER(?))", column)
			conditions = append(conditions, condition)
			args = append(args, v)
		}

		// Join conditions with OR and wrap in parentheses
		if len(conditions) > 0 {
			whereClause := "(" + strings.Join(conditions, " OR ") + ")"
			db = db.Where(whereClause, args...)
		}

		return db
	}

	if len(tags) > 0 {
		q = applyOrList(q, "tags", tags)
	}
	if len(skills) > 0 {
		q = applyOrList(q, "skills", skills)
	}
	if len(learnerTags) > 0 {
		q = applyOrList(q, "learner_tags", learnerTags)
	}
	return q
}

// Filter retrieves nodes filtered by tags/skills/learner_tags with pagination
func (r *learningNodeRepository) Filter(ctx context.Context, tags, skills, learnerTags []string, limit, offset int) ([]*models.LearningNode, error) {
	q := r.buildFilterQuery(ctx, tags, skills, learnerTags)
	var nodes []*models.LearningNode
	if err := q.Limit(limit).Offset(offset).Order("created_at DESC").Find(&nodes).Error; err != nil {
		return nil, fmt.Errorf("failed to filter learning nodes: %w", err)
	}
	return nodes, nil
}

// CountFilter returns total count for filtered nodes
func (r *learningNodeRepository) CountFilter(ctx context.Context, tags, skills, learnerTags []string) (int64, error) {
	q := r.buildFilterQuery(ctx, tags, skills, learnerTags)
	var count int64
	if err := q.Count(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to count filtered learning nodes: %w", err)
	}
	return count, nil
}

// FilterAndSearch retrieves nodes with combined tag/skill/learnerTag filters and text search
func (r *learningNodeRepository) FilterAndSearch(ctx context.Context, tags, skills, learnerTags []string, query, mode string, limit, offset int) ([]*models.LearningNode, error) {
	db := r.buildFilterQuery(ctx, tags, skills, learnerTags)
	db = r.applySearch(db, query, mode)
	var nodes []*models.LearningNode
	if err := db.Limit(limit).Offset(offset).Order("created_at DESC").Find(&nodes).Error; err != nil {
		return nil, fmt.Errorf("failed to filter+search learning nodes: %w", err)
	}
	return nodes, nil
}

func (r *learningNodeRepository) CountFilterAndSearch(ctx context.Context, tags, skills, learnerTags []string, query, mode string) (int64, error) {
	db := r.buildFilterQuery(ctx, tags, skills, learnerTags)
	db = r.applySearch(db, query, mode)
	var count int64
	if err := db.Count(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to count filter+search learning nodes: %w", err)
	}
	return count, nil
}

// applySearch applies text search on title/description with mode
func (r *learningNodeRepository) applySearch(db *gorm.DB, query, mode string) *gorm.DB {
	if query == "" {
		return db
	}
	if mode == "exact" {
		// case-insensitive equality using LOWER
		return db.Where("LOWER(title) = LOWER(?) OR LOWER(description) = LOWER(?)", query, query)
	}
	// default fuzzy
	pattern := "%" + query + "%"
	return db.Where("title ILIKE ? OR description ILIKE ?", pattern, pattern)
}

// ============================================================================
// LearningPathNodeRepository Implementation
// ============================================================================

// Create creates a new learning path node association
func (r *learningPathNodeRepository) Create(ctx context.Context, pathNode *models.LearningPathNode) error {
	if err := r.db.WithContext(ctx).Create(pathNode).Error; err != nil {
		return fmt.Errorf("failed to create learning path node: %w", err)
	}
	return nil
}

// GetByID retrieves a learning path node by ID
func (r *learningPathNodeRepository) GetByID(ctx context.Context, id uuid.UUID) (*models.LearningPathNode, error) {
	var pathNode models.LearningPathNode
	if err := r.db.WithContext(ctx).Where("id = ?", id).First(&pathNode).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("learning path node not found with id %s", id.String())
		}
		return nil, fmt.Errorf("failed to get learning path node: %w", err)
	}
	return &pathNode, nil
}

// Delete deletes a learning path node by ID
func (r *learningPathNodeRepository) Delete(ctx context.Context, id uuid.UUID) error {
	if err := r.db.WithContext(ctx).Delete(&models.LearningPathNode{}, "id = ?", id).Error; err != nil {
		return fmt.Errorf("failed to delete learning path node: %w", err)
	}
	return nil
}

// GetByPathID retrieves all nodes for a specific path
func (r *learningPathNodeRepository) GetByPathID(ctx context.Context, pathID uuid.UUID) ([]*models.LearningPathNode, error) {
	var pathNodes []*models.LearningPathNode
	if err := r.db.WithContext(ctx).Where("learning_path_id = ?", pathID).Find(&pathNodes).Error; err != nil {
		return nil, fmt.Errorf("failed to get nodes for path: %w", err)
	}
	return pathNodes, nil
}

// GetByNodeID retrieves all paths for a specific node
func (r *learningPathNodeRepository) GetByNodeID(ctx context.Context, nodeID uuid.UUID) ([]*models.LearningPathNode, error) {
	var pathNodes []*models.LearningPathNode
	if err := r.db.WithContext(ctx).Where("node_id = ?", nodeID).Find(&pathNodes).Error; err != nil {
		return nil, fmt.Errorf("failed to get paths for node: %w", err)
	}
	return pathNodes, nil
}

// DeleteByPathID deletes all nodes for a specific path
func (r *learningPathNodeRepository) DeleteByPathID(ctx context.Context, pathID uuid.UUID) error {
	if err := r.db.WithContext(ctx).Delete(&models.LearningPathNode{}, "learning_path_id = ?", pathID).Error; err != nil {
		return fmt.Errorf("failed to delete nodes for path: %w", err)
	}
	return nil
}

// DeleteByNodeID deletes all paths for a specific node
func (r *learningPathNodeRepository) DeleteByNodeID(ctx context.Context, nodeID uuid.UUID) error {
	if err := r.db.WithContext(ctx).Delete(&models.LearningPathNode{}, "node_id = ?", nodeID).Error; err != nil {
		return fmt.Errorf("failed to delete paths for node: %w", err)
	}
	return nil
}

// ExistsByPathAndNode checks if an association exists between path and node
func (r *learningPathNodeRepository) ExistsByPathAndNode(ctx context.Context, pathID, nodeID uuid.UUID) (bool, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&models.LearningPathNode{}).
		Where("learning_path_id = ? AND node_id = ?", pathID, nodeID).Count(&count).Error; err != nil {
		return false, fmt.Errorf("failed to check path-node association: %w", err)
	}
	return count > 0, nil
}

// GetNodesForPath retrieves all nodes for a specific path with details
func (r *learningPathNodeRepository) GetNodesForPath(ctx context.Context, pathID uuid.UUID) ([]*models.LearningNode, error) {
	var nodes []*models.LearningNode
	if err := r.db.WithContext(ctx).Table("learning_nodes").
		Joins("JOIN learning_path_nodes ON learning_nodes.id = learning_path_nodes.node_id").
		Where("learning_path_nodes.learning_path_id = ?", pathID).
		Find(&nodes).Error; err != nil {
		return nil, fmt.Errorf("failed to get nodes for path: %w", err)
	}
	return nodes, nil
}

// GetPathsForNode retrieves all paths for a specific node with details
func (r *learningPathNodeRepository) GetPathsForNode(ctx context.Context, nodeID uuid.UUID) ([]*models.LearningPath, error) {
	var paths []*models.LearningPath
	if err := r.db.WithContext(ctx).Table("learning_paths").
		Joins("JOIN learning_path_nodes ON learning_paths.id = learning_path_nodes.learning_path_id").
		Where("learning_path_nodes.node_id = ?", nodeID).
		Find(&paths).Error; err != nil {
		return nil, fmt.Errorf("failed to get paths for node: %w", err)
	}
	return paths, nil
}

// ============================================================================
// NodeLessonRepository Implementation
// ============================================================================

// Create creates a new node lesson association
func (r *nodeLessonRepository) Create(ctx context.Context, nodeLesson *models.NodeLesson) error {
	if err := r.db.WithContext(ctx).Create(nodeLesson).Error; err != nil {
		return fmt.Errorf("failed to create node lesson: %w", err)
	}
	return nil
}

// GetByID retrieves a node lesson by ID
func (r *nodeLessonRepository) GetByID(ctx context.Context, id uuid.UUID) (*models.NodeLesson, error) {
	var nodeLesson models.NodeLesson
	if err := r.db.WithContext(ctx).Where("id = ?", id).First(&nodeLesson).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("node lesson not found with id %s", id.String())
		}
		return nil, fmt.Errorf("failed to get node lesson: %w", err)
	}
	return &nodeLesson, nil
}

// Update updates an existing node lesson
func (r *nodeLessonRepository) Update(ctx context.Context, nodeLesson *models.NodeLesson) error {
	if err := r.db.WithContext(ctx).Save(nodeLesson).Error; err != nil {
		return fmt.Errorf("failed to update node lesson: %w", err)
	}
	return nil
}

// Delete deletes a node lesson by ID
func (r *nodeLessonRepository) Delete(ctx context.Context, id uuid.UUID) error {
	if err := r.db.WithContext(ctx).Delete(&models.NodeLesson{}, "id = ?", id).Error; err != nil {
		return fmt.Errorf("failed to delete node lesson: %w", err)
	}
	return nil
}

// GetByNodeID retrieves all lessons for a specific node
func (r *nodeLessonRepository) GetByNodeID(ctx context.Context, nodeID uuid.UUID) ([]*models.NodeLesson, error) {
	var nodeLessons []*models.NodeLesson
	if err := r.db.WithContext(ctx).Where("node_id = ?", nodeID).Order("order_key ASC").Find(&nodeLessons).Error; err != nil {
		return nil, fmt.Errorf("failed to get lessons for node: %w", err)
	}
	return nodeLessons, nil
}

// GetByLessonID retrieves all nodes for a specific lesson
func (r *nodeLessonRepository) GetByLessonID(ctx context.Context, lessonID string) ([]*models.NodeLesson, error) {
	var nodeLessons []*models.NodeLesson
	if err := r.db.WithContext(ctx).Where("lesson_id = ?", lessonID).Find(&nodeLessons).Error; err != nil {
		return nil, fmt.Errorf("failed to get nodes for lesson: %w", err)
	}
	return nodeLessons, nil
}

// DeleteByNodeID deletes all lessons for a specific node
func (r *nodeLessonRepository) DeleteByNodeID(ctx context.Context, nodeID uuid.UUID) error {
	if err := r.db.WithContext(ctx).Delete(&models.NodeLesson{}, "node_id = ?", nodeID).Error; err != nil {
		return fmt.Errorf("failed to delete lessons for node: %w", err)
	}
	return nil
}

// DeleteByLessonID deletes all nodes for a specific lesson
func (r *nodeLessonRepository) DeleteByLessonID(ctx context.Context, lessonID string) error {
	if err := r.db.WithContext(ctx).Delete(&models.NodeLesson{}, "lesson_id = ?", lessonID).Error; err != nil {
		return fmt.Errorf("failed to delete nodes for lesson: %w", err)
	}
	return nil
}

// ExistsByNodeAndLesson checks if an association exists between node and lesson
func (r *nodeLessonRepository) ExistsByNodeAndLesson(ctx context.Context, nodeID uuid.UUID, lessonID string) (bool, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&models.NodeLesson{}).
		Where("node_id = ? AND lesson_id = ?", nodeID, lessonID).Count(&count).Error; err != nil {
		return false, fmt.Errorf("failed to check node-lesson association: %w", err)
	}
	return count > 0, nil
}

// GetLessonsForNode retrieves all lesson IDs for a specific node in order
func (r *nodeLessonRepository) GetLessonsForNode(ctx context.Context, nodeID uuid.UUID) ([]string, error) {
	var nodeLessons []*models.NodeLesson
	if err := r.db.WithContext(ctx).Where("node_id = ?", nodeID).Order("order_key ASC").Find(&nodeLessons).Error; err != nil {
		return nil, fmt.Errorf("failed to get lessons for node: %w", err)
	}

	lessonIDs := make([]string, len(nodeLessons))
	for i, nl := range nodeLessons {
		lessonIDs[i] = nl.LessonID
	}
	return lessonIDs, nil
}

// GetNodesForLesson retrieves all nodes for a specific lesson with details
func (r *nodeLessonRepository) GetNodesForLesson(ctx context.Context, lessonID string) ([]*models.LearningNode, error) {
	var nodes []*models.LearningNode
	if err := r.db.WithContext(ctx).Table("learning_nodes").
		Joins("JOIN node_lessons ON learning_nodes.id = node_lessons.node_id").
		Where("node_lessons.lesson_id = ?", lessonID).
		Find(&nodes).Error; err != nil {
		return nil, fmt.Errorf("failed to get nodes for lesson: %w", err)
	}
	return nodes, nil
}

// (numeric order removed; using only order_key)

// UpdateOrderKey updates order_key for a specific node-lesson row
func (r *nodeLessonRepository) UpdateOrderKey(ctx context.Context, nodeID uuid.UUID, lessonID string, newKey string) error {
	if newKey == "" {
		return fmt.Errorf("new order_key cannot be empty")
	}
	return r.db.WithContext(ctx).Model(&models.NodeLesson{}).
		Where("node_id = ? AND lesson_id = ?", nodeID, lessonID).
		Updates(map[string]interface{}{
			"order_key":  newKey,
			"updated_at": gorm.Expr("NOW()"),
		}).Error
}
