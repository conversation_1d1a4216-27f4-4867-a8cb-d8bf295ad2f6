/*
 * @Description: PersonalizeWork repository implementation
 * @Author: AI Assistant
 * @Date: 2025-08-16
 */
package repositories

import (
	"context"
	"fmt"
	"time"

	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/internal/models"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// PersonalizeWorkRepositoryInterface defines the interface for PersonalizeWork repository
type PersonalizeWorkRepositoryInterface interface {
	Create(ctx context.Context, work *models.PersonalizeWork) error
	GetByID(ctx context.Context, id uuid.UUID) (*models.PersonalizeWork, error)
	GetByConversationID(ctx context.Context, conversationID string) (*models.PersonalizeWork, error)
	GetByUserID(ctx context.Context, userID uuid.UUID) ([]*models.PersonalizeWork, error)
	GetActiveWorkByUserID(ctx context.Context, userID uuid.UUID) (*models.PersonalizeWork, error)
	Update(ctx context.Context, work *models.PersonalizeWork) error
	UpdateStatus(ctx context.Context, id uuid.UUID, status models.PersonalizeWorkStatus) error
	UpdateLastOperation(ctx context.Context, id uuid.UUID) error
	SetWorkingPath(ctx context.Context, id uuid.UUID, workingPathID uuid.UUID) error
	MarkCompleted(ctx context.Context, id uuid.UUID) error
	MarkFailed(ctx context.Context, id uuid.UUID, errorMsg string) error
	Delete(ctx context.Context, id uuid.UUID) error
	GetRecentWorks(ctx context.Context, userID uuid.UUID, limit int) ([]*models.PersonalizeWork, error)
	GetWorksByStatus(ctx context.Context, userID uuid.UUID, status models.PersonalizeWorkStatus) ([]*models.PersonalizeWork, error)
}

// PersonalizeWorkRepository implements PersonalizeWorkRepositoryInterface
type PersonalizeWorkRepository struct {
	db *gorm.DB
}

// NewPersonalizeWorkRepository creates a new PersonalizeWork repository
func NewPersonalizeWorkRepository(db *gorm.DB) PersonalizeWorkRepositoryInterface {
	return &PersonalizeWorkRepository{db: db}
}

// Create creates a new personalize work record
func (r *PersonalizeWorkRepository) Create(ctx context.Context, work *models.PersonalizeWork) error {
	if err := r.db.WithContext(ctx).Create(work).Error; err != nil {
		return fmt.Errorf("failed to create personalize work: %w", err)
	}
	return nil
}

// GetByID retrieves a personalize work by ID
func (r *PersonalizeWorkRepository) GetByID(ctx context.Context, id uuid.UUID) (*models.PersonalizeWork, error) {
	var work models.PersonalizeWork
	if err := r.db.WithContext(ctx).Preload("User").First(&work, "id = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("personalize work not found with id %s", id)
		}
		return nil, fmt.Errorf("failed to get personalize work: %w", err)
	}
	return &work, nil
}

// GetByConversationID retrieves a personalize work by conversation ID
func (r *PersonalizeWorkRepository) GetByConversationID(ctx context.Context, conversationID string) (*models.PersonalizeWork, error) {
	var work models.PersonalizeWork
	if err := r.db.WithContext(ctx).Preload("User").First(&work, "conversation_id = ?", conversationID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("personalize work not found with conversation_id %s", conversationID)
		}
		return nil, fmt.Errorf("failed to get personalize work: %w", err)
	}
	return &work, nil
}

// GetByUserID retrieves all personalize works for a user
func (r *PersonalizeWorkRepository) GetByUserID(ctx context.Context, userID uuid.UUID) ([]*models.PersonalizeWork, error) {
	var works []*models.PersonalizeWork
	if err := r.db.WithContext(ctx).
		Where("user_id = ?", userID).
		Order("created_at DESC").
		Find(&works).Error; err != nil {
		return nil, fmt.Errorf("failed to get personalize works for user %s: %w", userID, err)
	}
	return works, nil
}

// GetActiveWorkByUserID retrieves the active (in_progress) personalize work for a user
func (r *PersonalizeWorkRepository) GetActiveWorkByUserID(ctx context.Context, userID uuid.UUID) (*models.PersonalizeWork, error) {
	var work models.PersonalizeWork
	if err := r.db.WithContext(ctx).
		Preload("User").
		Where("user_id = ? AND status = ?", userID, models.PersonalizeWorkStatusInProgress).
		Order("last_operation DESC").
		First(&work).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil // No active work found is not an error
		}
		return nil, fmt.Errorf("failed to get active personalize work: %w", err)
	}
	return &work, nil
}

// Update updates a personalize work record
func (r *PersonalizeWorkRepository) Update(ctx context.Context, work *models.PersonalizeWork) error {
	if err := r.db.WithContext(ctx).Save(work).Error; err != nil {
		return fmt.Errorf("failed to update personalize work: %w", err)
	}
	return nil
}

// UpdateStatus updates the status of a personalize work
func (r *PersonalizeWorkRepository) UpdateStatus(ctx context.Context, id uuid.UUID, status models.PersonalizeWorkStatus) error {
	now := time.Now()
	updates := map[string]interface{}{
		"status":         status,
		"last_operation": now,
	}
	
	// Set completion time if marking as completed
	if status == models.PersonalizeWorkStatusCompleted {
		updates["completed_at"] = now
	}
	
	if err := r.db.WithContext(ctx).
		Model(&models.PersonalizeWork{}).
		Where("id = ?", id).
		Updates(updates).Error; err != nil {
		return fmt.Errorf("failed to update personalize work status: %w", err)
	}
	return nil
}

// UpdateLastOperation updates the last operation time
func (r *PersonalizeWorkRepository) UpdateLastOperation(ctx context.Context, id uuid.UUID) error {
	if err := r.db.WithContext(ctx).
		Model(&models.PersonalizeWork{}).
		Where("id = ?", id).
		Update("last_operation", time.Now()).Error; err != nil {
		return fmt.Errorf("failed to update last operation time: %w", err)
	}
	return nil
}

// SetWorkingPath sets the working path ID for a personalize work
func (r *PersonalizeWorkRepository) SetWorkingPath(ctx context.Context, id uuid.UUID, workingPathID uuid.UUID) error {
	updates := map[string]interface{}{
		"working_path_id": workingPathID,
		"last_operation":  time.Now(),
	}
	
	if err := r.db.WithContext(ctx).
		Model(&models.PersonalizeWork{}).
		Where("id = ?", id).
		Updates(updates).Error; err != nil {
		return fmt.Errorf("failed to set working path: %w", err)
	}
	return nil
}

// MarkCompleted marks a personalize work as completed
func (r *PersonalizeWorkRepository) MarkCompleted(ctx context.Context, id uuid.UUID) error {
	return r.UpdateStatus(ctx, id, models.PersonalizeWorkStatusCompleted)
}

// MarkFailed marks a personalize work as failed with an error message
func (r *PersonalizeWorkRepository) MarkFailed(ctx context.Context, id uuid.UUID, errorMsg string) error {
	now := time.Now()
	updates := map[string]interface{}{
		"status":         models.PersonalizeWorkStatusFailed,
		"error_message":  errorMsg,
		"last_operation": now,
	}
	
	if err := r.db.WithContext(ctx).
		Model(&models.PersonalizeWork{}).
		Where("id = ?", id).
		Updates(updates).Error; err != nil {
		return fmt.Errorf("failed to mark personalize work as failed: %w", err)
	}
	return nil
}

// Delete soft deletes a personalize work record
func (r *PersonalizeWorkRepository) Delete(ctx context.Context, id uuid.UUID) error {
	if err := r.db.WithContext(ctx).Delete(&models.PersonalizeWork{}, "id = ?", id).Error; err != nil {
		return fmt.Errorf("failed to delete personalize work: %w", err)
	}
	return nil
}

// GetRecentWorks retrieves the most recent personalize works for a user
func (r *PersonalizeWorkRepository) GetRecentWorks(ctx context.Context, userID uuid.UUID, limit int) ([]*models.PersonalizeWork, error) {
	var works []*models.PersonalizeWork
	if err := r.db.WithContext(ctx).
		Where("user_id = ?", userID).
		Order("last_operation DESC").
		Limit(limit).
		Find(&works).Error; err != nil {
		return nil, fmt.Errorf("failed to get recent personalize works: %w", err)
	}
	return works, nil
}

// GetWorksByStatus retrieves personalize works by status for a user
func (r *PersonalizeWorkRepository) GetWorksByStatus(ctx context.Context, userID uuid.UUID, status models.PersonalizeWorkStatus) ([]*models.PersonalizeWork, error) {
	var works []*models.PersonalizeWork
	if err := r.db.WithContext(ctx).
		Where("user_id = ? AND status = ?", userID, status).
		Order("last_operation DESC").
		Find(&works).Error; err != nil {
		return nil, fmt.Errorf("failed to get personalize works by status: %w", err)
	}
	return works, nil
}