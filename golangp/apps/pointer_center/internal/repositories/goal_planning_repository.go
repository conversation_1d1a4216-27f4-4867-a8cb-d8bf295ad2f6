package repositories

import (
	"context"

	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/internal/models"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type GoalPlanningRepository interface {
    Create(ctx context.Context, task *models.GoalPlanningTask) error
    Update(ctx context.Context, task *models.GoalPlanningTask) error
    GetByID(ctx context.Context, id uuid.UUID) (*models.GoalPlanningTask, error)
    GetByConversationID(ctx context.Context, convID string) (*models.GoalPlanningTask, error)
}

type goalPlanningRepository struct { db *gorm.DB }

func NewGoalPlanningRepository(db *gorm.DB) GoalPlanningRepository { return &goalPlanningRepository{db: db} }

func (r *goalPlanningRepository) Create(ctx context.Context, task *models.GoalPlanningTask) error {
    return r.db.WithContext(ctx).Create(task).Error
}

func (r *goalPlanningRepository) Update(ctx context.Context, task *models.GoalPlanningTask) error {
    return r.db.WithContext(ctx).Save(task).Error
}

func (r *goalPlanningRepository) GetByID(ctx context.Context, id uuid.UUID) (*models.GoalPlanningTask, error) {
    var task models.GoalPlanningTask
    if err := r.db.WithContext(ctx).First(&task, "id = ?", id).Error; err != nil {
        return nil, err
    }
    return &task, nil
}

func (r *goalPlanningRepository) GetByConversationID(ctx context.Context, convID string) (*models.GoalPlanningTask, error) {
    var task models.GoalPlanningTask
    if err := r.db.WithContext(ctx).Where("conversation_id = ?", convID).Order("created_at DESC").First(&task).Error; err != nil {
        return nil, err
    }
    return &task, nil
}
