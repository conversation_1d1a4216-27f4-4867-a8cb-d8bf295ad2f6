package repositories

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/internal/models"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// LearningPathRepository interface defines methods for learning path operations
type LearningPathRepository interface {
	Create(ctx context.Context, path *models.LearningPath) error
	GetByID(ctx context.Context, id uuid.UUID) (*models.LearningPath, error)
	Update(ctx context.Context, path *models.LearningPath) error
	Delete(ctx context.Context, id uuid.UUID) error
	List(ctx context.Context, limit, offset int) ([]*models.LearningPath, error)
	// ListByUser retrieves paths by assignee user (owner/holder of the path)
	ListByUser(ctx context.Context, userID uuid.UUID, limit, offset int) ([]*models.LearningPath, error)
	ListByType(ctx context.Context, pathType string, limit, offset int) ([]*models.LearningPath, error)
	ListByDifficulty(ctx context.Context, difficulty models.DifficultyLevel, limit, offset int) ([]*models.LearningPath, error)
	ListByCreator(ctx context.Context, creatorID uuid.UUID, limit, offset int) ([]*models.LearningPath, error)
	Count(ctx context.Context) (int64, error)
	CountPublic(ctx context.Context) (int64, error)
	CountByType(ctx context.Context, pathType string) (int64, error)
	Search(ctx context.Context, query string, limit, offset int) ([]*models.LearningPath, error)
	GetPublicPaths(ctx context.Context, limit, offset int) ([]*models.LearningPath, error)
	// GetPublicPathsByTags returns public paths filtered by tag names; matchAll indicates AND semantics
	GetPublicPathsByTags(ctx context.Context, tagNames []string, matchAll bool, limit, offset int) ([]*models.LearningPath, error)
	GetPopularPaths(ctx context.Context, limit, offset int) ([]*models.LearningPath, error)
	// CountPublicByTags counts public paths filtered by tag names with AND/OR semantics
	CountPublicByTags(ctx context.Context, tagNames []string, matchAll bool) (int64, error)
	GetPathsByEstimatedTime(ctx context.Context, minHours, maxHours int) ([]*models.LearningPath, error)
	IncrementUsageCount(ctx context.Context, pathID uuid.UUID) error
	UpdateRating(ctx context.Context, pathID uuid.UUID, rating float64) error
	ExistsByID(ctx context.Context, id uuid.UUID) (bool, error)
	GetPathStatistics(ctx context.Context) (map[string]interface{}, error)
	UpdateProgress(ctx context.Context, pathID uuid.UUID, completedNodes, completedLessons int, progressPercent float64) error
	UpdateCurrentPosition(ctx context.Context, pathID, nodeID uuid.UUID, lessonID string) error
	UpdateProgressStatus(ctx context.Context, pathID uuid.UUID, status models.ProgressStatus) error
}

// learningPathRepository implements LearningPathRepository interface
type learningPathRepository struct {
	db *gorm.DB
}

// UserNodeProgressRepository interface defines methods for user node progress operations
type UserNodeProgressRepository interface {
	Create(ctx context.Context, rec *models.UserNodeProgress) error
	GetByUserNode(ctx context.Context, userID, nodeID uuid.UUID) (*models.UserNodeProgress, error)
	// IncrementRef increments/decrements ref_count for a specific user+node and returns the updated ref_count
	IncrementRef(ctx context.Context, userID, nodeID uuid.UUID, delta int) (int, error)
	DeleteByUserNode(ctx context.Context, userID, nodeID uuid.UUID) error
	ListByUser(ctx context.Context, userID uuid.UUID) ([]*models.UserNodeProgress, error)
}

// UserLessonProgressRepository interface defines methods for user lesson progress operations
type UserLessonProgressRepository interface {
	Create(ctx context.Context, rec *models.UserLessonProgress) error
	GetByUserPathNodeLesson(ctx context.Context, userID, nodeID uuid.UUID, lessonID string) (*models.UserLessonProgress, error)
	GetAllByUserPathNode(ctx context.Context, userID, nodeID uuid.UUID) ([]*models.UserLessonProgress, error)
	GetAllByUser(ctx context.Context, userID uuid.UUID) ([]*models.UserLessonProgress, error)
	Update(ctx context.Context, rec *models.UserLessonProgress) error
	DeleteByUserPathNode(ctx context.Context, userID, pathID, nodeID uuid.UUID) error

	// ContentFlow progress methods
	AddCompletedContentFlow(ctx context.Context, userID, nodeID uuid.UUID, lessonID, contentFlowID string, progress float64) error
	RemoveCompletedContentFlow(ctx context.Context, userID, nodeID uuid.UUID, lessonID, contentFlowID string, progress float64) error
	GetCompletedContentFlows(ctx context.Context, userID, nodeID uuid.UUID, lessonID string) ([]string, error)
	UpdateCompletedContentFlows(ctx context.Context, userID, pathID, nodeID uuid.UUID, lessonID string, contentFlowIDs []string) error
}

// NewLearningPathRepository creates a new learning path repository
func NewLearningPathRepository(db *gorm.DB) LearningPathRepository {
	return &learningPathRepository{db: db}
}

// NewUserNodeProgressRepository creates a new user node progress repository
func NewUserNodeProgressRepository(db *gorm.DB) UserNodeProgressRepository {
	return &userNodeProgressRepository{db: db}
}

// NewUserLessonProgressRepository creates a new user lesson progress repository
func NewUserLessonProgressRepository(db *gorm.DB) UserLessonProgressRepository {
	return &userLessonProgressRepository{db: db}
}

// ============================================================================
// LearningPathRepository Implementation
// ============================================================================

// Create creates a new learning path
func (r *learningPathRepository) Create(ctx context.Context, path *models.LearningPath) error {
	if err := r.db.WithContext(ctx).Create(path).Error; err != nil {
		return fmt.Errorf("failed to create learning path: %w", err)
	}
	return nil
}

// GetByID retrieves a learning path by ID
func (r *learningPathRepository) GetByID(ctx context.Context, id uuid.UUID) (*models.LearningPath, error) {
	var path models.LearningPath
	if err := r.db.WithContext(ctx).Where("id = ?", id).First(&path).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("learning path not found with id %s", id.String())
		}
		return nil, fmt.Errorf("failed to get learning path: %w", err)
	}
	return &path, nil
}

// Update updates an existing learning path
func (r *learningPathRepository) Update(ctx context.Context, path *models.LearningPath) error {
	if err := r.db.WithContext(ctx).Save(path).Error; err != nil {
		return fmt.Errorf("failed to update learning path: %w", err)
	}
	return nil
}

// Delete deletes a learning path by ID (soft delete)
func (r *learningPathRepository) Delete(ctx context.Context, id uuid.UUID) error {
	if err := r.db.WithContext(ctx).Delete(&models.LearningPath{}, "id = ?", id).Error; err != nil {
		return fmt.Errorf("failed to delete learning path: %w", err)
	}
	return nil
}

// List retrieves a list of learning paths with pagination
func (r *learningPathRepository) List(ctx context.Context, limit, offset int) ([]*models.LearningPath, error) {
	var paths []*models.LearningPath
	if err := r.db.WithContext(ctx).Limit(limit).Offset(offset).Order("created_at DESC").Find(&paths).Error; err != nil {
		return nil, fmt.Errorf("failed to list learning paths: %w", err)
	}
	return paths, nil
}

// ListByUser retrieves learning paths by user (assignee/owner)
func (r *learningPathRepository) ListByUser(ctx context.Context, userID uuid.UUID, limit, offset int) ([]*models.LearningPath, error) {
	var paths []*models.LearningPath
	if err := r.db.WithContext(ctx).Where("user_id = ?", userID).
		Limit(limit).Offset(offset).Order("created_at DESC").Find(&paths).Error; err != nil {
		return nil, fmt.Errorf("failed to list paths by user: %w", err)
	}
	return paths, nil
}

// ListByType retrieves learning paths by type
func (r *learningPathRepository) ListByType(ctx context.Context, pathType string, limit, offset int) ([]*models.LearningPath, error) {
	var paths []*models.LearningPath
	if err := r.db.WithContext(ctx).Where("path_type = ?", pathType).
		Limit(limit).Offset(offset).Order("created_at DESC").Find(&paths).Error; err != nil {
		return nil, fmt.Errorf("failed to list paths by type: %w", err)
	}
	return paths, nil
}

// ListByDifficulty retrieves learning paths by difficulty level
func (r *learningPathRepository) ListByDifficulty(ctx context.Context, difficulty models.DifficultyLevel, limit, offset int) ([]*models.LearningPath, error) {
	var paths []*models.LearningPath
	if err := r.db.WithContext(ctx).Where("difficulty = ?", difficulty).
		Limit(limit).Offset(offset).Order("created_at DESC").Find(&paths).Error; err != nil {
		return nil, fmt.Errorf("failed to list paths by difficulty: %w", err)
	}
	return paths, nil
}

// ListByCreator retrieves learning paths by creator
func (r *learningPathRepository) ListByCreator(ctx context.Context, creatorID uuid.UUID, limit, offset int) ([]*models.LearningPath, error) {
	var paths []*models.LearningPath
	if err := r.db.WithContext(ctx).Where("creator_id = ?", creatorID).
		Limit(limit).Offset(offset).Order("created_at DESC").Find(&paths).Error; err != nil {
		return nil, fmt.Errorf("failed to list paths by creator: %w", err)
	}
	return paths, nil
}

// Count returns the total number of learning paths
func (r *learningPathRepository) Count(ctx context.Context) (int64, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&models.LearningPath{}).Count(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to count learning paths: %w", err)
	}
	return count, nil
}

// CountPublic returns the number of public learning paths
func (r *learningPathRepository) CountPublic(ctx context.Context) (int64, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&models.LearningPath{}).Where("is_public = ?", true).Count(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to count public learning paths: %w", err)
	}
	return count, nil
}

// CountPublicByTags counts public paths filtered by tag names using AND/OR logic
func (r *learningPathRepository) CountPublicByTags(ctx context.Context, tagNames []string, matchAll bool) (int64, error) {
	if len(tagNames) == 0 {
		return r.CountPublic(ctx)
	}
	names := make([]string, 0, len(tagNames))
	for _, n := range tagNames { if n != "" { names = append(names, n) } }
	if len(names) == 0 { return r.CountPublic(ctx) }

	q := r.db.WithContext(ctx).Table("learning_paths lp").
		Joins("JOIN learning_path_tags lpt ON lp.id = lpt.learning_path_id").
		Joins("JOIN tags t ON t.id = lpt.tag_id").
		Where("lp.is_public = ?", true)

	var count int64
	if matchAll {
		// Count grouped rows that satisfy all tag names
		sub := q.Where("t.name IN ?", names).
			Group("lp.id").
			Having("COUNT(DISTINCT t.id) = ?", len(names)).
			Select("lp.id")
		// Scan and count rows
		type row struct{ ID uuid.UUID }
		var rows []row
		if err := sub.Scan(&rows).Error; err != nil {
			return 0, fmt.Errorf("failed to count public paths by tags (AND): %w", err)
		}
		count = int64(len(rows))
	} else {
		if err := q.Where("t.name IN ?", names).Distinct("lp.id").Count(&count).Error; err != nil {
			return 0, fmt.Errorf("failed to count public paths by tags (OR): %w", err)
		}
	}
	return count, nil
}

// CountByType returns the number of paths by type
func (r *learningPathRepository) CountByType(ctx context.Context, pathType string) (int64, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&models.LearningPath{}).Where("path_type = ?", pathType).Count(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to count paths by type: %w", err)
	}
	return count, nil
}

// Search searches for paths by title, description, or goal
func (r *learningPathRepository) Search(ctx context.Context, query string, limit, offset int) ([]*models.LearningPath, error) {
	var paths []*models.LearningPath
	searchQuery := "%" + query + "%"
	if err := r.db.WithContext(ctx).Where("title ILIKE ? OR description ILIKE ? OR goal ILIKE ?",
		searchQuery, searchQuery, searchQuery).
		Limit(limit).Offset(offset).Order("created_at DESC").Find(&paths).Error; err != nil {
		return nil, fmt.Errorf("failed to search learning paths: %w", err)
	}
	return paths, nil
}

// GetPublicPaths retrieves public learning paths
func (r *learningPathRepository) GetPublicPaths(ctx context.Context, limit, offset int) ([]*models.LearningPath, error) {
	var paths []*models.LearningPath
	if err := r.db.WithContext(ctx).Where("is_public = ?", true).
		Limit(limit).Offset(offset).Order("usage_count DESC, rating DESC").Find(&paths).Error; err != nil {
		return nil, fmt.Errorf("failed to get public paths: %w", err)
	}
	return paths, nil
}

// GetPublicPathsByTags retrieves public learning paths filtered by tag names with AND/OR logic
func (r *learningPathRepository) GetPublicPathsByTags(ctx context.Context, tagNames []string, matchAll bool, limit, offset int) ([]*models.LearningPath, error) {
	// Base: public paths
	dbq := r.db.WithContext(ctx).Model(&models.LearningPath{}).Where("is_public = ?", true)
	if len(tagNames) == 0 {
		var paths []*models.LearningPath
		if err := dbq.Limit(limit).Offset(offset).Order("usage_count DESC, rating DESC").Find(&paths).Error; err != nil {
			return nil, fmt.Errorf("failed to get public paths: %w", err)
		}
		return paths, nil
	}

	// join with tags associations
	q := r.db.WithContext(ctx).Table("learning_paths lp").
		Joins("JOIN learning_path_tags lpt ON lp.id = lpt.learning_path_id").
		Joins("JOIN tags t ON t.id = lpt.tag_id").
		Where("lp.is_public = ?", true)

	// Normalize names and build filter
	names := make([]string, 0, len(tagNames))
	for _, n := range tagNames {
		if n == "" { continue }
		names = append(names, n)
	}
	if len(names) == 0 {
		var paths []*models.LearningPath
		if err := dbq.Limit(limit).Offset(offset).Order("usage_count DESC, rating DESC").Find(&paths).Error; err != nil {
			return nil, fmt.Errorf("failed to get public paths: %w", err)
		}
		return paths, nil
	}

	var paths []*models.LearningPath
	if matchAll {
		// AND: group by path and require count(distinct tag) == len(names)
		if err := q.Where("t.name IN ?", names).
			Group("lp.id").
			Having("COUNT(DISTINCT t.id) = ?", len(names)).
			Select("lp.*").
			Order("lp.usage_count DESC, lp.rating DESC").
			Limit(limit).Offset(offset).
			Scan(&paths).Error; err != nil {
			return nil, fmt.Errorf("failed to get public paths by tags (AND): %w", err)
		}
	} else {
		// OR: any tag matches
		if err := q.Where("t.name IN ?", names).
			Distinct().
			Select("lp.*").
			Order("lp.usage_count DESC, lp.rating DESC").
			Limit(limit).Offset(offset).
			Scan(&paths).Error; err != nil {
			return nil, fmt.Errorf("failed to get public paths by tags (OR): %w", err)
		}
	}
	return paths, nil
}

// GetPopularPaths retrieves popular learning paths by usage count and rating
func (r *learningPathRepository) GetPopularPaths(ctx context.Context, limit, offset int) ([]*models.LearningPath, error) {
	var paths []*models.LearningPath
	if err := r.db.WithContext(ctx).Where("is_public = ?", true).
		Order("usage_count DESC, rating DESC").
		Limit(limit).Offset(offset).Find(&paths).Error; err != nil {
		return nil, fmt.Errorf("failed to get popular paths: %w", err)
	}
	return paths, nil
}

// GetPathsByEstimatedTime retrieves paths by estimated time range
func (r *learningPathRepository) GetPathsByEstimatedTime(ctx context.Context, minHours, maxHours int) ([]*models.LearningPath, error) {
	var paths []*models.LearningPath
	if err := r.db.WithContext(ctx).Where("estimated_times BETWEEN ? AND ?", minHours, maxHours).Find(&paths).Error; err != nil {
		return nil, fmt.Errorf("failed to get paths by estimated time: %w", err)
	}
	return paths, nil
}

// IncrementUsageCount increments the usage count of a path
func (r *learningPathRepository) IncrementUsageCount(ctx context.Context, pathID uuid.UUID) error {
	if err := r.db.WithContext(ctx).Model(&models.LearningPath{}).
		Where("id = ?", pathID).Update("usage_count", gorm.Expr("usage_count + 1")).Error; err != nil {
		return fmt.Errorf("failed to increment usage count: %w", err)
	}
	return nil
}

// UpdateRating updates the rating of a path
func (r *learningPathRepository) UpdateRating(ctx context.Context, pathID uuid.UUID, rating float64) error {
	if err := r.db.WithContext(ctx).Model(&models.LearningPath{}).
		Where("id = ?", pathID).Update("rating", rating).Error; err != nil {
		return fmt.Errorf("failed to update rating: %w", err)
	}
	return nil
}

// ExistsByID checks if a path exists by ID
func (r *learningPathRepository) ExistsByID(ctx context.Context, id uuid.UUID) (bool, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&models.LearningPath{}).Where("id = ?", id).Count(&count).Error; err != nil {
		return false, fmt.Errorf("failed to check if path exists: %w", err)
	}
	return count > 0, nil
}

// GetPathStatistics returns various statistics about paths
func (r *learningPathRepository) GetPathStatistics(ctx context.Context) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// Total count
	totalCount, err := r.Count(ctx)
	if err != nil {
		return nil, err
	}
	stats["total"] = totalCount

	// Public count
	var publicCount int64
	if err := r.db.WithContext(ctx).Model(&models.LearningPath{}).Where("is_public = ?", true).Count(&publicCount).Error; err != nil {
		return nil, fmt.Errorf("failed to count public paths: %w", err)
	}
	stats["public"] = publicCount

	// Average rating
	var avgRating float64
	if err := r.db.WithContext(ctx).Model(&models.LearningPath{}).Select("AVG(rating)").Scan(&avgRating).Error; err != nil {
		return nil, fmt.Errorf("failed to calculate average rating: %w", err)
	}
	stats["average_rating"] = avgRating

	return stats, nil
}

func (r *learningPathRepository) UpdateProgress(ctx context.Context, pathID uuid.UUID, completedNodes, completedLessons int, progressPercent float64) error {
	now := time.Now()
	if err := r.db.WithContext(ctx).Model(&models.LearningPath{}).
		Where("id = ?", pathID).
		Updates(map[string]interface{}{
			"completed_nodes":   completedNodes,
			"completed_lessons": completedLessons,
			"progress_percent":  progressPercent,
			"last_accessed_at":  &now,
		}).Error; err != nil {
		return fmt.Errorf("failed to update path progress: %w", err)
	}
	return nil
}

func (r *learningPathRepository) UpdateCurrentPosition(ctx context.Context, pathID, nodeID uuid.UUID, lessonID string) error {
	now := time.Now()
	if err := r.db.WithContext(ctx).Model(&models.LearningPath{}).
		Where("id = ?", pathID).
		Updates(map[string]interface{}{
			"current_node_id":   nodeID,
			"current_lesson_id": lessonID,
			"last_accessed_at":  &now,
		}).Error; err != nil {
		return fmt.Errorf("failed to update current position: %w", err)
	}
	return nil
}

func (r *learningPathRepository) UpdateProgressStatus(ctx context.Context, pathID uuid.UUID, status models.ProgressStatus) error {
	if err := r.db.WithContext(ctx).Model(&models.LearningPath{}).
		Where("id = ?", pathID).Update("progress_status", status).Error; err != nil {
		return fmt.Errorf("failed to update progress status: %w", err)
	}
	return nil
}

// ============================================================================
// UserNodeProgressRepository Implementation
// ============================================================================

type userNodeProgressRepository struct {
	db *gorm.DB
}

// Create creates a new user node progress record
func (r *userNodeProgressRepository) Create(ctx context.Context, rec *models.UserNodeProgress) error {
	if err := r.db.WithContext(ctx).Create(rec).Error; err != nil {
		return fmt.Errorf("failed to create user node progress: %w", err)
	}
	return nil
}

// GetByUserNode retrieves a record by composite keys
func (r *userNodeProgressRepository) GetByUserNode(ctx context.Context, userID, nodeID uuid.UUID) (*models.UserNodeProgress, error) {
	var rec models.UserNodeProgress
	if err := r.db.WithContext(ctx).Where("user_id = ? AND node_id = ?", userID, nodeID).First(&rec).Error; err != nil {
		return nil, err
	}
	return &rec, nil
}

// IncrementRef increments/decrements ref_count for a specific user+path+node
func (r *userNodeProgressRepository) IncrementRef(ctx context.Context, userID, nodeID uuid.UUID, delta int) (int, error) {
	// First try to update existing record
	res := r.db.WithContext(ctx).Model(&models.UserNodeProgress{}).
		Where("user_id = ? AND node_id = ?", userID, nodeID).
		Update("ref_count", gorm.Expr("GREATEST(ref_count + ?, 0)", delta))
	if res.Error != nil {
		return 0, fmt.Errorf("failed to update ref_count: %w", res.Error)
	}
	if res.RowsAffected == 0 {
		// No existing row; create one only when delta > 0
		if delta > 0 {
			now := time.Now()
			rec := &models.UserNodeProgress{
				ID:        uuid.New(),
				UserID:    userID,
				NodeID:    nodeID,
				RefCount:  delta,
				CreatedAt: now,
				UpdatedAt: now,
			}
			if err := r.db.WithContext(ctx).Create(rec).Error; err != nil {
				return 0, fmt.Errorf("failed to create user node progress: %w", err)
			}
			return rec.RefCount, nil
		}
		// delta <= 0 and no row existed => ref_count is logically 0
		return 0, nil
	}

	// Fetch updated count
	var out models.UserNodeProgress
	if err := r.db.WithContext(ctx).Where("user_id = ? AND node_id = ?", userID, nodeID).First(&out).Error; err != nil {
		return 0, fmt.Errorf("failed to fetch updated ref_count: %w", err)
	}
	return out.RefCount, nil
}

// DeleteByUserNode deletes a record by composite keys
func (r *userNodeProgressRepository) DeleteByUserNode(ctx context.Context, userID, nodeID uuid.UUID) error {
	if err := r.db.WithContext(ctx).Where("user_id = ? AND node_id = ?", userID, nodeID).
		Delete(&models.UserNodeProgress{}).Error; err != nil {
		return fmt.Errorf("failed to delete user node progress: %w", err)
	}
	return nil
}

// ListByUser lists all node progress for a user within a path
func (r *userNodeProgressRepository) ListByUser(ctx context.Context, userID uuid.UUID) ([]*models.UserNodeProgress, error) {
	var recs []*models.UserNodeProgress
	if err := r.db.WithContext(ctx).Where("user_id = ?", userID).Find(&recs).Error; err != nil {
		return nil, fmt.Errorf("failed to list user node progress: %w", err)
	}
	return recs, nil
}

// ============================================================================
// UserLessonProgressRepository Implementation
// ============================================================================

type userLessonProgressRepository struct {
	db *gorm.DB
}

// Create creates a new user lesson progress record
func (r *userLessonProgressRepository) Create(ctx context.Context, rec *models.UserLessonProgress) error {
	if err := r.db.WithContext(ctx).Create(rec).Error; err != nil {
		return fmt.Errorf("failed to create user lesson progress: %w", err)
	}
	return nil
}

// GetByUserPathNodeLesson retrieves a record by composite keys
func (r *userLessonProgressRepository) GetByUserPathNodeLesson(ctx context.Context, userID, nodeID uuid.UUID, lessonID string) (*models.UserLessonProgress, error) {
	var rec models.UserLessonProgress
	if err := r.db.WithContext(ctx).Where("user_id = ? AND  node_id = ? AND lesson_id = ?", userID, nodeID, lessonID).First(&rec).Error; err != nil {
		return nil, err
	}
	return &rec, nil
}

// GetAllByUserPathNode gets all lesson progress for a user within a path or node
func (r *userLessonProgressRepository) GetAllByUserPathNode(ctx context.Context, userID, nodeID uuid.UUID) ([]*models.UserLessonProgress, error) {
	var records []*models.UserLessonProgress

	query := r.db.WithContext(ctx).Where("user_id = ?", userID)

	if nodeID != uuid.Nil {
		query = query.Where("node_id = ?", nodeID)
	}

	if err := query.Find(&records).Error; err != nil {
		return nil, fmt.Errorf("failed to get user lesson progress records: %w", err)
	}

	return records, nil
}

// GetAllByUser gets all lesson progress for a user across all paths and nodes
func (r *userLessonProgressRepository) GetAllByUser(ctx context.Context, userID uuid.UUID) ([]*models.UserLessonProgress, error) {
	var records []*models.UserLessonProgress

	if err := r.db.WithContext(ctx).Where("user_id = ?", userID).Find(&records).Error; err != nil {
		return nil, fmt.Errorf("failed to get all user lesson progress records: %w", err)
	}

	return records, nil
}

// Update updates an existing user lesson progress record
func (r *userLessonProgressRepository) Update(ctx context.Context, rec *models.UserLessonProgress) error {
	if err := r.db.WithContext(ctx).Save(rec).Error; err != nil {
		return fmt.Errorf("failed to update user lesson progress: %w", err)
	}
	return nil
}

// DeleteByUserPathNode deletes all lesson progress under a node for a user within a path
func (r *userLessonProgressRepository) DeleteByUserPathNode(ctx context.Context, userID, pathID, nodeID uuid.UUID) error {
	if err := r.db.WithContext(ctx).Where("user_id = ? AND learning_path_id = ? AND node_id = ?", userID, pathID, nodeID).
		Delete(&models.UserLessonProgress{}).Error; err != nil {
		return fmt.Errorf("failed to delete user lesson progress: %w", err)
	}
	return nil
}

// AddCompletedContentFlow adds a ContentFlow ID to the completed list
func (r *userLessonProgressRepository) AddCompletedContentFlow(ctx context.Context, userID, nodeID uuid.UUID, lessonID, contentFlowID string, progress float64) error {
	// Try to get existing record first
	rec, err := r.GetByUserPathNodeLesson(ctx, userID, nodeID, lessonID)
	if err != nil {
		// Check if it's a "record not found" error
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// Create new record if it doesn't exist
			now := time.Now()
			rec = &models.UserLessonProgress{
				ID:          uuid.New(),
				UserID:      userID,
				LessonID:    lessonID,
				NodeID:      nodeID,
				Progress:    progress,
				IsCompleted: progress >= 1.0,
				CreatedAt:   now,
				UpdatedAt:   now,
			}

			// Set completion time if completed
			if progress >= 1.0 {
				rec.CompletedAt = &now
			}

			// Add the content flow ID to the new record
			if err := rec.AddCompletedContentFlowID(contentFlowID); err != nil {
				return fmt.Errorf("failed to add completed content flow ID to new record: %w", err)
			}

			// Create the new record
			if err := r.Create(ctx, rec); err != nil {
				return fmt.Errorf("failed to create user lesson progress: %w", err)
			}
			return nil
		}
		return fmt.Errorf("failed to get user lesson progress: %w", err)
	}

	// Record exists, update it
	if err := rec.AddCompletedContentFlowID(contentFlowID); err != nil {
		return fmt.Errorf("failed to add completed content flow ID: %w", err)
	}

	// Update progress field
	now := time.Now()
	rec.Progress = progress
	rec.UpdatedAt = now

	// Update completion status if progress is 1.0
	if progress >= 1.0 {
		rec.IsCompleted = true
		rec.CompletedAt = &now
	}

	return r.Update(ctx, rec)
}

// RemoveCompletedContentFlow removes a ContentFlow ID from the completed list
func (r *userLessonProgressRepository) RemoveCompletedContentFlow(ctx context.Context, userID, nodeID uuid.UUID, lessonID, contentFlowID string, progress float64) error {
	rec, err := r.GetByUserPathNodeLesson(ctx, userID, nodeID, lessonID)
	if err != nil {
		return fmt.Errorf("failed to get user lesson progress: %w", err)
	}

	if err := rec.RemoveCompletedContentFlowID(contentFlowID); err != nil {
		return fmt.Errorf("failed to remove completed content flow ID: %w", err)
	}

	// Update progress field
	rec.Progress = progress
	rec.UpdatedAt = time.Now()

	// Update completion status based on progress
	if progress < 1.0 && rec.IsCompleted {
		// If progress is less than 1.0 and lesson was previously completed, mark as incomplete
		rec.IsCompleted = false
		rec.CompletedAt = nil

	}

	return r.Update(ctx, rec)
}

// GetCompletedContentFlows returns the list of completed ContentFlow IDs
func (r *userLessonProgressRepository) GetCompletedContentFlows(ctx context.Context, userID, nodeID uuid.UUID, lessonID string) ([]string, error) {
	rec, err := r.GetByUserPathNodeLesson(ctx, userID, nodeID, lessonID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user lesson progress: %w", err)
	}

	return rec.GetCompletedContentFlowIDs(), nil
}

// UpdateCompletedContentFlows updates the entire list of completed ContentFlow IDs
func (r *userLessonProgressRepository) UpdateCompletedContentFlows(ctx context.Context, userID, pathID, nodeID uuid.UUID, lessonID string, contentFlowIDs []string) error {
	rec, err := r.GetByUserPathNodeLesson(ctx, userID, nodeID, lessonID)
	if err != nil {
		return fmt.Errorf("failed to get user lesson progress: %w", err)
	}

	if err := rec.SetCompletedContentFlowIDs(contentFlowIDs); err != nil {
		return fmt.Errorf("failed to set completed content flow IDs: %w", err)
	}

	return r.Update(ctx, rec)
}
