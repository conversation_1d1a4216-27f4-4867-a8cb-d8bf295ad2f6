package repositories

import (
	"context"
	"fmt"
	"time"

	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/internal/models"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// LessonBrief represents minimal lesson info for batch queries
type LessonBrief struct {
	ID          string `bson:"_id" json:"id"`
	Title       string `bson:"title" json:"title"`
	Description string `bson:"description" json:"description"`
}

// LearningLessonRepository interface defines methods for learning lesson operations
type LearningLessonRepository interface {
	Create(ctx context.Context, lesson *models.LearningLesson) error
	GetByID(ctx context.Context, id primitive.ObjectID) (*models.LearningLesson, error)
	// GetByLessonID now expects Mongo ObjectID hex string and queries by _id
	GetByLessonID(ctx context.Context, lessonID string) (*models.LearningLesson, error)
	Update(ctx context.Context, lesson *models.LearningLesson) error
	Delete(ctx context.Context, id primitive.ObjectID) error
	List(ctx context.Context, limit, offset int) ([]*models.LearningLesson, error)
	ListByType(ctx context.Context, lessonType models.LessonType, limit, offset int) ([]*models.LearningLesson, error)
	ListByDifficulty(ctx context.Context, difficulty models.DifficultyLevel, limit, offset int) ([]*models.LearningLesson, error)
	ListByStatus(ctx context.Context, status models.LessonStatus, limit, offset int) ([]*models.LearningLesson, error)
	Count(ctx context.Context) (int64, error)
	CountByStatus(ctx context.Context, status models.LessonStatus) (int64, error)
	CountByType(ctx context.Context, lessonType models.LessonType) (int64, error)
	Search(ctx context.Context, query string, limit, offset int) ([]*models.LearningLesson, error)
	GetByCreator(ctx context.Context, creatorID string, limit, offset int) ([]*models.LearningLesson, error)
	GetActiveLessons(ctx context.Context, limit, offset int) ([]*models.LearningLesson, error)
	GetLessonsByEstimatedTime(ctx context.Context, minMinutes, maxMinutes int) ([]*models.LearningLesson, error)
	// The following methods expect lessonID to be Mongo ObjectID hex string
	AddContentToLesson(ctx context.Context, lessonID string, contentType models.AtomicContentType, data interface{}) error
	RemoveContentFromLesson(ctx context.Context, lessonID string, contentOrder int) error
	ReorderLessonContent(ctx context.Context, lessonID string, newOrder []int) error
	GetLessonsByContentType(ctx context.Context, contentType models.AtomicContentType) ([]*models.LearningLesson, error)
	UpdateLessonStatus(ctx context.Context, lessonID string, status models.LessonStatus) error
	GetLessonStatistics(ctx context.Context) (map[string]interface{}, error)
	// ExistsByLessonID expects Mongo ObjectID hex string
	ExistsByLessonID(ctx context.Context, lessonID string) (bool, error)
	// GetByLessonIDs retrieves multiple lessons by their IDs (expects Mongo ObjectID hex strings)
	GetByLessonIDs(ctx context.Context, lessonIDs []string) (map[string]*models.LearningLesson, error)
}

// learningLessonRepository implements LearningLessonRepository interface
type learningLessonRepository struct {
	collection *mongo.Collection
}

// NewLearningLessonRepository creates a new learning lesson repository
func NewLearningLessonRepository(db *mongo.Database) LearningLessonRepository {
	return &learningLessonRepository{
		collection: db.Collection("learning_lessons"),
	}
}

// Create creates a new learning lesson
func (r *learningLessonRepository) Create(ctx context.Context, lesson *models.LearningLesson) error {
	if lesson.ID.IsZero() {
		lesson.ID = primitive.NewObjectID()
	}
	lesson.CreatedAt = time.Now()
	lesson.UpdatedAt = time.Now()

	_, err := r.collection.InsertOne(ctx, lesson)
	if err != nil {
		return fmt.Errorf("failed to create learning lesson: %w", err)
	}
	return nil
}

// GetByID retrieves a learning lesson by ID
func (r *learningLessonRepository) GetByID(ctx context.Context, id primitive.ObjectID) (*models.LearningLesson, error) {
	var lesson models.LearningLesson
	err := r.collection.FindOne(ctx, bson.M{"_id": id}).Decode(&lesson)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, fmt.Errorf("learning lesson not found with id %s", id.Hex())
		}
		return nil, fmt.Errorf("failed to get learning lesson: %w", err)
	}
	return &lesson, nil
}

// GetByLessonID retrieves a learning lesson by lesson ID
func (r *learningLessonRepository) GetByLessonID(ctx context.Context, lessonID string) (*models.LearningLesson, error) {
	// Interpret lessonID as Mongo ObjectID hex string and fetch by _id
	oid, err := primitive.ObjectIDFromHex(lessonID)
	if err != nil {
		return nil, fmt.Errorf("invalid lesson_id hex: %w", err)
	}
	return r.GetByID(ctx, oid)
}

// Update updates an existing learning lesson
func (r *learningLessonRepository) Update(ctx context.Context, lesson *models.LearningLesson) error {
	lesson.UpdatedAt = time.Now()
	filter := bson.M{"_id": lesson.ID}
	_, err := r.collection.ReplaceOne(ctx, filter, lesson)
	if err != nil {
		return fmt.Errorf("failed to update learning lesson: %w", err)
	}
	return nil
}

// Delete deletes a learning lesson by ID
func (r *learningLessonRepository) Delete(ctx context.Context, id primitive.ObjectID) error {
	_, err := r.collection.DeleteOne(ctx, bson.M{"_id": id})
	if err != nil {
		return fmt.Errorf("failed to delete learning lesson: %w", err)
	}
	return nil
}

// List retrieves a list of learning lessons with pagination
func (r *learningLessonRepository) List(ctx context.Context, limit, offset int) ([]*models.LearningLesson, error) {
	opts := options.Find().SetLimit(int64(limit)).SetSkip(int64(offset)).SetSort(bson.D{{Key: "created_at", Value: -1}})
	cursor, err := r.collection.Find(ctx, bson.M{}, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to list learning lessons: %w", err)
	}
	defer cursor.Close(ctx)

	var lessons []*models.LearningLesson
	for cursor.Next(ctx) {
		var lesson models.LearningLesson
		if err := cursor.Decode(&lesson); err != nil {
			return nil, fmt.Errorf("failed to decode learning lesson: %w", err)
		}
		lessons = append(lessons, &lesson)
	}

	if err := cursor.Err(); err != nil {
		return nil, fmt.Errorf("cursor error: %w", err)
	}

	return lessons, nil
}

// ListByType retrieves learning lessons by type
func (r *learningLessonRepository) ListByType(ctx context.Context, lessonType models.LessonType, limit, offset int) ([]*models.LearningLesson, error) {
	filter := bson.M{"type": lessonType}
	opts := options.Find().SetLimit(int64(limit)).SetSkip(int64(offset)).SetSort(bson.D{{Key: "created_at", Value: -1}})

	cursor, err := r.collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to list lessons by type: %w", err)
	}
	defer cursor.Close(ctx)

	var lessons []*models.LearningLesson
	for cursor.Next(ctx) {
		var lesson models.LearningLesson
		if err := cursor.Decode(&lesson); err != nil {
			return nil, fmt.Errorf("failed to decode lesson: %w", err)
		}
		lessons = append(lessons, &lesson)
	}

	return lessons, nil
}

// ListByDifficulty retrieves learning lessons by difficulty level
func (r *learningLessonRepository) ListByDifficulty(ctx context.Context, difficulty models.DifficultyLevel, limit, offset int) ([]*models.LearningLesson, error) {
	filter := bson.M{"difficulty": difficulty}
	opts := options.Find().SetLimit(int64(limit)).SetSkip(int64(offset)).SetSort(bson.D{{Key: "created_at", Value: -1}})

	cursor, err := r.collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to list lessons by difficulty: %w", err)
	}
	defer cursor.Close(ctx)

	var lessons []*models.LearningLesson
	for cursor.Next(ctx) {
		var lesson models.LearningLesson
		if err := cursor.Decode(&lesson); err != nil {
			return nil, fmt.Errorf("failed to decode lesson: %w", err)
		}
		lessons = append(lessons, &lesson)
	}

	return lessons, nil
}

// ListByStatus retrieves learning lessons by status
func (r *learningLessonRepository) ListByStatus(ctx context.Context, status models.LessonStatus, limit, offset int) ([]*models.LearningLesson, error) {
	filter := bson.M{"status": status}
	opts := options.Find().SetLimit(int64(limit)).SetSkip(int64(offset)).SetSort(bson.D{{Key: "created_at", Value: -1}})

	cursor, err := r.collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to list lessons by status: %w", err)
	}
	defer cursor.Close(ctx)

	var lessons []*models.LearningLesson
	for cursor.Next(ctx) {
		var lesson models.LearningLesson
		if err := cursor.Decode(&lesson); err != nil {
			return nil, fmt.Errorf("failed to decode lesson: %w", err)
		}
		lessons = append(lessons, &lesson)
	}

	return lessons, nil
}

// Count returns the total number of learning lessons
func (r *learningLessonRepository) Count(ctx context.Context) (int64, error) {
	count, err := r.collection.CountDocuments(ctx, bson.M{})
	if err != nil {
		return 0, fmt.Errorf("failed to count learning lessons: %w", err)
	}
	return count, nil
}

// CountByStatus returns the number of lessons by status
func (r *learningLessonRepository) CountByStatus(ctx context.Context, status models.LessonStatus) (int64, error) {
	count, err := r.collection.CountDocuments(ctx, bson.M{"status": status})
	if err != nil {
		return 0, fmt.Errorf("failed to count lessons by status: %w", err)
	}
	return count, nil
}

// CountByType returns the number of lessons by type
func (r *learningLessonRepository) CountByType(ctx context.Context, lessonType models.LessonType) (int64, error) {
	count, err := r.collection.CountDocuments(ctx, bson.M{"type": lessonType})
	if err != nil {
		return 0, fmt.Errorf("failed to count lessons by type: %w", err)
	}
	return count, nil
}

// Search searches for lessons by title or description
func (r *learningLessonRepository) Search(ctx context.Context, query string, limit, offset int) ([]*models.LearningLesson, error) {
	filter := bson.M{
		"$or": []bson.M{
			{"title": bson.M{"$regex": query, "$options": "i"}},
			{"description": bson.M{"$regex": query, "$options": "i"}},
		},
	}
	opts := options.Find().SetLimit(int64(limit)).SetSkip(int64(offset)).SetSort(bson.D{{Key: "created_at", Value: -1}})

	cursor, err := r.collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to search lessons: %w", err)
	}
	defer cursor.Close(ctx)

	var lessons []*models.LearningLesson
	for cursor.Next(ctx) {
		var lesson models.LearningLesson
		if err := cursor.Decode(&lesson); err != nil {
			return nil, fmt.Errorf("failed to decode lesson: %w", err)
		}
		lessons = append(lessons, &lesson)
	}

	return lessons, nil
}

// GetByCreator retrieves lessons created by a specific user
func (r *learningLessonRepository) GetByCreator(ctx context.Context, creatorID string, limit, offset int) ([]*models.LearningLesson, error) {
	filter := bson.M{"created_by": creatorID}
	opts := options.Find().SetLimit(int64(limit)).SetSkip(int64(offset)).SetSort(bson.D{{Key: "created_at", Value: -1}})

	cursor, err := r.collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to get lessons by creator: %w", err)
	}
	defer cursor.Close(ctx)

	var lessons []*models.LearningLesson
	for cursor.Next(ctx) {
		var lesson models.LearningLesson
		if err := cursor.Decode(&lesson); err != nil {
			return nil, fmt.Errorf("failed to decode lesson: %w", err)
		}
		lessons = append(lessons, &lesson)
	}

	return lessons, nil
}

// GetActiveLessons retrieves only active lessons
func (r *learningLessonRepository) GetActiveLessons(ctx context.Context, limit, offset int) ([]*models.LearningLesson, error) {
	return r.ListByStatus(ctx, models.LessonStatusActive, limit, offset)
}

// GetLessonsByEstimatedTime retrieves lessons by estimated time range
func (r *learningLessonRepository) GetLessonsByEstimatedTime(ctx context.Context, minMinutes, maxMinutes int) ([]*models.LearningLesson, error) {
	filter := bson.M{
		"estimated_times": bson.M{
			"$gte": minMinutes * 60,
			"$lte": maxMinutes * 60,
		},
	}

	cursor, err := r.collection.Find(ctx, filter)
	if err != nil {
		return nil, fmt.Errorf("failed to get lessons by estimated time: %w", err)
	}
	defer cursor.Close(ctx)

	var lessons []*models.LearningLesson
	for cursor.Next(ctx) {
		var lesson models.LearningLesson
		if err := cursor.Decode(&lesson); err != nil {
			return nil, fmt.Errorf("failed to decode lesson: %w", err)
		}
		lessons = append(lessons, &lesson)
	}

	return lessons, nil
}

// AddContentToLesson adds content to a lesson's content flow
func (r *learningLessonRepository) AddContentToLesson(ctx context.Context, lessonID string, contentType models.AtomicContentType, data interface{}) error {
	lesson, err := r.GetByLessonID(ctx, lessonID)
	if err != nil {
		return err
	}

	lesson.AddContentFlow(contentType, data)
	return r.Update(ctx, lesson)
}

// RemoveContentFromLesson removes content from a lesson by order
func (r *learningLessonRepository) RemoveContentFromLesson(ctx context.Context, lessonID string, contentOrder int) error {
	oid, err := primitive.ObjectIDFromHex(lessonID)
	if err != nil {
		return fmt.Errorf("invalid lesson_id hex: %w", err)
	}
	filter := bson.M{"_id": oid}
	update := bson.M{
		"$pull": bson.M{
			"content_flow": bson.M{"order": contentOrder},
		},
		"$set": bson.M{"updated_at": time.Now()},
	}

	_, err = r.collection.UpdateOne(ctx, filter, update)
	if err != nil {
		return fmt.Errorf("failed to remove content from lesson: %w", err)
	}
	return nil
}

// ReorderLessonContent reorders the content flow of a lesson
func (r *learningLessonRepository) ReorderLessonContent(ctx context.Context, lessonID string, newOrder []int) error {
	lesson, err := r.GetByLessonID(ctx, lessonID)
	if err != nil {
		return err
	}

	if err := lesson.ReorderContentFlow(newOrder); err != nil {
		return err
	}

	return r.Update(ctx, lesson)
}

// GetLessonsByContentType retrieves lessons that contain a specific content type
func (r *learningLessonRepository) GetLessonsByContentType(ctx context.Context, contentType models.AtomicContentType) ([]*models.LearningLesson, error) {
	filter := bson.M{"content_flow.type": contentType}

	cursor, err := r.collection.Find(ctx, filter)
	if err != nil {
		return nil, fmt.Errorf("failed to get lessons by content type: %w", err)
	}
	defer cursor.Close(ctx)

	var lessons []*models.LearningLesson
	for cursor.Next(ctx) {
		var lesson models.LearningLesson
		if err := cursor.Decode(&lesson); err != nil {
			return nil, fmt.Errorf("failed to decode lesson: %w", err)
		}
		lessons = append(lessons, &lesson)
	}

	return lessons, nil
}

// UpdateLessonStatus updates the status of a lesson
func (r *learningLessonRepository) UpdateLessonStatus(ctx context.Context, lessonID string, status models.LessonStatus) error {
	oid, err := primitive.ObjectIDFromHex(lessonID)
	if err != nil {
		return fmt.Errorf("invalid lesson_id hex: %w", err)
	}
	filter := bson.M{"_id": oid}
	update := bson.M{
		"$set": bson.M{
			"status":     status,
			"updated_at": time.Now(),
		},
	}

	_, err = r.collection.UpdateOne(ctx, filter, update)
	if err != nil {
		return fmt.Errorf("failed to update lesson status: %w", err)
	}
	return nil
}

// GetLessonStatistics returns various statistics about lessons
func (r *learningLessonRepository) GetLessonStatistics(ctx context.Context) (map[string]interface{}, error) {
	pipeline := []bson.M{
		{
			"$group": bson.M{
				"_id":   "$status",
				"count": bson.M{"$sum": 1},
			},
		},
	}

	cursor, err := r.collection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, fmt.Errorf("failed to get lesson statistics: %w", err)
	}
	defer cursor.Close(ctx)

	stats := make(map[string]interface{})
	for cursor.Next(ctx) {
		var result bson.M
		if err := cursor.Decode(&result); err != nil {
			return nil, fmt.Errorf("failed to decode statistics: %w", err)
		}
		stats[result["_id"].(string)] = result["count"]
	}

	// Get total count
	totalCount, err := r.Count(ctx)
	if err != nil {
		return nil, err
	}
	stats["total"] = totalCount

	return stats, nil
}

// ExistsByLessonID checks if a lesson exists by lesson ID
func (r *learningLessonRepository) ExistsByLessonID(ctx context.Context, lessonID string) (bool, error) {
	oid, err := primitive.ObjectIDFromHex(lessonID)
	if err != nil {
		return false, fmt.Errorf("invalid lesson_id hex: %w", err)
	}
	count, err := r.collection.CountDocuments(ctx, bson.M{"_id": oid})
	if err != nil {
		return false, fmt.Errorf("failed to check if lesson exists: %w", err)
	}
	return count > 0, nil
}

// GetByLessonIDs retrieves multiple lessons by their IDs
func (r *learningLessonRepository) GetByLessonIDs(ctx context.Context, lessonIDs []string) (map[string]*models.LearningLesson, error) {
	if len(lessonIDs) == 0 {
		return make(map[string]*models.LearningLesson), nil
	}

	// Convert string IDs to ObjectIDs with validation
	objectIDs := make([]primitive.ObjectID, 0, len(lessonIDs))
	for _, idStr := range lessonIDs {
		oid, err := primitive.ObjectIDFromHex(idStr)
		if err != nil {
			// Skip invalid IDs but continue processing
			continue
		}
		objectIDs = append(objectIDs, oid)
	}

	if len(objectIDs) == 0 {
		return make(map[string]*models.LearningLesson), nil
	}

	// Query for lessons
	filter := bson.M{"_id": bson.M{"$in": objectIDs}}

	// Use optimized options for better performance
	opts := options.Find().
		SetHint(bson.M{"_id": 1}).   // Force use of _id index
		SetBatchSize(100).           // Optimize batch size for network efficiency
		SetMaxTime(10 * time.Second) // Set query timeout

	cursor, err := r.collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to find lessons: %w", err)
	}
	defer cursor.Close(ctx)

	// Pre-allocate result map for better performance
	result := make(map[string]*models.LearningLesson, len(objectIDs))

	// Use cursor.All for better performance with small result sets
	var lessons []models.LearningLesson
	if err := cursor.All(ctx, &lessons); err != nil {
		return nil, fmt.Errorf("failed to decode lessons: %w", err)
	}

	// Build result map using lesson ID as key
	for i := range lessons {
		result[lessons[i].ID.Hex()] = &lessons[i]
	}

	return result, nil
}
