package repositories

import (
	"context"

	"go.mongodb.org/mongo-driver/mongo"
	"gorm.io/gorm"
)

// Repositories holds all repository instances
type Repositories struct {
	pg *gorm.DB
	mg *mongo.Database
	// PostgreSQL repositories
	StaticProfile      StaticProfileRepository
	LearningPath       LearningPathRepository
	UserNodeProgress   UserNodeProgressRepository
	UserLessonProgress UserLessonProgressRepository
	LearningNode       LearningNodeRepository
	LearningPathNode   LearningPathNodeRepository
	NodeLesson         NodeLessonRepository
	Tag                TagRepository
	LearningPathTag    LearningPathTagRepository
	PersonalizeWork    PersonalizeWorkRepositoryInterface
	GoalPlanning       GoalPlanningRepository

	// MongoDB repositories
	TechCompetency TechCompetencyRepository
	LearningLesson LearningLessonRepository
	CourseOutline  CourseOutlineRepository
}

// NewRepositories creates and initializes all repositories
func NewRepositories(postgresDB *gorm.DB, mongoDB *mongo.Database) *Repositories {
	return &Repositories{
		pg: postgresDB,
		mg: mongoDB,
		// PostgreSQL repositories
		StaticProfile:    NewStaticProfileRepository(postgresDB),
		LearningPath:     NewLearningPathRepository(postgresDB),
		UserNodeProgress: NewUserNodeProgressRepository(postgresDB),

		UserLessonProgress: NewUserLessonProgressRepository(postgresDB),
		LearningNode:       NewLearningNodeRepository(postgresDB),
		LearningPathNode:   NewLearningPathNodeRepository(postgresDB),
		NodeLesson:         NewNodeLessonRepository(postgresDB),
		Tag:                NewTagRepository(postgresDB),
		LearningPathTag:    NewLearningPathTagRepository(postgresDB),
		PersonalizeWork:    NewPersonalizeWorkRepository(postgresDB),
	GoalPlanning:       NewGoalPlanningRepository(postgresDB),

		// MongoDB repositories
		TechCompetency: NewTechCompetencyRepository(mongoDB),
		LearningLesson: NewLearningLessonRepository(mongoDB),
		CourseOutline:  NewCourseOutlineRepository(mongoDB),
	}
}

// InTransaction runs the given function within a SQL transaction and provides a tx-scoped Repositories
func (r *Repositories) InTransaction(ctx context.Context, fn func(txRepos *Repositories) error) error {
	if r.pg == nil {
		return fn(r) // fallback: no transaction capability
	}
	return r.pg.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		txRepos := NewRepositories(tx, r.mg)
		return fn(txRepos)
	})
}

// Close closes all repository connections (if needed)
func (r *Repositories) Close() error {
	// Add any cleanup logic here if repositories need explicit cleanup
	// For GORM and MongoDB driver, connections are typically managed at the database level
	return nil
}
