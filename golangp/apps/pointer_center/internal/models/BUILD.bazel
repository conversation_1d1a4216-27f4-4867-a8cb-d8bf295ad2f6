load("@rules_go//go:def.bzl", "go_library")

go_library(
    name = "models",
    srcs = glob(["**/*.go"]),
    importpath = "github.com/Arxtect/CoreXWorkSpace/golangp/apps/pointer_center/internal/models",
    visibility = ["//visibility:public"],
    deps = [
        "@com_github_google_uuid//:uuid",
        "@io_gorm_datatypes//:datatypes",
        "@io_gorm_gorm//:gorm",
        "@org_mongodb_go_mongo_driver//bson/primitive",
    ],
)
