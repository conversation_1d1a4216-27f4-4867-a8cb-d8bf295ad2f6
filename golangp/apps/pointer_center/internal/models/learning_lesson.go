package models

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/google/uuid"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// LessonType represents the type of lesson content
type LessonType string

const (
	LessonTypeText        LessonType = "text"
	LessonTypeInteractive LessonType = "interactive"
	LessonTypeQuiz        LessonType = "quiz"
	LessonTypeCode        LessonType = "code"
	LessonTypeProject     LessonType = "project"
	LessonTypeLive        LessonType = "live"
	LessonTypeAssignment  LessonType = "assignment"
)

// LessonStatus represents the status of a lesson
type LessonStatus string

const (
	LessonStatusDraft      LessonStatus = "draft"
	LessonStatusGenerating LessonStatus = "generating"
	LessonStatusFiled      LessonStatus = "failed"
	LessonStatusActive     LessonStatus = "active"
	LessonStatusArchived   LessonStatus = "archived"
	LessonStatusDeprecated LessonStatus = "deprecated"
)

// LearningLesson represents a lesson within a node (stored in MongoDB)
type LearningLesson struct {
	ID             primitive.ObjectID `bson:"_id,omitempty" json:"id"`
	Title          string             `bson:"title" json:"title"`
	Description    string             `bson:"description" json:"description"`
	Type           LessonType         `bson:"type" json:"type"`
	EstimatedTimes int                `bson:"estimated_times" json:"estimated_times" common:"预计时间（秒）"`
	Difficulty     DifficultyLevel    `bson:"difficulty" json:"difficulty"`

	// Content and metadata
	ContentFlow               []AtomicContent `bson:"content_flow" json:"content_flow"`
	LearningObjectives        []string        `bson:"learning_objectives" json:"learning_objectives"`
	CommonMisconceptions      []string        `bson:"common_misconceptions" json:"common_misconceptions"`
	ExtensionIdea             string          `bson:"extension_idea" json:"extension_idea"`
	StudentProfileAssociation string          `bson:"student_profile_association" json:"student_profile_association"`

	// Status and ownership
	Status    LessonStatus `bson:"status" json:"status"`
	CreatedBy string       `bson:"created_by" json:"created_by"`
	UpdatedBy string       `bson:"updated_by" json:"updated_by"`

	// Timestamps
	CreatedAt time.Time `bson:"created_at" json:"created_at"`
	UpdatedAt time.Time `bson:"updated_at" json:"updated_at"`
}

// GetCollectionName returns the MongoDB collection name for learning lessons
func (LearningLesson) GetCollectionName() string {
	return "learning_lessons"
}

// NewLearningLesson creates a new learning lesson
func NewLearningLesson(title string, lessonType LessonType) *LearningLesson {
	return &LearningLesson{
		Title:                title,
		Type:                 lessonType,
		EstimatedTimes:       30,
		Difficulty:           DifficultyIntermediate5,
		ContentFlow:          make([]AtomicContent, 0),
		LearningObjectives:   make([]string, 0),
		CommonMisconceptions: make([]string, 0),
		Status:               LessonStatusDraft,
		CreatedAt:            time.Now(),
		UpdatedAt:            time.Now(),
	}
}

// AddLearningObjective adds a learning objective
func (ll *LearningLesson) AddLearningObjective(objective string) {
	for _, obj := range ll.LearningObjectives {
		if obj == objective {
			return
		}
	}
	ll.LearningObjectives = append(ll.LearningObjectives, objective)
	ll.UpdatedAt = time.Now()
}

// AddCommonMisconception adds a common misconception
func (ll *LearningLesson) AddCommonMisconception(misconception string) {
	for _, m := range ll.CommonMisconceptions {
		if m == misconception {
			return
		}
	}
	ll.CommonMisconceptions = append(ll.CommonMisconceptions, misconception)
	ll.UpdatedAt = time.Now()
}

// IsValidDifficulty checks if the difficulty level is valid
func (ll *LearningLesson) IsValidDifficulty() bool {
	return ll.Difficulty.IsValid()
}

// Activate sets the lesson status to active
func (ll *LearningLesson) Activate() {
	ll.Status = LessonStatusActive
	ll.UpdatedAt = time.Now()
}

// Archive sets the lesson status to archived
func (ll *LearningLesson) Archive() {
	ll.Status = LessonStatusArchived
	ll.UpdatedAt = time.Now()
}

// IsActive checks if the lesson is active
func (ll *LearningLesson) IsActive() bool {
	return ll.Status == LessonStatusActive
}

// AddContentFlow adds an atomic content block to the lesson's content flow
func (ll *LearningLesson) AddContentFlow(contentType AtomicContentType, data interface{}) {
	order := len(ll.ContentFlow)
	atomicContent := AtomicContent{
		ID:    uuid.New().String(), // Generate UUID for each content block
		Type:  contentType,
		Order: order,
		Data:  normalizeToMap(data),
	}
	ll.ContentFlow = append(ll.ContentFlow, atomicContent)
	ll.UpdatedAt = time.Now()
}

// EnsureContentFlowIDs ensures all AtomicContent items in ContentFlow have UUIDs
func (ll *LearningLesson) EnsureContentFlowIDs() {
	for i := range ll.ContentFlow {
		if ll.ContentFlow[i].ID == "" {
			ll.ContentFlow[i].ID = uuid.New().String()
		}
	}
}

// GetContentFlowByType returns all content blocks of a specific type
func (ll *LearningLesson) GetContentFlowByType(contentType AtomicContentType) []AtomicContent {
	var result []AtomicContent
	for _, content := range ll.ContentFlow {
		if content.Type == contentType {
			result = append(result, content)
		}
	}
	return result
}

// HasContentType checks if the lesson contains a specific content type
func (ll *LearningLesson) HasContentType(contentType AtomicContentType) bool {
	for _, content := range ll.ContentFlow {
		if content.Type == contentType {
			return true
		}
	}
	return false
}

// GetContentFlowCount returns the number of content blocks in the flow
func (ll *LearningLesson) GetContentFlowCount() int {
	return len(ll.ContentFlow)
}

// ReorderContentFlow reorders the content flow based on the provided order
func (ll *LearningLesson) ReorderContentFlow(newOrder []int) error {
	if len(newOrder) != len(ll.ContentFlow) {
		return fmt.Errorf("new order length (%d) doesn't match content flow length (%d)", len(newOrder), len(ll.ContentFlow))
	}

	reorderedContent := make([]AtomicContent, len(ll.ContentFlow))
	for i, oldIndex := range newOrder {
		if oldIndex >= len(ll.ContentFlow) {
			return fmt.Errorf("invalid index %d in new order", oldIndex)
		}
		reorderedContent[i] = ll.ContentFlow[oldIndex]
		reorderedContent[i].Order = i
	}

	ll.ContentFlow = reorderedContent
	ll.UpdatedAt = time.Now()
	return nil
}

// String returns the string representation of lesson type
func (lt LessonType) String() string {
	return string(lt)
}

// IsValid checks if the lesson type is valid
func (lt LessonType) IsValid() bool {
	switch lt {
	case LessonTypeText, LessonTypeInteractive, LessonTypeQuiz,
		LessonTypeCode, LessonTypeProject, LessonTypeLive, LessonTypeAssignment:
		return true
	default:
		return false
	}
}

// String returns the string representation of lesson status
func (ls LessonStatus) String() string {
	return string(ls)
}

// IsValid checks if the lesson status is valid
func (ls LessonStatus) IsValid() bool {
	switch ls {
	case LessonStatusDraft, LessonStatusActive, LessonStatusArchived, LessonStatusDeprecated:
		return true
	default:
		return false
	}
}

// ============================================================================
// Atomic Content Types for ContentFlow
// ============================================================================

// AtomicContentType represents the type of atomic content
type AtomicContentType string

const (
	AtomicTypeTextExplanation      AtomicContentType = "text_explanation"
	AtomicTypeCodeSnippet          AtomicContentType = "code_snippet"
	AtomicTypeDiagramDescription   AtomicContentType = "diagram_description"
	AtomicTypeFlowchartDescription AtomicContentType = "flowchart_description"
	AtomicTypeMultipleChoiceQuiz   AtomicContentType = "multiple_choice_quiz"
	AtomicTypeFillInBlankQuiz      AtomicContentType = "fill_in_blank_quiz"
	AtomicTypePracticeExercise     AtomicContentType = "practice_exercise"
	AtomicTypeMathFormula          AtomicContentType = "math_formula"
)

// AtomicContent represents a single atomic content block in the lesson flow
type AtomicContent struct {
	ID    string                 `bson:"id" json:"id"` // UUID for each content block
	Type  AtomicContentType      `bson:"type" json:"type"`
	Order int                    `bson:"order" json:"order"`
	Data  map[string]interface{} `bson:"data" json:"data"` // Polymorphic data based on type; normalized to object
}

// normalizeToMap converts various possible inputs to a map[string]interface{} so that
// JSON to the frontend is a plain object instead of a Key/Value array (primitive.D).
func normalizeToMap(v interface{}) map[string]interface{} {
	if v == nil {
		return map[string]interface{}{}
	}
	switch t := v.(type) {
	case map[string]interface{}:
		return t
	case primitive.D:
		m := make(map[string]interface{}, len(t))
		for _, e := range t {
			m[e.Key] = e.Value
		}
		return m
	}
	// Fallback: try JSON round-trip to map
	b, err := json.Marshal(v)
	if err == nil {
		var m map[string]interface{}
		if json.Unmarshal(b, &m) == nil {
			if m == nil {
				return map[string]interface{}{}
			}
			return m
		}
	}
	// Last resort: wrap under a generic key
	return map[string]interface{}{"value": v}
}

// TextExplanation represents rich text explanation content
type TextExplanation struct {
	Title string `bson:"title" json:"title"`
	Body  string `bson:"body" json:"body"`
}

// CodeSnippetContent represents executable/editable code content
type CodeSnippetContent struct {
	Title          string `bson:"title" json:"title"`
	Language       string `bson:"language" json:"language"`
	InitialCode    string `bson:"initial_code" json:"initial_code"`
	ExpectedOutput string `bson:"expected_output" json:"expected_output"`
	Explanation    string `bson:"explanation" json:"explanation"`
	Editable       bool   `bson:"editable" json:"editable"`
	Runnable       bool   `bson:"runnable" json:"runnable"`
}

// TODO: 后续这里只需要保留图像的url即可，图像生成在AI内容生成后、传入数据库前的过程中完成
// DiagramDescription represents diagram generation instructions
type DiagramDescription struct {
	Title                  string `bson:"title" json:"title"`
	DiagramType            string `bson:"diagram_type" json:"diagram_type"` // memory_layout, class_hierarchy, data_flow
	VisualStyle            string `bson:"visual_style" json:"visual_style"` // flat, hand_drawn, technical
	DescriptionForImageGen string `bson:"description_for_image_gen" json:"description_for_image_gen"`
	AltText                string `bson:"alt_text" json:"alt_text"`
}

// FlowchartDescription represents flowchart content (Mermaid)
type FlowchartDescription struct {
	Title         string `bson:"title" json:"title"`
	DiagramSyntax string `bson:"diagram_syntax" json:"diagram_syntax"` // Mermaid syntax
	AltText       string `bson:"alt_text" json:"alt_text"`
}

// MultipleChoiceQuiz represents single/multiple choice questions
type MultipleChoiceQuiz struct {
	Difficulty           string   `bson:"difficulty" json:"difficulty"`
	Question             string   `bson:"question" json:"question"`
	Options              []string `bson:"options" json:"options"`
	CorrectAnswerIndex   int      `bson:"correct_answer_index" json:"correct_answer_index"`
	CorrectAnswerIndices []int    `bson:"correct_answer_indices" json:"correct_answer_indices"` // For multiple selection
	Explanation          string   `bson:"explanation" json:"explanation"`
	Hint                 string   `bson:"hint" json:"hint"`
	SingleAnswer         bool     `bson:"single_answer" json:"single_answer"`
}

// FillInBlankQuiz represents fill-in-the-blank questions
type FillInBlankQuiz struct {
	Difficulty    string `bson:"difficulty" json:"difficulty"`
	Question      string `bson:"question" json:"question"` // Use <blank> as placeholder
	CorrectAnswer string `bson:"correct_answer" json:"correct_answer"`
	CaseSensitive bool   `bson:"case_sensitive" json:"case_sensitive"`
	Explanation   string `bson:"explanation" json:"explanation"`
	Hint          string `bson:"hint" json:"hint"`
}

// PracticeExerciseContent represents coding/problem-solving exercises
type PracticeExerciseContent struct {
	Title            string             `bson:"title" json:"title"`
	Difficulty       string             `bson:"difficulty" json:"difficulty"`
	Description      string             `bson:"description" json:"description"`
	StarterCode      string             `bson:"starter_code" json:"starter_code"`
	SolutionCode     string             `bson:"solution_code" json:"solution_code"`
	TestCases        []ExerciseTestCase `bson:"test_cases" json:"test_cases"`
	EvaluationMethod string             `bson:"evaluation_method" json:"evaluation_method"` // output_match, regex_match, ast_match
	Hints            []string           `bson:"hints" json:"hints"`
	FeedbackTemplate string             `bson:"feedback_template" json:"feedback_template"`
}

// MathFormula represents mathematical formulas (LaTeX)
type MathFormula struct {
	Title                 string `bson:"title" json:"title"`
	Formula               string `bson:"formula" json:"formula"` // LaTeX format
	Description           string `bson:"description" json:"description"`
	RenderMode            string `bson:"render_mode" json:"render_mode"` // inline, block
	AccessibleDescription string `bson:"accessible_description" json:"accessible_description"`
}

// ExerciseTestCase represents test cases for practice exercises
type ExerciseTestCase struct {
	Input          string `bson:"input" json:"input"`
	ExpectedOutput string `bson:"expected_output" json:"expected_output"`
	Description    string `bson:"description" json:"description"`
}

// ============================================================================
// Helper Methods for Atomic Content Types
// ============================================================================

// String returns the string representation of atomic content type
func (act AtomicContentType) String() string {
	return string(act)
}

// IsValid checks if the atomic content type is valid
func (act AtomicContentType) IsValid() bool {
	switch act {
	case AtomicTypeTextExplanation, AtomicTypeCodeSnippet, AtomicTypeDiagramDescription,
		AtomicTypeFlowchartDescription, AtomicTypeMultipleChoiceQuiz, AtomicTypeFillInBlankQuiz,
		AtomicTypePracticeExercise, AtomicTypeMathFormula:
		return true
	default:
		return false
	}
}

// IsInteractive checks if the content type is interactive
func (act AtomicContentType) IsInteractive() bool {
	switch act {
	case AtomicTypeCodeSnippet, AtomicTypeMultipleChoiceQuiz, AtomicTypeFillInBlankQuiz, AtomicTypePracticeExercise:
		return true
	default:
		return false
	}
}

// GetCategory returns the category of the atomic content type
func (act AtomicContentType) GetCategory() string {
	switch act {
	case AtomicTypeTextExplanation:
		return "explanation"
	case AtomicTypeCodeSnippet, AtomicTypePracticeExercise:
		return "coding"
	case AtomicTypeDiagramDescription, AtomicTypeFlowchartDescription:
		return "visual"
	case AtomicTypeMultipleChoiceQuiz, AtomicTypeFillInBlankQuiz:
		return "assessment"
	case AtomicTypeMathFormula:
		return "math"
	default:
		return "unknown"
	}
}
