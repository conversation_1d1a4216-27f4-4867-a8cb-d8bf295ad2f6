package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// NodeStatus represents the status of a learning node
type NodeStatus string

const (
	NodeStatusDraft      NodeStatus = "draft"     // Node is being created
	NodeStatusGenerating NodeStatus = "generating" // Node is being generated
	NodeStatusFailed     NodeStatus = "failed"    // Node generation failed
	NodeStatusActive     NodeStatus = "active"    // Node is active and available
	NodeStatusArchived   NodeStatus = "archived"  // Node is archived
	NodeStatusDeprecated NodeStatus = "deprecated" // Node is deprecated
)

// LearningNode represents a node within a learning path (stored in PostgreSQL)
type LearningNode struct {
	ID             uuid.UUID       `gorm:"type:uuid;default:uuid_generate_v4();primaryKey;comment:学习节点唯一标识符" json:"id"`
	Title          string          `gorm:"size:255;not null;comment:节点标题" json:"title"`
	Description    string          `gorm:"type:text;comment:节点描述" json:"description"`
	EstimatedTimes int             `gorm:"default:1;comment:预计学习时长(秒)" json:"estimated_times"`
	Difficulty     DifficultyLevel `gorm:"type:int;default:5;comment:难度级别(1-10)" json:"difficulty"`

	// Pricing information
	Price            *int `gorm:"comment:节点价格(分)" json:"price"`
	CertificatePrice *int `gorm:"comment:证书价格(分)" json:"certificate_price"`

	// Tags and skills
	Skills           []string `gorm:"type:jsonb;serializer:json;comment:技能标签数组" json:"skills"`
	Tags             []string `gorm:"type:jsonb;serializer:json;comment:标签数组" json:"tags"`
	LearnerTags      []string `gorm:"type:jsonb;serializer:json;comment:学习者标签数组" json:"learner_tags"`
	WhatYouWillLearn []string `gorm:"type:jsonb;serializer:json;comment:学习成果数组" json:"what_you_will_learn"`

	// Status and metadata
	// Prerequisites stores an array of prerequisite node IDs (UUID strings) as JSONB
	Prerequisites []string   `gorm:"type:jsonb;serializer:json;comment:前置条件(UUID字符串数组)" json:"prerequisites"`
	Status        NodeStatus `gorm:"size:50;default:'draft';comment:节点状态" json:"status"`
	CreatedBy     uuid.UUID  `gorm:"type:uuid;comment:创建者ID" json:"created_by"`
	UpdatedBy     uuid.UUID  `gorm:"type:uuid;comment:更新者ID" json:"updated_by"`

	// Timestamps
	CreatedAt time.Time      `gorm:"autoCreateTime;comment:创建时间" json:"created_at"`
	UpdatedAt time.Time      `gorm:"autoUpdateTime;comment:更新时间" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index:idx_learning_nodes_deleted_at;comment:软删除时间戳" json:"-"`
}

// LearningPathNode represents the association between learning paths and nodes (PostgreSQL)
type LearningPathNode struct {
	ID             uuid.UUID `gorm:"type:uuid;default:uuid_generate_v4();primaryKey;comment:关联唯一标识符" json:"id"`
	LearningPathID uuid.UUID `gorm:"type:uuid;not null;index:idx_path_node_path;comment:学习路径ID" json:"learning_path_id"`
	NodeID         uuid.UUID `gorm:"type:uuid;not null;index:idx_path_node_node;comment:学习节点ID" json:"node_id"`

	// Timestamps
	CreatedAt time.Time      `gorm:"autoCreateTime;comment:创建时间" json:"created_at"`
	UpdatedAt time.Time      `gorm:"autoUpdateTime;comment:更新时间" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index:idx_path_node_deleted_at;comment:软删除时间戳" json:"-"`

	// Associations
	LearningPath LearningPath `gorm:"foreignKey:LearningPathID;references:ID" json:"learning_path,omitempty"`
	Node         LearningNode `gorm:"foreignKey:NodeID;references:ID" json:"node,omitempty"`
}

// NodeLesson represents the association between nodes and lessons (PostgreSQL)
type NodeLesson struct {
	ID       uuid.UUID `gorm:"type:uuid;default:uuid_generate_v4();primaryKey;comment:关联唯一标识符" json:"id"`
	NodeID   uuid.UUID `gorm:"type:uuid;not null;index:idx_node_lesson_node;index:idx_node_lesson_node_orderkey,priority:1;comment:学习节点ID" json:"node_id"`
	LessonID string    `gorm:"size:100;not null;index:idx_node_lesson_lesson;comment:课程ID(MongoDB)" json:"lesson_id"`
	// OrderKey is a lexicographically sortable string rank for stable insertions without bulk updates
	OrderKey string `gorm:"size:64;index:idx_node_lesson_node_orderkey,priority:2;comment:排序键(字典序)" json:"order_key"`

	// Cached lesson metadata to avoid frequent Mongo lookups in list views
	LessonTitle       string `gorm:"size:255;comment:课程标题(缓存)" json:"lesson_title"`
	LessonDescription string `gorm:"type:text;comment:课程描述(缓存)" json:"lesson_description"`

	// Timestamps
	CreatedAt time.Time      `gorm:"autoCreateTime;comment:创建时间" json:"created_at"`
	UpdatedAt time.Time      `gorm:"autoUpdateTime;comment:更新时间" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index:idx_node_lesson_deleted_at;comment:软删除时间戳" json:"-"`

	// Associations
	Node LearningNode `gorm:"foreignKey:NodeID;references:ID" json:"node,omitempty"`
	// Note: Lesson is stored in MongoDB, so no direct GORM association
}

// BeforeCreate sets the ID if not provided for LearningNode
func (ln *LearningNode) BeforeCreate(tx *gorm.DB) error {
	if ln.ID == uuid.Nil {
		ln.ID = uuid.New()
	}
	return nil
}

// BeforeCreate sets the ID if not provided for LearningPathNode
func (lpn *LearningPathNode) BeforeCreate(tx *gorm.DB) error {
	if lpn.ID == uuid.Nil {
		lpn.ID = uuid.New()
	}
	return nil
}

// BeforeCreate sets the ID if not provided for NodeLesson
func (nl *NodeLesson) BeforeCreate(tx *gorm.DB) error {
	if nl.ID == uuid.Nil {
		nl.ID = uuid.New()
	}
	return nil
}

// TableName returns the table name for the LearningNode model
func (LearningNode) TableName() string {
	return "learning_nodes"
}

// TableName returns the table name for the LearningPathNode model
func (LearningPathNode) TableName() string {
	return "learning_path_nodes"
}

// TableName returns the table name for the NodeLesson model
func (NodeLesson) TableName() string {
	return "node_lessons"
}

// NewLearningNode creates a new learning node
func NewLearningNode(nodeID, title string) *LearningNode {
	return &LearningNode{
		Title:            title,
		EstimatedTimes:   1,
		Difficulty:       DifficultyIntermediate5,
		Prerequisites:    []string{},
		Skills:           []string{},
		Tags:             []string{},
		LearnerTags:      []string{},
		WhatYouWillLearn: []string{},
		Status:           NodeStatusDraft,
	}
}

// GetPrerequisites returns prerequisites as string slice
func (ln *LearningNode) GetPrerequisites() ([]string, error) {
	return ln.Prerequisites, nil
}

// SetPrerequisites sets prerequisites from string slice
func (ln *LearningNode) SetPrerequisites(prerequisites []string) error {
	ln.Prerequisites = prerequisites
	return nil
}

// AddPrerequisite adds a prerequisite node ID
func (ln *LearningNode) AddPrerequisite(nodeID string) error {
	prerequisites, err := ln.GetPrerequisites()
	if err != nil {
		return err
	}

	// Check if already exists
	for _, prereq := range prerequisites {
		if prereq == nodeID {
			return nil // Already exists
		}
	}

	prerequisites = append(prerequisites, nodeID)
	return ln.SetPrerequisites(prerequisites)
}

// RemovePrerequisite removes a prerequisite node ID
func (ln *LearningNode) RemovePrerequisite(nodeID string) error {
	prerequisites, err := ln.GetPrerequisites()
	if err != nil {
		return err
	}

	for i, prereq := range prerequisites {
		if prereq == nodeID {
			prerequisites = append(prerequisites[:i], prerequisites[i+1:]...)
			break
		}
	}

	return ln.SetPrerequisites(prerequisites)
}

// IsValidDifficulty checks if the difficulty level is valid
func (ln *LearningNode) IsValidDifficulty() bool {
	return ln.Difficulty.IsValid()
}

// AddSkill adds a skill to the skills array
func (ln *LearningNode) AddSkill(skill string) {
	// Check if already exists
	for _, s := range ln.Skills {
		if s == skill {
			return // Already exists
		}
	}
	ln.Skills = append(ln.Skills, skill)
}

// RemoveSkill removes a skill from the skills array
func (ln *LearningNode) RemoveSkill(skill string) {
	for i, s := range ln.Skills {
		if s == skill {
			ln.Skills = append(ln.Skills[:i], ln.Skills[i+1:]...)
			break
		}
	}
}

// AddTag adds a tag to the tags array
func (ln *LearningNode) AddTag(tag string) {
	// Check if already exists
	for _, t := range ln.Tags {
		if t == tag {
			return // Already exists
		}
	}
	ln.Tags = append(ln.Tags, tag)
}

// RemoveTag removes a tag from the tags array
func (ln *LearningNode) RemoveTag(tag string) {
	for i, t := range ln.Tags {
		if t == tag {
			ln.Tags = append(ln.Tags[:i], ln.Tags[i+1:]...)
			break
		}
	}
}

// AddLearnerTag adds a learner tag to the learner_tags array
func (ln *LearningNode) AddLearnerTag(tag string) {
	// Check if already exists
	for _, t := range ln.LearnerTags {
		if t == tag {
			return // Already exists
		}
	}
	ln.LearnerTags = append(ln.LearnerTags, tag)
}

// RemoveLearnerTag removes a learner tag from the learner_tags array
func (ln *LearningNode) RemoveLearnerTag(tag string) {
	for i, t := range ln.LearnerTags {
		if t == tag {
			ln.LearnerTags = append(ln.LearnerTags[:i], ln.LearnerTags[i+1:]...)
			break
		}
	}
}

// SetPrice sets the price (can be nil for free)
func (ln *LearningNode) SetPrice(price *int) {
	ln.Price = price
}

// SetCertificatePrice sets the certificate price (can be nil for free)
func (ln *LearningNode) SetCertificatePrice(price *int) {
	ln.CertificatePrice = price
}

// IsFree checks if the node is free (price is nil or 0)
func (ln *LearningNode) IsFree() bool {
	return ln.Price == nil || *ln.Price == 0
}

// IsCertificateFree checks if the certificate is free (certificate_price is nil or 0)
func (ln *LearningNode) IsCertificateFree() bool {
	return ln.CertificatePrice == nil || *ln.CertificatePrice == 0
}

// Activate sets the node status to active
func (ln *LearningNode) Activate() {
	ln.Status = NodeStatusActive
}

// Archive sets the node status to archived
func (ln *LearningNode) Archive() {
	ln.Status = NodeStatusArchived
}

// Deprecate sets the node status to deprecated
func (ln *LearningNode) Deprecate() {
	ln.Status = NodeStatusDeprecated
}

// IsActive checks if the node is active
func (ln *LearningNode) IsActive() bool {
	return ln.Status == NodeStatusActive
}

// String returns the string representation of node status
func (ns NodeStatus) String() string {
	return string(ns)
}

// IsValid checks if the node status is valid
func (ns NodeStatus) IsValid() bool {
	switch ns {
	case NodeStatusDraft, NodeStatusActive, NodeStatusArchived, NodeStatusDeprecated:
		return true
	default:
		return false
	}
}
