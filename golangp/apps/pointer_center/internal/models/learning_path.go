package models

import (
	"encoding/json"
	"time"

	"github.com/google/uuid"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// PathStatus represents the status of a learning path
type PathStatus string

const (
	PathStatusDraft      PathStatus = "draft"       // Path is being created
	PathStatusGenerating PathStatus = "generating" // Path is being generated
	PathStatusFailed     PathStatus = "failed"      // Path generation failed
	PathStatusActive     PathStatus = "active"      // Path is active and can be followed
	PathStatusArchived   PathStatus = "archived"    // Path is archived
	PathStatusDeprecated PathStatus = "deprecated" // Path is deprecated
)

// DifficultyLevel represents difficulty level using numeric scale 1-10
type DifficultyLevel int

const (
	DifficultyBeginner1     DifficultyLevel = 1  // Very Easy - Basic concepts
	DifficultyBeginner2     DifficultyLevel = 2  // Easy - Simple applications
	DifficultyBeginner3     DifficultyLevel = 3  // Easy-Medium - Guided practice
	DifficultyIntermediate4 DifficultyLevel = 4  // Medium-Easy - Independent practice
	DifficultyIntermediate5 DifficultyLevel = 5  // Medium - Standard complexity
	DifficultyIntermediate6 DifficultyLevel = 6  // Medium-Hard - Complex applications
	DifficultyAdvanced7     DifficultyLevel = 7  // Hard - Advanced concepts
	DifficultyAdvanced8     DifficultyLevel = 8  // Very Hard - Expert level
	DifficultyExpert9       DifficultyLevel = 9  // Extremely Hard - Cutting edge
	DifficultyExpert10      DifficultyLevel = 10 // Master Level - Research/Innovation
)

// String returns the string representation of difficulty level
func (d DifficultyLevel) String() string {
	switch d {
	case DifficultyBeginner1:
		return "Beginner (1)"
	case DifficultyBeginner2:
		return "Beginner (2)"
	case DifficultyBeginner3:
		return "Beginner (3)"
	case DifficultyIntermediate4:
		return "Intermediate (4)"
	case DifficultyIntermediate5:
		return "Intermediate (5)"
	case DifficultyIntermediate6:
		return "Intermediate (6)"
	case DifficultyAdvanced7:
		return "Advanced (7)"
	case DifficultyAdvanced8:
		return "Advanced (8)"
	case DifficultyExpert9:
		return "Expert (9)"
	case DifficultyExpert10:
		return "Expert (10)"
	default:
		return "Unknown"
	}
}

// GetCategory returns the general category of the difficulty level
func (d DifficultyLevel) GetCategory() string {
	switch {
	case d >= 1 && d <= 3:
		return "Beginner"
	case d >= 4 && d <= 6:
		return "Intermediate"
	case d >= 7 && d <= 8:
		return "Advanced"
	case d >= 9 && d <= 10:
		return "Expert"
	default:
		return "Unknown"
	}
}

// IsValid checks if the difficulty level is within valid range
func (d DifficultyLevel) IsValid() bool {
	return d >= 1 && d <= 10
}

// LearningPath represents a complete learning path that can be reused
type LearningPath struct {
	ID               uuid.UUID       `gorm:"type:uuid;default:uuid_generate_v4();primaryKey;comment:学习路径唯一标识符" json:"id"`
	CreatorID        uuid.UUID       `gorm:"type:uuid;not null;index:idx_learning_path_creator;comment:创建者ID" json:"creator_id"`
	UserID           uuid.UUID       `gorm:"type:uuid;not null;index:idx_learning_path_user;comment:用户ID" json:"user_id"`
	ForkFrom         uuid.UUID       `gorm:"type:uuid;index:idx_learning_path_fork_from;comment:源路径ID" json:"fork_from"`


	// Goal Information (moved from separate goal table)
	Goal         string `gorm:"type:text;not null;comment:学习目标" json:"goal"`
	GoalCategory string `gorm:"size:100;comment:目标类别" json:"goal_category"`

	// Path Information
	Title       string          `gorm:"size:200;not null;comment:学习路径标题" json:"title"`
	Description string          `gorm:"type:text;comment:学习路径描述" json:"description"`
	PathType    string          `gorm:"size:50;not null;comment:路径类型" json:"path_type"`
	Difficulty  DifficultyLevel `gorm:"size:50;comment:难度级别" json:"difficulty"`

	// Path Characteristics

	EstimatedTimes   int             `gorm:"comment:预估学习时长(秒)" json:"estimated_times"`
	TotalNodes       int             `gorm:"comment:总节点数" json:"total_nodes"`
	SuitableFor      datatypes.JSON  `gorm:"type:jsonb;comment:适用人群(JSON)" json:"suitable_for"`
	LearningOutcomes datatypes.JSON  `gorm:"type:jsonb;comment:学习成果(JSON数组)" json:"learning_outcomes"`

	// Per-user progress state now lives on LearningPath itself (merged from UserPathProgress)
	ProgressStatus   ProgressStatus  `gorm:"size:50;default:'active';comment:学习进度状态" json:"progress_status"`
	CurrentNodeID    uuid.UUID       `gorm:"type:uuid;comment:当前节点ID" json:"current_node_id"`
	CurrentLessonID  string          `gorm:"size:100;comment:当前课程ID" json:"current_lesson_id"`
	CompletedNodes   int             `gorm:"default:0;comment:已完成节点数" json:"completed_nodes"`
	CompletedLessons int             `gorm:"default:0;comment:已完成课程数" json:"completed_lessons"`
	ProgressPercent  float64         `gorm:"default:0;comment:完成百分比" json:"progress_percent"`
    
	// Timing Information (per-user)
	StartedAt        *time.Time      `gorm:"comment:开始学习时间" json:"started_at"`
	LastAccessedAt   *time.Time      `gorm:"comment:最后访问时间" json:"last_accessed_at"`
	CompletedAt      *time.Time      `gorm:"comment:完成时间" json:"completed_at"`
    
	// Performance Metrics (per-user)
	TotalTimeSpent   int             `gorm:"default:0;comment:总学习时间(分钟)" json:"total_time_spent"`
	AverageScore     float64         `gorm:"default:0;comment:平均分数" json:"average_score"`
	
	// Reusability and Sharing
	IsPublic         bool            `gorm:"default:false;comment:是否公开可复用" json:"is_public"`
	UsageCount       int             `gorm:"default:0;comment:被使用次数" json:"usage_count"`
	Rating           float64         `gorm:"default:0;comment:用户评分" json:"rating"`

	// Timestamps
	CreatedAt        time.Time       `gorm:"autoCreateTime;comment:创建时间" json:"created_at"`
	UpdatedAt        time.Time       `gorm:"autoUpdateTime;comment:更新时间" json:"updated_at"`
	DeletedAt        gorm.DeletedAt  `gorm:"index:idx_learning_path_deleted_at;comment:软删除时间戳" json:"-"`

}

type ProgressStatus string

const (
	StatusActive     ProgressStatus = "active"
	StatusCompleted  ProgressStatus = "completed"
	StatusCancelled  ProgressStatus = "cancelled"
	StatusGenerating ProgressStatus = "generating"
	StatusFailed     ProgressStatus = "failed"
)


// BeforeCreate sets the ID if not provided for LearningPath
func (lp *LearningPath) BeforeCreate(tx *gorm.DB) error {
	if lp.ID == uuid.Nil {
		lp.ID = uuid.New()
	}
	return nil
}

// IsPublicPath checks if the learning path is public
func (lp *LearningPath) IsPublicPath() bool {
	return lp.IsPublic
}

// IncrementUsage increments the usage count
func (lp *LearningPath) IncrementUsage() {
	lp.UsageCount++
}

// UpdateProgress updates the progress information on LearningPath
func (lp *LearningPath) UpdateProgress(completedNodes, totalNodes int) {
	lp.CompletedNodes = completedNodes
	if totalNodes > 0 {
		lp.ProgressPercent = float64(completedNodes) / float64(totalNodes) * 100
	}
	lp.LastAccessedAt = &[]time.Time{time.Now()}[0]
	if lp.ProgressPercent >= 100 {
		lp.ProgressStatus = StatusCompleted
		lp.CompletedAt = &[]time.Time{time.Now()}[0]
	}
}

// TableName returns the table name for the LearningPath model
func (LearningPath) TableName() string {
	return "learning_paths"
}

// ============================================================================
// PostgreSQL Models for Structured Metadata and Relationships
// ============================================================================

// UserNodeProgress represents user progress on learning nodes (PostgreSQL)
type UserNodeProgress struct {
	ID               uuid.UUID       `gorm:"type:uuid;default:uuid_generate_v4();primaryKey;comment:用户节点进度唯一标识符" json:"id"`
	UserID           uuid.UUID       `gorm:"type:uuid;not null;index:idx_user_node_user;comment:用户ID" json:"user_id"`
	NodeID           uuid.UUID       `gorm:"type:uuid;not null;index:idx_user_node_node;comment:学习节点ID" json:"node_id"`

	// Reference count across the user's multiple paths that include this node
	RefCount int `gorm:"default:1;comment:引用计数(同一用户在多条路径中使用相同节点的次数)" json:"ref_count"`

	// Progress Information
	IsCompleted    bool       `gorm:"default:false;comment:是否完成" json:"is_completed"`
	CompletedAt    *time.Time `gorm:"comment:完成时间" json:"completed_at"`
	Score          float64    `gorm:"default:0;comment:节点分数(0-100)" json:"score"`
	TimeSpent      int        `gorm:"default:0;comment:学习时间(分钟)" json:"time_spent"`
	Attempts       int        `gorm:"default:0;comment:尝试次数" json:"attempts"`
	LastAccessedAt *time.Time `gorm:"comment:最后访问时间" json:"last_accessed_at"`

	// Timestamps
	Status           ProgressStatus  `gorm:"default:'active';comment:学习状态" json:"status"`
	CreatedAt        time.Time       `gorm:"autoCreateTime;comment:创建时间" json:"created_at"`
	UpdatedAt        time.Time       `gorm:"autoUpdateTime;comment:更新时间" json:"updated_at"`
	DeletedAt        gorm.DeletedAt  `gorm:"index:idx_user_node_deleted_at;comment:软删除时间戳" json:"-"`
}

// UserLessonProgress represents user progress on lessons (PostgreSQL)
type UserLessonProgress struct {
	ID               uuid.UUID       `gorm:"type:uuid;default:uuid_generate_v4();primaryKey;comment:用户课程进度唯一标识符" json:"id"`
	UserID           uuid.UUID       `gorm:"type:uuid;not null;index:idx_user_lesson_user;uniqueIndex:uniq_user_node_lesson;comment:用户ID" json:"user_id"`
	LessonID         string          `gorm:"size:100;not null;index:idx_user_lesson_lesson;uniqueIndex:uniq_user_node_lesson;comment:课程ID(MongoDB)" json:"lesson_id"`
	NodeID           uuid.UUID       `gorm:"type:uuid;not null;index:idx_user_lesson_node;uniqueIndex:uniq_user_node_lesson;comment:学习节点ID" json:"node_id"`
	
	// Progress Information
	Progress       float64    `gorm:"default:0;comment:学习进度(0-1)" json:"progress"`
	IsCompleted    bool       `gorm:"default:false;comment:是否完成" json:"is_completed"`
	CompletedAt    *time.Time `gorm:"comment:完成时间" json:"completed_at"`
	Score          float64    `gorm:"default:0;comment:课程分数(0-100)" json:"score"`
	TimeSpent      int        `gorm:"default:0;comment:学习时间(分钟)" json:"time_spent"`
	Attempts       int        `gorm:"default:0;comment:尝试次数" json:"attempts"`
	LastAccessedAt *time.Time `gorm:"comment:最后访问时间" json:"last_accessed_at"`

	// ContentFlow Progress
	CompletedContentFlowIDs datatypes.JSON `gorm:"type:jsonb;default:'[]';comment:已完成的ContentFlow ID数组(JSON)" json:"completed_content_flow_ids"`

	// Timestamps
	CreatedAt time.Time      `gorm:"autoCreateTime;comment:创建时间" json:"created_at"`
	UpdatedAt time.Time      `gorm:"autoUpdateTime;comment:更新时间" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index:idx_user_lesson_deleted_at;comment:软删除时间戳" json:"-"`
}

// TODO: 这里要考虑如何收集用户学习数据喂给AI或是算法分析
// LearningAnalytics represents learning analytics data (PostgreSQL)
type LearningAnalytics struct {
	ID             uuid.UUID `gorm:"type:uuid;default:uuid_generate_v4();primaryKey;comment:学习分析唯一标识符" json:"id"`
	UserID         uuid.UUID `gorm:"type:uuid;not null;index:idx_analytics_user;comment:用户ID" json:"user_id"`
	LearningPathID uuid.UUID `gorm:"type:uuid;not null;index:idx_analytics_path;comment:学习路径ID" json:"learning_path_id"`

	// Analytics Data
	TotalTimeSpent        int     `gorm:"default:0;comment:总学习时间(分钟)" json:"total_time_spent"`
	AverageSessionTime    int     `gorm:"default:0;comment:平均会话时间(分钟)" json:"average_session_time"`
	CompletionRate        float64 `gorm:"default:0;comment:完成率" json:"completion_rate"`
	EngagementScore       float64 `gorm:"default:0;comment:参与度评分" json:"engagement_score"`
	DifficultyAreas       string  `gorm:"type:jsonb;comment:困难领域(JSON数组)" json:"difficulty_areas"`
	StrengthAreas         string  `gorm:"type:jsonb;comment:优势领域(JSON数组)" json:"strength_areas"`
	LearningVelocity      float64 `gorm:"default:0;comment:学习速度" json:"learning_velocity"`
	RetentionRate         float64 `gorm:"default:0;comment:知识保持率" json:"retention_rate"`
	PreferredContentTypes string  `gorm:"type:jsonb;comment:偏好内容类型(JSON数组)" json:"preferred_content_types"`
	OptimalStudyTimes     string  `gorm:"type:jsonb;comment:最佳学习时间(JSON数组)" json:"optimal_study_times"`
	LearningPatterns      string  `gorm:"type:jsonb;comment:学习模式(JSON对象)" json:"learning_patterns"`

	// Timestamps
	CreatedAt time.Time      `gorm:"autoCreateTime;comment:创建时间" json:"created_at"`
	UpdatedAt time.Time      `gorm:"autoUpdateTime;comment:更新时间" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index:idx_analytics_deleted_at;comment:软删除时间戳" json:"-"`

	// Associations
	LearningPath LearningPath `gorm:"foreignKey:LearningPathID;references:ID" json:"learning_path,omitempty"`
}

// ============================================================================
// BeforeCreate Hooks for New Models
// ============================================================================

// BeforeCreate sets the ID if not provided for UserNodeProgress
func (unp *UserNodeProgress) BeforeCreate(tx *gorm.DB) error {
	if unp.ID == uuid.Nil {
		unp.ID = uuid.New()
	}
	return nil
}

// BeforeCreate sets the ID if not provided for UserLessonProgress
func (ulp *UserLessonProgress) BeforeCreate(tx *gorm.DB) error {
	if ulp.ID == uuid.Nil {
		ulp.ID = uuid.New()
	}
	return nil
}

// BeforeCreate sets the ID if not provided for LearningAnalytics
func (la *LearningAnalytics) BeforeCreate(tx *gorm.DB) error {
	if la.ID == uuid.Nil {
		la.ID = uuid.New()
	}
	return nil
}

// ============================================================================
// Table Names for New Models
// ============================================================================

// TableName returns the table name for the UserNodeProgress model
func (UserNodeProgress) TableName() string {
	return "user_node_progress"
}

// TableName returns the table name for the UserLessonProgress model
func (UserLessonProgress) TableName() string {
	return "user_lesson_progress"
}

// TableName returns the table name for the LearningAnalytics model
func (LearningAnalytics) TableName() string {
	return "learning_analytics"
}

// ============================================================================
// Helper Methods for Progress Management
// ============================================================================

// UpdateNodeProgress updates the progress information for a node
func (unp *UserNodeProgress) UpdateProgress(score float64, timeSpent int, isCompleted bool) {
	unp.Score = score
	unp.TimeSpent += timeSpent
	unp.Attempts++
	unp.LastAccessedAt = &[]time.Time{time.Now()}[0]

	if isCompleted && !unp.IsCompleted {
		unp.IsCompleted = true
		unp.CompletedAt = &[]time.Time{time.Now()}[0]
	}
}

// UpdateLessonProgress updates the progress information for a lesson
func (ulp *UserLessonProgress) UpdateProgress(score float64, timeSpent int, isCompleted bool) {
	ulp.Score = score
	ulp.TimeSpent += timeSpent
	ulp.Attempts++
	ulp.LastAccessedAt = &[]time.Time{time.Now()}[0]

	if isCompleted && !ulp.IsCompleted {
		ulp.IsCompleted = true
		ulp.CompletedAt = &[]time.Time{time.Now()}[0]
	}
}

// GetCompletedContentFlowIDs returns the list of completed ContentFlow IDs
func (ulp *UserLessonProgress) GetCompletedContentFlowIDs() []string {
	var ids []string
	if ulp.CompletedContentFlowIDs != nil {
		if err := json.Unmarshal(ulp.CompletedContentFlowIDs, &ids); err != nil {
			return []string{}
		}
	}
	return ids
}

// SetCompletedContentFlowIDs sets the list of completed ContentFlow IDs
func (ulp *UserLessonProgress) SetCompletedContentFlowIDs(ids []string) error {
	if ids == nil {
		ids = []string{}
	}
	data, err := json.Marshal(ids)
	if err != nil {
		return err
	}
	ulp.CompletedContentFlowIDs = datatypes.JSON(data)
	return nil
}

// AddCompletedContentFlowID adds a ContentFlow ID to the completed list
func (ulp *UserLessonProgress) AddCompletedContentFlowID(contentFlowID string) error {
	ids := ulp.GetCompletedContentFlowIDs()

	// Check if already exists
	for _, id := range ids {
		if id == contentFlowID {
			return nil // Already exists, no need to add
		}
	}

	// Add new ID
	ids = append(ids, contentFlowID)
	return ulp.SetCompletedContentFlowIDs(ids)
}

// RemoveCompletedContentFlowID removes a ContentFlow ID from the completed list
func (ulp *UserLessonProgress) RemoveCompletedContentFlowID(contentFlowID string) error {
	ids := ulp.GetCompletedContentFlowIDs()

	// Find and remove the ID
	newIds := make([]string, 0, len(ids))
	for _, id := range ids {
		if id != contentFlowID {
			newIds = append(newIds, id)
		}
	}

	return ulp.SetCompletedContentFlowIDs(newIds)
}

// IsContentFlowCompleted checks if a specific ContentFlow ID is completed
func (ulp *UserLessonProgress) IsContentFlowCompleted(contentFlowID string) bool {
	ids := ulp.GetCompletedContentFlowIDs()
	for _, id := range ids {
		if id == contentFlowID {
			return true
		}
	}
	return false
}

// GetCompletedContentFlowCount returns the number of completed ContentFlow items
func (ulp *UserLessonProgress) GetCompletedContentFlowCount() int {
	return len(ulp.GetCompletedContentFlowIDs())
}
