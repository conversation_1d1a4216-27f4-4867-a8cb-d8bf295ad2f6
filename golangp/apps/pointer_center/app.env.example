# Fluxpage Center Application Environment Configuration
# Copy this file to app.env and update the values according to your environment

# Application Environment
ENVIRONMENT=development
SERVER_PORT=8012
SERVER_READ_TIMEOUT=30
SERVER_WRITE_TIMEOUT=30

# Database Configuration
DB_HOST=***********
DB_PORT=5439
DB_USERNAME=geok
DB_PASSWORD=geok
DB_NAME=pointer
DB_SSL_MODE=disable

# mongodb configuration
MONGO_HOST=***********
MONGO_PORT=27017
MONGO_USERNAME=pageflux
MONGO_PASSWORD=pageflux
MONGO_DB_NAME=pageflux

# JWT Configuration
JWT_SECRET=uX_vBS2VWWky3kOb_wKxdblZIxgOijZQTCJnaJt8ovc=
JWT_EXPIRY_HOURS=24

# Export Configuration
EXPORT_MAX_RECORDS=10000
EXPORT_TEMP_DIR=/tmp

# External API Configuration
AI_SERVICE_URL=https://api.example.com
API_TIMEOUT=30

# Redis Configuration (if using Redis for caching/sessions)
REDIS_HOST=***********
REDIS_PORT=6389
REDIS_PASSWORD=geok
REDIS_DB=0

# Logging Configuration
LOG_PATH=/tmp/logs

# CORS Configuration
CORS_ALLOWED_ORIGINS=*
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=Origin,Content-Type,Accept,Authorization,X-Requested-With

# Rate Limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=100
RATE_LIMIT_BURST=10

# File Upload Configuration
MAX_UPLOAD_SIZE=10485760
ALLOWED_FILE_TYPES=csv,json,xlsx

# Google OAuth Configuration
GOOGLE_CLIENT_ID=837367551114-old3ir22j8ajdn9ej2jsphsm7ndfaqqr.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
GOOGLE_REDIRECT_URI=http://localhost:8081/google.html

# Storage Configuration (S3-compatible)
STORAGE_PROVIDER=minio
STORAGE_ENDPOINT=nas.corp.jancsitech.net:9000
STORAGE_ACCESS_KEY_ID=jancsitech
STORAGE_SECRET_KEY=jancsitech
STORAGE_USE_SSL=false
STORAGE_REGION=us-east-1
STORAGE_BUCKET=geok-center
STORAGE_BASE_URL=http://localhost:9000
ALLOWED_IMAGE_EXTS=jpg,jpeg,png,webp

# Email Configuration (for notifications)
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-email-password
SMTP_FROM=<EMAIL>

# Cookie Configuration
DOMAIN=localhost
ACCESS_TOKEN_MAX_AGE=15
REFRESH_TOKEN_MAX_AGE=30
ACCESS_TOKEN_PRIVATE_KEY=LS0tLS1CRUdJTiBSU0EgUFJJVkFURSBLRVktLS0tLQpNSUlCUEFJQkFBSkJBTzVIKytVM0xrWC91SlRvRHhWN01CUURXSTdGU0l0VXNjbGFFKzlaUUg5Q2VpOGIxcUVmCnJxR0hSVDVWUis4c3UxVWtCUVpZTER3MnN3RTVWbjg5c0ZVQ0F3RUFBUUpCQUw4ZjRBMUlDSWEvQ2ZmdWR3TGMKNzRCdCtwOXg0TEZaZXMwdHdtV3Vha3hub3NaV0w4eVpSTUJpRmI4a25VL0hwb3piTnNxMmN1ZU9wKzVWdGRXNApiTlVDSVFENm9JdWxqcHdrZTFGY1VPaldnaXRQSjNnbFBma3NHVFBhdFYwYnJJVVI5d0loQVBOanJ1enB4ckhsCkUxRmJxeGtUNFZ5bWhCOU1HazU0Wk1jWnVjSmZOcjBUQWlFQWhML3UxOVZPdlVBWVd6Wjc3Y3JxMTdWSFBTcXoKUlhsZjd2TnJpdEg1ZGdjQ0lRRHR5QmFPdUxuNDlIOFIvZ2ZEZ1V1cjg3YWl5UHZ1YStxeEpXMzQrb0tFNXdJZwpQbG1KYXZsbW9jUG4rTkVRdGhLcTZuZFVYRGpXTTlTbktQQTVlUDZSUEs0PQotLS0tLUVORCBSU0EgUFJJVkFURSBLRVktLS0tLQ==
ACCESS_TOKEN_PUBLIC_KEY=LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0KTUZ3d0RRWUpLb1pJaHZjTkFRRUJCUUFEU3dBd1NBSkJBTzVIKytVM0xrWC91SlRvRHhWN01CUURXSTdGU0l0VQpzY2xhRSs5WlFIOUNlaThiMXFFZnJxR0hSVDVWUis4c3UxVWtCUVpZTER3MnN3RTVWbjg5c0ZVQ0F3RUFBUT09Ci0tLS0tRU5EIFBVQkxJQyBLRVktLS0tLQ==
REFRESH_TOKEN_PRIVATE_KEY=LS0tLS1CRUdJTiBSU0EgUFJJVkFURSBLRVktLS0tLQpNSUlCT1FJQkFBSkJBSWFJcXZXeldCSndnYjR1SEhFQ01RdHFZMTI5b2F5RzVZMGlGcG51a0J1VHpRZVlQWkE4Cmx4OC9lTUh3Rys1MlJGR3VxMmE2N084d2s3TDR5dnY5dVY4Q0F3RUFBUUpBRUZ6aEJqOUk3LzAxR285N01CZUgKSlk5TUJLUEMzVHdQQVdwcSswL3p3UmE2ZkZtbXQ5NXNrN21qT3czRzNEZ3M5T2RTeWdsbTlVdndNWXh6SXFERAplUUloQVA5UStrMTBQbGxNd2ZJbDZtdjdTMFRYOGJDUlRaZVI1ZFZZb3FTeW40YmpBaUVBaHVUa2JtZ1NobFlZCnRyclNWZjN0QWZJcWNVUjZ3aDdMOXR5MVlvalZVRlVDSUhzOENlVHkwOWxrbkVTV0dvV09ZUEZVemhyc3Q2Z08KU3dKa2F2VFdKdndEQWlBdWhnVU8yeEFBaXZNdEdwUHVtb3hDam8zNjBMNXg4d012bWdGcEFYNW9uUUlnQzEvSwpNWG1heWtsaFRDeWtXRnpHMHBMWVdkNGRGdTI5M1M2ZUxJUlNIS009Ci0tLS0tRU5EIFJTQSBQUklWQVRFIEtFWS0tLS0t
REFRESH_TOKEN_PUBLIC_KEY=LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0KTUZ3d0RRWUpLb1pJaHZjTkFRRUJCUUFEU3dBd1NBSkJBSWFJcXZXeldCSndnYjR1SEhFQ01RdHFZMTI5b2F5Rwo1WTBpRnBudWtCdVR6UWVZUFpBOGx4OC9lTUh3Rys1MlJGR3VxMmE2N084d2s3TDR5dnY5dVY4Q0F3RUFBUT09Ci0tLS0tRU5EIFBVQkxJQyBLRVktLS0tLQ==

ALIPAY_APP_ID=2021000000000000
ALIPAY_PRIVATE_KEY=MIIEpAIBAAKCAQEAjLe8e8s72qo96/AfN8Zmm/WiHFKV93tMQ5OsyyhDJfWv5AShpPe7PP3bB5K6LvajA4MP5DWWIhCTLY2DQAAi/X+GMGpJsnIqC7WShmI8n0FMPjVNxuv4ej7PaKmpIyE6r0Ll5bhvs0vvBoY6JocwgSrmb5D1qS7sfZVN5tzXWVnCbwXjEbbT91ZwUcMLLpjaGYIxeMvceEgDEbDFxsimGUOT4av/PtDnt8cwjNKeGcvhMXP+Mqj1Go2YN6QHHEoggPnViEJkk3FY0Z4t07ZoPOc1voGqzudQvB/RuamxJougYSEIcef/LsJ9B8nW6jJFa/JJDCtXDprUqzAuZie15wIDAQABAoIBAFs4vRpvXKTbWfr6lfI9TsVpziaYLxnZN05TjF/DAQy5ocWqbvOXmSzn0DavmrzZLEIZk5K4XlRE9HRCWcSCkixNoDsAnDfvJGykz5EIXA5NcVSej4YV8OWmlCRgYAIjB30YXBq4nsjXv+M13rqdRwdlPS80BN0hSzP6Nx8jKQOFY0wx8+UWm4jl86UgCRy66p76pncIjygCw9f/gCbNZ4rA8NJzwZgNPaT8odJbZxALl6nLmZJYLqbqUaJf4GD5OLpq/ZXGQe73xmhWBTgJ5+EnRJixbRD3m1ZFGRcD++zCrWKeAL2xDLItcuCssefZRnG0OR8ep+DdiQGaylxbFaECgYEAx7w+QxouIkm8GmZ+rNe3JGRyVmaly85gf92YLgJSUZ0EgDEd428D1/UbbhyrWzcwATEJ3SKiLd90cuYU1QUXdEkLpBm/naaDKddVFai5C2bU6WYLsL8wKMTXXEHjVHOAQX7JTyvirNf5vGUr0rk9WZqeHtzeSl61n4KK70AqUrECgYEAtFt8Dnu1sJ1UptM1YyY2WBNCZv0QlR3BC6yKc+ahYA9GiIAr8DkpHgo0syIMJjl2+068BYmpb1Dhkui3RIxQo958v3//jVS30uSXMIkLHRo9JGPAtucwWSqKadydH3THFkd8q79tTdYitAE0XXO4S5bvpuCsbHSqq08aOp3AyBcCgYEAqMucLqc8X7vCnOkLmtR+sOG2gvAio3dYtBa4WgGaQeCZgiCAKsmSDPrWYJsX8XFVP1yLAngXaP8T8VoiNtgjtyFYFimwdvHWnRCeljxNjaYsvsZ/kLIyZ/HaMtx32tH1jOQyCpeC7CFlq2Iww9fKUiRKBrkPnvglM5VmBsVKPzECgYB72y+Qiqg1R3Ywsm7BTiaXyXIaY8OVg79kkvLpsQI1g4f+iXajG73+4SdbtQ/+2Au7fVBlxSnURgkC6EmjC3D9d/bXU8Fdf0ZVz6F8NUCqlbBTYyWe9ZkgQrSXCmfkiD+IL0OQdn9LqrheQU2Ct8tiLZ8aTMRcHqptxn7OrjMLtQKBgQDHX6Q51HMOkw+8sPUS2Q2EIQuR/g22C2jm2ZSt7gEXlUGPi3WQybRnJhyPeiWN7jzKzFlq9v/9r/AXWTUAMaXUsZhJ3n6ZGerqzCQLrO76I/FMUon9J6CyWgN8LeHGtS+GHdPaMUpdAx4ngyvoBaIIcny1b3Pf9motte4uuHDz7c==

ALIPAY_PUBLIC_KEY=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAk6EfCw7e2jNSm5WbqL4cnA9UuYxojJQPuDG9vJr9NhSp09avUYkmHSSI4Bw/h6o2Fluc4HMVRqPsWHm0R/MHUmE6IWj+iRZSkStk53hgFJX2j81UUDWP5IdMEWqqtt5vr9qiBMZ18/i8ZH0e9A+dDcaU18/C0XJheI97lrsJ3BXfzYlk9JLJEZxqPCzVFArbcRR0jRD2fHNgU7ww5KTdXTgaK/8wwHfAQzCmRxrWI1Gzd5stEPf9NAPXA0jRG0cp5AqT49+15Uo42A3Nwtn0XntZkQgJnKO0Wi+EJnkKv1JrVRxyMVk4R4+Z+/fp7q4Vc0JYP8211h+Nj9Ia4qvj5wIDAQAC
ALIPAY_GATEWAY_URL=https://openapi.alipaydev.com/gateway.do
ALIPAY_SIGN_TYPE=RSA2
ALIPAY_CHARSET=utf-8
ALIPAY_FORMAT=JSON
ALIPAY_VERSION=1.0
ALIPAY_RETURN_URL=http://localhost:8012/alipay/return
ALIPAY_NOTIFY_URL=http://localhost:8012/api/alipay/notify
ALIPAY_TIMEOUT_EXPRESS=30
ALIPAY_SANDBOX=true

# WeChat Pay Configuration
WECHAT_APP_ID=wxbf2a7bf9525ac893
WECHAT_MCH_ID=**********
WECHAT_API_KEY=openailink123456789abcdefghijkV2
WECHAT_API_V3_KEY=openailink123456789abcdefghijkV3
WECHAT_CERT_FILE=certs/apiclient_cert.pem
WECHAT_KEY_FILE=certs/apiclient_key.pem
WECHAT_SERIAL_NO=20736642C47368093A005C4A1532A07E16636578
WECHAT_API_VERSION=v3
WECHAT_NOTIFY_URL=http://weixin.open2any.tools:8000/api/wechat/payment/notify
WECHAT_TIMEOUT_EXPRESS=30
WECHAT_SIGN_TYPE=WECHATPAY2-SHA256-RSA2048
PAYMENT_SUB_MCH_ID=
PAYMENT_SUB_APP_ID=
WECHAT_HTTP_TIMEOUT=30
WECHAT_ENABLE_LOG=true
WECHAT_LOG_LEVEL=info

# dify api-keys and proxy configuration
DIFY_BASE_URL=
DIFY_TIMEOUT=300
AI_AGENT_TIMEOUT=300
DIFY_PROXY_TIMEOUT=300
AGENT_GOAL_PLANNER_API_KEY=
AGENT_PATH_GENERATOR_API_KEY=
AGENT_COURSE_GENERATOR_API_KEY=
AGENT_LESSON_GENERATOR_API_KEY=
AGENT_QUALITY_ASSURANCE_API_KEY=
AGENT_PROFILE_ANALYZER_API_KEY=
AGENT_ANY_API_KEY=
AGENT_PERSONALIZE_API_KEY=
