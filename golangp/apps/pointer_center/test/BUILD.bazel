load("@rules_go//go:def.bzl", "go_test")

go_test(
    name = "integration_test",
    srcs = ["integration_test.go"],
    deps = [
        "//golangp/apps/pointer_center/internal/config",
        "//golangp/apps/pointer_center/internal/routes",
        "//golangp/common/database/mongodb",
        "//golangp/common/database/postgres",
        "@com_github_gin_gonic_gin//:gin",
        "@com_github_stretchr_testify//assert",
    ],
)
