services:
  geok:
    image: geok_center:v1.0-beta
    build:
      context: .
      dockerfile: Dockerfile
    container_name: geok_container
    ports:
      - "0.0.0.0:8012:8012"
    depends_on:
      - postgres
      - redis
    volumes:
      - ./app.env:/app/app.env
      - ./geok:/tmp/geok/
    networks:
      - geok-network

  postgres:
    image: postgres:14
    container_name: geok_postgres_container
    environment:
      POSTGRES_USER: geok
      POSTGRES_PASSWORD: geok
      POSTGRES_DB: geok
    restart: always
    volumes:
      - ./docker-data/postgres_data:/var/lib/postgresql/data
    networks:
      - geok-network

  redis:
    image: redis:7.0-alpine
    container_name: geok_redis_container
    command: redis-server --requirepass geok --appendonly yes
    volumes:
      - ./docker-data/redis-data:/data
    networks:
      - geok-network

networks:
  geok-network:
    driver: bridge
