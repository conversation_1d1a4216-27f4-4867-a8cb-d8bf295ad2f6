load("@rules_go//go:def.bzl", "go_library")

go_library(
    name = "mockdata",
    srcs = glob(["*.go"]),
    importpath = "github.com/Arxtect/CoreXWorkSpace/golangp/apps/geok_center/mockdata",
    visibility = ["//visibility:public"],
    deps = [
        "//golangp/apps/geok_center/internal/models",
        "//golangp/apps/geok_center/internal/services",
        "//golangp/common/database/postgres",
        "@com_github_google_uuid//:uuid",
        "@io_gorm_datatypes//:datatypes",
        "@io_gorm_gorm//:gorm",
    ],
)
