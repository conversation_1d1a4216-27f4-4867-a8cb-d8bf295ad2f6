/*
 * @Description: GEO optimization service for GEOK Center
 * @Author: AI Assistant
 * @Date: 2025-07-21
 */
package services

import (
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"strings"
	"time"

	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/geok_center/internal/models"
	"github.com/Arxtect/CoreXWorkSpace/golangp/common/logging"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// GEOOptimizationService handles GEO optimization related business logic based on AI search data
type GEOOptimizationService struct {
	db                       *gorm.DB
	keywordExtractionService *KeywordExtractionService
}

// NewGEOOptimizationService creates a new GEO optimization service
func NewGEOOptimizationService(db *gorm.DB) *GEOOptimizationService {
	return &GEOOptimizationService{
		db:                       db,
		keywordExtractionService: NewKeywordExtractionService(),
	}
}

// CreateGEOOptimization creates a new GEO optimization record
func (s *GEOOptimizationService) CreateGEOOptimization(geo *models.GEOOptimization) (*models.GEOOptimization, error) {
	if geo.ID == uuid.Nil {
		geo.ID = uuid.New()
	}

	if err := s.db.Create(geo).Error; err != nil {
		logging.Error("Failed to create GEO optimization: %v", err)
		return nil, err
	}

	logging.Info("Created GEO optimization for brand %s in region %s", geo.BrandID, geo.Region)
	return geo, nil
}

// CreateGEOOptimizationFromAISearch creates a GEO optimization record based on AI search data
func (s *GEOOptimizationService) CreateGEOOptimizationFromAISearch(brandID uuid.UUID, region string) (*models.GEOOptimization, error) {
	// Extract keywords from AI search data
	keywordAnalysis, err := s.keywordExtractionService.ExtractKeywordsFromBrand(brandID, region)
	if err != nil {
		logging.Error("Failed to extract keywords for brand %s: %v", brandID, err)
		return nil, fmt.Errorf("failed to extract keywords: %w", err)
	}

	// Get AI searches for analysis
	var aiSearches []models.AISearch
	query := s.db.Where("brand_id = ?", brandID)
	if region != "" {
		query = query.Where("region = ?", region)
	}
	if err := query.Find(&aiSearches).Error; err != nil {
		logging.Error("Failed to get AI searches for brand %s: %v", brandID, err)
		return nil, fmt.Errorf("failed to get AI searches: %w", err)
	}

	// Analyze AI search data to extract insights
	brandOverview := s.extractBrandOverview(aiSearches)
	advantages := s.extractAdvantages(aiSearches)
	weaknesses := s.extractWeaknesses(aiSearches)
	expertiseAreas := s.extractExpertiseAreas(aiSearches, keywordAnalysis)
	commonAssociations := s.extractCommonAssociations(aiSearches, keywordAnalysis)
	contentStrategy := s.generateContentStrategy(aiSearches, keywordAnalysis, region)
	competitorAnalysis := s.extractCompetitorAnalysis(aiSearches)
	referencesData := s.extractReferencesData(aiSearches)
	statistics := s.calculateStatistics(aiSearches, keywordAnalysis)

	// Create GEO optimization record
	geo := &models.GEOOptimization{
		ID:                  uuid.New(),
		BrandID:             brandID,
		Region:              region,
		Country:             s.getCountryFromRegion(region),
		Status:              models.GEOOptimizationStatusActive,
		Priority:            1,
		CreatedAt:           time.Now(),
		UpdatedAt:           time.Now(),
		LastAnalyzedAt:      time.Now(),
		DataVersion:         1,
		SourceAISearchCount: int(keywordAnalysis.TotalQuestions),
	}

	// Set JSON fields
	if err := geo.SetBrandOverview(brandOverview); err != nil {
		return nil, fmt.Errorf("failed to set brand overview: %w", err)
	}
	if err := geo.SetAdvantages(advantages); err != nil {
		return nil, fmt.Errorf("failed to set advantages: %w", err)
	}
	if err := geo.SetWeaknesses(weaknesses); err != nil {
		return nil, fmt.Errorf("failed to set weaknesses: %w", err)
	}

	// Set other JSON fields
	expertiseJSON, _ := json.Marshal(expertiseAreas)
	geo.MainExpertiseAreas = expertiseJSON

	associationsJSON, _ := json.Marshal(commonAssociations)
	geo.CommonAssociations = associationsJSON

	strategyJSON, _ := json.Marshal(contentStrategy)
	geo.ContentStrategyRecommendations = strategyJSON

	competitorJSON, _ := json.Marshal(competitorAnalysis)
	geo.CompetitorAnalysis = competitorJSON

	referencesJSON, _ := json.Marshal(referencesData)
	geo.ReferencesData = referencesJSON

	statisticsJSON, _ := json.Marshal(statistics)
	geo.Statistics = statisticsJSON

	keywordsJSON, _ := json.Marshal(keywordAnalysis)
	geo.ExtractedKeywords = keywordsJSON

	// Calculate performance metrics
	geo.VisibilityScore = s.calculateVisibilityScore(aiSearches, keywordAnalysis)
	geo.MarketPenetration = s.calculateMarketPenetration(aiSearches, keywordAnalysis)
	geo.SearchVolume = s.calculateSearchVolume(keywordAnalysis)
	geo.MentionFrequency = s.calculateMentionFrequency(aiSearches)
	geo.SentimentScore = s.calculateSentimentScore(aiSearches)
	geo.CompetitionLevel = s.determineCompetitionLevel(aiSearches, keywordAnalysis)
	geo.OpportunityScore = s.calculateOpportunityScore(geo)

	// Save to database
	if err := s.db.Create(geo).Error; err != nil {
		logging.Error("Failed to create GEO optimization from AI search: %v", err)
		return nil, err
	}

	logging.Info("Created GEO optimization from AI search for brand %s in region %s", brandID, region)
	return geo, nil
}

// GetGEOOptimizationByID retrieves a GEO optimization by ID
func (s *GEOOptimizationService) GetGEOOptimizationByID(id uuid.UUID) (*models.GEOOptimization, error) {
	var geo models.GEOOptimization
	if err := s.db.Preload("Brand").Preload("GeoDatabases").First(&geo, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("GEO optimization not found")
		}
		return nil, err
	}
	return &geo, nil
}

// GetGEOOptimizationsByBrandID retrieves all GEO optimizations for a brand
func (s *GEOOptimizationService) GetGEOOptimizationsByBrandID(brandID uuid.UUID, limit, offset int) ([]models.GEOOptimization, int64, error) {
	var geos []models.GEOOptimization
	var total int64

	// Count total records
	if err := s.db.Model(&models.GEOOptimization{}).Where("brand_id = ?", brandID).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Get paginated records
	query := s.db.Where("brand_id = ?", brandID).
		Preload("Brand").
		Preload("GeoDatabases").
		Order("created_at DESC")

	if limit > 0 {
		query = query.Limit(limit)
	}
	if offset > 0 {
		query = query.Offset(offset)
	}

	if err := query.Find(&geos).Error; err != nil {
		return nil, 0, err
	}

	return geos, total, nil
}

// GetGEOOptimizationsByRegion retrieves GEO optimizations by region
func (s *GEOOptimizationService) GetGEOOptimizationsByRegion(region string, limit, offset int) ([]models.GEOOptimization, int64, error) {
	var geos []models.GEOOptimization
	var total int64

	// Count total records
	if err := s.db.Model(&models.GEOOptimization{}).Where("region = ?", region).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Get paginated records
	query := s.db.Where("region = ?", region).
		Preload("Brand").
		Preload("GeoDatabases").
		Order("visibility_score DESC")

	if limit > 0 {
		query = query.Limit(limit)
	}
	if offset > 0 {
		query = query.Offset(offset)
	}

	if err := query.Find(&geos).Error; err != nil {
		return nil, 0, err
	}

	return geos, total, nil
}

// UpdateGEOOptimization updates a GEO optimization record
func (s *GEOOptimizationService) UpdateGEOOptimization(id uuid.UUID, updates map[string]interface{}) (*models.GEOOptimization, error) {
	var geo models.GEOOptimization
	if err := s.db.First(&geo, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("GEO optimization not found")
		}
		return nil, err
	}

	if err := s.db.Model(&geo).Updates(updates).Error; err != nil {
		logging.Error("Failed to update GEO optimization %s: %v", id, err)
		return nil, err
	}

	// Reload with associations
	if err := s.db.Preload("Brand").Preload("GeoDatabases").First(&geo, "id = ?", id).Error; err != nil {
		return nil, err
	}

	logging.Info("Updated GEO optimization %s", id)
	return &geo, nil
}

// DeleteGEOOptimization soft deletes a GEO optimization record
func (s *GEOOptimizationService) DeleteGEOOptimization(id uuid.UUID) error {
	var geo models.GEOOptimization
	if err := s.db.First(&geo, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("GEO optimization not found")
		}
		return err
	}

	if err := s.db.Delete(&geo).Error; err != nil {
		logging.Error("Failed to delete GEO optimization %s: %v", id, err)
		return err
	}

	logging.Info("Deleted GEO optimization %s", id)
	return nil
}

// CreateGEODatabase creates a new GEO database entry
func (s *GEOOptimizationService) CreateGEODatabase(geoDB *models.GEODatabase) (*models.GEODatabase, error) {
	if geoDB.ID == uuid.Nil {
		geoDB.ID = uuid.New()
	}

	if err := s.db.Create(geoDB).Error; err != nil {
		logging.Error("Failed to create GEO database entry: %v", err)
		return nil, err
	}

	logging.Info("Created GEO database entry for brand %s", geoDB.BrandID)
	return geoDB, nil
}

// GetGEODatabaseByID retrieves a GEO database entry by ID
func (s *GEOOptimizationService) GetGEODatabaseByID(id uuid.UUID) (*models.GEODatabase, error) {
	var geoDB models.GEODatabase
	if err := s.db.Preload("Brand").Preload("GeoOptimization").Preload("AISearch").First(&geoDB, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("GEO database entry not found")
		}
		return nil, err
	}
	return &geoDB, nil
}

// GetGEODatabasesByGeoOptimizationID retrieves GEO database entries by GEO optimization ID
func (s *GEOOptimizationService) GetGEODatabasesByGeoOptimizationID(geoOptimizationID uuid.UUID, limit, offset int) ([]models.GEODatabase, int64, error) {
	var geoDBs []models.GEODatabase
	var total int64

	// Count total records
	if err := s.db.Model(&models.GEODatabase{}).Where("geo_optimization_id = ?", geoOptimizationID).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Get paginated records
	query := s.db.Where("geo_optimization_id = ?", geoOptimizationID).
		Preload("Brand").
		Preload("GeoOptimization").
		Preload("AISearch").
		Order("created_at DESC")

	if limit > 0 {
		query = query.Limit(limit)
	}
	if offset > 0 {
		query = query.Offset(offset)
	}

	if err := query.Find(&geoDBs).Error; err != nil {
		return nil, 0, err
	}

	return geoDBs, total, nil
}

// GetGEODatabasesByBrandID retrieves GEO database entries by brand ID
func (s *GEOOptimizationService) GetGEODatabasesByBrandID(brandID uuid.UUID, limit, offset int) ([]models.GEODatabase, int64, error) {
	var geoDBs []models.GEODatabase
	var total int64

	query := s.db.Model(&models.GEODatabase{}).Where("brand_id = ?", brandID)

	// Count total records
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Get paginated records
	query = s.db.Where("brand_id = ?", brandID)

	query = query.Preload("Brand").
		Preload("GeoOptimization").
		Preload("AISearch").
		Order("created_at DESC")

	if limit > 0 {
		query = query.Limit(limit)
	}
	if offset > 0 {
		query = query.Offset(offset)
	}

	if err := query.Find(&geoDBs).Error; err != nil {
		return nil, 0, err
	}

	return geoDBs, total, nil
}

// GetGEODatabaseStatsForBrand gets GEO database statistics for a brand with latest N days aggregation
func (s *GEOOptimizationService) GetGEODatabaseStatsForBrand(brandID uuid.UUID, days int) ([]map[string]interface{}, error) {
	// 获取品牌信息和关键词
	var brand models.Brand
	if err := s.db.First(&brand, "id = ?", brandID).Error; err != nil {
		logging.Error("Failed to get brand for GEO database stats: %v", err)
		return nil, fmt.Errorf("failed to get brand: %w", err)
	}

	// 解析品牌关键词
	var keywords []string
	if brand.Keywords != "" {
		// 简单的逗号分割，您可以根据需要调整解析逻辑
		keywordParts := strings.Split(brand.Keywords, ",")
		for _, keyword := range keywordParts {
			trimmed := strings.TrimSpace(keyword)
			if trimmed != "" {
				keywords = append(keywords, trimmed)
			}
		}
	}

	// 获取最新的N天数据
	var geoDBs []models.GEODatabase
	query := s.db.Where("brand_id = ?", brandID).
		Order("created_at DESC").
		Limit(days)

	if err := query.Find(&geoDBs).Error; err != nil {
		logging.Error("Failed to get GEO databases for stats: %v", err)
		return nil, fmt.Errorf("failed to get GEO databases: %w", err)
	}

	// 获取前N天到2N天的数据用于计算变化
	var previousGeoDBs []models.GEODatabase
	previousQuery := s.db.Where("brand_id = ?", brandID).
		Order("created_at DESC").
		Limit(days).
		Offset(days)

	if err := previousQuery.Find(&previousGeoDBs).Error; err != nil {
		logging.Error("Failed to get previous GEO databases for comparison: %v", err)
		// 如果获取失败，继续处理，但变化值为0
	}

	// 聚合当前N天的数据
	currentSearchCount := int64(0)
	currentMentionRate := 0.0
	currentCount := len(geoDBs)

	for _, geoDB := range geoDBs {
		currentSearchCount += geoDB.SearchCount
		currentMentionRate += geoDB.MentionRate
	}

	if currentCount > 0 {
		currentMentionRate = currentMentionRate / float64(currentCount) // 计算平均提及率
	}

	// 聚合前N天的数据
	previousSearchCount := int64(0)
	previousMentionRate := 0.0
	previousCount := len(previousGeoDBs)

	for _, geoDB := range previousGeoDBs {
		previousSearchCount += geoDB.SearchCount
		previousMentionRate += geoDB.MentionRate
	}

	if previousCount > 0 {
		previousMentionRate = previousMentionRate / float64(previousCount) // 计算平均提及率
	}

	// 计算变化值
	searchCountChange := currentSearchCount - previousSearchCount
	mentionRateChange := currentMentionRate - previousMentionRate

	// 格式化浮点数，保留2位小数
	formattedMentionRate := math.Round(currentMentionRate*100) / 100
	formattedMentionRateChange := math.Round(mentionRateChange*100) / 100

	// 构建结果数组（每个GEO数据库条目一个对象）
	var result []map[string]interface{}
	for _, geoDB := range geoDBs {
		result = append(result, map[string]interface{}{
			"title":               geoDB.Title,
			"keywords":            keywords,
			"search_count":        currentSearchCount,
			"search_count_change": searchCountChange,
			"mention_rate":        formattedMentionRate,
			"mention_rate_change": formattedMentionRateChange,
		})
	}

	// 如果没有数据，至少返回一个包含品牌关键词的对象
	if len(result) == 0 {
		result = append(result, map[string]interface{}{
			"title":               brand.Name + " GEO数据",
			"keywords":            keywords,
			"search_count":        currentSearchCount,
			"search_count_change": searchCountChange,
			"mention_rate":        formattedMentionRate,
			"mention_rate_change": formattedMentionRateChange,
		})
	}

	logging.Info("Retrieved GEO database stats for brand %s: %d entries", brandID, len(result))
	return result, nil
}

// UpdateGEODatabase updates a GEO database entry
func (s *GEOOptimizationService) UpdateGEODatabase(id uuid.UUID, updates map[string]interface{}) (*models.GEODatabase, error) {
	var geoDB models.GEODatabase
	if err := s.db.First(&geoDB, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("GEO database entry not found")
		}
		return nil, err
	}

	if err := s.db.Model(&geoDB).Updates(updates).Error; err != nil {
		logging.Error("Failed to update GEO database entry %s: %v", id, err)
		return nil, err
	}

	// Reload with associations
	if err := s.db.Preload("Brand").Preload("GeoOptimization").Preload("AISearch").First(&geoDB, "id = ?", id).Error; err != nil {
		return nil, err
	}

	logging.Info("Updated GEO database entry %s", id)
	return &geoDB, nil
}

// DeleteGEODatabase soft deletes a GEO database entry
func (s *GEOOptimizationService) DeleteGEODatabase(id uuid.UUID) error {
	var geoDB models.GEODatabase
	if err := s.db.First(&geoDB, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("GEO database entry not found")
		}
		return err
	}

	if err := s.db.Delete(&geoDB).Error; err != nil {
		logging.Error("Failed to delete GEO database entry %s: %v", id, err)
		return err
	}

	logging.Info("Deleted GEO database entry %s", id)
	return nil
}

// AnalyzeGEOPerformance analyzes GEO performance for a brand
func (s *GEOOptimizationService) AnalyzeGEOPerformance(brandID uuid.UUID) (map[string]interface{}, error) {
	var geos []models.GEOOptimization
	if err := s.db.Where("brand_id = ?", brandID).Find(&geos).Error; err != nil {
		return nil, err
	}

	if len(geos) == 0 {
		return map[string]interface{}{
			"total_regions":         0,
			"active_campaigns":      0,
			"global_reach_score":    0.0,
			"top_performing_region": "",
			"geo_performance":       []interface{}{},
		}, nil
	}

	// Calculate overview metrics
	totalRegions := len(geos)
	activeCampaigns := 0
	totalVisibilityScore := 0.0
	topPerformingRegion := ""
	maxVisibilityScore := 0.0

	var geoPerformance []map[string]interface{}

	for _, geo := range geos {
		if geo.Status == models.GEOOptimizationStatusActive {
			activeCampaigns++
		}

		totalVisibilityScore += geo.VisibilityScore

		if geo.VisibilityScore > maxVisibilityScore {
			maxVisibilityScore = geo.VisibilityScore
			topPerformingRegion = geo.Region
		}

		// Parse statistics data
		var statistics map[string]interface{}
		if geo.Statistics != nil {
			json.Unmarshal(geo.Statistics, &statistics)
		}

		// Parse extracted keywords
		var extractedKeywords map[string]interface{}
		if geo.ExtractedKeywords != nil {
			json.Unmarshal(geo.ExtractedKeywords, &extractedKeywords)
		}

		// Parse brand overview
		brandOverview, _ := geo.GetBrandOverview()

		geoPerformance = append(geoPerformance, map[string]interface{}{
			"id":                     geo.ID,
			"region":                 geo.Region,
			"country":                geo.Country,
			"visibility_score":       geo.VisibilityScore,
			"market_penetration":     geo.MarketPenetration,
			"search_volume":          geo.SearchVolume,
			"mention_frequency":      geo.MentionFrequency,
			"sentiment_score":        geo.SentimentScore,
			"competition_level":      geo.CompetitionLevel,
			"opportunity_score":      geo.OpportunityScore,
			"statistics":             statistics,
			"extracted_keywords":     extractedKeywords,
			"brand_overview":         brandOverview,
			"last_analyzed_at":       geo.LastAnalyzedAt,
			"data_version":           geo.DataVersion,
			"source_ai_search_count": geo.SourceAISearchCount,
		})
	}

	globalReachScore := 0.0
	if totalRegions > 0 {
		globalReachScore = totalVisibilityScore / float64(totalRegions)
	}

	return map[string]interface{}{
		"overview": map[string]interface{}{
			"total_regions":         totalRegions,
			"active_campaigns":      activeCampaigns,
			"global_reach_score":    globalReachScore,
			"top_performing_region": topPerformingRegion,
		},
		"geo_performance": geoPerformance,
	}, nil
}

// GenerateGEORecommendations generates optimization recommendations for a brand
func (s *GEOOptimizationService) GenerateGEORecommendations(brandID uuid.UUID) ([]map[string]interface{}, error) {
	var geos []models.GEOOptimization
	if err := s.db.Where("brand_id = ?", brandID).Order("opportunity_score DESC").Find(&geos).Error; err != nil {
		return nil, err
	}

	var recommendations []map[string]interface{}

	for _, geo := range geos {
		priority := "low"
		expectedROI := "80%"
		timeline := "18-24个月"

		if geo.OpportunityScore >= 8.0 {
			priority = "high"
			expectedROI = "150%"
			timeline = "6-12个月"
		} else if geo.OpportunityScore >= 6.0 {
			priority = "medium"
			expectedROI = "120%"
			timeline = "12-18个月"
		}

		action := fmt.Sprintf("在%s市场加大投资", geo.Region)
		if geo.VisibilityScore < 50 {
			action = fmt.Sprintf("进入%s市场", geo.Region)
		} else if geo.VisibilityScore < 70 {
			action = fmt.Sprintf("扩展%s业务", geo.Region)
		}

		recommendations = append(recommendations, map[string]interface{}{
			"priority":          priority,
			"action":            action,
			"expected_roi":      expectedROI,
			"timeline":          timeline,
			"region":            geo.Region,
			"country":           geo.Country,
			"opportunity_score": geo.OpportunityScore,
		})
	}

	return recommendations, nil
}

// GetGEOStats returns statistics for GEO optimizations
func (s *GEOOptimizationService) GetGEOStats(brandID *uuid.UUID) (map[string]interface{}, error) {
	query := s.db.Model(&models.GEOOptimization{})
	if brandID != nil {
		query = query.Where("brand_id = ?", *brandID)
	}

	var totalCount int64
	var activeCount int64
	var avgVisibilityScore float64
	var avgOpportunityScore float64

	// Get total count
	if err := query.Count(&totalCount).Error; err != nil {
		return nil, err
	}

	// Get active count
	if err := query.Where("status = ?", models.GEOOptimizationStatusActive).Count(&activeCount).Error; err != nil {
		return nil, err
	}

	// Get average scores
	var result struct {
		AvgVisibility  float64
		AvgOpportunity float64
	}

	if err := query.Select("AVG(visibility_score) as avg_visibility, AVG(opportunity_score) as avg_opportunity").Scan(&result).Error; err != nil {
		return nil, err
	}

	avgVisibilityScore = result.AvgVisibility
	avgOpportunityScore = result.AvgOpportunity

	return map[string]interface{}{
		"total_count":           totalCount,
		"active_count":          activeCount,
		"avg_visibility_score":  avgVisibilityScore,
		"avg_opportunity_score": avgOpportunityScore,
	}, nil
}

// Helper functions for type conversion
func convertToFloat64(v interface{}) float64 {
	switch val := v.(type) {
	case float64:
		return val
	case int:
		return float64(val)
	case int64:
		return float64(val)
	default:
		return 0.0
	}
}

func convertToInt64(v interface{}) int64 {
	switch val := v.(type) {
	case int64:
		return val
	case int:
		return int64(val)
	case float64:
		return int64(val)
	default:
		return 0
	}
}

// AI Search Analysis Methods

// extractBrandOverview extracts brand overview from AI search data
func (s *GEOOptimizationService) extractBrandOverview(aiSearches []models.AISearch) *models.BrandOverviewData {
	overview := &models.BrandOverviewData{
		CoreValues:     []string{},
		TargetAudience: "技术用户",
		MarketSegment:  "科技产品",
		Description:    "基于AI搜索数据分析的品牌概述",
	}

	// Analyze questions to extract positioning
	positioningKeywords := []string{}
	for _, search := range aiSearches {
		if strings.Contains(search.Question, "定位") || strings.Contains(search.Question, "特点") {
			positioningKeywords = append(positioningKeywords, search.Keywords)
		}
	}

	if len(positioningKeywords) > 0 {
		overview.Positioning = strings.Join(positioningKeywords, ", ")
	} else {
		overview.Positioning = "技术领先的品牌"
	}

	// Extract core values from frequent keywords
	valueKeywords := []string{"创新", "质量", "性能", "可靠", "专业"}
	for _, search := range aiSearches {
		for _, keyword := range valueKeywords {
			if strings.Contains(search.Question, keyword) {
				overview.CoreValues = append(overview.CoreValues, keyword)
			}
			// Check in responses if available
			for _, response := range search.Responses {
				if strings.Contains(response.Response, keyword) {
					overview.CoreValues = append(overview.CoreValues, keyword)
				}
			}
		}
	}

	// Remove duplicates
	overview.CoreValues = removeDuplicates(overview.CoreValues)

	return overview
}

// extractAdvantages extracts brand advantages from AI search data
func (s *GEOOptimizationService) extractAdvantages(aiSearches []models.AISearch) *models.AdvantagesData {
	advantages := &models.AdvantagesData{
		TechnicalAdvantages: []string{},
		MarketAdvantages:    []string{},
		ProductAdvantages:   []string{},
		ServiceAdvantages:   []string{},
		OverallStrengths:    []string{},
	}

	for _, search := range aiSearches {
		// Look for positive mentions and advantages in responses
		hasPositiveMention := false
		for _, response := range search.Responses {
			if strings.Contains(response.Response, "优势") || strings.Contains(response.Response, "优点") ||
				strings.Contains(response.Response, "强项") || strings.Contains(response.Response, "擅长") {
				hasPositiveMention = true
				break
			}
		}

		if hasPositiveMention {
			// Categorize advantages
			if strings.Contains(search.Question, "技术") || strings.Contains(search.Question, "性能") {
				advantages.TechnicalAdvantages = append(advantages.TechnicalAdvantages, search.Keywords)
			} else if strings.Contains(search.Question, "市场") || strings.Contains(search.Question, "销售") {
				advantages.MarketAdvantages = append(advantages.MarketAdvantages, search.Keywords)
			} else if strings.Contains(search.Question, "产品") {
				advantages.ProductAdvantages = append(advantages.ProductAdvantages, search.Keywords)
			} else if strings.Contains(search.Question, "服务") {
				advantages.ServiceAdvantages = append(advantages.ServiceAdvantages, search.Keywords)
			} else {
				advantages.OverallStrengths = append(advantages.OverallStrengths, search.Keywords)
			}
		}
	}

	// Remove duplicates and empty values
	advantages.TechnicalAdvantages = removeDuplicates(filterEmpty(advantages.TechnicalAdvantages))
	advantages.MarketAdvantages = removeDuplicates(filterEmpty(advantages.MarketAdvantages))
	advantages.ProductAdvantages = removeDuplicates(filterEmpty(advantages.ProductAdvantages))
	advantages.ServiceAdvantages = removeDuplicates(filterEmpty(advantages.ServiceAdvantages))
	advantages.OverallStrengths = removeDuplicates(filterEmpty(advantages.OverallStrengths))

	return advantages
}

// extractWeaknesses extracts brand weaknesses from AI search data
func (s *GEOOptimizationService) extractWeaknesses(aiSearches []models.AISearch) *models.WeaknessesData {
	weaknesses := &models.WeaknessesData{
		TechnicalWeaknesses: []string{},
		MarketWeaknesses:    []string{},
		ProductWeaknesses:   []string{},
		ServiceWeaknesses:   []string{},
		ImprovementAreas:    []string{},
	}

	for _, search := range aiSearches {
		// Look for negative mentions and weaknesses in responses
		hasNegativeMention := false
		for _, response := range search.Responses {
			if strings.Contains(response.Response, "缺点") || strings.Contains(response.Response, "不足") ||
				strings.Contains(response.Response, "弱点") || strings.Contains(response.Response, "问题") {
				hasNegativeMention = true
				break
			}
		}

		if hasNegativeMention {
			// Categorize weaknesses
			if strings.Contains(search.Question, "技术") || strings.Contains(search.Question, "性能") {
				weaknesses.TechnicalWeaknesses = append(weaknesses.TechnicalWeaknesses, search.Keywords)
			} else if strings.Contains(search.Question, "市场") || strings.Contains(search.Question, "销售") {
				weaknesses.MarketWeaknesses = append(weaknesses.MarketWeaknesses, search.Keywords)
			} else if strings.Contains(search.Question, "产品") {
				weaknesses.ProductWeaknesses = append(weaknesses.ProductWeaknesses, search.Keywords)
			} else if strings.Contains(search.Question, "服务") {
				weaknesses.ServiceWeaknesses = append(weaknesses.ServiceWeaknesses, search.Keywords)
			} else {
				weaknesses.ImprovementAreas = append(weaknesses.ImprovementAreas, search.Keywords)
			}
		}
	}

	// Remove duplicates and empty values
	weaknesses.TechnicalWeaknesses = removeDuplicates(filterEmpty(weaknesses.TechnicalWeaknesses))
	weaknesses.MarketWeaknesses = removeDuplicates(filterEmpty(weaknesses.MarketWeaknesses))
	weaknesses.ProductWeaknesses = removeDuplicates(filterEmpty(weaknesses.ProductWeaknesses))
	weaknesses.ServiceWeaknesses = removeDuplicates(filterEmpty(weaknesses.ServiceWeaknesses))
	weaknesses.ImprovementAreas = removeDuplicates(filterEmpty(weaknesses.ImprovementAreas))

	return weaknesses
}

// extractExpertiseAreas extracts main expertise areas from AI search data
func (s *GEOOptimizationService) extractExpertiseAreas(aiSearches []models.AISearch, keywordAnalysis *KeywordAnalysisResult) *models.ExpertiseAreasData {
	areas := &models.ExpertiseAreasData{
		PrimaryAreas:   []string{},
		SecondaryAreas: []string{},
		EmergingAreas:  []string{},
		Specialties:    []string{},
	}

	// Extract from trending keywords
	if len(keywordAnalysis.TrendingKeywords) > 0 {
		areas.PrimaryAreas = keywordAnalysis.TrendingKeywords[:min(5, len(keywordAnalysis.TrendingKeywords))]
	}

	// Extract from question types
	questionTypes := make(map[string]int)
	for _, search := range aiSearches {
		if search.QuestionType != "" {
			questionTypes[search.QuestionType]++
		}
	}

	// Sort by frequency
	for qType := range questionTypes {
		if questionTypes[qType] > 2 {
			areas.SecondaryAreas = append(areas.SecondaryAreas, qType)
		} else {
			areas.EmergingAreas = append(areas.EmergingAreas, qType)
		}
	}

	// Extract specialties from keywords
	for category, keywords := range keywordAnalysis.KeywordCategories {
		if len(keywords) > 0 {
			areas.Specialties = append(areas.Specialties, category)
		}
	}

	return areas
}

// extractCommonAssociations extracts common word associations
func (s *GEOOptimizationService) extractCommonAssociations(aiSearches []models.AISearch, keywordAnalysis *KeywordAnalysisResult) *models.CommonAssociationsData {
	associations := &models.CommonAssociationsData{
		PositiveAssociations: []string{},
		NeutralAssociations:  []string{},
		NegativeAssociations: []string{},
		FrequentPairs:        []string{},
		ContextualWords:      []string{},
	}

	// Analyze sentiment and categorize keywords
	positiveWords := []string{"好", "优秀", "推荐", "值得", "性价比", "高效", "稳定"}
	negativeWords := []string{"差", "不好", "问题", "缺点", "不足", "失望"}

	for keyword := range keywordAnalysis.Keywords {
		isPositive := false
		isNegative := false

		for _, pos := range positiveWords {
			if strings.Contains(keyword, pos) {
				isPositive = true
				break
			}
		}

		for _, neg := range negativeWords {
			if strings.Contains(keyword, neg) {
				isNegative = true
				break
			}
		}

		if isPositive {
			associations.PositiveAssociations = append(associations.PositiveAssociations, keyword)
		} else if isNegative {
			associations.NegativeAssociations = append(associations.NegativeAssociations, keyword)
		} else {
			associations.NeutralAssociations = append(associations.NeutralAssociations, keyword)
		}
	}

	// Extract frequent pairs from questions
	for _, search := range aiSearches {
		words := strings.Fields(search.Question)
		for i := 0; i < len(words)-1; i++ {
			pair := words[i] + " " + words[i+1]
			associations.FrequentPairs = append(associations.FrequentPairs, pair)
		}
	}

	// Remove duplicates
	associations.PositiveAssociations = removeDuplicates(associations.PositiveAssociations)
	associations.NeutralAssociations = removeDuplicates(associations.NeutralAssociations)
	associations.NegativeAssociations = removeDuplicates(associations.NegativeAssociations)
	associations.FrequentPairs = removeDuplicates(associations.FrequentPairs)

	return associations
}

// generateContentStrategy generates content strategy recommendations
func (s *GEOOptimizationService) generateContentStrategy(aiSearches []models.AISearch, keywordAnalysis *KeywordAnalysisResult, region string) *models.ContentStrategyData {
	strategy := &models.ContentStrategyData{
		RecommendedTopics:  []string{},
		ContentTypes:       []string{"技术文档", "产品评测", "使用指南", "对比分析"},
		TargetKeywords:     keywordAnalysis.TrendingKeywords,
		PublishingChannels: []string{"官方网站", "技术博客", "社交媒体", "行业论坛"},
		OptimalTiming:      []string{"工作日上午", "技术发布会期间", "行业展会前后"},
		LocalizationTips:   []string{},
	}

	// Extract topics from question types
	topicMap := make(map[string]int)
	for _, search := range aiSearches {
		if search.QuestionType != "" {
			topicMap[search.QuestionType]++
		}
	}

	for topic, count := range topicMap {
		if count > 1 {
			strategy.RecommendedTopics = append(strategy.RecommendedTopics, topic)
		}
	}

	// Add region-specific localization tips
	switch region {
	case "北美":
		strategy.LocalizationTips = append(strategy.LocalizationTips, "注重技术创新和性能指标", "强调企业级应用场景")
	case "欧洲":
		strategy.LocalizationTips = append(strategy.LocalizationTips, "重视数据隐私和合规性", "突出环保和可持续发展")
	case "亚太":
		strategy.LocalizationTips = append(strategy.LocalizationTips, "关注性价比和实用性", "适应本地化需求")
	default:
		strategy.LocalizationTips = append(strategy.LocalizationTips, "根据当地市场特点调整内容策略")
	}

	return strategy
}

// extractCompetitorAnalysis extracts competitor analysis from AI search data
func (s *GEOOptimizationService) extractCompetitorAnalysis(aiSearches []models.AISearch) *models.CompetitorAnalysisData {
	analysis := &models.CompetitorAnalysisData{
		MainCompetitors:      []string{},
		CompetitorStrengths:  make(map[string][]string),
		CompetitorWeaknesses: make(map[string][]string),
		MarketPositioning:    make(map[string]string),
		CompetitiveAdvantage: []string{},
	}

	// Extract competitor mentions from AI responses
	competitorKeywords := []string{"竞争对手", "对比", "vs", "比较", "其他品牌"}

	for _, search := range aiSearches {
		for _, keyword := range competitorKeywords {
			// Check in question
			if strings.Contains(search.Question, keyword) {
				analysis.MainCompetitors = append(analysis.MainCompetitors, search.Keywords)
			}

			// Check in responses
			for _, response := range search.Responses {
				if strings.Contains(response.Response, keyword) {
					// Extract potential competitor names (simplified)
					words := strings.Fields(response.Response)
					for _, word := range words {
						if len(word) > 2 && !strings.Contains(word, "的") && !strings.Contains(word, "是") {
							analysis.MainCompetitors = append(analysis.MainCompetitors, word)
						}
					}
				}
			}
		}
	}

	// Remove duplicates and limit to top competitors
	analysis.MainCompetitors = removeDuplicates(analysis.MainCompetitors)
	if len(analysis.MainCompetitors) > 5 {
		analysis.MainCompetitors = analysis.MainCompetitors[:5]
	}

	// Add generic competitive advantages
	analysis.CompetitiveAdvantage = []string{"技术创新", "产品质量", "客户服务", "市场经验"}

	return analysis
}

// extractReferencesData extracts references data from AI search data
func (s *GEOOptimizationService) extractReferencesData(aiSearches []models.AISearch) *models.ReferencesData {
	references := &models.ReferencesData{
		SourceDomains:   []string{},
		AuthorityScores: make(map[string]float64),
		CitationCount:   len(aiSearches),
		QualityMetrics:  make(map[string]float64),
		TrustScores:     make(map[string]float64),
	}

	// Extract source domains from AI search data (simplified)
	domains := []string{"官方网站", "技术论坛", "评测网站", "新闻媒体", "社交媒体"}
	references.SourceDomains = domains

	// Set default authority scores
	for _, domain := range domains {
		references.AuthorityScores[domain] = 0.8
		references.QualityMetrics[domain] = 0.75
		references.TrustScores[domain] = 0.85
	}

	return references
}

// calculateStatistics calculates statistics from AI search data
func (s *GEOOptimizationService) calculateStatistics(aiSearches []models.AISearch, keywordAnalysis *KeywordAnalysisResult) *models.StatisticsData {
	statistics := &models.StatisticsData{
		TotalSearches:        int64(len(aiSearches)),
		TotalMentions:        0,
		PositiveMentions:     0,
		NegativeMentions:     0,
		NeutralMentions:      0,
		AveragePosition:      1.0,
		TopQuestions:         []string{},
		ResponseDistribution: make(map[string]int64),
		TimeSeriesData:       make(map[string]int64),
	}

	// Calculate mentions and sentiment
	for _, search := range aiSearches {
		statistics.TotalMentions++

		// Simple sentiment analysis based on keywords
		if strings.Contains(search.Question, "好") || strings.Contains(search.Question, "推荐") ||
			strings.Contains(search.Question, "优秀") || strings.Contains(search.Question, "值得") {
			statistics.PositiveMentions++
		} else if strings.Contains(search.Question, "差") || strings.Contains(search.Question, "问题") ||
			strings.Contains(search.Question, "不好") || strings.Contains(search.Question, "缺点") {
			statistics.NegativeMentions++
		} else {
			statistics.NeutralMentions++
		}

		// Collect top questions
		if len(statistics.TopQuestions) < 10 {
			statistics.TopQuestions = append(statistics.TopQuestions, search.Question)
		}

		// Response distribution by question type
		if search.QuestionType != "" {
			statistics.ResponseDistribution[search.QuestionType]++
		}
	}

	return statistics
}

// Performance calculation methods

// calculateVisibilityScore calculates visibility score based on AI search data
func (s *GEOOptimizationService) calculateVisibilityScore(aiSearches []models.AISearch, keywordAnalysis *KeywordAnalysisResult) float64 {
	if len(aiSearches) == 0 {
		return 0.0
	}

	// Base score from search count
	baseScore := float64(len(aiSearches)) * 2.0
	if baseScore > 80 {
		baseScore = 80
	}

	// Bonus from keyword diversity
	keywordBonus := float64(len(keywordAnalysis.Keywords)) * 0.5
	if keywordBonus > 20 {
		keywordBonus = 20
	}

	return baseScore + keywordBonus
}

// calculateMarketPenetration calculates market penetration based on search data
func (s *GEOOptimizationService) calculateMarketPenetration(aiSearches []models.AISearch, keywordAnalysis *KeywordAnalysisResult) float64 {
	if len(aiSearches) == 0 {
		return 0.0
	}

	// Calculate based on search volume and frequency
	totalVolume := int64(0)
	for _, volume := range keywordAnalysis.SearchVolumes {
		totalVolume += volume
	}

	penetration := float64(totalVolume) / 10000.0 * 100
	if penetration > 100 {
		penetration = 100
	}

	return penetration
}

// calculateSearchVolume calculates total search volume
func (s *GEOOptimizationService) calculateSearchVolume(keywordAnalysis *KeywordAnalysisResult) int64 {
	total := int64(0)
	for _, volume := range keywordAnalysis.SearchVolumes {
		total += volume
	}
	return total
}

// calculateMentionFrequency calculates mention frequency
func (s *GEOOptimizationService) calculateMentionFrequency(aiSearches []models.AISearch) int64 {
	return int64(len(aiSearches))
}

// calculateSentimentScore calculates sentiment score
func (s *GEOOptimizationService) calculateSentimentScore(aiSearches []models.AISearch) float64 {
	if len(aiSearches) == 0 {
		return 0.0
	}

	positive := 0
	negative := 0

	for _, search := range aiSearches {
		if strings.Contains(search.Question, "好") || strings.Contains(search.Question, "推荐") ||
			strings.Contains(search.Question, "优秀") || strings.Contains(search.Question, "值得") {
			positive++
		} else if strings.Contains(search.Question, "差") || strings.Contains(search.Question, "问题") ||
			strings.Contains(search.Question, "不好") || strings.Contains(search.Question, "缺点") {
			negative++
		}
	}

	if positive+negative == 0 {
		return 0.0
	}

	return float64(positive-negative) / float64(positive+negative)
}

// determineCompetitionLevel determines competition level
func (s *GEOOptimizationService) determineCompetitionLevel(aiSearches []models.AISearch, keywordAnalysis *KeywordAnalysisResult) string {
	// Simple logic based on search volume and keyword difficulty
	avgDifficulty := 0.0
	count := 0
	for _, difficulty := range keywordAnalysis.KeywordDifficulty {
		avgDifficulty += difficulty
		count++
	}

	if count > 0 {
		avgDifficulty /= float64(count)
	}

	if avgDifficulty > 80 {
		return models.CompetitionLevelVeryHigh
	} else if avgDifficulty > 60 {
		return models.CompetitionLevelHigh
	} else if avgDifficulty > 40 {
		return models.CompetitionLevelMedium
	}

	return models.CompetitionLevelLow
}

// calculateOpportunityScore calculates opportunity score
func (s *GEOOptimizationService) calculateOpportunityScore(geo *models.GEOOptimization) float64 {
	// Weighted calculation based on multiple factors
	visibilityWeight := 0.3
	marketWeight := 0.3
	sentimentWeight := 0.2
	competitionWeight := 0.2

	// Normalize sentiment score
	normalizedSentiment := (geo.SentimentScore + 1) * 5 // Convert [-1,1] to [0,10]

	// Competition level to score
	competitionScore := 10.0
	switch geo.CompetitionLevel {
	case models.CompetitionLevelLow:
		competitionScore = 8.0
	case models.CompetitionLevelMedium:
		competitionScore = 6.0
	case models.CompetitionLevelHigh:
		competitionScore = 4.0
	case models.CompetitionLevelVeryHigh:
		competitionScore = 2.0
	}

	score := (geo.VisibilityScore/10)*visibilityWeight +
		(geo.MarketPenetration/10)*marketWeight +
		normalizedSentiment*sentimentWeight +
		competitionScore*competitionWeight

	return score
}

// getCountryFromRegion maps region to country
func (s *GEOOptimizationService) getCountryFromRegion(region string) string {
	regionMap := map[string]string{
		"北美":  "美国",
		"欧洲":  "德国",
		"亚太":  "中国",
		"南美":  "巴西",
		"非洲":  "南非",
		"中东":  "阿联酋",
		"东南亚": "新加坡",
	}

	if country, exists := regionMap[region]; exists {
		return country
	}
	return "未知"
}

// Utility functions

// removeDuplicates removes duplicate strings from slice
func removeDuplicates(slice []string) []string {
	keys := make(map[string]bool)
	result := []string{}

	for _, item := range slice {
		if !keys[item] && item != "" {
			keys[item] = true
			result = append(result, item)
		}
	}

	return result
}

// filterEmpty removes empty strings from slice
func filterEmpty(slice []string) []string {
	result := []string{}
	for _, item := range slice {
		if strings.TrimSpace(item) != "" {
			result = append(result, item)
		}
	}
	return result
}
