package models

import (
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
)

type User struct {
	ID             uuid.UUID  `gorm:"type:uuid;primary_key"  json:"id"`
	Name           string     `gorm:"type:varchar(255);not null;unique" json:"name"`
	Email          string     `gorm:"uniqueIndex;not null"  json:"email"`
	Password       string     `gorm:"not null" json:"-"`
	Role           string     `gorm:"type:varchar(255);not null" json:"role"`
	SharedProjects []*Project `gorm:"many2many:project_members;" json:"sharedProjects"`

	// 订阅相关字段
	StorageUsedGB            float64 `gorm:"type:decimal(10,2);default:0.0;comment:已使用存储容量（GB）" json:"storage_used_gb"`
	SharedProjectCount       int     `gorm:"default:0;comment:创建的共享项目数量" json:"shared_project_count"`
	CollaboratedProjectCount int     `gorm:"default:0;comment:参与协作的项目数量" json:"collaborated_project_count"`

	// Legacy User Fields
	Provider           string    `gorm:"not null" json:"-"`
	Photo              string    `gorm:"type:varchar(255)" json:"photo"`
	VerificationCode   string    `json:"-"`
	PasswordResetToken string    `json:"-"`
	PasswordResetAt    time.Time `json:"-"`
	Verified           bool      `gorm:"not null" json:"-"`
	Balance            int64     `gorm:"not null" json:"-"`
	CreatedAt          time.Time `gorm:"not null" json:"-"`
	UpdatedAt          time.Time `gorm:"not null" json:"-"`

	// 关联
	UserSubscription *UserSubscription `gorm:"foreignKey:UserID" json:"subscription,omitempty"`
}

type RefreshToken struct {
	ID        uuid.UUID `gorm:"type:uuid;primary_key"`
	UserID    uuid.UUID `gorm:"type:uuid;not null"`
	User      *User     `gorm:"foreignKey:UserID"`
	ExpiredAt time.Time `gorm:"not null"`
}

type TokenClaims struct {
	UserID uuid.UUID `json:"userId"`
	jwt.RegisteredClaims
}

type SafeUser struct {
	Base
	Name string `json:"name"`
}

func (s *SafeUser) TableName() string {
	return "users"
}

type SignUpInput struct {
	Name            string `json:"name" binding:"required"`
	Email           string `json:"email" binding:"required"`
	Password        string `json:"password" binding:"required,min=8"`
	PasswordConfirm string `json:"password_confirm" binding:"required"`
	Photo           string `json:"photo,omitempty"`
}

type CodeInput struct {
	Code string `json:"code" binding:"required"`
}

type SignInInput struct {
	Email    string `json:"email"  binding:"required"`
	Password string `json:"password"  binding:"required"`
}

type UserResponse struct {
	ID        uuid.UUID `json:"id,omitempty"`
	Name      string    `json:"name,omitempty"`
	Email     string    `json:"email,omitempty"`
	Role      string    `json:"role,omitempty"`
	Photo     string    `json:"photo,omitempty"`
	Provider  string    `json:"provider"`
	Verified  bool      `json:"verified"`
	Balance   int64     `json:"balance"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	// 订阅相关字段
	StorageUsedGB            float64           `json:"storage_used_gb"`
	SharedProjectCount       int               `json:"shared_project_count"`
	CollaboratedProjectCount int               `json:"collaborated_project_count"`
	UserSubscription         *UserSubscription `json:"subscription,omitempty"`
}

// ForgotPasswordInput struct
type ForgotPasswordInput struct {
	Email string `json:"email" binding:"required"`
}

// ResetPasswordInput struct
type ResetPasswordInput struct {
	ResetToken      string `json:"resetToken" binding:"required"`
	Password        string `json:"password" binding:"required"`
	PasswordConfirm string `json:"passwordConfirm" binding:"required"`
}

type UpdateBalanceInput struct {
	Email  string `json:"email"  binding:"required"`
	Amount int64  `json:"amount"  binding:"required"`
}

type SubscribeRequest struct {
	RoomId string `json:"room_id"`
}

// 用户订阅相关方法

// UpdateStorageUsage 更新存储使用量
func (u *User) UpdateStorageUsage(sizeGB float64) {
	u.StorageUsedGB += sizeGB
	if u.StorageUsedGB < 0 {
		u.StorageUsedGB = 0
	}
}

// IncrementSharedProjectCount 增加共享项目计数
func (u *User) IncrementSharedProjectCount() {
	u.SharedProjectCount++
}

// DecrementSharedProjectCount 减少共享项目计数
func (u *User) DecrementSharedProjectCount() {
	if u.SharedProjectCount > 0 {
		u.SharedProjectCount--
	}
}

// IncrementCollaboratedProjectCount 增加参与协作项目计数
func (u *User) IncrementCollaboratedProjectCount() {
	u.CollaboratedProjectCount++
}

// DecrementCollaboratedProjectCount 减少参与协作项目计数
func (u *User) DecrementCollaboratedProjectCount() {
	if u.CollaboratedProjectCount > 0 {
		u.CollaboratedProjectCount--
	}
}

// GetCurrentPlan 获取当前订阅计划
func (u *User) GetCurrentPlan() string {
	if u.UserSubscription == nil || !u.UserSubscription.IsActive() {
		return "Free"
	}
	return u.UserSubscription.SubscriptionPlanName
}
