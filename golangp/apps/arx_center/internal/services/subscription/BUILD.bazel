load("@rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "subscription",
    srcs = [
        "payment_service.go",
        "subscription_service.go",
        "user_service.go",
    ],
    importpath = "github.com/Arxtect/CoreXWorkSpace/golangp/apps/arx_center/internal/services/subscription",
    visibility = ["//visibility:public"],
    deps = [
        "//golangp/apps/arx_center/internal/config",
        "//golangp/apps/arx_center/internal/models",
        "//golangp/apps/arx_center/pkg/constants",
        "//golangp/apps/arx_center/pkg/initializers",
        "//golangp/apps/arx_center/pkg/logger",
        "//golangp/common/payment/alipay",
        "//golangp/common/payment/wechat",
        "//golangp/common/redis",
        "@com_github_gin_gonic_gin//:gin",
        "@com_github_google_uuid//:uuid",
        "@io_gorm_gorm//:gorm",
    ],
)

go_test(
    name = "subscription_test",
    srcs = [
        "payment_service_test.go",
        "subscription_service_test.go",
    ],
    embed = [":subscription"],
    deps = [
        "//golangp/apps/arx_center/internal/models",
        "//golangp/apps/arx_center/pkg/constants",
        "//golangp/common/logging",
        "@com_github_google_uuid//:uuid",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//mock",
        "@com_github_stretchr_testify//suite",
        "@io_gorm_driver_sqlite//:sqlite",
        "@io_gorm_gorm//:gorm",
    ],
)
