load("@rules_go//go:def.bzl", "go_library")

go_library(
    name = "controllers",
    srcs = [
        "auth.controller.go",
        "chat.controller.go",
        "conversations.controller.go",
        "creditSystem.go",
        "payment_controller.go",
        "subscription_controller.go",
        "template.controller.go",
        "template_conversion.controller.go",
        "user.controller.go",
    ],
    importpath = "github.com/Arxtect/CoreXWorkSpace/golangp/apps/arx_center/api/controllers",
    visibility = ["//visibility:public"],
    deps = [
        "//golangp/apps/arx_center/internal/config",
        "//golangp/apps/arx_center/internal/models",
        "//golangp/apps/arx_center/internal/services/subscription",
        "//golangp/apps/arx_center/pkg/constants",
        "//golangp/apps/arx_center/pkg/dify",
        "//golangp/apps/arx_center/pkg/gitea",
        "//golangp/apps/arx_center/pkg/logger",
        "//golangp/apps/arx_center/pkg/openai-config",
        "//golangp/apps/arx_center/pkg/template_tools",
        "//golangp/apps/arx_center/pkg/utils",
        "//golangp/apps/arx_center/pkg/xminio",
        "//golangp/apps/arx_center/pkg/ziputils",
        "//golangp/apps/geok_center/pkg/response",
        "//golangp/common/payment/alipay",
        "//golangp/common/payment/wechat",
        "//golangp/common/redis",
        "@com_github_gin_gonic_gin//:gin",
        "@com_github_golang_jwt_jwt_v5//:jwt",
        "@com_github_google_uuid//:uuid",
        "@com_github_minio_minio_go_v7//:minio-go",
        "@com_github_pkoukk_tiktoken_go//:tiktoken-go",
        "@com_github_sashabaranov_go_openai//:go-openai",
        "@com_github_thanhpk_randstr//:randstr",
        "@io_gorm_gorm//:gorm",
        "@org_golang_x_net//proxy",
    ],
)
