package project_controller

import (
	"fmt"
	"net/http"
	"time"

	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/arx_center/internal/models"
	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/arx_center/internal/services/oss"
	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/arx_center/pkg/constants"
	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/arx_center/pkg/logger"
	"github.com/Arxtect/CoreXWorkSpace/golangp/apps/arx_center/pkg/xminio"
	"github.com/minio/minio-go/v7"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type ProjectController struct {
	DB  *gorm.DB
	S3  *xminio.S3Manager
	OSS *oss.OssService
}

func NewProjectController(DB *gorm.DB, S3 *xminio.S3Manager, OSS *oss.OssService) ProjectController {
	return ProjectController{
		DB,
		S3,
		OSS,
	}
}

func (controller *ProjectController) getOwnedProjects(userId uuid.UUID) ([]models.Project, error) {
	var projects []models.Project
	if err := controller.DB.Preload("Owner").Where("owner_id = ?", userId).Find(&projects).Error; err != nil {
		return nil, err
	}

	return projects, nil
}

func (controller *ProjectController) getDeletedProjects(userId uuid.UUID) ([]models.Project, error) {
	var projects []models.Project

	// Calculate the time 30 days ago
	thirtyDaysAgo := time.Now().AddDate(0, 0, -30)

	// Perform query: include soft-deleted records, filter by owner_id and deleted_at within 30 days
	err := controller.DB.
		Unscoped().
		Preload("Owner").
		Where("owner_id = ? AND deleted_at IS NOT NULL AND deleted_at >= ?", userId, thirtyDaysAgo).
		Find(&projects).Error

	return projects, err
}

func (controller *ProjectController) getSharedProjects(userId uuid.UUID) ([]models.Project, error) {
	var projects []models.Project
	err := controller.DB.Preload("Owner").Joins("JOIN project_members ON projects.id = project_members.project_id").
		Where("project_members.user_id = ?", userId).
		Find(&projects).Error
	return projects, err
}

type ProjectResponse struct {
	models.Project
	PreviewURL string `json:"previewUrl,omitempty"`
}

func (controller *ProjectController) GetProjects(c *gin.Context) {
	currentUser := c.MustGet("currentUser").(models.User)
	relation := c.Query("relation") // Get relation query parameter

	// Validate relation parameter
	if relation != "all" && relation != "owner" && relation != "member" && relation != "deleted" {
		logger.Warning("Invalid relation parameter: %s", relation)
		c.JSON(http.StatusBadRequest, gin.H{"message": "Invalid relation parameter. Must be 'all', 'owner', 'member' or 'deleted"})
		return
	}

	projects := make([]models.Project, 0)
	if relation == "all" || relation == "owner" {
		ownedProjects, err := controller.getOwnedProjects(currentUser.ID)
		if err != nil {
			logger.Warning("Failed to retrieve owned projects: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"message": "Failed to retrieve owned projects"})
			return
		}
		projects = append(projects, ownedProjects...)
	}

	if relation == "all" || relation == "member" {
		sharedProjects, err := controller.getSharedProjects(currentUser.ID)
		if err != nil {
			logger.Warning("Failed to retrieve shared projects: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"message": "Failed to retrieve shared projects"})
			return
		}
		projects = append(projects, sharedProjects...)
	}

	if relation == "deleted" {
		deletedProjects, err := controller.getDeletedProjects(currentUser.ID)
		if err != nil {
			logger.Warning("Failed to retrieve deleted projects: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"message": "Failed to retrieve deleted projects"})
			return
		}
		projects = append(projects, deletedProjects...)
	}

	// Convert projects to response with presigned URLs
	response := make([]ProjectResponse, len(projects))
	for i, project := range projects {
		response[i] = ProjectResponse{
			Project: project,
		}
	}

	c.JSON(http.StatusOK, response)
}

// GetProjectViaID retrieves a project by its ID
func (controller *ProjectController) GetProjectViaID(c *gin.Context) {
	idParam := c.Param("id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		logger.Warning("Invalid project ID: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"message": "Invalid project ID"})
		return
	}

	var project models.Project
	if err := controller.DB.Where("id = ?", id).Preload("Owner").First(&project).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			logger.Warning("Project not found: %v", err)
			c.JSON(http.StatusNotFound, gin.H{"message": "Project not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"message": "Failed to retrieve project: " + err.Error()})
		return
	}
	if err != nil {
		logger.Warning("Project not found: %v", err)
		c.JSON(http.StatusNotFound, gin.H{"message": "Project not found"})
		return
	}

	c.JSON(http.StatusOK, project)
}

// CreateProject creates a new project
func (controller *ProjectController) CreateProject(c *gin.Context) {
	currentUser := c.MustGet("currentUser").(models.User)
	createPayload := models.CreateProjectPayload{}

	if err := c.ShouldBindJSON(&createPayload); err != nil {
		logger.Warning("Invalid input: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"message": "Invalid input"})
		return
	}

	projectManager := models.Project{
		ID:          uuid.New(),
		OwnerID:     currentUser.ID,
		ProjectName: createPayload.ProjectName,
	}

	if err := projectManager.CreateProject(controller.DB); err != nil {
		logger.Warning("Failed to create project: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"message": "Failed to create project"})
		return
	}

	c.JSON(http.StatusCreated, projectManager)
}

// UpdateProject updates an existing project
func (controller *ProjectController) UpdateProject(c *gin.Context) {
	project, err := controller.validateProjectId(c, c.Param("id"), 3)
	if err != nil {
		return
	}

	var updatePayload models.UpdateProjectPayload
	if err := c.ShouldBindJSON(&updatePayload); err != nil {
		logger.Warning("Invalid input: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"message": "Invalid input"})
		return
	}

	if updatePayload.ProjectName != nil && *updatePayload.ProjectName != "" {
		project.ProjectName = *updatePayload.ProjectName
	}

	if updatePayload.Published != nil {
		project.Published = *updatePayload.Published
	}

	if err := project.UpdateProject(controller.DB); err != nil {
		logger.Warning("Failed to update project: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"message": "Failed to update project"})
		return
	}

	c.JSON(http.StatusOK, project)
}

// DeleteProject deletes a project by its ID
func (controller *ProjectController) DeleteProject(c *gin.Context) {
	project, err := controller.validateProjectId(c, c.Param("id"), 3)
	if err != nil {
		return
	}

	if err := controller.DB.Delete(&project).Error; err != nil {
		logger.Warning("Failed to delete project: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"message": "Failed to delete project"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Project deleted successfully"})
}

/*
permission:
0: no relation
1: viewer
2: collaborator
3: owner
*/
func (controller *ProjectController) validateProjectId(c *gin.Context, idString string, requirePermission int) (*models.Project, error) {
	id, err := uuid.Parse(idString)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"message": "Invalid project ID"})
		return nil, err
	}

	var project models.Project
	if err := controller.DB.First(&project, "id = ?", id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"message": "Project not found"})
		return nil, err
	}

	if requirePermission == 0 {
		return &project, nil
	}

	currentUser := c.MustGet("currentUser").(models.User)
	currentPermission := 0

	if project.OwnerID == currentUser.ID {
		currentPermission = 3
	} else {
		var member models.ProjectMember
		if err := controller.DB.Where("user_id = ? AND project_id = ?", currentUser.ID, project.ID).First(&member).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				currentPermission = 0
			} else {
				// Unknown error
				c.JSON(http.StatusInternalServerError, gin.H{"message": "Unknown error"})
				return nil, err
			}
		} else {
			if member.Role == constants.ProjectRoleCollaborator {
				currentPermission = 2
			} else if member.Role == constants.ProjectRoleViewer {
				currentPermission = 1
			}
		}
	}

	if currentPermission < requirePermission {
		c.JSON(http.StatusForbidden, gin.H{"message": "You do not have permission to access this project"})
		return nil, fmt.Errorf("permission denied")
	}

	return &project, nil
}

func (controller *ProjectController) GetPreview(c *gin.Context) {
	project, err := controller.validateProjectId(c, c.Param("id"), 0)
	if err != nil {
		return
	}
	// Generate presigned URL for preview if it exists
	previewPath := PROJECT_PREVIEW_PATH + project.ID.String()
	_, err = controller.S3.Client.StatObject(c, controller.S3.BucketName, previewPath, minio.StatObjectOptions{})
	if err == nil {
		// Object exists, generate presigned URL
		presignedURL, err := controller.S3.Client.PresignedGetObject(c, controller.S3.BucketName, previewPath, time.Hour*24, nil)
		if err == nil {
			// Redirect to the presigned URL
			c.Redirect(http.StatusFound, presignedURL.String())
			return
		} else {
			logger.Warning("Failed to generate presigned URL: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"message": "Failed to generate preview URL"})
			return
		}
	}

	if minio.ToErrorResponse(err).Code == "NoSuchKey" {
		c.JSON(http.StatusNotFound, gin.H{"message": "Preview not found"})
		return
	} else {
		logger.Warning("Failed to check preview existence: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"message": "Failed to check preview existence"})
		return
	}

}

func (controller *ProjectController) UploadPreview(c *gin.Context) {
	project, err := controller.validateProjectId(c, c.Param("id"), 2)
	if err != nil {
		return
	}
	file, header, err := c.Request.FormFile("image")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"message": "File is required"})
		return
	}
	defer file.Close()
	putOpts := minio.PutObjectOptions{
		ContentType: header.Header.Get("Content-Type"),
	}

	_, err = controller.S3.Client.PutObject(
		c,
		controller.S3.BucketName,
		PROJECT_PREVIEW_PATH+project.ID.String(),
		file,
		header.Size,
		putOpts,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"message": "Failed to upload preview"})
		return
	}
	c.JSON(http.StatusOK, gin.H{"message": "Preview uploaded successfully"})
}

func (controller *ProjectController) GetPublishedProjects(c *gin.Context) {
	var projects []models.Project
	if err := controller.DB.Where("published = ?", true).Preload("Owner").Find(&projects).Error; err != nil {
		logger.Warning("Failed to retrieve published projects: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"message": "Failed to retrieve published projects"})
		return
	}

	response := make([]ProjectResponse, len(projects))
	for i, project := range projects {
		response[i] = ProjectResponse{
			Project: project,
		}
	}

	c.JSON(http.StatusOK, response)
}
