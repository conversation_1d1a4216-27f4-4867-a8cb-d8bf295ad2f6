#!/usr/bin/env python3
"""
获取Educative.io课程列表
输出为JSON文件，包含所有url_slug
"""

import json
import requests
import sys
import os
from datetime import datetime

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 在这里设置你的Educative.io cookie
COOKIE = "_ga=GA1.1.261593851.1752502181; FPID=FPID2.2.ETFtoefQhgmivFHYHiH01%2BSO6PZwkMmywypBqjd%2FXDg%3D.1752502181; _fbp=fb.1.1752502182115.435372465284676372; usprivacy=1---; OneTrustWPCCPAGoogleOptOut=false; _gcl_au=1.1.1237489861.1752502298; __stripe_mid=98cdeed9-9ff1-49ba-a584-162789c41f27d0a227; cf_clearance=_pCx0qwEd37Qt8B3UoYKTxY0ZsqBwvzzwtf95uPeN8A-1752544844-*******-84f.YifGcQPdy5kWsP5lxff9iftNuKg.KVjiklca64sMlKH_Hkv7sKg1OtZkcufNf1rMFudEJq_XGuJtxSRrxn2fqDz1x9W7ku46kRBo40IREMz14Qv7wpwnZNkhCgxe_WScIzvf9JoXSjpRAQ3NM7gQ6SYqPzX_F9Eeyb5yRh_39WFPEqftMA5.X2o2.y1edsdUdwY6x1r1ZG_Qk7jtM5GyTR3XZk1X38dXlSXjXpk; hubspotutk=b7faf9db32623687136f0be3ba426504; logged_in=; show_sd_pal_transition_modal=false; _uetvid=2f6c6f8060bc11f0baffa32a157014ef; theme=light; use_system_preference=system_preference; _clck=1o81x5v%7C2%7Cfym%7C0%7C2021; FPLC=4zJd%2BkAVVYXCMjwuo%2BlNFFl8HHeU8Q5N2v%2BTh6X7KMxKwLBsQKh3O6nLA7CJlRnbG%2Byq7G6PYK3ZigkWAgqUoAAXjAf6Wao%2Bus61bltL1E7bLhREbkvun8gQDzSw%2BA%3D%3D; enterprise_nav=false; enterprise_new_sidebar=true; trial_availed=false; subscribed=false; l2c_subscribed=false; font-family-body-lesson-markdown=Droid Serif; font-family-heading-lesson-markdown=Nunito Sans; font-size-lesson-markdown=18px; line-height-lesson-markdown=175%; content-width-lesson-markdown=1024px; __hssrc=1; flask-auth=.eJyLNjMzMTUwNDUyNjO0MDIwNNMx0FEyywowMfBKTzQ0LvOOcMrMD4pKd0oNUgJKASWTqyqzc1Iz8xzyS0ty8vOz9ZLzc5VwCZcUlabqpCXmFKfq5JXm5OgYQyhDc1NTMwsDM2ODWACvASgp; __hstc=10449898.b7faf9db32623687136f0be3ba426504.1752735489920.1755669423848.1755680643433.65; visited-explore-page=false; recommendations=true; _rdt_uuid=1752502180915.4606c1a1-dbef-408e-a1da-7bc243879383; __hssc=10449898.6.1755680643433; _uetsid=ac86e3507b2e11f0b43e31d6be602a21; OptanonConsent=isGpcEnabled=0&datestamp=Wed+Aug+20+2025+17%3A12%3A48+GMT%2B0800+(%E4%B8%AD%E5%9B%BD%E6%A0%87%E5%87%86%E6%97%B6%E9%97%B4)&version=202505.1.0&browserGpcFlag=0&isIABGlobal=false&hosts=&consentId=36735b12-233a-4da5-abf6-30e3636d4a7b&interactionCount=1&isAnonUser=1&landingPath=NotLandingPage&groups=C0001%3A1%2CC0002%3A1%2CC0003%3A1%2CC0004%3A1&AwaitingReconsent=false&geolocation=JP%3B27; OptanonAlertBoxClosed=2025-08-20T09:12:48.689Z; __cf_bm=IwuxOjZ5Fr1Si4DjQJ4GgSkrYRaqM6cTB7UUL4dcm08-1755681564-*******-mxJvOQFwB_FKkP.X56NgRSIyT4Y1Vr3JE.4JQGqF1RkU60aZDU_EQpaA40DwYBkw9BAEhW2h6RPPPUtMn0AxzF6v6rRfFw_AG.Hm3RzLHk4; FPGSID=1.1755680630.1755681565.G-MWGSGCW5SP.ys6LtsrV4HQLxidZkEZF8Q; magicbox-auth=eyJ1c2VyX2lkIjogNjY0NTAxNTIzNjE4MjAxNiwgInRva2VuIjogIlcxR01wN01hN0lnTlZidzFmeWo1WVYiLCAidG9rZW5fdHMiOiAxNzU1NjgxNTg0MDAwLCAibG91X3Nlc3Npb24iOiBmYWxzZX0=|ac1921911c97e3ca3cea326d5eba15115b96f9df; flask-session=.eJx1jzsLwjAUhf_LnYMkbRNLJ3HURTqJIiG011KbR8lDUPG_G6yr04HvwHm8QM7ojbJoIzTRJyQgU0APzVmIilPGi1KwuqBMEEpA3A4V3Q2Klff9cTu69jRssYVsZbN7PiaNo924FLVz06pzBv7hb9dV6YDEJq1JuQhbcy5qKkp6IaAChmDyNGkwql5FJcceGsihYRxsmmVwyXf4Q8HJFM1y4_0B4bJHxQ.aKWTMA.TdjYdAIlIfxCu72TAfnsucOPbZc; _ga_MWGSGCW5SP=GS2.1.s1755680626$o92$g1$t1755681580$j40$l0$h1325278617; _clsk=12nlh0f%5E1755681581277%5E24%5E1%5Ez.clarity.ms%2Fcollect"

def fetch_page_data(page=0):
    """
    获取指定页面的数据

    Args:
        page: 页码

    Returns:
        API响应数据或None
    """
    url = "https://www.educative.io/api/search/collection-search"

    # 使用POST请求体而不是URL参数
    data = {
        'filters': '{"collection_type":["collection"]}',
        'page': str(page),
        'query': '',
        'range_filters': '{}'
    }

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Content-Type': 'application/x-www-form-urlencoded',
        'Connection': 'keep-alive',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'Referer': 'https://www.educative.io/',
        'Origin': 'https://www.educative.io',
        'X-Requested-With': 'XMLHttpRequest',
    }

    # 添加cookie
    if COOKIE:
        headers['Cookie'] = COOKIE

    try:
        # 使用data参数发送POST请求体
        response = requests.post(url, data=data, headers=headers, timeout=30)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"页面 {page} 请求失败: {e}")
        if hasattr(e, 'response') and e.response is not None:
            print(f"错误状态码: {e.response.status_code}")
            print(f"错误响应内容: {e.response.text[:500]}...")
        return None
    except Exception as e:
        print(f"页面 {page} 其他错误: {e}")
        return None

def get_all_courses(pages=10):
    """
    获取所有课程数据
    
    Returns:
        包含所有课程信息的列表
    """
    all_courses = []
    page = 0
    consecutive_empty_pages = 0
    
    print("开始获取Educative.io课程列表...")
    print("-" * 50)
    
    while page <= pages:  # 最多测试100页
        print(f"获取页面 {page:3d}...", end=" ")
        
        data = fetch_page_data(page)
        
        if data and 'hits' in data and 'hits' in data['hits']:
            results = data['hits']['hits']
            results_count = len(results)

            print(f"✓ 成功 ({results_count:2d} 个结果)")

            if results_count > 0:
                # 提取每个课程的信息
                for hit in results:
                    course = hit.get('_source', {})
                    course_info = {
                        'title': course.get('title', ''),
                        'url_slug': course.get('url_slug', ''),
                        'id': course.get('id', ''),
                        'collection_id': course.get('collection_id', ''),
                        'author_id': course.get('author_id', ''),
                        'type': course.get('type', ''),
                        'brief_summary': course.get('brief_summary', ''),
                        'cover_image_url': course.get('cover_image_url', ''),
                        'creation_time': course.get('creation_time', ''),
                        'published_time': course.get('published_time', ''),
                        'read_time': course.get('read_time', 0),
                        'price': course.get('price', 0),
                        'tags': course.get('tags', []),
                        'skills': course.get('skills', []),
                        'level_one_learner_tags': course.get('level_one_learner_tags', []),
                        'page_source': page  # 记录来源页面
                    }
                    all_courses.append(course_info)
                
                consecutive_empty_pages = 0
            else:
                consecutive_empty_pages += 1
                
            # 检查是否还有更多数据
            total_hits = data.get('hits', {}).get('total', {}).get('value', 0)
            current_results = (page + 1) * 10  # 假设每页10个结果
            has_more = current_results < total_hits

            if not has_more or consecutive_empty_pages >= 5:
                print(f"\n已到达数据末尾 (总数: {total_hits}, 已获取: {current_results}, 连续空页: {consecutive_empty_pages})")
                break
                
        else:
            print("✗ 失败")
            consecutive_empty_pages += 1
            
            if consecutive_empty_pages >= 5:
                print(f"\n连续5个页面失败，停止获取")
                break
        
        page += 1
    
    return all_courses

def save_course_list(courses):
    """
    保存课程列表到JSON文件
    
    Args:
        courses: 课程列表
    """
    # 统计信息
    total_courses = len(courses)
    unique_url_slugs = list(set(course['url_slug'] for course in courses if course['url_slug']))
    
    print(f"\n" + "=" * 50)
    print("课程列表统计:")
    print(f"总课程数: {total_courses}")
    print(f"唯一URL_SLUG数: {len(unique_url_slugs)}")
    
    # 创建输出数据
    output_data = {
        'timestamp': datetime.now().isoformat(),
        'summary': {
            'total_courses': total_courses,
            'unique_url_slugs': len(unique_url_slugs),
            'date_range_filter': '2025-07-20T09:14:05.856Z',
            'collection_type': 'collection'
        },
        'url_slugs': unique_url_slugs,
        'courses': courses
    }
    
    # 保存完整数据
    filename = f"educative_courses_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, ensure_ascii=False, indent=2)
        print(f"\n完整课程数据已保存到: {filename}")
    except Exception as e:
        print(f"保存完整数据失败: {e}")
    
    # 保存纯URL_SLUG列表
    url_slugs_filename = f"course_url_slugs_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    try:
        with open(url_slugs_filename, 'w', encoding='utf-8') as f:
            json.dump(unique_url_slugs, f, ensure_ascii=False, indent=2)
        print(f"URL_SLUG列表已保存到: {url_slugs_filename}")
    except Exception as e:
        print(f"保存URL_SLUG列表失败: {e}")
    
    # 显示前10个URL_SLUG作为示例
    print(f"\n前10个URL_SLUG示例:")
    for i, slug in enumerate(unique_url_slugs[:10]):
        print(f"  {i+1:2d}. {slug}")
    
    if len(unique_url_slugs) > 10:
        print(f"  ... 还有 {len(unique_url_slugs) - 10} 个")
    
    return filename, url_slugs_filename

def main():
    """主函数"""
    # 获取所有课程
    courses = get_all_courses(83)  # 获取更多页面
    
    if courses:
        # 保存结果
        full_file, slugs_file = save_course_list(courses)
        
        print(f"\n" + "=" * 50)
        print("获取完成！")
        print(f"完整数据文件: {full_file}")
        print(f"URL_SLUG列表文件: {slugs_file}")
    else:
        print("\n未获取到任何课程数据")

if __name__ == "__main__":
    main()
