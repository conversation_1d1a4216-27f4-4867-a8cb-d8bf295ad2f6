#!/usr/bin/env python3
"""
Educative.io Course Data Fetcher
获取Educative.io课程信息并输出为JSON格式
"""

import json
import requests
import sys
import os
import tempfile
from typing import List, Dict, Any, Optional
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.table import Table
from rich import print as rprint

console = Console()

# 图片上传配置
IMAGE_CONFIG = {
    'enabled': True,  # 启用图片上传
    'server_ip': '127.0.0.1:8012',
    'email': '<EMAIL>',
    'password': '123123123',
    'timeout': 300,
    'retry_count': 3,
    'retry_delay': 1,
}

# 全局token和cookie存储
_auth_token = None
_educative_cookie = None


def set_educative_cookie(cookie: str) -> None:
    """设置全局的Educative.io cookie"""
    global _educative_cookie
    _educative_cookie = cookie


def login_to_server() -> bool:
    """登录到服务器获取认证token"""
    global _auth_token

    if _auth_token:
        return True  # 已经有token了

    login_url = f"http://{IMAGE_CONFIG['server_ip']}/api/v1/auth/login"
    login_data = {
        "email": IMAGE_CONFIG['email'],
        "password": IMAGE_CONFIG['password']
    }

    try:
        console.print(f"[yellow]🔐 登录到服务器: {IMAGE_CONFIG['server_ip']}[/yellow]")
        response = requests.post(login_url, json=login_data, timeout=IMAGE_CONFIG['timeout'])
        response.raise_for_status()

        data = response.json()
        _auth_token = data.get('data', {}).get("access_token")

        if not _auth_token:
            console.print("[red]❌ 登录失败：无法获取访问令牌[/red]")
            return False

        console.print("[green]✅ 登录成功，获取到访问令牌[/green]")
        return True

    except requests.exceptions.RequestException as e:
        console.print(f"[red]❌ 登录失败: {str(e)}[/red]")
        return False


def upload_image_to_server(image_url: str, cookie: str = None, course_slug: str = None) -> Optional[str]:
    """
    下载图片并上传到服务器，同时保存到原始文件夹，返回新的URL

    Args:
        image_url: 原始图片URL
        cookie: 用于下载图片的cookie字符串（可选，如果不提供则使用全局cookie）
        course_slug: 课程slug，用于保存原始图片到对应文件夹

    Returns:
        上传成功后的新URL，失败返回None
    """
    import os
    import tempfile
    global _educative_cookie

    # 检查是否启用图片上传功能
    if not IMAGE_CONFIG['enabled']:
        console.print(f"[yellow]⚠️  图片上传功能已禁用，使用原始URL[/yellow]")
        return None

    # 先登录获取token
    if not login_to_server():
        console.print("[red]❌ 无法登录，跳过图片上传[/red]")
        return None

    try:
        # 下载图片 - 使用cookie进行认证
        console.print(f"[yellow]📥 下载图片: {image_url}[/yellow]")

        # 设置请求头，包含cookie
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Sec-Fetch-Dest': 'image',
            'Sec-Fetch-Mode': 'no-cors',
            'Sec-Fetch-Site': 'same-origin',
        }

        # 使用提供的cookie或全局cookie
        use_cookie = cookie or _educative_cookie
        if use_cookie:
            headers['Cookie'] = use_cookie
            console.print(f"[cyan]🍪 使用cookie进行认证下载[/cyan]")
        else:
            console.print(f"[yellow]⚠️  没有cookie，尝试无认证下载[/yellow]")

        response = requests.get(image_url, headers=headers, timeout=IMAGE_CONFIG['timeout'])
        response.raise_for_status()

        # 获取文件扩展名 - 先检查内容，再检查Content-Type
        file_extension = '.jpg'  # 默认扩展名

        # 检查文件内容来确定真实格式
        content_start = response.content[:20]
        if content_start.startswith(b'\x89PNG'):
            file_extension = '.png'
        elif content_start.startswith(b'\xff\xd8\xff'):
            file_extension = '.jpg'
        elif content_start.startswith(b'GIF8'):
            file_extension = '.gif'
        elif content_start.startswith(b'<svg') or content_start.startswith(b'<?xml'):
            file_extension = '.svg'
        elif content_start.startswith(b'RIFF') and b'WEBP' in response.content[:50]:
            file_extension = '.webp'
        else:
            # 如果内容检测失败，回退到Content-Type检测
            if 'content-type' in response.headers:
                content_type = response.headers['content-type'].lower()
                if 'svg' in content_type:
                    file_extension = '.svg'
                elif 'png' in content_type:
                    file_extension = '.png'
                elif 'gif' in content_type:
                    file_extension = '.gif'
                elif 'webp' in content_type:
                    file_extension = '.webp'
                elif 'jpeg' in content_type or 'jpg' in content_type:
                    file_extension = '.jpg'

        console.print(f"[cyan]🔍 检测到文件格式: {file_extension}[/cyan]")

        # 保存到原始文件夹（如果提供了course_slug）
        original_filepath = None
        console.print(f"[cyan]🔍 course_slug参数: {course_slug}[/cyan]")
        if course_slug:
            try:
                from urllib.parse import urlparse

                # 创建原始文件夹路径
                origin_course_dir = os.path.join("origin_course", course_slug)
                os.makedirs(origin_course_dir, exist_ok=True)

                # 生成原始文件名（直接使用URL，替换非法字符）
                import re
                # 移除协议部分，保留域名和路径
                url_without_protocol = image_url.replace('https://', '').replace('http://', '')
                # 替换非法字符为下划线
                original_filename = re.sub(r'[<>:"/\\|?*]', '_', url_without_protocol) + file_extension

                # 设置原始文件路径
                original_filepath = os.path.join(origin_course_dir, original_filename)

                # 保存原始图片
                with open(original_filepath, 'wb') as f:
                    f.write(response.content)
                console.print(f"[green]✓ 保存原始图片: {original_filename}[/green]")
            except Exception as e:
                console.print(f"[yellow]⚠️  保存原始图片失败: {str(e)}[/yellow]")
                original_filepath = None  # 确保失败时重置为None

        # 如果有原始文件路径，使用它；否则创建临时文件
        if original_filepath and os.path.exists(original_filepath):
            temp_file_path = original_filepath
        else:
            # 创建临时文件作为备选
            with tempfile.NamedTemporaryFile(delete=False, suffix=file_extension) as temp_file:
                temp_file.write(response.content)
                temp_file_path = temp_file.name

        try:
            # 上传到服务器
            console.print(f"[yellow]📤 上传图片到服务器...[/yellow]")
            upload_url = f"http://{IMAGE_CONFIG['server_ip']}/api/v1/upload/file"

            with open(temp_file_path, 'rb') as f:
                # 设置正确的MIME类型
                mime_type = 'image/jpeg'  # 默认
                if file_extension == '.png':
                    mime_type = 'image/png'
                elif file_extension == '.gif':
                    mime_type = 'image/gif'
                elif file_extension == '.svg':
                    mime_type = 'image/svg+xml'
                elif file_extension == '.webp':
                    mime_type = 'image/webp'

                files = {'file': (f'image{file_extension}', f, mime_type)}
                headers = {
                    'Authorization': f'Bearer {_auth_token}',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }

                upload_response = requests.post(
                    upload_url,
                    files=files,
                    headers=headers,
                    timeout=IMAGE_CONFIG['timeout']
                )

                upload_response.raise_for_status()

                # 解析响应
                result = upload_response.json()
                if result.get('success') and result.get('data', {}).get('file_url'):
                    new_url = result['data']['file_url']
                    console.print(f"[green]✅ 图片上传成功: {new_url}[/green]")
                    return new_url
                else:
                    console.print(f"[red]❌ 上传失败: {result.get('message', '未知错误')}[/red]")
                    return None

        finally:
            # 清理临时文件（但保留原始文件）
            if os.path.exists(temp_file_path):
                # 只有当temp_file_path不是原始文件路径时才删除
                if not (original_filepath and temp_file_path == original_filepath):
                    os.unlink(temp_file_path)

    except requests.RequestException as e:
        console.print(f"[red]❌ 网络请求失败: {str(e)}[/red]")
        return None
    except Exception as e:
        console.print(f"[red]❌ 图片上传失败: {str(e)}[/red]")
        return None


def generate_image_markdown(content: Dict[str, Any], component_type: str, index: int, nested_components: list = None, course_slug: str = None) -> str:
    """
    根据图片组件内容生成Markdown格式的图片标记

    Args:
        content: 组件内容
        component_type: 组件类型
        index: 组件索引
        nested_components: 嵌套组件列表

    Returns:
        生成的Markdown字符串
    """
    # 特殊处理 Columns 布局组件
    if component_type == 'Columns' and nested_components:
        return generate_columns_layout(nested_components, content, course_slug)

    # 尝试提取图片URL
    image_url = None
    url_fields = ['url', 'src', 'image_url', 'path', 'href']

    for field in url_fields:
        if field in content and content[field]:
            image_url = content[field]
            break

    # 尝试提取图片描述信息
    alt_text = content.get('alt', content.get('title', content.get('caption', f'{component_type} {index + 1}')))

    # 如果有URL，生成标准的Markdown图片语法
    if image_url:
        # 确保URL是完整的
        if image_url.startswith('/'):
            image_url = f"https://www.educative.io{image_url}"
        elif not image_url.startswith('http'):
            image_url = f"https://www.educative.io/{image_url}"

        # 上传图片到服务器并获取新URL
        uploaded_url = upload_image_to_server(image_url, course_slug=course_slug)
        final_url = uploaded_url if uploaded_url else image_url  # 如果上传失败，使用原URL

        return f"![{alt_text}]({final_url})"

    # 如果是特殊的图表组件，生成特殊标记
    if component_type in ['MxGraphWidget', 'CodeDrawing', 'CanvasAnimation']:
        return f"\n**[{component_type} 图表]** - 交互式图表内容\n"

    # 如果是代码相关的组件，检查是否包含实际代码内容
    if component_type in ['Code', 'CodeWidget']:
        # 检查是否包含代码内容
        code_content = content.get('code', content.get('content', ''))
        if code_content and isinstance(code_content, str):
            # 如果有实际的代码内容，直接返回代码块
            language = content.get('language', content.get('lang', ''))
            if language:
                return f"\n```{language}\n{code_content}\n```\n"
            else:
                return f"\n```\n{code_content}\n```\n"
        elif 'image' in str(content).lower():
            # 只有在没有代码内容但有图片时才标记为代码截图
            return f"\n**[代码截图]** - {alt_text}\n"

    return ""


def replace_images_in_xml(xml_content: str, course_slug: str = None) -> str:
    """
    替换XML中的Educative.io图片URL为上传后的URL

    Args:
        xml_content: 原始XML内容
        course_slug: 课程slug，用于保存原始图片

    Returns:
        替换后的XML内容
    """
    import re

    # 查找所有的image URL
    image_pattern = r'image=(https://www\.educative\.io/api/collection/[^;]+)'

    def replace_url(match):
        original_url = match.group(1)
        console.print(f"[cyan]🔄 处理XML中的图片URL: {original_url}[/cyan]")

        # 上传图片并获取新URL
        uploaded_url = upload_image_to_server(original_url, course_slug=course_slug)
        final_url = uploaded_url if uploaded_url else original_url

        return f'image={final_url}'

    # 替换所有匹配的URL
    updated_xml = re.sub(image_pattern, replace_url, xml_content)
    return updated_xml


def extract_image_dimensions_from_xml(xml_content: str) -> tuple:
    """
    从MxGraphWidget的XML中提取图片尺寸

    Args:
        xml_content: XML字符串

    Returns:
        (width, height) 元组
    """
    import re

    # 查找width和height属性
    width_match = re.search(r'width="(\d+(?:\.\d+)?)"', xml_content)
    height_match = re.search(r'height="(\d+(?:\.\d+)?)"', xml_content)

    width = width_match.group(1) if width_match else None
    height = height_match.group(1) if height_match else None

    return width, height


def generate_columns_layout(nested_components: list, content: Dict[str, Any], course_slug: str = None) -> str:
    """
    生成分栏布局的Markdown - 优化版本

    Args:
        nested_components: 嵌套组件列表
        content: 组件内容

    Returns:
        分栏布局的Markdown字符串
    """
    if not nested_components:
        return ""

    # 如果只有一个组件，直接渲染内容
    if len(nested_components) == 1:
        comp = nested_components[0]
        comp_type = comp.get('type', '')
        comp_content = comp.get('content', {})

        if comp_type == 'MarkdownEditor':
            if 'text' in comp_content:
                return f"\n{comp_content['text']}\n"
            elif 'mdHtml' in comp_content:
                import re
                clean_text = re.sub(r'<[^>]+>', '', comp_content['mdHtml'])
                return f"\n{clean_text}\n"
        elif comp_type in ['MxGraphWidget', 'Image', 'ImageWidget']:
            img_markdown = generate_enhanced_image_markdown(comp_content, comp_type, 0, course_slug)
            return f"\n{img_markdown}\n" if img_markdown else ""
        else:
            return f"\n**[{comp_type}]**\n"

    # 生成简化的flexbox分栏布局
    markdown_parts = []
    markdown_parts.append('\n<div class="flex justify-center sm:flex-row sm:justify-start -mt-4 flex-col" style="gap: 20px;">\n')

    for i, comp in enumerate(nested_components):
        comp_type = comp.get('type', '')
        comp_content = comp.get('content', {})

        # 获取实际的width值，如果没有则不设置width
        comp_width = comp.get('width', '')
        if comp_width:
            width_style = f"width: {comp_width}%;"
        else:
            width_style = ""

        # 简化的div结构，不需要嵌套
        markdown_parts.append(f"<div style='flex: 1; {width_style}'>\n")

        # 处理不同类型的嵌套组件
        if comp_type == 'MarkdownEditor':
            if 'text' in comp_content:
                # 直接添加文本内容，不需要额外的p标签包装
                markdown_parts.append(comp_content['text'])
            elif 'mdHtml' in comp_content:
                # 简单的HTML到Markdown转换
                import re
                clean_text = re.sub(r'<[^>]+>', '', comp_content['mdHtml'])
                markdown_parts.append(clean_text)
        elif comp_type in ['MxGraphWidget', 'Image', 'ImageWidget']:
            # 处理图片组件，提取尺寸信息
            img_markdown = generate_enhanced_image_markdown(comp_content, comp_type, i, course_slug)
            if img_markdown:
                markdown_parts.append(img_markdown)
        else:
            # 其他类型组件的占位符
            markdown_parts.append(f"**[{comp_type}]**")

        markdown_parts.append("\n</div>\n")

    markdown_parts.append("</div>\n")

    return "".join(markdown_parts)


def generate_enhanced_image_markdown(content: Dict[str, Any], component_type: str, index: int, course_slug: str = None) -> str:
    """
    生成增强的图片Markdown，包含尺寸信息
    专门用于处理特殊widget（包含original_width和original_height的内容）

    Args:
        content: 组件内容
        component_type: 组件类型
        index: 组件索引

    Returns:
        生成的Markdown字符串
    """
    # 首先检查是否有原始尺寸信息（特殊widget的标志）
    if 'original_width' in content and 'original_height' in content:
        width = str(content['original_width'])
        height = str(content['original_height'])

        # 获取图片URL
        image_url = None

        # 方法1: 从path字段获取
        if 'path' in content:
            image_url = content['path']
        # 方法2: 从XML中提取（MxGraphWidget）
        elif component_type == 'MxGraphWidget' and 'xml' in content:
            xml_content = content.get('xml', '')
            import re
            url_match = re.search(r'image=([^;]+)', xml_content)
            if url_match:
                image_url = url_match.group(1)
                # 清理URL
                if '?' in image_url:
                    image_url = image_url.split('?')[0]

        # 确保URL是完整的
        if image_url:
            if image_url.startswith('/'):
                image_url = f"https://www.educative.io{image_url}"
            elif not image_url.startswith('http'):
                image_url = f"https://www.educative.io/{image_url}"

            # 上传图片到服务器并获取新URL
            uploaded_url = upload_image_to_server(image_url, course_slug=course_slug)
            final_url = uploaded_url if uploaded_url else image_url  # 如果上传失败，使用原URL

            # 生成带尺寸的HTML图片标记
            alt_text = content.get('caption', f'Image {index+1}')
            return f'<img src="{final_url}" alt="{alt_text}" width="{width}" height="{height}" style="width: 100%; height: auto;" />'

    # 特殊处理MxGraphWidget（没有original_width/height的情况）
    elif component_type == 'MxGraphWidget' and 'xml' in content:
        xml_content = content.get('xml', '')
        width, height = extract_image_dimensions_from_xml(xml_content)

        # 提取图片URL
        import re
        url_match = re.search(r'image=([^;]+)', xml_content)
        if url_match:
            image_url = url_match.group(1)
            # 清理URL
            if '?' in image_url:
                image_url = image_url.split('?')[0]

            # 确保URL是完整的
            if image_url.startswith('/'):
                image_url = f"https://www.educative.io{image_url}"
            elif not image_url.startswith('http'):
                image_url = f"https://www.educative.io/{image_url}"

            # 上传图片到服务器并获取新URL
            uploaded_url = upload_image_to_server(image_url, course_slug=course_slug)
            final_url = uploaded_url if uploaded_url else image_url  # 如果上传失败，使用原URL

            # 生成带尺寸的图片标记
            alt_text = content.get('caption', f'Image {index+1}')
            if width and height:
                return f'<img src="{final_url}" alt="{alt_text}" width="{width}" height="{height}" style="width: 100%; height: auto;" />'
            else:
                return f'![{alt_text}]({final_url})'

    # 回退到原来的图片处理逻辑
    return generate_image_markdown(content, component_type, index)


class EducativeFetcher:
    """Educative.io课程数据获取器"""

    def __init__(self, cookie=None):
        self.base_url = "https://www.educative.io/api"
        self.session = requests.Session()
        # 设置请求头，模拟浏览器访问
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
        }

        # 如果提供了Cookie，添加到请求头中
        if cookie:
            headers['Cookie'] = cookie

        self.session.headers.update(headers)

    def get_widget_content(self, author_id: str, collection_id: str, page_id: str, content_revision: str, widget_index: int) -> Dict[str, Any]:
        """
        获取特殊widget的内容

        Args:
            author_id: 作者ID
            collection_id: 集合ID
            page_id: 页面ID
            content_revision: 内容版本
            widget_index: widget索引

        Returns:
            widget内容数据
        """
        url = f"https://www.educative.io/api/collection/{author_id}/{collection_id}/page/{page_id}/{content_revision}/{widget_index}?work_type=collection"

        try:
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            console.print(f"[red]获取widget内容失败: {e}[/red]")
            return {}

    def fetch_collection_info(self, title: str) -> Optional[Dict[str, Any]]:
        """
        获取课程集合基本信息

        Args:
            title: 课程标题，如 'react-beginner-to-advanced'

        Returns:
            包含课程基本信息的字典，如果失败返回None
        """
        url = f"{self.base_url}/collection/{title}"

        try:
            console.print(f"[blue]正在获取课程信息: {title}[/blue]")
            response = self.session.get(url, timeout=30)
            response.raise_for_status()

            data = response.json()

            # 检查响应格式
            if not isinstance(data, dict) or 'instance' not in data:
                console.print(f"[red]错误: 响应格式不正确 - {title}[/red]")
                return None

            instance = data.get('instance', {})
            details = instance.get('details', {})

            if not details:
                console.print(f"[red]错误: 未找到课程详情 - {title}[/red]")
                return None

            console.print(f"[green]✓ 成功获取课程信息: {title}[/green]")
            return data

        except requests.exceptions.RequestException as e:
            console.print(f"[red]网络请求失败 - {title}: {str(e)}[/red]")
            return None
        except json.JSONDecodeError as e:
            console.print(f"[red]JSON解析失败 - {title}: {str(e)}[/red]")
            return None
        except Exception as e:
            console.print(f"[red]未知错误 - {title}: {str(e)}[/red]")
            return None

    def fetch_collection_toc(self, author_id: str, collection_id: str, page_id: str) -> Optional[Dict[str, Any]]:
        """
        获取课程目录信息

        Args:
            author_id: 作者ID
            collection_id: 课程集合ID
            page_id: 页面ID

        Returns:
            包含目录信息的字典，如果失败返回None
        """
        url = f"{self.base_url}/collection/{author_id}/{collection_id}/page/{page_id}"

        try:
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            console.print(f"[red]获取目录信息失败: {e}[/red]")
            return None

    def fetch_page_original_data(self, author_id: str, collection_id: str, page_id: str) -> Dict[str, Any]:
        """
        获取页面原始数据（不做任何处理）

        Args:
            author_id: 作者ID
            collection_id: 课程集合ID
            page_id: 页面ID

        Returns:
            原始页面数据字典
        """
        url = f"{self.base_url}/collection/{author_id}/{collection_id}/page/{page_id}"

        response = self.session.get(url, timeout=30)
        response.raise_for_status()
        return response.json()

    def fetch_page_content(self, author_id: str, collection_id: str, page_id: str, course_slug: str = None) -> Optional[Dict[str, Any]]:
        """
        获取页面内容

        Args:
            author_id: 作者ID
            collection_id: 课程集合ID
            page_id: 页面ID

        Returns:
            包含页面内容的字典，如果失败返回None
        """
        url = f"{self.base_url}/collection/{author_id}/{collection_id}/page/{page_id}"

        try:
            response = self.session.get(url, timeout=30)
            response.raise_for_status()

            data = response.json()

            # 检查响应格式
            if not isinstance(data, dict) or 'components' not in data:
                return None

            components = data.get('components', [])

            # 提取并合并所有组件的内容
            combined_text = ""
            combined_mdHtml = ""
            image_components = []  # 存储图片组件信息

            console.print(f"[yellow]🔍 调试: 页面 {page_id} 包含 {len(components)} 个组件[/yellow]")

            for i, component in enumerate(components):
                content = component.get('content', {})
                component_type = component.get('type', '')

                console.print(f"[cyan]  组件 {i+1}: 类型={component_type}[/cyan]")

                # 检查是否是特殊的widget类型（需要额外API调用）
                # 特殊widget可能直接在组件中，也可能在LazyLoadPlaceholder的content中
                special_widget_data = None

                # 情况1: 直接在组件中
                if all(field in component for field in ['actualType', 'contentRevision', 'widgetIndex']):
                    special_widget_data = component
                # 情况2: 在LazyLoadPlaceholder的content中
                elif (component_type == 'LazyLoadPlaceholder' and
                      isinstance(content, dict) and
                      all(field in content for field in ['actualType', 'contentRevision', 'widgetIndex'])):
                    special_widget_data = content

                if special_widget_data:
                    actual_type = special_widget_data.get('actualType', '')
                    content_revision = special_widget_data.get('contentRevision', '')
                    widget_index = special_widget_data.get('widgetIndex', 0)
                    original_page_id = special_widget_data.get('pageId', page_id)

                    console.print(f"[yellow]    ↳ 发现特殊widget: {actual_type}, 获取详细内容...[/yellow]")
                    console.print(f"[yellow]      ↳ contentRevision: {content_revision}, widgetIndex: {widget_index}[/yellow]")

                    # 获取widget的详细内容
                    widget_data = self.get_widget_content(author_id, collection_id, str(original_page_id), str(content_revision), widget_index)
                    if widget_data and 'components' in widget_data:
                        widget_components = widget_data['components']
                        if widget_components:
                            # 使用widget的详细内容替换原始内容
                            widget_component = widget_components[0]  # 通常只有一个组件
                            content = widget_component.get('content', {})
                            component_type = widget_component.get('type', actual_type)

                            # 添加原始的width和height信息
                            if 'width' in special_widget_data:
                                content['original_width'] = special_widget_data['width']
                            if 'height' in special_widget_data:
                                content['original_height'] = special_widget_data['height']

                            console.print(f"[yellow]      ↳ 成功获取widget详细内容，类型: {component_type}[/yellow]")
                        else:
                            console.print(f"[red]      ↳ Widget API响应中没有组件[/red]")
                    else:
                        console.print(f"[red]      ↳ 获取widget详细内容失败[/red]")

                # 处理 Quiz 组件 - 不提取 text/mdHtml，但要记录有Quiz
                if component_type == 'Quiz':
                    console.print(f"[blue]    ↳ Quiz组件，跳过文本提取[/blue]")
                    continue

                # 检查是否包含图片相关内容、布局组件或代码组件并直接拼接到文本中
                if component_type in ['Image', 'ImageWidget', 'Figure', 'Columns', 'Code', 'CodeWidget'] or 'image' in str(content).lower():
                    image_info = {
                        'type': component_type,
                        'content': content,
                        'index': i
                    }
                    image_components.append(image_info)
                    console.print(f"[magenta]    ↳ 发现图片/布局/代码组件: {component_type}[/magenta]")

                    # 获取嵌套组件信息（如果有的话）
                    nested_components = content.get('comps', []) if component_type == 'Columns' else None

                    # 生成图片的Markdown内容并直接拼接
                    # 检查是否是特殊widget（从API获取的内容）
                    if hasattr(content, 'get') and content.get('original_width') and content.get('original_height'):
                        # 特殊widget使用增强的图片生成
                        image_markdown = generate_enhanced_image_markdown(content, component_type, i, course_slug)
                    else:
                        # 普通图片使用标准的图片生成
                        image_markdown = generate_image_markdown(content, component_type, i, nested_components, course_slug)
                    if image_markdown:
                        combined_text += image_markdown + "\n"
                        combined_mdHtml += f"<div class='image-component'>{image_markdown}</div>\n"
                        console.print(f"[magenta]      ↳ 添加图片/布局/代码Markdown: {image_markdown[:50]}...[/magenta]")

                    # 尝试提取图片URL或相关信息
                    if 'url' in content:
                        console.print(f"[magenta]      图片URL: {content.get('url', 'N/A')}[/magenta]")
                    if 'src' in content:
                        console.print(f"[magenta]      图片SRC: {content.get('src', 'N/A')}[/magenta]")
                    if 'alt' in content:
                        console.print(f"[magenta]      图片ALT: {content.get('alt', 'N/A')}[/magenta]")

                # 处理 comps 数组（嵌套组件）- 但跳过已经在上面处理过的Columns组件
                if 'comps' in content and component_type != 'Columns':
                    console.print(f"[green]    ↳ 包含 {len(content['comps'])} 个嵌套组件[/green]")
                    for j, comp in enumerate(content['comps']):
                        comp_type = comp.get('type', '')
                        console.print(f"[green]      嵌套组件 {j+1}: {comp_type}[/green]")

                        # 检查嵌套组件是否是图片相关
                        if comp_type in ['Image', 'ImageWidget', 'Figure', 'MxGraphWidget', 'CodeDrawing'] or 'image' in str(comp.get('content', {})).lower():
                            comp_content = comp.get('content', {})
                            image_markdown = generate_image_markdown(comp_content, comp_type, j)
                            if image_markdown:
                                combined_text += image_markdown + "\n"
                                combined_mdHtml += f"<div class='nested-image-component'>{image_markdown}</div>\n"
                                console.print(f"[green]        ↳ 添加嵌套图片: {image_markdown[:50]}...[/green]")

                        if comp.get('type') == 'MarkdownEditor' and 'content' in comp:
                            comp_content = comp['content']
                            if 'text' in comp_content:
                                text_preview = comp_content['text'][:100] + "..." if len(comp_content['text']) > 100 else comp_content['text']
                                console.print(f"[green]        ↳ 添加文本: {text_preview}[/green]")
                                combined_text += comp_content['text'] + "\n"
                            if 'mdHtml' in comp_content:
                                html_preview = comp_content['mdHtml'][:100] + "..." if len(comp_content['mdHtml']) > 100 else comp_content['mdHtml']
                                console.print(f"[green]        ↳ 添加HTML: {html_preview}[/green]")
                                combined_mdHtml += comp_content['mdHtml'] + "\n"
                        # 处理其他类型的组件
                        elif 'content' in comp:
                            comp_content = comp['content']
                            if 'text' in comp_content:
                                text_preview = comp_content['text'][:100] + "..." if len(comp_content['text']) > 100 else comp_content['text']
                                console.print(f"[green]        ↳ 添加其他文本: {text_preview}[/green]")
                                combined_text += comp_content['text'] + "\n"
                            if 'mdHtml' in comp_content:
                                html_preview = comp_content['mdHtml'][:100] + "..." if len(comp_content['mdHtml']) > 100 else comp_content['mdHtml']
                                console.print(f"[green]        ↳ 添加其他HTML: {html_preview}[/green]")
                                combined_mdHtml += comp_content['mdHtml'] + "\n"

                # 处理直接的 text 和 mdHtml 字段
                text = content.get('text', '')
                mdHtml = content.get('mdHtml', '')

                if text:
                    text_preview = text[:100] + "..." if len(text) > 100 else text
                    console.print(f"[yellow]    ↳ 添加直接文本: {text_preview}[/yellow]")
                    combined_text += text + "\n"
                if mdHtml:
                    html_preview = mdHtml[:100] + "..." if len(mdHtml) > 100 else mdHtml
                    console.print(f"[yellow]    ↳ 添加直接HTML: {html_preview}[/yellow]")
                    combined_mdHtml += mdHtml + "\n"

            # 提取页面摘要信息 - summary和components同级
            page_summary = {}
            if 'summary' in data:
                summary_info = data['summary']
                page_summary = {
                    'titleUpdated': summary_info.get('titleUpdated', False),
                    'title': summary_info.get('title', ''),
                    'description': summary_info.get('description', ''),
                    'tags': summary_info.get('tags', [])
                }

            # 检查是否是 Quiz 页面，如果是则提取 questions
            quiz_questions = []
            has_quiz = False
            if 'components' in data:
                for component in data['components']:
                    comp_type = component.get('type', '')
                    if comp_type == 'Quiz':
                        has_quiz = True
                        if 'content' in component:
                            quiz_content = component['content']
                            if 'questions' in quiz_content:
                                quiz_questions = quiz_content['questions']
                                break

            # 打印最终统计信息
            console.print(f"[blue]📊 页面 {page_id} 统计:[/blue]")
            console.print(f"[blue]  - 文本长度: {len(combined_text)} 字符[/blue]")
            console.print(f"[blue]  - HTML长度: {len(combined_mdHtml)} 字符[/blue]")
            console.print(f"[blue]  - 图片组件数量: {len(image_components)}[/blue]")

            result = {
                'text': combined_text.strip(),
                'mdHtml': combined_mdHtml.strip(),
                'summary': page_summary,
                # 'images': image_components  # 添加图片组件信息
            }

            # 如果是 Quiz 页面，添加 questions 字段
            if has_quiz:
                result['questions'] = quiz_questions  # 即使为空也添加字段

            return result

        except requests.exceptions.RequestException as e:
            console.print(f"[yellow]获取页面内容失败 - {page_id}: {str(e)}[/yellow]")
            return None
        except json.JSONDecodeError as e:
            console.print(f"[yellow]页面内容JSON解析失败 - {page_id}: {str(e)}[/yellow]")
            return None
        except Exception as e:
            console.print(f"[yellow]获取页面内容未知错误 - {page_id}: {str(e)}[/yellow]")
            return None

    def extract_course_data(self, collection_data: Dict[str, Any], toc_data: Dict[str, Any], author_id: str, collection_id: str, course_slug: str = None) -> tuple:
        """
        提取并整理课程数据

        Args:
            collection_data: 课程基本信息
            toc_data: 课程目录信息
            author_id: 作者ID
            collection_id: 课程集合ID

        Returns:
            (整理后的课程数据, 所有页面的原始数据) 元组
        """
        # 提取基本信息
        instance = collection_data.get('instance', {})
        details = instance.get('details', {})

        # 提取目录信息
        toc_instance = toc_data.get('instance', {})
        toc_details = toc_instance.get('details', {})
        toc = toc_details.get('toc', {})
        categories = toc.get('categories', [])

        # 提取根级别数据（从collection_data的根级别）
        root_level_data = {
            'deletion_time': collection_data.get('deletion_time'),
            'ai_assistant_enabled': collection_data.get('ai_assistant_enabled', False),
            'certificate_price': collection_data.get('certificate_price', 0),
            'is_recommended_by_reader': collection_data.get('is_recommended_by_reader', False),
            'is_reader_subscribed': collection_data.get('is_reader_subscribed', False),
            'pinned_by_reader': collection_data.get('pinned_by_reader', False),
            'is_published': collection_data.get('is_published', True),
            'first_published_time': collection_data.get('first_published_time', ''),
            'last_published_time': collection_data.get('last_published_time', ''),
            'price': details.get('price', 0),
            'skills': details.get('skills', []),
            'tags': details.get('tags', []),
            'aggregated_widget_stats': details.get('aggregated_widget_stats', {}),
            'url_slug': details.get('url_slug', ''),
            'authors': details.get('authors', []),  # 可能不存在，使用空列表
            'read_time': details.get('read_time', 0),
            'learner_tags': details.get('learner_tags', []),
            'level_one_learner_tags': details.get('level_one_learner_tags', []),
            'id': details.get('collection_id', '')
        }

        # 构建结果数据
        result = {
            'title': details.get('title', ''),
            'summary': details.get('summary', ''),
            'brief_summary': details.get('brief_summary', ''),
            'whatYouWillLearn': details.get('clos', ''),
            'target_audience': details.get('target_audience', ''),
            'cover_image_url': details.get('cover_image_url', ''),
            'creation_time': details.get('creation_time', ''),
            'modified_time': details.get('modified_time', ''),
            'published_time': details.get('published_time', ''),
            'categories': [],
            '_root_level_data': root_level_data  # 临时存储根级别数据
        }

        # 存储所有页面的原始数据
        all_pages_original_data = {}

        # 处理分类和页面
        for category in categories:
            category_data = {
                'id': category.get('id', ''),
                'title': category.get('title', ''),
                'summary': category.get('summary', ''),
                'pages': []
            }

            pages = category.get('pages', [])
            console.print(f"[blue]正在处理分类 '{category.get('title', '')}' 的 {len(pages)} 个页面...[/blue]")

            for i, page in enumerate(pages):
                page_id = page.get('page_id') or page.get('id')

                console.print(f"[cyan]  获取页面内容 {i+1}/{len(pages)}: {page.get('title', '')}[/cyan]")

                # 获取页面内容（使用原有的方法进行转换处理）
                page_content = self.fetch_page_content(str(author_id), str(collection_id), str(page_id), course_slug)

                # 同时获取原始数据
                try:
                    original_page_data = self.fetch_page_original_data(str(author_id), str(collection_id), str(page_id))
                    all_pages_original_data[str(page_id)] = original_page_data
                except Exception as e:
                    console.print(f"[yellow]获取页面原始数据失败: {e}[/yellow]")
                    all_pages_original_data[str(page_id)] = {"error": f"Failed to fetch original data: {str(e)}"}

                # 确定页面类型：如果包含questions则为Quiz，否则为Lesson
                page_type = "Quiz" if page_content and 'questions' in page_content else "Lesson"

                page_data = {
                    'id': str(page_id),
                    'title': page.get('title', ''),
                    'is_preview': page.get('is_preview', False),
                    'slug': page.get('slug', ''),
                    'text': page_content.get('text', '') if page_content else '',
                    'mdHtml': page_content.get('mdHtml', '') if page_content else '',
                    'summary': page_content.get('summary', {}) if page_content else {},
                    'images': page_content.get('images', []) if page_content else [],  # 添加图片信息
                    'type': page_type
                }

                # 如果页面包含Quiz questions，添加questions字段
                if page_content and 'questions' in page_content:
                    page_data['questions'] = page_content['questions']
                category_data['pages'].append(page_data)

            result['categories'].append(category_data)

        return result, all_pages_original_data

    def process_course(self, title: str) -> Optional[tuple]:
        """
        处理单个课程，获取完整信息

        Args:
            title: 课程标题

        Returns:
            (处理后的课程数据, 原始数据) 元组，如果失败返回None
        """
        # 第一步：获取课程基本信息
        collection_data = self.fetch_collection_info(title)
        if not collection_data:
            return None

        # 保存原始数据
        original_data = {
            'collection_data': collection_data,
            'toc_data': None
        }

        # 从基本信息中提取必要的ID
        instance = collection_data.get('instance', {})
        details = instance.get('details', {})

        author_id = details.get('author_id')
        collection_id = details.get('collection_id')

        if not author_id or not collection_id:
            console.print(f"[red]错误: 无法获取必要的ID信息 - {title}[/red]")
            return None

        # 检查是否已经有完整的目录信息
        toc = details.get('toc', {})
        categories = toc.get('categories', [])

        if categories:
            # 第一个API已经包含了完整的目录信息，直接使用
            console.print(f"[green]✓ 使用第一个API响应中的完整目录信息 - {title}[/green]")
            toc_data = collection_data
        else:
            # 如果没有目录信息，尝试第二个API
            page_id = None
            if categories and len(categories) > 0:
                pages = categories[0].get('pages', [])
                if pages and len(pages) > 0:
                    page_id = pages[0].get('page_id') or pages[0].get('id')

            if page_id:
                # 第二步：获取详细目录信息
                toc_data = self.fetch_collection_toc(str(author_id), str(collection_id), str(page_id))
                if not toc_data:
                    console.print(f"[yellow]警告: 使用基本目录信息 - {title}[/yellow]")
                    toc_data = collection_data
            else:
                console.print(f"[yellow]警告: 无法获取页面ID，使用基本信息 - {title}[/yellow]")
                toc_data = collection_data

        # 更新原始数据
        original_data['toc_data'] = toc_data

        # 第三步：提取并整理数据
        course_data, pages_original_data = self.extract_course_data(collection_data, toc_data, str(author_id), str(collection_id), title)

        # 将页面原始数据添加到原始数据中
        original_data['pages_data'] = pages_original_data

        return (course_data, original_data)

    def process_courses(self, titles: List[str]) -> List[Dict[str, Any]]:
        """
        处理多个课程，每个课程生成独立的JSON文件

        Args:
            titles: 课程标题列表

        Returns:
            课程数据列表
        """
        results = []

        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:

            task = progress.add_task("处理课程中...", total=len(titles))

            for i, title in enumerate(titles):
                progress.update(task, description=f"处理课程 {i+1}/{len(titles)}: {title}")

                result = self.process_course(title)
                if result:
                    course_data, original_data = result
                    # 为每个课程生成独立的JSON文件，包含原始数据
                    self.save_course_json(course_data, title, original_data)
                    results.append(course_data)
                    console.print(f"[green]✓ 完成: {title}[/green]")
                else:
                    console.print(f"[red]✗ 失败: {title}[/red]")

                progress.advance(task)

        return results

    def save_course_json(self, course_data: Dict[str, Any], title: str, original_data: Dict[str, Any] = None):
        """
        为单个课程保存JSON文件到course文件夹，并保存原始数据+图片到origin_course文件夹

        Args:
            course_data: 处理后的课程数据
            title: 课程标题（用于备用文件名）
            original_data: 原始未处理的课程数据
        """
        import datetime
        import os

        # 创建course文件夹（如果不存在）
        course_dir = "course"
        if not os.path.exists(course_dir):
            os.makedirs(course_dir)
            console.print(f"[blue]✓ 创建文件夹: {course_dir}[/blue]")

        # 提取根级别数据
        root_level_data = course_data.get('_root_level_data', {})

        # 使用url_slug作为文件名，如果没有则使用title
        url_slug = root_level_data.get('url_slug', title)
        if not url_slug:
            url_slug = title

        # 创建origin_course下的课程子文件夹
        origin_course_dir = "origin_course"
        course_origin_dir = os.path.join(origin_course_dir, url_slug)
        if not os.path.exists(course_origin_dir):
            os.makedirs(course_origin_dir)
            console.print(f"[blue]✓ 创建课程原始数据文件夹: {course_origin_dir}[/blue]")

        # 构建单个课程的完整数据结构（处理后的数据）
        output_data = {
            'deletion_time': root_level_data.get('deletion_time'),
            'ai_assistant_enabled': root_level_data.get('ai_assistant_enabled', False),
            'certificate_price': root_level_data.get('certificate_price', 0),
            'is_recommended_by_reader': root_level_data.get('is_recommended_by_reader', False),
            'is_reader_subscribed': root_level_data.get('is_reader_subscribed', False),
            'pinned_by_reader': root_level_data.get('pinned_by_reader', False),
            'is_published': root_level_data.get('is_published', False),
            'first_published_time': root_level_data.get('first_published_time', ''),
            'last_published_time': root_level_data.get('last_published_time', ''),
            'timestamp': datetime.datetime.now().isoformat(),
            'total_courses': 1,  # 每个文件只包含一个课程
            'price': root_level_data.get('price', 0),
            'skills': root_level_data.get('skills', []),
            'tags': root_level_data.get('tags', []),
            'aggregated_widget_stats': root_level_data.get('aggregated_widget_stats', {}),
            'url_slug': root_level_data.get('url_slug', ''),
            'authors': root_level_data.get('authors', []),
            'read_time': root_level_data.get('read_time', 0),
            'learner_tags': root_level_data.get('learner_tags', []),
            'level_one_learner_tags': root_level_data.get('level_one_learner_tags', []),
            'courses': [
                {k: v for k, v in course_data.items() if k != '_root_level_data'}
            ]
        }

        # 生成文件路径
        processed_filename = os.path.join(course_dir, f"{url_slug}.json")
        original_filename = os.path.join(course_origin_dir, f"{url_slug}.json")

        # 保存处理后的数据（移除images字段）
        try:
            clean_output_data = remove_images_from_data(output_data)
            console.print(f"[yellow]📝 已移除处理后数据中的images字段[/yellow]")

            json_output = json.dumps(clean_output_data, ensure_ascii=False, indent=2)
            with open(processed_filename, 'w', encoding='utf-8') as f:
                f.write(json_output)
            console.print(f"[green]✓ 已保存处理后数据: {processed_filename}[/green]")
        except Exception as e:
            console.print(f"[red]保存处理后数据失败 {processed_filename}: {str(e)}[/red]")

        # 保存原始数据JSON
        if original_data:
            try:
                original_json_output = json.dumps(original_data, ensure_ascii=False, indent=2)
                with open(original_filename, 'w', encoding='utf-8') as f:
                    f.write(original_json_output)
                console.print(f"[green]✓ 已保存原始数据: {original_filename}[/green]")
            except Exception as e:
                console.print(f"[red]保存原始数据失败 {original_filename}: {str(e)}[/red]")
        else:
            console.print(f"[yellow]⚠️  没有原始数据可保存[/yellow]")

    def download_original_images(self, original_data: Dict[str, Any], course_dir: str, course_slug: str):
        """
        下载原始图片到课程文件夹

        Args:
            original_data: 原始数据
            course_dir: 课程文件夹路径
            course_slug: 课程slug
        """
        import os
        import re
        from urllib.parse import urlparse

        console.print(f"[cyan]🖼️  开始下载原始图片到: {course_dir}[/cyan]")

        # 收集所有图片URL
        image_urls = set()

        # 从pages_data中提取图片URL
        pages_data = original_data.get('pages_data', {})
        console.print(f"[cyan]🔍 检查 {len(pages_data)} 个页面的图片数据[/cyan]")
        for page_id, page_data in pages_data.items():
            console.print(f"[cyan]🔍 处理页面 {page_id} 的图片[/cyan]")
            self._extract_image_urls_from_page(page_data, image_urls)

        # 下载图片
        console.print(f"[cyan]🔍 总共发现 {len(image_urls)} 个图片URL[/cyan]")
        for url in list(image_urls)[:5]:  # 显示前5个URL作为示例
            console.print(f"[cyan]  - {url}[/cyan]")

        downloaded_count = 0
        for image_url in image_urls:
            try:
                # 生成文件名（基于原始URL路径）
                filename = self._generate_original_image_filename(image_url)
                if filename:
                    filepath = os.path.join(course_dir, filename)
                    console.print(f"[cyan]🔍 准备下载: {image_url} -> {filename}[/cyan]")

                    # 下载图片
                    if self._download_original_image(image_url, filepath):
                        downloaded_count += 1
                        console.print(f"[green]✓ 下载原始图片: {filename}[/green]")
                    else:
                        console.print(f"[yellow]⚠️  下载失败: {filename}[/yellow]")
            except Exception as e:
                console.print(f"[red]下载图片失败 {image_url}: {str(e)}[/red]")

        console.print(f"[cyan]📊 原始图片下载完成: {downloaded_count} 个文件[/cyan]")

    def _extract_image_urls_from_page(self, page_data: Dict[str, Any], image_urls: set):
        """
        从页面数据中递归提取图片URL
        """
        if isinstance(page_data, dict):
            for key, value in page_data.items():
                # 检查各种可能的图片路径字段
                if key in ['path', 'image_url', 'cover_image_serving_url', 'profile_image_serving_url', 'url', 'src'] and isinstance(value, str):
                    if value.startswith('/api/') or value.startswith('/v2api/'):
                        # 相对路径，需要构建完整URL
                        full_url = f"https://www.educative.io{value}"
                        image_urls.add(full_url)
                        console.print(f"[cyan]🔍 发现图片路径: {full_url}[/cyan]")
                    elif value.startswith('http') and ('image' in value or 'img' in value):
                        # 已经是完整URL且包含图片相关关键词
                        image_urls.add(value)
                        console.print(f"[cyan]🔍 发现图片URL: {value}[/cyan]")
                elif key == 'text' and isinstance(value, str):
                    # 从Markdown文本中提取图片路径
                    import re
                    # 匹配 ![](path) 格式的图片
                    img_matches = re.findall(r'!\[.*?\]\((/api/[^)]+)\)', value)
                    for img_path in img_matches:
                        full_url = f"https://www.educative.io{img_path}"
                        image_urls.add(full_url)
                        console.print(f"[cyan]🔍 从Markdown提取图片: {full_url}[/cyan]")

                    # 匹配HTML img标签中的src
                    html_img_matches = re.findall(r'<img[^>]+src=["\']([^"\']+)["\']', value)
                    for img_src in html_img_matches:
                        if img_src.startswith('/api/') or img_src.startswith('/v2api/'):
                            full_url = f"https://www.educative.io{img_src}"
                            image_urls.add(full_url)
                            console.print(f"[cyan]🔍 从HTML提取图片: {full_url}[/cyan]")
                elif key == 'images' and isinstance(value, list):
                    for img in value:
                        if isinstance(img, dict):
                            for img_key in ['url', 'src', 'path']:
                                if img_key in img:
                                    url = img[img_key]
                                    if isinstance(url, str):
                                        if url.startswith('/api/') or url.startswith('/v2api/'):
                                            full_url = f"https://www.educative.io{url}"
                                            image_urls.add(full_url)
                                            console.print(f"[cyan]🔍 从images数组提取: {full_url}[/cyan]")
                                        elif url.startswith('http'):
                                            image_urls.add(url)
                                            console.print(f"[cyan]🔍 从images数组提取URL: {url}[/cyan]")
                elif isinstance(value, (dict, list)):
                    self._extract_image_urls_from_page(value, image_urls)
        elif isinstance(page_data, list):
            for item in page_data:
                self._extract_image_urls_from_page(item, image_urls)

    def _generate_original_image_filename(self, image_url: str) -> str:
        """
        基于原始图片URL生成文件名，使用完整的URL路径作为文件名
        """
        import re
        from urllib.parse import urlparse

        try:
            # 解析URL
            parsed = urlparse(image_url)
            path = parsed.path

            # 移除开头的斜杠
            if path.startswith('/'):
                path = path[1:]

            # 将路径分隔符替换为下划线，创建唯一的文件名
            filename = path.replace('/', '_')

            # 如果没有扩展名，根据URL类型添加扩展名
            if '.' not in filename:
                if '/image/' in image_url:
                    filename += '.svg'  # 大多数图表是SVG格式
                else:
                    filename += '.png'  # 默认为PNG

            # 清理文件名，移除非法字符（但保留下划线）
            filename = re.sub(r'[<>:"/\\|?*]', '_', filename)

            # 确保文件名不为空
            if not filename:
                filename = f"image_{hash(image_url) % 1000000}.png"

            console.print(f"[cyan]🔍 生成文件名: {image_url} -> {filename}[/cyan]")
            return filename
        except Exception as e:
            console.print(f"[yellow]⚠️  生成文件名失败 {image_url}: {str(e)}[/yellow]")
            # 如果解析失败，使用hash生成文件名
            return f"image_{hash(image_url) % 1000000}.png"

    def _download_original_image(self, image_url: str, filepath: str) -> bool:
        """
        下载原始图片到指定路径，添加时间戳参数
        """
        try:
            import time
            import os

            # 确保目标目录存在
            os.makedirs(os.path.dirname(filepath), exist_ok=True)

            # 添加时间戳参数到URL
            separator = '&' if '?' in image_url else '?'
            timestamped_url = f"{image_url}{separator}t={int(time.time() * 1000)}"

            # 设置请求头，包含cookie
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Sec-Fetch-Dest': 'image',
                'Sec-Fetch-Mode': 'no-cors',
                'Sec-Fetch-Site': 'same-origin',
            }

            # 添加cookie认证
            if hasattr(self, 'session') and self.session.cookies:
                # 使用session的cookies
                response = self.session.get(timestamped_url, headers=headers, timeout=30)
            else:
                # 使用全局cookie
                global _educative_cookie
                if _educative_cookie:
                    headers['Cookie'] = _educative_cookie
                response = requests.get(timestamped_url, headers=headers, timeout=30)

            response.raise_for_status()

            # 直接写入到目标文件路径
            with open(filepath, 'wb') as f:
                f.write(response.content)

            return True
        except Exception as e:
            console.print(f"[red]下载原始图片失败 {image_url}: {str(e)}[/red]")
            return False


def remove_images_from_data(data: Any) -> Any:
    """
    递归移除数据中的images字段

    Args:
        data: 要处理的数据

    Returns:
        移除images字段后的数据
    """
    if isinstance(data, dict):
        # 创建新字典，排除images字段
        new_dict = {}
        for key, value in data.items():
            if key != 'images':  # 跳过images字段
                new_dict[key] = remove_images_from_data(value)  # 递归处理值
        return new_dict
    elif isinstance(data, list):
        # 递归处理列表中的每个元素
        return [remove_images_from_data(item) for item in data]
    else:
        # 基本类型直接返回
        return data


def remove_images_from_data_except_origin(data: Any) -> Any:
    """
    递归移除数据中的images字段，但保留origin_course中的原始数据

    Args:
        data: 要处理的数据

    Returns:
        移除images字段后的数据（origin_course除外）
    """
    if isinstance(data, dict):
        # 创建新字典
        new_dict = {}
        for key, value in data.items():
            if key == 'origin_course':
                # 保留origin_course的原始数据，不做任何处理
                new_dict[key] = value
            elif key != 'images':  # 跳过images字段
                new_dict[key] = remove_images_from_data_except_origin(value)  # 递归处理值
        return new_dict
    elif isinstance(data, list):
        # 递归处理列表中的每个元素
        return [remove_images_from_data_except_origin(item) for item in data]
    else:
        # 基本类型直接返回
        return data


def generate_markdown_from_course(course_data: Dict[str, Any], output_dir: str = "markdown_output") -> str:
    """
    将课程数据转换为Markdown格式并保存到文件

    Args:
        course_data: 课程数据
        output_dir: 输出目录

    Returns:
        生成的Markdown文件路径
    """
    import os
    import re

    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        console.print(f"[blue]✓ 创建输出目录: {output_dir}[/blue]")

    # 生成文件名
    title = course_data.get('title', 'Unknown Course')
    safe_title = re.sub(r'[^\w\s-]', '', title).strip()
    safe_title = re.sub(r'[-\s]+', '-', safe_title)
    filename = f"{safe_title}.md"
    filepath = os.path.join(output_dir, filename)

    markdown_content = []

    # 课程标题和基本信息
    markdown_content.append(f"# {title}\n")

    if course_data.get('summary'):
        markdown_content.append(f"## 课程简介\n{course_data['summary']}\n")

    if course_data.get('brief_summary'):
        markdown_content.append(f"## 简要说明\n{course_data['brief_summary']}\n")

    if course_data.get('whatYouWillLearn'):
        markdown_content.append(f"## 学习目标\n{course_data['whatYouWillLearn']}\n")

    if course_data.get('target_audience'):
        markdown_content.append(f"## 目标受众\n{course_data['target_audience']}\n")

    # 处理分类和页面
    categories = course_data.get('categories', [])
    total_images = 0

    for category in categories:
        category_title = category.get('title', 'Untitled Category')
        markdown_content.append(f"## {category_title}\n")

        if category.get('summary'):
            markdown_content.append(f"{category['summary']}\n")

        pages = category.get('pages', [])
        for page in pages:
            page_title = page.get('title', 'Untitled Page')
            markdown_content.append(f"### {page_title}\n")

            # 处理页面内容
            page_text = page.get('text', '')
            page_html = page.get('mdHtml', '')

            # 优先使用text，如果没有则使用mdHtml
            content = page_text if page_text else page_html

            if content:
                # 清理HTML标签（如果是mdHtml）
                if not page_text and page_html:
                    import html
                    content = re.sub(r'<[^>]+>', '', content)
                    content = html.unescape(content)

                markdown_content.append(f"{content}\n")

            # 图片已经在处理页面内容时直接拼接到文本中了，这里统计一下数量
            images = page.get('images', [])
            if images:
                total_images += len(images)

            # 处理Quiz问题
            if page.get('questions'):
                markdown_content.append("#### Quiz Questions\n")
                questions = page.get('questions', [])
                for j, question in enumerate(questions):
                    markdown_content.append(f"**问题 {j+1}:** {question.get('question', 'N/A')}\n")

                    options = question.get('options', [])
                    for k, option in enumerate(options):
                        marker = "✓" if option.get('correct', False) else "○"
                        markdown_content.append(f"{marker} {chr(65+k)}. {option.get('text', 'N/A')}\n")

                    if question.get('explanation'):
                        markdown_content.append(f"*解释: {question['explanation']}*\n")

                    markdown_content.append("\n")

    # 写入文件
    try:
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write('\n'.join(markdown_content))

        console.print(f"[green]✓ Markdown文件已生成: {filepath}[/green]")
        console.print(f"[blue]📊 统计信息:[/blue]")
        console.print(f"[blue]  - 总分类数: {len(categories)}[/blue]")
        console.print(f"[blue]  - 总页面数: {sum(len(cat.get('pages', [])) for cat in categories)}[/blue]")
        console.print(f"[blue]  - 总图片数: {total_images}[/blue]")
        console.print(f"[blue]  - 文件大小: {os.path.getsize(filepath)} 字节[/blue]")

        return filepath

    except Exception as e:
        console.print(f"[red]生成Markdown文件失败: {str(e)}[/red]")
        return ""


def print_course_summary(course_data: Dict[str, Any]):
    """打印课程摘要信息"""
    table = Table(title=f"课程信息: {course_data.get('title', 'Unknown')}")

    table.add_column("属性", style="cyan", no_wrap=True)
    table.add_column("值", style="magenta")

    table.add_row("标题", course_data.get('title', 'N/A'))
    table.add_row("作者ID", str(course_data.get('author_id', 'N/A')))
    table.add_row("课程ID", str(course_data.get('collection_id', 'N/A')))
    table.add_row("分类数量", str(len(course_data.get('categories', []))))

    total_pages = sum(len(cat.get('pages', [])) for cat in course_data.get('categories', []))
    total_images = sum(len(page.get('images', [])) for cat in course_data.get('categories', []) for page in cat.get('pages', []))

    table.add_row("总页面数", str(total_pages))
    table.add_row("总图片数", str(total_images))

    console.print(table)

    # 打印第一个分类的详细信息作为示例
    categories = course_data.get('categories', [])
    if categories:
        first_category = categories[0]
        console.print(f"\n[bold]第一个分类示例:[/bold]")
        console.print(f"ID: {first_category.get('id', 'N/A')}")
        console.print(f"标题: {first_category.get('title', 'N/A')}")
        console.print(f"摘要: {first_category.get('summary', 'N/A')}")

        pages = first_category.get('pages', [])
        if pages:
            console.print(f"页面数量: {len(pages)}")
            first_page = pages[0]
            console.print(f"第一个页面: {first_page.get('title', 'N/A')}")
            console.print(f"第一个页面图片数: {len(first_page.get('images', []))}")

            # 显示第一个页面的图片信息
            images = first_page.get('images', [])
            if images:
                console.print(f"[magenta]第一个页面的图片信息:[/magenta]")
                for i, img in enumerate(images[:3]):  # 只显示前3个
                    img_type = img.get('type', 'Unknown')
                    img_content = img.get('content', {})
                    img_url = img_content.get('url') or img_content.get('src', 'No URL')
                    console.print(f"[magenta]  图片 {i+1}: 类型={img_type}, URL={img_url}[/magenta]")


def replace_external_images_in_json(json_file_path: str, cookie: str = None) -> bool:
    """
    替换JSON文件中的外部图片URL为上传后的URL

    Args:
        json_file_path: JSON文件路径
        cookie: 用于下载图片的cookie字符串

    Returns:
        是否成功替换
    """
    import re
    import os

    # 如果提供了cookie，设置为全局cookie
    if cookie:
        set_educative_cookie(cookie)

    # 从文件路径中提取课程slug
    course_slug = None
    try:
        # 假设文件路径格式为: .../course_name.json
        filename = os.path.basename(json_file_path)
        if filename.endswith('.json'):
            course_slug = filename[:-5]  # 移除.json扩展名
    except Exception:
        pass

    try:
        # 读取JSON文件
        console.print(f"[blue]📖 读取JSON文件: {json_file_path}[/blue]")
        with open(json_file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 查找所有的educative.io图片URL
        educative_pattern = r'https://www\.educative\.io/api/collection/[^"]*'
        matches = re.findall(educative_pattern, content)

        if not matches:
            console.print(f"[green]✅ 文件中没有找到外部图片URL: {json_file_path}[/green]")
            return True

        console.print(f"[yellow]🔍 找到 {len(matches)} 个外部图片URL[/yellow]")

        # 去重
        unique_urls = list(set(matches))
        console.print(f"[yellow]🔍 去重后有 {len(unique_urls)} 个唯一URL[/yellow]")

        # 替换每个URL
        updated_content = content
        replacement_count = 0

        for i, url in enumerate(unique_urls):
            console.print(f"[cyan]📤 处理图片 {i+1}/{len(unique_urls)}: {url}[/cyan]")

            # 上传图片并获取新URL
            uploaded_url = upload_image_to_server(url, course_slug=course_slug)

            if uploaded_url:
                # 替换所有出现的该URL
                updated_content = updated_content.replace(url, uploaded_url)
                replacement_count += 1
                console.print(f"[green]✅ 替换成功: {url} -> {uploaded_url}[/green]")
            else:
                console.print(f"[red]❌ 上传失败，保持原URL: {url}[/red]")

        # 如果有替换，保存文件
        if replacement_count > 0:
            # 备份原文件
            backup_path = f"{json_file_path}.backup"
            with open(backup_path, 'w', encoding='utf-8') as f:
                f.write(content)
            console.print(f"[blue]💾 已备份原文件: {backup_path}[/blue]")

            # 保存更新后的文件
            with open(json_file_path, 'w', encoding='utf-8') as f:
                f.write(updated_content)
            console.print(f"[green]✅ 已更新文件: {json_file_path}[/green]")
            console.print(f"[green]📊 成功替换 {replacement_count}/{len(unique_urls)} 个图片URL[/green]")
        else:
            console.print(f"[yellow]⚠️  没有成功替换任何URL[/yellow]")

        return replacement_count > 0

    except Exception as e:
        console.print(f"[red]❌ 处理文件失败 {json_file_path}: {str(e)}[/red]")
        return False


def process_all_json_files(directory: str = "course", cookie: str = None) -> None:
    """
    处理指定目录下的所有JSON文件，替换外部图片URL

    Args:
        directory: 包含JSON文件的目录
        cookie: 用于下载图片的cookie字符串
    """
    import os
    import glob

    # 查找所有JSON文件
    json_pattern = os.path.join(directory, "*.json")
    json_files = glob.glob(json_pattern)

    if not json_files:
        console.print(f"[yellow]⚠️  在目录 {directory} 中没有找到JSON文件[/yellow]")
        return

    console.print(f"[blue]🔍 找到 {len(json_files)} 个JSON文件[/blue]")

    # 处理每个文件
    success_count = 0
    for i, json_file in enumerate(json_files):
        console.print(f"\n[blue]{'='*60}[/blue]")
        console.print(f"[blue]📁 处理文件 {i+1}/{len(json_files)}: {os.path.basename(json_file)}[/blue]")
        console.print(f"[blue]{'='*60}[/blue]")

        if replace_external_images_in_json(json_file, cookie):
            success_count += 1

    console.print(f"\n[green]🎉 处理完成！成功处理 {success_count}/{len(json_files)} 个文件[/green]")


def main():

    # Educative.io Cookie - 用于访问需要认证的内容
    cookie = '_ga=GA1.1.261593851.1752502181; FPID=FPID2.2.ETFtoefQhgmivFHYHiH01%2BSO6PZwkMmywypBqjd%2FXDg%3D.1752502181; _fbp=fb.1.1752502182115.435372465284676372; usprivacy=1---; OneTrustWPCCPAGoogleOptOut=false; _gcl_au=1.1.1237489861.1752502298; __stripe_mid=98cdeed9-9ff1-49ba-a584-162789c41f27d0a227; cf_clearance=_pCx0qwEd37Qt8B3UoYKTxY0ZsqBwvzzwtf95uPeN8A-1752544844-*******-84f.YifGcQPdy5kWsP5lxff9iftNuKg.KVjiklca64sMlKH_Hkv7sKg1OtZkcufNf1rMFudEJq_XGuJtxSRrxn2fqDz1x9W7ku46kRBo40IREMz14Qv7wpwnZNkhCgxe_WScIzvf9JoXSjpRAQ3NM7gQ6SYqPzX_F9Eeyb5yRh_39WFPEqftMA5.X2o2.y1edsdUdwY6x1r1ZG_Qk7jtM5GyTR3XZk1X38dXlSXjXpk; hubspotutk=b7faf9db32623687136f0be3ba426504; logged_in=; show_sd_pal_transition_modal=false; _uetvid=2f6c6f8060bc11f0baffa32a157014ef; theme=light; use_system_preference=system_preference; enterprise_nav=false; enterprise_new_sidebar=true; trial_availed=false; subscribed=false; l2c_subscribed=false; font-family-body-lesson-markdown=Droid Serif; font-family-heading-lesson-markdown=Nunito Sans; font-size-lesson-markdown=18px; line-height-lesson-markdown=175%; content-width-lesson-markdown=1024px; __hssrc=1; visited-explore-page=false; recommendations=true; cloudlab_redirect_url=%7B%22path%22%3A%22%2F%22%7D; project_redirect_url=%7B%22path%22%3A%22%2F%22%7D; subscriptionBannerStatus=true; _clck=1o81x5v%5E2%5Efyn%5E0%5E2021; FPLC=FVlRL4hdfqmCfTtySgbcvyJ1BlD9THpOq4tDdM7xeyHPG%2BIarRxKeTl9WkIz2gCunpsPopmtxF5Ocq78%2BvMOQSTcYvquc1W%2BHumrI1hyQ%2BIoGIHf%2BuGQVB7pVenAtw%3D%3D; __cf_bm=eJAM1OzjMeaY1XTKxobvmYJ.rDM08k44uUHoh_RofMM-1755741576-*******-Rm30nFFqC7_L8VERTxTSccTUximGchKrX3d47Mvz43Jgx04GX2G5_0VysdXTmTljkR__45h_lUQ6JxS6xgSmnHu1HJbtFClQtbIxCi9kxT0; flask-auth=.eJyLNjMzMTUwNDUyNjO0MDIwNNMx0FEyywowMfBKTzQ0LvOOcMrMD4pKd0oNUgJKASWTqyqzc1Iz8xzyS0ty8vOz9ZLzc5VwCZcUlabqpCXmFKfq5JXm5OgYQyhDc1NTcxNDU3PLWACvDygz; __hstc=10449898.b7faf9db32623687136f0be3ba426504.1752735489920.1755680643433.1755741591266.66; _clsk=14z8omb%5E1755741615579%5E7%5E1%5Ez.clarity.ms%2Fcollect; __hssc=10449898.2.1755741591266; FPGSID=1.1755741575.1755741753.G-MWGSGCW5SP.tFwp708c42DWd2EDR9yWJA; _uetsid=ac86e3507b2e11f0b43e31d6be602a21; _rdt_uuid=1752502180915.4606c1a1-dbef-408e-a1da-7bc243879383; OptanonConsent=isGpcEnabled=0&datestamp=Thu+Aug+21+2025+10%3A02%3A31+GMT%2B0800+(%E4%B8%AD%E5%9B%BD%E6%A0%87%E5%87%86%E6%97%B6%E9%97%B4)&version=202505.1.0&browserGpcFlag=0&isIABGlobal=false&hosts=&consentId=36735b12-233a-4da5-abf6-30e3636d4a7b&interactionCount=1&isAnonUser=1&landingPath=NotLandingPage&groups=C0001%3A1%2CC0002%3A1%2CC0003%3A1%2CC0004%3A1&AwaitingReconsent=false&geolocation=JP%3B27; _ga_MWGSGCW5SP=GS2.1.s1755741569$o93$g1$t1755741751$j56$l0$h1769477038; OptanonAlertBoxClosed=2025-08-21T02:02:31.576Z; magicbox-auth=eyJ1c2VyX2lkIjogNjY0NTAxNTIzNjE4MjAxNiwgInRva2VuIjogIlcxR01wN01hN0lnTlZidzFmeWo1WVYiLCAidG9rZW5fdHMiOiAxNzU1NzQxNzU4MDAwLCAibG91X3Nlc3Npb24iOiBmYWxzZX0=|94de2f3ba34fa57702f0c995915e496bb61e1a99; flask-session=.eJx1jzsLwjAUhf_LnYM0bZNqJ-moi3QSRUJor6U2j5KHoOJ_N1hXpwPfgfN4gZjRaWnQBKiDi0hARI8O6jPnJcsoywtO13lGOckI8NuhzHaDpMV9f2xG256GBltIVjK752NSOJqtjUFZO606q-Ef_nZdpfJITFSKFIvQirGqpKzaXAhIj97rNE1oDLKXQYqxhxpSqB8HE2fhbXQd_pC3Iga93Hh_AOQsR88.aKZ-Pg.Lvu8qJhkoXjkTFhNQj0SHs8O7Nw'

    """主函数"""
    import sys

    # 检查命令行参数
    if len(sys.argv) > 1:
        command = sys.argv[1]

        if command == "replace-images":
            # 替换图片URL模式
            directory = sys.argv[2] if len(sys.argv) > 2 else "course"
            console.print(f"[blue]🚀 开始替换目录 {directory} 中的外部图片URL...[/blue]")
            
            process_all_json_files(directory, cookie)
            return
        elif command == "process-file":
            # 处理单个文件
            if len(sys.argv) < 3:
                console.print("[red]❌ 请提供JSON文件路径[/red]")
                console.print("[yellow]用法: python get_cursor.py process-file <json_file_path>[/yellow]")
                return

            json_file = sys.argv[2]
            console.print(f"[blue]🚀 开始处理文件: {json_file}[/blue]")
            replace_external_images_in_json(json_file, cookie)
            return

    # 默认课程列表，可以通过命令行参数覆盖
    default_titles = ["full-speed-python",]

    # default_titles =["full-speed-python","full-stack-django-and-react","learn-html-css-javascript-from-scratch","javascript-in-detail-from-beginner-to-advanced"]

    # default_titles =["react-beginner-to-advanced","database-design-fundamentals","learn-cpp-complete-course","building-a-jamstack-application-with-nextjs-and-strapi-cms","full-speed-python","full-stack-django-and-react","learn-html-css-javascript-from-scratch","javascript-in-detail-from-beginner-to-advanced"]

    # new ["learn-object-oriented-programming-in-cpp",]

    #  "aws-solutions-architect-associate" need auth

    # 正常的课程获取模式
    console.print(f"[bold blue]Educative.io 课程数据获取器[/bold blue]")
    console.print(f"准备处理 {len(default_titles)} 个课程:")
    for i, title in enumerate(default_titles, 1):
        console.print(f"  {i}. {title}")

    # 设置全局cookie用于图片下载
    set_educative_cookie(cookie)

    # 创建获取器实例（使用Cookie进行认证）
    fetcher = EducativeFetcher(cookie=cookie)

    # 处理课程
    results = fetcher.process_courses(default_titles)

    if not results:
        console.print("[red]错误: 没有成功获取任何课程数据[/red]")
        sys.exit(1)

    # 输出结果
    console.print(f"\n[bold green]成功获取 {len(results)} 个课程的数据[/bold green]")

    # 打印每个课程的摘要并生成Markdown
    # for course_data in results:
    #     print_course_summary(course_data)

    #     # 生成Markdown文件
    #     markdown_file = generate_markdown_from_course(course_data)
    #     if markdown_file:
    #         console.print(f"[green]✓ 已生成Markdown文件: {markdown_file}[/green]")

    #     console.print()  # 空行分隔

    # 输出JSON数据
    # import datetime

    # # 从第一个课程的数据中提取根级别字段（如果有课程数据的话）
    # if results:
    #     # 这些字段需要从原始API响应中获取，我们需要修改extract_course_data来返回这些信息
    #     first_course_data = results[0]
    #     root_level_data = first_course_data.get('_root_level_data', {})

    #     output_data = {
    #         'deletion_time': root_level_data.get('deletion_time'),
    #         'ai_assistant_enabled': root_level_data.get('ai_assistant_enabled', False),
    #         'certificate_price': root_level_data.get('certificate_price', 0),
    #         'is_recommended_by_reader': root_level_data.get('is_recommended_by_reader', False),
    #         'is_reader_subscribed': root_level_data.get('is_reader_subscribed', False),
    #         'pinned_by_reader': root_level_data.get('pinned_by_reader', False),
    #         'is_published': root_level_data.get('is_published', True),
    #         'first_published_time': root_level_data.get('first_published_time', ''),
    #         'last_published_time': root_level_data.get('last_published_time', ''),
    #         'timestamp': datetime.datetime.now().isoformat(),
    #         'total_courses': len(results),
    #         'price': root_level_data.get('price', 0),
    #         'skills': root_level_data.get('skills', []),
    #         'tags': root_level_data.get('tags', []),
    #         'aggregated_widget_stats': root_level_data.get('aggregated_widget_stats', {}),
    #         'url_slug': root_level_data.get('url_slug', ''),
    #         'authors': root_level_data.get('authors', []),
    #         'read_time': root_level_data.get('read_time', 0),
    #         'learner_tags': root_level_data.get('learner_tags', []),
    #         'level_one_learner_tags': root_level_data.get('level_one_learner_tags', []),
    #         'courses': [
    #             {k: v for k, v in course.items() if k != '_root_level_data'}
    #             for course in results
    #         ]
    #     }
    # else:
    #     # 如果没有课程数据，使用默认值
    #     output_data = {
    #         'deletion_time': None,
    #         'ai_assistant_enabled': False,
    #         'certificate_price': 0,
    #         'is_recommended_by_reader': False,
    #         'is_reader_subscribed': False,
    #         'pinned_by_reader': False,
    #         'is_published': True,
    #         'first_published_time': '',
    #         'last_published_time': '',
    #         'timestamp': datetime.datetime.now().isoformat(),
    #         'total_courses': 0,
    #         'price': 0,
    #         'skills': [],
    #         'tags': [],
    #         'aggregated_widget_stats': {},
    #         'url_slug': '',
    #         'authors': [],
    #         'read_time': 0,
    #         'learner_tags': [],
    #         'level_one_learner_tags': [],
    #         'courses': []
    #     }

    # json_output = json.dumps(output_data, ensure_ascii=False, indent=2)



    # 保存到文件
    # output_file = 'educative_courses.json'
    # try:
    #     with open(output_file, 'w', encoding='utf-8') as f:
    #         f.write(json_output)
    #     console.print(f"[green]✓ 数据已保存到: {output_file}[/green]")
    # except Exception as e:
    #     console.print(f"[red]保存文件失败: {str(e)}[/red]")

    # 同时输出到控制台
    # console.print("\n[bold]JSON输出:[/bold]")
    # rprint(json_output)


if __name__ == "__main__":
    main()