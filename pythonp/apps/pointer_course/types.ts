/**
 * Educative.io 课程数据类型定义
 *
 * 这个文件定义了从 get_cursor.py 脚本生成的 JSON 数据的 TypeScript 类型
 */

/**
 * 页面摘要信息接口
 */
export interface PageSummary {
  /** 标题是否已更新 */
  titleUpdated: boolean;

  /** 页面标题 */
  title: string;

  /** 页面描述 */
  description: string;

  /** 页面标签 */
  tags: string[];
}

/**
 * 页面内容接口
 */
export interface Page {
  /** 页面唯一标识符 */
  id: string;

  /** 页面标题 */
  title: string;

  /** 是否为预览页面（免费访问） */
  is_preview: boolean;

  /** URL友好的页面标识符 */
  slug: string;

  /** 页面的原始文本内容（Markdown格式） */
  text: string;

  /** 页面内容转换为HTML格式 */
  mdHtml: string;

  /** 页面摘要信息 */
  summary: PageSummary;
}

/**
 * 分类接口
 */
export interface Category {
  /** 分类唯一标识符 */
  id: string;

  /** 分类标题 */
  title: string;

  /** 分类摘要描述 */
  summary: string;

  /** 该分类下的所有页面 */
  pages: Page[];
}

/**
 * 课程接口
 */
export interface Course {
  /** 课程标题 */
  title: string;

  /** 课程摘要 */
  summary: string;

  /** 课程简短摘要 */
  brief_summary: string;

  /** 课程学习目标 (Course Learning Objectives) */
  whatYouWillLearn: string;

  /** 目标受众 */
  target_audience: string;

  /** 课程封面图片URL */
  cover_image_url: string;

  /** 课程创建时间 */
  creation_time: string;

  /** 课程修改时间 */
  modified_time: string;

  /** 课程发布时间 */
  published_time: string;

  /** 课程分类列表 */
  categories: Category[];
}

/**
 * 根级别数据接口
 */
export interface EducativeCoursesData {
  /** 删除时间（如果已删除） */
  deletion_time: string | null;

  /** 是否启用AI助手 */
  ai_assistant_enabled: boolean;

  /** 证书价格 */
  certificate_price: number;

  /** 是否被读者推荐 */
  is_recommended_by_reader: boolean;

  /** 读者是否已订阅 */
  is_reader_subscribed: boolean;

  /** 是否被读者置顶 */
  pinned_by_reader: boolean;

  /** 是否已发布 */
  is_published: boolean;

  /** 首次发布时间 */
  first_published_time: string;

  /** 最后发布时间 */
  last_published_time: string;

  /** 数据获取时间戳（ISO 8601格式） */
  timestamp: string;

  /** 课程总数 */
  total_courses: number;

  /** 课程价格 */
  price: number;

  /** 技能标签列表 */
  skills: string[];

  /** 标签列表 */
  tags: string[];

  /** 聚合小部件统计信息 */
  aggregated_widget_stats: Record<string, any>;

  /** URL标识符 */
  url_slug: string;

  /** 作者列表 */
  authors: string[];

  /** 阅读时间（分钟） */
  read_time: number;

  /** 学习者标签 */
  learner_tags: string[];

  /** 一级学习者标签 */
  level_one_learner_tags: string[];

  /** 课程列表 */
  courses: Course[];
}

/**
 * 搜索结果接口
 */
export interface SearchResult {
  /** 匹配的页面 */
  page: Page;

  /** 所属分类 */
  category: Category;

  /** 所属课程 */
  course: Course;

  /** 匹配的文本片段 */
  matchedText?: string;
}

/**
 * 课程统计信息接口
 */
export interface CourseStats {
  /** 课程标题 */
  courseTitle: string;

  /** 分类数量 */
  categoryCount: number;

  /** 总页面数 */
  totalPages: number;

  /** 预览页面数 */
  previewPages: number;

  /** 付费页面数 */
  premiumPages: number;

  /** 总文本长度 */
  totalTextLength: number;
}

/**
 * 工具函数类型定义
 */
export namespace EducativeUtils {
  /**
   * 搜索选项
   */
  export interface SearchOptions {
    /** 是否区分大小写 */
    caseSensitive?: boolean;

    /** 是否只搜索标题 */
    titleOnly?: boolean;

    /** 是否只搜索预览页面 */
    previewOnly?: boolean;

    /** 最大结果数量 */
    maxResults?: number;
  }

  /**
   * 导出选项
   */
  export interface ExportOptions {
    /** 导出格式 */
    format: "json" | "markdown" | "html" | "txt";

    /** 是否包含HTML内容 */
    includeHtml?: boolean;

    /** 是否只导出预览内容 */
    previewOnly?: boolean;

    /** 文件名前缀 */
    filePrefix?: string;
  }
}

/**
 * API响应类型（用于扩展）
 */
export interface ApiResponse<T = any> {
  /** 是否成功 */
  success: boolean;

  /** 响应数据 */
  data?: T;

  /** 错误信息 */
  error?: string;

  /** 响应时间戳 */
  timestamp: string;
}

/**
 * 课程元数据（扩展信息）
 */
export interface CourseMetadata {
  /** 课程URL标识符 */
  slug: string;

  /** 课程难度级别 */
  level?: "beginner" | "intermediate" | "advanced";

  /** 预计完成时间（小时） */
  EstimatedTimes?: number;

  /** 课程标签 */
  tags?: string[];

  /** 最后更新时间 */
  lastUpdated?: string;

  /** 课程评分 */
  rating?: number;

  /** 学习人数 */
  enrollmentCount?: number;
}
