{"deletion_time": null, "ai_assistant_enabled": false, "certificate_price": 19.0, "is_recommended_by_reader": false, "is_reader_subscribed": false, "pinned_by_reader": false, "is_published": true, "first_published_time": "2019-01-08T12:58:06.167553", "last_published_time": "2025-06-26T05:31:31.699526", "timestamp": "2025-08-22T15:32:47.250763", "total_courses": 1, "price": 79.0, "skills": [], "tags": ["Python programming", "Datatypes in Python", "Lists in Python", "Fucntions in Python", "Loops in Python"], "aggregated_widget_stats": {"MarkdownEditor": 367, "codeExerciseCount": 45, "codeRunnableCount": 117, "codeSnippetCount": 54, "illustrations": 276, "Code": 172, "TabbedCode": 3, "Matrix": 1, "MxGraphWidget": 21, "CanvasAnimation": 49, "Quiz": 7, "Columns": 3, "DrawIOWidget": 1}, "url_slug": "full-speed-python", "authors": [], "read_time": 32400.0, "learner_tags": ["become-a-developer", "python", "programming-language"], "level_one_learner_tags": ["become-a-developer", "python", "programming-language"], "courses": [{"title": "Full Speed Python", "summary": "Python is one of the most popular coding languages today; it's categorized as a vital benchmark of computer science knowledge in industry interviews. This highly interactive course is an accelerated introduction to Python. It is intended for users who are already familiar with the fundamentals of programming and aims to teach the Python programming language using a practical approach. It not only covers the basic Python syntax but also teaches methods specific to Python3. With most companies already switching from Python2 to Python3, this version of Python is the future.\n\nBefore moving on to more complex and powerful tools, we’ll examine the fundamentals of the language. You can also experiment with the code provided and, therefore, gain a higher understanding of how things work. This course is perfect for anyone who works as a Python developer and wants to recognize its full potential. Happy learning!", "brief_summary": "Accelerate your Python learning! Explore Python 3 syntax and methods with interactive lessons made for experienced programmers to sharpen coding skills.", "whatYouWillLearn": [], "target_audience": "beginner", "cover_image_url": "", "creation_time": "2025-06-26T05:12:12.376180", "modified_time": "2025-08-22T00:14:38.682741", "published_time": "2025-06-26T05:31:31.699526", "categories": [{"id": "6<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "Basic Data Types", "summary": "Sharpen your skills in Python's basic data types, arithmetic, parity checks, and string operations.", "pages": [{"id": "5742179444064256", "title": "Numbers", "is_preview": false, "slug": "numbers", "text": "## Numbers\n\n Start your Python [REPL](https://en.wikipedia.org/wiki/Read%E2%80%93eval%E2%80%93print_loop) and write the following:\n\n### Print the Data Type\nTo print the number data type, use the built-in function `type(number)`:  \n\n```python310\na = 2\nprint(type(a)) #print the data type of variable a\n\nb = 2.5\nprint(type(b)) # prints data type of variable b\n```\n\n\nBasically, you are declaring two variables (named `a` and `b`) which will hold some numbers; variable `a` is an integer number `<type 'int'>` and variable `b` is a real number `<type 'float'>`.\n### Basic Operations on Numbers\nWe can now use our variables or any other numbers to do some calculations:\n\n```python310\na=2\nb=2.5\nprint(a + b)  \nprint ((a + b) * 2)\nprint(2 + 2 + 4 - 2/3)\n```\n\nNow that the basics of the numbers are clear, let’s check your knowledge in the upcoming exercises before moving on to the 'Strings'​ lesson.", "mdHtml": "<h1 id=\"numbers\">Numbers</h1>\n<p>Start your Python <a href=\"https://en.wikipedia.org/wiki/Read%E2%80%93eval%E2%80%93print_loop\">REPL</a> and write the following:</p>\n<h3 id=\"print-the-data-type\">Print the Data Type</h3>\n<p>To print the number data type, use the built-in function <code>type(number)</code>:</p>\n\n<div class='image-component'>\n```python310\na = 2\nprint(type(a)) #print the data type of variable a\n\nb = 2.5\nprint(type(b)) # prints data type of variable b\n```\n</div>\n<p>Basically, you are declaring two variables (named <code>a</code> and <code>b</code>) which will hold some numbers; variable <code>a</code> is an integer number <code>&lt;type 'int'&gt;</code> and variable <code>b</code> is a real number <code>&lt;type 'float'&gt;</code>.</p>\n<h3 id=\"basic-operations-on-numbers\">Basic Operations on Numbers</h3>\n<p>We can now use our variables or any other numbers to do some calculations:</p>\n\n<div class='image-component'>\n```python310\na=2\nb=2.5\nprint(a + b)  \nprint ((a + b) * 2)\nprint(2 + 2 + 4 - 2/3)\n```\n</div>\n<p>Now that the basics of the numbers are clear, let’s check your knowledge in the upcoming exercises before moving on to the 'Strings’​ lesson.</p>", "summary": {"titleUpdated": true, "title": "Numbers & Strings", "description": "In this chapter, we will work with the most basic data type: numbers.", "tags": []}, "type": "Lesson"}, {"id": "5698218306306048", "title": "Challenge 1: Mathematical Calculations", "is_preview": false, "slug": "challenge-1-mathematical-calculations", "text": "# Problem Statement\nGiven a `MathOp()` function, try the following mathematical calculations and print the output:\n\n\n```python\n(3 / 2)\n(3 // 2)\n(3 % 2)\n(3**2)\n```\n## Output\nThe expected output:\n\n```python\n1.5\n1\n1\n9\n```\n\n# Coding Exercise\n**Suggestion:** Check the [Python library reference](https://docs.python.org/3/library/stdtypes.html#numeric-types-int-float-complex) for more details!\n\nPlay with the following code to print the expected output.\nLet's move on to the detailed solution of the above problem.", "mdHtml": "<h2 id=\"problem-statement\">Problem Statement</h2>\n<p>Given a <code>MathOp()</code> function, try the following mathematical calculations and print the output:</p>\n<pre><code class=\"language-python\">(3 / 2)\n(3 // 2)\n(3 % 2)\n(3**2)\n</code></pre>\n<h3 id=\"output\">Output</h3>\n<p>The expected output:</p>\n\n<div class='image-component'>\n```python\n1.5\n1\n1\n9\n```\n</div>\n<h2 id=\"coding-exercise\">Coding Exercise</h2>\n<p><strong>Suggestion:</strong> Check the <a href=\"https://docs.python.org/3/library/stdtypes.html#numeric-types-int-float-complex\">Python library reference</a> for more details!</p>\n<p>Play with the following code to print the expected output.</p>\n\n<p>Let’s move on to the detailed solution of the above problem.</p>", "summary": {"titleUpdated": true, "title": "Challenge 1: Mathematical Calculations", "description": "This challenge will test you on mathematical operations in python.", "tags": []}, "type": "Lesson"}, {"id": "6271696164093952", "title": "Solution Review: Mathematical Calculations", "is_preview": false, "slug": "solution-review-mathematical-calculations", "text": "## Solution: Use Basic Mathematical Operators\n\nThis is a fairly straightforward depiction of how mathematical calculations in Python are done. \n\nA brief description of the previous operations’ functions is given below.\nHave a look at the following Python code.\n\n```python310\ndef MathOp():\n  \n  classic_division = 3/2 ## Calculate 3/2 here\n  floor_division = 3//2 ## Calculate 3//2 here\n  modulus = 3%2 ## Calculate 3%2 here \n  power = 3**2 ## Calculate 3**2 here\n  ## Returning the calculations for evaluation\n  return [classic_division, floor_division, modulus, power]\n\n[classic_division, floor_division, modulus, power]=MathOp()\nprint(classic_division)\nprint(floor_division)\nprint(modulus)\nprint(power)\n```\n\nTry these calculations on your own to make sure each operator is performing its intended functionality. \n\nNow that you know how to perform basic arithmetic operations in Python, let's move on to solving other problems involving these operations!", "mdHtml": "<h2 id=\"solution-use-basic-mathematical-operators\">Solution: Use Basic Mathematical Operators</h2>\n<p>This is a fairly straightforward depiction of how mathematical calculations in Python are done.</p>\n<p>A brief description of the previous operations’ functions is given below.</p>\n\n<p>Have a look at the following Python code.</p>\n\n<div class='image-component'>\n```python310\ndef MathOp():\n  \n  classic_division = 3/2 ## Calculate 3/2 here\n  floor_division = 3//2 ## Calculate 3//2 here\n  modulus = 3%2 ## Calculate 3%2 here \n  power = 3**2 ## Calculate 3**2 here\n  ## Returning the calculations for evaluation\n  return [classic_division, floor_division, modulus, power]\n\n[classic_division, floor_division, modulus, power]=MathOp()\nprint(classic_division)\nprint(floor_division)\nprint(modulus)\nprint(power)\n```\n</div>\n<p>Try these calculations on your own to make sure each operator is performing its intended functionality.</p>\n<p>Now that you know how to perform basic arithmetic operations in Python, let’s move on to solving other problems involving these operations!</p>", "summary": {"titleUpdated": true, "title": "", "description": "This review provides a detailed review of mathematical operations in python. ", "tags": []}, "type": "Lesson"}, {"id": "5098640871784448", "title": "Challenge 2: Check Parity of a Number", "is_preview": false, "slug": "challenge-2-check-parity-of-a-number", "text": "Parity is a term to express if a given integer is even or odd. \n\n## Problem Statement\n\nGiven a `checkParity(n)` function, write code to determine if a given number `n` is **even** or **odd**. Think of this as a function that returns 0 if the number is even, and 1 if it is odd. You have been given some starter code where the function and return statement have already been written, so don't worry about any Python-specific details about functions; just implement the function logic!\n\n### Input\nA number\n\n### Output\nThe parity of the number\n\n### Sample Input\n4\n\n### Sample Output\n0\n\n\n## Coding Exercise\nCheck the [Python library reference](https://docs.python.org/3/library/stdtypes.html#numeric-types-int-float-complex) for more details!\n\nWrite your code below. It is recommended​ that you try solving the exercise yourself before viewing the solution.\n\n\n\n\n```python310\ndef checkParity(n):\n  ## Write your code here\n  result = -1 ## Update result according to the parity\n  return result\n```\n\nLet's move on to the solution of how to calculate the parity of a number.", "mdHtml": "<p>Parity is a term to express if a given integer is even or odd.</p>\n<h2 id=\"problem-statement\">Problem Statement</h2>\n<p>Given a <code>checkParity(n)</code> function, write code to determine if a given number <code>n</code> is <strong>even</strong> or <strong>odd</strong>. Think of this as a function that returns 0 if the number is even, and 1 if it is odd. You have been given some starter code where the function and return statement have already been written, so don’t worry about any Python-specific details about functions; just implement the function logic!</p>\n<h3 id=\"input\">Input</h3>\n<p>A number</p>\n<h3 id=\"output\">Output</h3>\n<p>The parity of the number</p>\n<h3 id=\"sample-input\">Sample Input</h3>\n<p>4</p>\n<h3 id=\"sample-output\">Sample Output</h3>\n<p>0</p>\n<h2 id=\"coding-exercise\">Coding Exercise</h2>\n<p>Check the <a href=\"https://docs.python.org/3/library/stdtypes.html#numeric-types-int-float-complex\">Python library reference</a> for more details!</p>\n<p>Write your code below. It is recommended​ that you try solving the exercise yourself before viewing the solution.</p>\n\n<div class='image-component'>\n```python310\ndef checkParity(n):\n  ## Write your code here\n  result = -1 ## Update result according to the parity\n  return result\n```\n</div>\n<p>Let’s move on to the solution of how to calculate the parity of a number.</p>", "summary": {"titleUpdated": true, "title": "Challenge 2: Check Parity of a Number", "description": "In this challenge, you have to calculate the parity of a number.", "tags": []}, "type": "Lesson"}, {"id": "5664886004645888", "title": "Solution Review : Check Parity of a Number", "is_preview": false, "slug": "solution-review-check-parity-of-a-number", "text": "## Solution:​ Use Modulus`(%)` Operator \n\nThe solution isn't complicated. You simply check the **remainders** when even and odd numbers are divided by 2; all **even** numbers result in a **remainder of 0** and all **odd** numbers produce a **remainder of 1**. This is convenient because the problem requires us to return a 0 if a number is even, and a 1 if it is odd. \nTherefore, you will just use the `%` operator to obtain the remainder of n when divided by 2 and return the answer.\n\n\n<img src=\"http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755847691251701530.svg\" alt=\"\" width=\"600\" height=\"400\" style=\"width: 100%; height: auto;\" />\n<img src=\"http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755847693399683722.svg\" alt=\"\" width=\"600\" height=\"400\" style=\"width: 100%; height: auto;\" />\nTo check the parity of a number, write the following python code.\n\n```python310\ndef checkParity(n):\n  result = (n % 2)\n  return result\n  \nprint(\"Odd parity\", checkParity(17))\nprint(\"Even parity\", checkParity(16))\n```\n\nLet's move on to solve another problem using numbers.", "mdHtml": "<h2 id=\"solution-use-modulus-operator\">Solution:​ Use Modulus<code>(%)</code> Operator</h2>\n<p>The solution isn’t complicated. You simply check the <strong>remainders</strong> when even and odd numbers are divided by 2; all <strong>even</strong> numbers result in a <strong>remainder of 0</strong> and all <strong>odd</strong> numbers produce a <strong>remainder of 1</strong>. This is convenient because the problem requires us to return a 0 if a number is even, and a 1 if it is odd.\nTherefore, you will just use the <code>%</code> operator to obtain the remainder of n when divided by 2 and return the answer.</p>\n\n<div class='image-component'><img src=\"http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755847691251701530.svg\" alt=\"\" width=\"600\" height=\"400\" style=\"width: 100%; height: auto;\" /></div>\n<div class='image-component'><img src=\"http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755847693399683722.svg\" alt=\"\" width=\"600\" height=\"400\" style=\"width: 100%; height: auto;\" /></div>\n<p>To check the parity of a number, write the following python code.</p>\n\n<div class='image-component'>\n```python310\ndef checkParity(n):\n  result = (n % 2)\n  return result\n  \nprint(\"Odd parity\", checkParity(17))\nprint(\"Even parity\", checkParity(16))\n```\n</div>\n<p>Let’s move on to solve another problem using numbers.</p>", "summary": {"titleUpdated": true, "title": "", "description": "This lesson gives a detailed review of how to check the parity of a number.", "tags": []}, "type": "Lesson"}, {"id": "5197695165661184", "title": "Challenge 3: Find Values Within a Range", "is_preview": false, "slug": "challenge-3-find-values-within-a-range", "text": "## Problem Statement\nGiven an `inRange(x,y)` function, write a method that determines whether a given pair `(x, y)` falls in the range `(x < 1/3 < y)`.\nEssentially, you'll be implementing the body of a function that takes in two numbers `x` and `y` and returns `True` if x < 1/3 < y; otherwise, it returns `False`.\n\n### Input\nTwo numbers, x and y\n\n### Output\nTrue if x and y are in the range and False otherwise.\n\n### Sample Input\n x = 2, y = 3\n\n### Sample Output\nFalse\n\n\n## Coding Exercise\nCheck the [Python library reference](https://docs.python.org/3/library/stdtypes.html#numeric-types-int-float-complex) for more details!\n\nYou have been given a basic starter skeleton function in which you will need to implement this functionality. \n\n```python310\ndef inRange(x, y):\n  ## Write your code here\n  return ## Change this to return True or return False depending on your output\n```\n\nLet's move on to the solution of the above problem.", "mdHtml": "<h2>Problem Statement</h2>\n<p>Given an <code>inRange(x,y)</code> function, write a method that determines whether a given pair <code>(x, y)</code> falls in the range <code>(x &lt; 1/3 &lt; y)</code>.\nEssentially, you’ll be implementing the body of a function that takes in two numbers <code>x</code> and <code>y</code> and returns <code>True</code> if x &lt; 1/3 &lt; y; otherwise, it returns <code>False</code>.</p>\n<h3>Input</h3>\n<p>Two numbers, x and y</p>\n<h3>Output</h3>\n<p>True if x and y are in the range and False otherwise.</p>\n<h3>Sample Input</h3>\n<p>x = 2, y = 3</p>\n<h3>Sample Output</h3>\n<p>False</p>\n<h2>Coding Exercise</h2>\n<p>Check the <a href=\"https://docs.python.org/3/library/stdtypes.html#numeric-types-int-float-complex\">Python library reference</a> for more details!</p>\n<p>You have been given a basic starter skeleton function in which you will need to implement this functionality.</p>\n\n<div class='image-component'>\n```python310\ndef inRange(x, y):\n  ## Write your code here\n  return ## Change this to return True or return False depending on your output\n```\n</div>\n<p>Let’s move on to the solution of the above problem.</p>", "summary": {"titleUpdated": true, "title": "Challenge 3: Find Values Within a Range", "description": "In this challenge, find if values x and y are within the range.", "tags": []}, "type": "Lesson"}, {"id": "5165346948382720", "title": "Solution Review: Find Values Within a Range", "is_preview": false, "slug": "solution-review-find-values-within-a-range", "text": "## Solution: Use Relational Operator \n\nRelational operators such as `<` return either `True` or `False` and the solution, therefore, becomes extremely straightforward. We can simply return whatever the `x < 1/3 < y` statement yields.\n\nThe following python code helps solve the above problem.\n\n```python310\ndef inRange(x, y):\n  return (x < 1/3 < y)\n\nprint(inRange(-1, 3))\nprint(inRange(2, 3))\n```\n\nNow that you have solved exercises using numbers, let's solve exercises using strings.", "mdHtml": "<h2 id=\"solution-use-relational-operator\">Solution: Use Relational Operator</h2>\n<p>Relational operators such as <code>&lt;</code> return either <code>True</code> or <code>False</code> and the solution, therefore, becomes extremely straightforward. We can simply return whatever the <code>x &lt; 1/3 &lt; y</code> statement yields.</p>\n<p>The following python code helps solve the above problem.</p>\n\n<div class='image-component'>\n```python310\ndef inRange(x, y):\n  return (x < 1/3 < y)\n\nprint(inRange(-1, 3))\nprint(inRange(2, 3))\n```\n</div>\n<p>Now that you have solved exercises using numbers, let’s solve exercises using strings.</p>", "summary": {"titleUpdated": true, "title": "", "description": "This lesson gives a detailed review of how to find values within a range.", "tags": []}, "type": "Lesson"}, {"id": "5118445905510400", "title": "Strings", "is_preview": false, "slug": "strings", "text": "## What are Strings?\nPython also has support for string datatypes.\nStrings are sequences of characters (like words).\n\nThere are number of operations that can be performed using strings. However, some basic operations are mentioned below:\n\n### Initialize a String\nA string can be defined using single or double quotes:\n\n```python310\nhi = \"hello\" # string within double quotes\nprint(hi)\n\nbye = 'goodbye' # string within single quote\nprint(bye)\n```\n\n### String Concatenation\n\nYou can concatenate strings by adding them using the `+` operator, but you can't mix different data types​ (e.g. strings and numbers).\n\n```python310\nhi = \"hello\"\nhi += \"world\"\nprint(hi)\n```\n\n> **Note:** Strings cannot be​ concatenated with numbers.\n\nThe following code gives an error, ❌, because a string is concatenated with a number.\n\n```python310\nhi = \"hello\"\nhi += 4\nprint(hi)\n```\n\n\nHowever, multiplication works as repetition.\nIf a string is multiplied by a number 'n' then that string is repeated n times.\n\n```python310\nprint(\"Hello\" * 3)\n```\n\nNow that you have learned the basics of strings, let's test your knowledge in the upcoming exercises.", "mdHtml": "<h2 id=\"what-are-strings\">What are Strings?</h2>\n<p>Python also has support for string datatypes.\nStrings are sequences of characters (like words).</p>\n<p>There are number of operations that can be performed using strings. However, some basic operations are mentioned below:</p>\n<h3 id=\"initialize-a-string\">Initialize a String</h3>\n<p>A string can be defined using single or double quotes:</p>\n\n<div class='image-component'>\n```python310\nhi = \"hello\" # string within double quotes\nprint(hi)\n\nbye = 'goodbye' # string within single quote\nprint(bye)\n```\n</div>\n<h3 id=\"string-concatenation\">String Concatenation</h3>\n<p>You can concatenate strings by adding them using the <code>+</code> operator, but you can’t mix different data types​ (e.g. strings and numbers).</p>\n\n<div class='image-component'>\n```python310\nhi = \"hello\"\nhi += \"world\"\nprint(hi)\n```\n</div>\n<blockquote>\n<p><strong>Note:</strong> Strings cannot be​ concatenated with numbers.</p>\n</blockquote>\n<p>The following code gives an error, ❌, because a string is concatenated with a number.</p>\n\n<div class='image-component'>\n```python310\nhi = \"hello\"\nhi += 4\nprint(hi)\n```\n</div>\n<p>However, multiplication works as repetition.\nIf a string is multiplied by a number ‘n’ then that string is repeated n times.</p>\n\n<div class='image-component'>\n```python310\nprint(\"Hello\" * 3)\n```\n</div>\n<p>Now that you have learned the basics of strings, let’s test your knowledge in the upcoming exercises.</p>", "summary": {"titleUpdated": true, "title": "", "description": "Now that you have learned numbers, let's dicuss the strings.", "tags": []}, "type": "Lesson"}, {"id": "5659043372728320", "title": "Challenge 4: String Transformation", "is_preview": false, "slug": "challenge-4-string-transformation", "text": "## Problem Statement\nGiven a `getStr()` function, write the necessary sequence of operations to transform the string (containing three literals) in such a way that every literal is tripled​ respectively.\n\n### Input\nA string\n\n### Output\nTriple of every string literal\n\n### Sample Input\nabc\n\n### Sample Output\naaabbbccc\n\n\n\n\n**[CanvasAnimation 图表]** - 交互式图表内容\n\n## Coding Exercise\n\nThe [Python documentation on strings](<https://docs.python.org/3/library/stdtypes.html?#text-sequence-type-str>) may be helpful. \n\nYou have been provided with some basic starter code to write the solution in, as well as a return statement that returns the required string.\n\n```python310\ndef getStr(s):\n  ## Write your code here\n  ## Transform the string\n  ## Update length of string\n  strlen = 0\n  return [s, strlen]\n```\n\nNow, let's move on to the solution of the above problem.", "mdHtml": "<h2 id=\"problem-statement\">Problem Statement</h2>\n<p>Given a <code>getStr()</code> function, write the necessary sequence of operations to transform the string (containing three literals) in such a way that every literal is tripled​ respectively.</p>\n<h3 id=\"input\">Input</h3>\n<p>A string</p>\n<h3 id=\"output\">Output</h3>\n<p>Triple of every string literal</p>\n<h3 id=\"sample-input\">Sample Input</h3>\n<p>abc</p>\n<h3 id=\"sample-output\">Sample Output</h3>\n<p>aaabbbccc</p>\n\n<div class='image-component'>\n**[CanvasAnimation 图表]** - 交互式图表内容\n</div>\n<h2 id=\"coding-exercise\">Coding Exercise</h2>\n<p>The <a href=\"https://docs.python.org/3/library/stdtypes.html?#text-sequence-type-str\">Python documentation on strings</a> may be helpful.</p>\n<p>You have been provided with some basic starter code to write the solution in, as well as a return statement that returns the required string.</p>\n\n<div class='image-component'>\n```python310\ndef getStr(s):\n  ## Write your code here\n  ## Transform the string\n  ## Update length of string\n  strlen = 0\n  return [s, strlen]\n```\n</div>\n<p>Now, let’s move on to the solution of the above problem.</p>", "summary": {"titleUpdated": true, "title": "Challenge 4: String Transformation", "description": "This challenge will test your knowledge on string transformation using python.", "tags": []}, "type": "Lesson"}, {"id": "5723235282845696", "title": "Solution Review: String Transformation", "is_preview": false, "slug": "solution-review-string-transformation", "text": "## Solution: Use `len()` and `concatenation(+)` Operation\n* Use `len(str)` to calculate the length of string `str`\n\n* Concatenate value at a certain position in the string using the concatenation operation\n\nGiven a string `'str'`, use the following piece of code to transform the string\n\n````str = str[:position] + character_to_insert + str[position:]````\n\nThe character needs to be inserted where the position is in the code.\n\nHave a look at the following illustration to get an insight on how to update the length of a string using concatenation operation.\nThe following python code shows how to transform the string.\n\n```python310\ndef getStr(s):\n  s=s[:1] + s[0] + s[1:]# Transform the string \n  s=s[:1] + s[0] + s[1:]\n  s=s[:3] + s[3] + s[3:]\n  s=s[:3] + s[3] + s[3:]\n  s=s[:6] + s[6] + s[6:]\n  s=s[:6] + s[6] + s[6:]\n  # Update the length of string\n  strlen = len(s)\n  return [s, strlen]\n\nprint(getStr(\"abc\"))\nprint(getStr(\"xyz\"))\n```\n\nLet's solve another problem using strings.", "mdHtml": "<h2 id=\"solution-use-len-and-concatenation-operation\">Solution: Use <code>len()</code> and <code>concatenation(+)</code> Operation</h2>\n<ul>\n<li>\n<p>Use <code>len(str)</code> to calculate the length of string <code>str</code></p>\n</li>\n<li>\n<p>Concatenate value at a certain position in the string using the concatenation operation</p>\n</li>\n</ul>\n<p>Given a string <code>'str'</code>, use the following piece of code to transform the string</p>\n<p><code>str = str[:position] + character_to_insert + str[position:]</code></p>\n<p>The character needs to be inserted where the position is in the code.</p>\n<p>Have a look at the following illustration to get an insight on how to update the length of a string using concatenation operation.</p>\n\n<p>The following python code shows how to transform the string.</p>\n\n<div class='image-component'>\n```python310\ndef getStr(s):\n  s=s[:1] + s[0] + s[1:]# Transform the string \n  s=s[:1] + s[0] + s[1:]\n  s=s[:3] + s[3] + s[3:]\n  s=s[:3] + s[3] + s[3:]\n  s=s[:6] + s[6] + s[6:]\n  s=s[:6] + s[6] + s[6:]\n  # Update the length of string\n  strlen = len(s)\n  return [s, strlen]\n\nprint(getStr(\"abc\"))\nprint(getStr(\"xyz\"))\n```\n</div>\n<p>Let’s solve another problem using strings.</p>", "summary": {"titleUpdated": true, "title": "", "description": "This lesson will give a detailed review of how to update a string.", "tags": []}, "type": "Lesson"}, {"id": "5730661952389120", "title": "Challenge 5: Find Index of a Specific Value in a String", "is_preview": false, "slug": "challenge-5-find-index-of-a-specific-value-in-a-string", "text": "## Problem Statement\nGiven a string, use a `findOccurence(s)` function that allows you to find the first occurrences of `\"b\"` and `\"ccc\"​` in the string.\n\n### Input\nA string\n\n### Output\nThe first occurrence of \"b\" and \"ccc\" in the string\n\n### Sample Input\naaabbbccc\n\n\n### Sample Output\n[3, 6]\n\n\n\n    \n\n**[CanvasAnimation 图表]** - 交互式图表内容\n\n## Coding Exercise\nUse the [Python documentation on strings](<https://docs.python.org/3/library/stdtypes.html?#text-sequence-type-str>) to solve the following exercise.\n\nWrite your code below. It is recommended​ that you try solving the exercise yourself before viewing the solution.\n\n```python310\ndef findOccurence(s):\n  # Write your code here\n  a = 0#find first occurrence of \"b\" in the string \n  b = 0#find first occurence  of \"ccc\" in the string\n  return [a, b]\n```\n\nLet's move on to the solution of the above problem.", "mdHtml": "<h2 id=\"problem-statement\">Problem Statement</h2>\n<p>Given a string, use a <code>findOccurence(s)</code> function that allows you to find the first occurrences of <code>&quot;b&quot;</code> and <code>&quot;ccc&quot;​</code> in the string.</p>\n<h3 id=\"input\">Input</h3>\n<p>A string</p>\n<h3 id=\"output\">Output</h3>\n<p>The first occurrence of “b” and “ccc” in the string</p>\n<h3 id=\"sample-input\">Sample Input</h3>\n<p>aaabbbccc</p>\n<h3 id=\"sample-output\">Sample Output</h3>\n<p>[3, 6]</p>\n\n<div class='image-component'>\n**[CanvasAnimation 图表]** - 交互式图表内容\n</div>\n<h2 id=\"coding-exercise\">Coding Exercise</h2>\n<p>Use the <a href=\"https://docs.python.org/3/library/stdtypes.html?#text-sequence-type-str\">Python documentation on strings</a> to solve the following exercise.</p>\n<p>Write your code below. It is recommended​ that you try solving the exercise yourself before viewing the solution.</p>\n\n<div class='image-component'>\n```python310\ndef findOccurence(s):\n  # Write your code here\n  a = 0#find first occurrence of \"b\" in the string \n  b = 0#find first occurence  of \"ccc\" in the string\n  return [a, b]\n```\n</div>\n<p>Let’s move on to the solution of the above problem.</p>", "summary": {"titleUpdated": true, "title": "Challenge 5: Find an index of a specific value in String", "description": "In this challenge, find the index of the desired value in a string.", "tags": []}, "type": "Lesson"}, {"id": "5684340059013120", "title": "Solution Review: Find Index of a Specific Value in a String", "is_preview": false, "slug": "solution-review-find-index-of-a-specific-value-in-a-string", "text": "## Solution: Use `find()`\nUse the built-in string function `string.find(character)` to find the index of a specific character in a string; this returns the index of the input character or string\n\n\n\nFor example:\n\nGiven \n```python\nstr = \"Python Programming\"\ncharacter = \"y\"\nstr.find(character)\n\nOutput:1\n```\nThe following Python code demonstrates how to find the index of a specific value in a string.\n\n```python310\ndef findOccurence(s):\n  a = s.find(\"b\")#find first occurrence of \"b\" in the string \n  b = s.find(\"ccc\")#find first occurence  of \"ccc\" in the string\n  return [a, b]\n\nstr = \"aaabbccc\"\nprint(findOccurence(str))\n```\n\nNow, let's move on to the next challenge.", "mdHtml": "<h2 id=\"solution-use-find\">Solution: Use <code>find()</code></h2>\n<p>Use the built-in string function <code>string.find(character)</code> to find the index of a specific character in a string; this returns the index of the input character or string</p>\n<p>For example:</p>\n<p>Given</p>\n<pre><code class=\"language-python\">str = &quot;Python Programming&quot;\ncharacter = &quot;y&quot;\nstr.find(character)\n\nOutput:1\n</code></pre>\n<p>The following Python code demonstrates how to find the index of a specific value in a string.</p>\n\n<div class='image-component'>\n```python310\ndef findOccurence(s):\n  a = s.find(\"b\")#find first occurrence of \"b\" in the string \n  b = s.find(\"ccc\")#find first occurence  of \"ccc\" in the string\n  return [a, b]\n\nstr = \"aaabbccc\"\nprint(findOccurence(str))\n```\n</div>\n<p>Now, let’s move on to the next challenge.</p>", "summary": {"titleUpdated": true, "title": "", "description": "This lesson gives a detailed review on how to find the index of a specific value in a string.", "tags": []}, "type": "Lesson"}, {"id": "5691766728556544", "title": "Challenge 6: Lowercase to Uppercase", "is_preview": true, "slug": "challenge-6-lowercase-to-uppercase", "text": "## Problem Statement\nGiven a function `changeCase(s)`, the task is to convert the strings from upper case to lower case.\n\n### Input\nA string in upper case\n\n### Output\nChange case of the string in lower case\n\n### Sample Input\n\"AAA BBB CCC\"\n\n### Sample Output\n\"aaa bbb ccc\"\n\n   \n\n\n\n**[CanvasAnimation 图表]** - 交互式图表内容\n\n## Coding Exercise\nUse the [Python documentation on strings](<https://docs.python.org/3/library/stdtypes.html?#text-sequence-type-str>) to solve the following exercise.\n\nWrite your code below. It is recommended​ that you try solving the exercise yourself before viewing the solution.\n\n```python310\ndef changeCase(s):\n      # Write your code here\n  a = 0 # convert string to \"AAA BBB CCC\"\n  b = 0 # convert string to \"aaa bbb ccc\"\n  return [a, b]\n```\n\nFind the solution of the above problem in the next lesson.", "mdHtml": "<h2 id=\"problem-statement\">Problem Statement</h2>\n<p>Given a function <code>changeCase(s)</code>, the task is to convert the strings from upper case to lower case.</p>\n<h3 id=\"input\">Input</h3>\n<p>A string in upper case</p>\n<h3 id=\"output\">Output</h3>\n<p>Change case of the string in lower case</p>\n<h3 id=\"sample-input\">Sample Input</h3>\n<p>“AAA BBB CCC”</p>\n<h3 id=\"sample-output\">Sample Output</h3>\n<p>“aaa bbb ccc”</p>\n\n<div class='image-component'>\n**[CanvasAnimation 图表]** - 交互式图表内容\n</div>\n<h2 id=\"coding-exercise\">Coding Exercise</h2>\n<p>Use the <a href=\"https://docs.python.org/3/library/stdtypes.html?#text-sequence-type-str\">Python documentation on strings</a> to solve the following exercise.</p>\n<p>Write your code below. It is recommended​ that you try solving the exercise yourself before viewing the solution.</p>\n\n<div class='image-component'>\n```python310\ndef changeCase(s):\n      # Write your code here\n  a = 0 # convert string to \"AAA BBB CCC\"\n  b = 0 # convert string to \"aaa bbb ccc\"\n  return [a, b]\n```\n</div>\n<p>Find the solution of the above problem in the next lesson.</p>", "summary": {"titleUpdated": true, "title": "Challenge 6: Lowercase to Uppercase", "description": "This challenge tests your knowledge of strings. Your task is to change the letter case of a string.", "tags": []}, "type": "Lesson"}, {"id": "5759450447085568", "title": "Solution Review: Lower To Uppercase", "is_preview": false, "slug": "solution-review-lower-to-uppercase", "text": "## Solution: Use `str.upper()` and `str.lower()`  \nGiven a string \"str\", use `str.upper()` to convert the string 'str' to **upper case**, and use `str.lower()` to convert string 'str' to **lower case**. \n\nFor example,\n\nGiven a string\n```python\nstr = \"AAA bbb CCC\"\na = str.upper()\nb = str.lower()\n\nOutput: \nAAA BBB CCC\naaa bbb ccc\n```\nThe following python code demonstrates how to change the letter case of a string.\n\n```python310\ndef changeCase(s):\n  a = s.upper()  # convert string to \"AAA BBB CCC\"\n  b = s.lower()  # convert string to \"aaa bbb ccc\"\n  return [a, b]\n\nstr = \"AAA bbb CCC\"\nprint(changeCase(str))\n```\n\nNow that you have solved challenges on  numbers and strings, let's move on to the quiz.", "mdHtml": "<h2 id=\"solution-use-strupper-and-strlower\">Solution: Use <code>str.upper()</code> and <code>str.lower()</code></h2>\n<p>Given a string “str”, use <code>str.upper()</code> to convert the string ‘str’ to <strong>upper case</strong>, and use <code>str.lower()</code> to convert string ‘str’ to <strong>lower case</strong>.</p>\n<p>For example,</p>\n<p>Given a string</p>\n<pre><code class=\"language-python\">str = &quot;AAA bbb CCC&quot;\na = str.upper()\nb = str.lower()\n\nOutput: \nAAA BBB CCC\naaa bbb ccc\n</code></pre>\n<p>The following python code demonstrates how to change the letter case of a string.</p>\n\n<div class='image-component'>\n```python310\ndef changeCase(s):\n  a = s.upper()  # convert string to \"AAA BBB CCC\"\n  b = s.lower()  # convert string to \"aaa bbb ccc\"\n  return [a, b]\n\nstr = \"AAA bbb CCC\"\nprint(changeCase(str))\n```\n</div>\n<p>Now that you have solved challenges on  numbers and strings, let’s move on to the quiz.</p>", "summary": {"titleUpdated": true, "title": "", "description": "This lesson will help you change the case of the string using a built-in function.", "tags": []}, "type": "Lesson"}, {"id": "5184225074479104", "title": "Quick Quiz on Basic Data Types", "is_preview": false, "slug": "quick-quiz-on-basic-data-types", "text": "Now that you have learned about the basic data types, what if you want to group elements? Let's learn about \"Lists\" in the next chapter.", "mdHtml": "<p>Now that you have learned about the basic data types, what if you want to group elements? Let’s learn about “Lists” in the next chapter.</p>", "summary": {"titleUpdated": true, "title": "Quick quiz on basic data types!", "description": "", "tags": []}, "type": "Quiz", "questions": [{"questionText": "What is the output of the following code\n\n`print(\"Hello World\" + \"Hello World\" + \"Hello World\" * 2)`\n\t\t", "questionOptions": [{"text": "Hello WorldHello WorldHello WorldHello World", "correct": true, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>Hello WorldHello WorldHello WorldHello World</p>\n"}, {"text": "Hello WorldHello WorldHello World", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>Hello WorldHello WorldHello World</p>\n"}, {"text": "Hello WorldHello WorldHello WorldHello WorldHello World", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>Hello WorldHello WorldHello WorldHello WorldHello World</p>\n"}, {"text": "Hello WorldHello WorldHello World", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>Hello WorldHello WorldHello World</p>\n"}], "questionTextHtml": "<p>What is the output of the following code</p>\n<p><code>print(&quot;Hello World&quot; + &quot;Hello World&quot; + &quot;Hello World&quot; * 2)</code></p>\n"}, {"questionText": "How can you calculate the length of a string `str` in python?\n", "questionOptions": [{"text": "str.length()", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>str.length()</p>\n"}, {"text": "len.str()", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>len.str()</p>\n"}, {"text": "len(str)", "correct": true, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>len(str)</p>\n"}, {"text": "str.len()", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>str.len()</p>\n"}], "questionTextHtml": "<p>How can you calculate the length of a string <code>str</code> in python?</p>\n"}]}]}, {"id": "hxksx4le4", "title": "Lists", "summary": "Get started with Python lists, slicing, appending, list comprehensions, and solving related challenges.", "pages": [{"id": "5674583101276160", "title": "Lists", "is_preview": false, "slug": "lists", "text": "## Lists\n\nPython lists are data structures that group sequences of elements. Lists **can have elements of several types**, and you can also **mix different types** within the same list (although all elements are usually of the same datatype).\n\nLists are created using square brackets  `[]`, and the elements are separated by commas (`,`). The elements in a list can be accessed by their positions, starting with 0 as the index of the first element.\n```\nlist=[element1,element2,...]\n```\nFor example, to create list `l` with five elements, write\n```\nl=[1,2,3,4,5]\n```\n\n**[CanvasAnimation 图表]** - 交互式图表内容\n\n\n```python310\nl = [1, 2, 3, 4, 5]\nprint(l)    #prints the entire list\nprint(l[0]) #prints value at index 0\nprint(l[1]) #prints value at index 1\n```\n\n\n## Sublist\nSometimes you want just a small portion of a list---a sublist. Sublists are simply subsets of a list; they can be retrieved using a technique called *slicing*.\n\nA sublist is created by writing the list name, then separating the start and end indexes with a colon (: ) and enclosing them within square brackets ([]). \n```\nsublist=list[startindex:endindex]\n```\nGiven a list `l` ,to create sublist `l1` and `l2` write:\n```\nl=[1,2,3,4,5]\n\nl1=l[0:3]\nl2=l[3:5]\n```\n\n> **Note:** The start index is inclusive and end index is exclusive in the range.\nWrite the following python code to print the substring.\n\n```python310\nl = ['a', 'b', 'c', 'd', 'e']\nprint(l[0:3])\n```\n\n## Operations on List\nSome basic operations on the lists are explained below:-\n\n### List Concatenation \nLists can be concatenated using the ````+```` operator\n\n**[CanvasAnimation 图表]** - 交互式图表内容\n\nIn python,write the following piece of code to concatenate the list:\n\n```python310\nprint([1,2] + [3,4])\n```\n\n### Traverse a List\nFinally, lists can be iterated over using `for` loops in Python.\nUse the following piece of code in python to traverse a list element-by-element:\n\n```python310\nl=[1, 2, 4, 8, 10]\nfor val in l:\n  print(val)\n```\n\nNow that the basics about the lists and sublists are clear, let’s check your knowledge in the upcoming exercises for lists before moving on to the next lesson---'List comprehension'.", "mdHtml": "<h2 id=\"lists\">Lists</h2>\n<p>Python lists are data structures that group sequences of elements. Lists <strong>can have elements of several types</strong>, and you can also <strong>mix different types</strong> within the same list (although all elements are usually of the same datatype).</p>\n<p>Lists are created using square brackets  <code>[]</code>, and the elements are separated by commas (<code>,</code>). The elements in a list can be accessed by their positions, starting with 0 as the index of the first element.</p>\n<pre><code>list=[element1,element2,...]\n</code></pre>\n<p>For example, to create list <code>l</code> with five elements, write</p>\n<pre><code>l=[1,2,3,4,5]\n</code></pre>\n\n<div class='image-component'>\n**[CanvasAnimation 图表]** - 交互式图表内容\n</div>\n<div class='image-component'>\n```python310\nl = [1, 2, 3, 4, 5]\nprint(l)    #prints the entire list\nprint(l[0]) #prints value at index 0\nprint(l[1]) #prints value at index 1\n```\n</div>\n<h2 id=\"sublist\">Sublist</h2>\n<p>Sometimes you want just a small portion of a list—a sublist. Sublists are simply subsets of a list; they can be retrieved using a technique called <em>slicing</em>.</p>\n<p>A sublist is created by writing the list name, then separating the start and end indexes with a colon (: ) and enclosing them within square brackets ([]).</p>\n<pre><code>sublist=list[startindex:endindex]\n</code></pre>\n<p>Given a list <code>l</code> ,to create sublist <code>l1</code> and <code>l2</code> write:</p>\n<pre><code>l=[1,2,3,4,5]\n\nl1=l[0:3]\nl2=l[3:5]\n</code></pre>\n<blockquote>\n<p><strong>Note:</strong> The start index is inclusive and end index is exclusive in the range.</p>\n</blockquote>\n\n<p>Write the following python code to print the substring.</p>\n\n<div class='image-component'>\n```python310\nl = ['a', 'b', 'c', 'd', 'e']\nprint(l[0:3])\n```\n</div>\n<h2 id=\"operations-on-list\">Operations on List</h2>\n<p>Some basic operations on the lists are explained below:-</p>\n<h3 id=\"list-concatenation\">List Concatenation</h3>\n<p>Lists can be concatenated using the <code>+</code> operator</p>\n\n<div class='image-component'>\n**[CanvasAnimation 图表]** - 交互式图表内容\n</div>\n<p>In python,write the following piece of code to concatenate the list:</p>\n\n<div class='image-component'>\n```python310\nprint([1,2] + [3,4])\n```\n</div>\n<h3 id=\"traverse-a-list\">Traverse a List</h3>\n<p>Finally, lists can be iterated over using <code>for</code> loops in Python.</p>\n\n<p>Use the following piece of code in python to traverse a list element-by-element:</p>\n\n<div class='image-component'>\n```python310\nl=[1, 2, 4, 8, 10]\nfor val in l:\n  print(val)\n```\n</div>\n<p>Now that the basics about the lists and sublists are clear, let’s check your knowledge in the upcoming exercises for lists before moving on to the next lesson—‘List comprehension’.</p>", "summary": {"titleUpdated": true, "title": "Lists", "description": "Now that we have finished working on numbers and strings, let's work with the lists and sublists.", "tags": []}, "type": "Lesson"}, {"id": "5743196814442496", "title": "Challenge 1: Sublist of a List", "is_preview": false, "slug": "challenge-1-sublist-of-a-list", "text": "## Problem Statement\nGiven a `getSublist()` function, create a list named `l`\n`[1, 4, 9, 10, 23]`. Using list slicing, get the sublists `[1, 4, 9]` and `[10, 23]`. \n\n### Input\nA list\n\n\n### Output\nTwo sublists\n\n\n\n\n**[CanvasAnimation 图表]** - 交互式图表内容\n\n## Coding Exercise\n**Suggestion:** Check out the [Python documentation on lists](https://docs.python.org/3.5/tutorial/introduction.html#lists) for more details!\n\nYou have been provided with some starter code and a return statement that allows you to return the sublists. Don't worry about these statements right now; we will be covering these in detail later.\n\n```python310\ndef getSubList():\n  l = [1, 4, 9, 10, 23]\n  l1 = 0\n  l2 = 0 ## Write your code here \n  return [l1, l2]  ## Alter the return statement to return your sublists\n```\n\nNow, let's look at the detailed solution of the above problem in the next lesson.", "mdHtml": "<h2 id=\"problem-statement\">Problem Statement</h2>\n<p>Given a <code>getSublist()</code> function, create a list named <code>l</code>\n<code>[1, 4, 9, 10, 23]</code>. Using list slicing, get the sublists <code>[1, 4, 9]</code> and <code>[10, 23]</code>.</p>\n<h3 id=\"input\">Input</h3>\n<p>A list</p>\n<h3 id=\"output\">Output</h3>\n<p>Two sublists</p>\n\n<div class='image-component'>\n**[CanvasAnimation 图表]** - 交互式图表内容\n</div>\n<h2 id=\"coding-exercise\">Coding Exercise</h2>\n<p><strong>Suggestion:</strong> Check out the <a href=\"https://docs.python.org/3.5/tutorial/introduction.html#lists\">Python documentation on lists</a> for more details!</p>\n<p>You have been provided with some starter code and a return statement that allows you to return the sublists. Don’t worry about these statements right now; we will be covering these in detail later.</p>\n\n<div class='image-component'>\n```python310\ndef getSubList():\n  l = [1, 4, 9, 10, 23]\n  l1 = 0\n  l2 = 0 ## Write your code here \n  return [l1, l2]  ## Alter the return statement to return your sublists\n```\n</div>\n<p>Now, let’s look at the detailed solution of the above problem in the next lesson.</p>", "summary": {"titleUpdated": true, "title": "Challenge 1: Sublist of a List", "description": "In this challenge, you are required to make a sublist from a given list.", "tags": []}, "type": "Lesson"}, {"id": "6305832564162560", "title": "Solution Review: Sublist of a List", "is_preview": false, "slug": "solution-review-sublist-of-a-list", "text": "## Solution:​ Use List Slicing\n\nThe solution is pretty straightforward; slice the list from the first index to the third index to obtain the first sublist, and then slice it from the third index to the end of the array to obtain the second sublist. This can be seen more clearly in the following illustration.\nList slicing is demonstrated in the following python code:\n\n```python310\ndef getSubList():\n  l = [1, 4, 9, 10, 23]\n  l1 = l[0:3] # sublist from index 0 to 3\n  l2 = l[3:] # sublist from 3 uptil end\n  return [l1, l2]\n[l1, l2] = getSubList()\nprint(l1)\nprint(l2)\n```\n\nLet's move on to the next problem.", "mdHtml": "<h2 id=\"solution-use-list-slicing\">Solution:​ Use List Slicing</h2>\n<p>The solution is pretty straightforward; slice the list from the first index to the third index to obtain the first sublist, and then slice it from the third index to the end of the array to obtain the second sublist. This can be seen more clearly in the following illustration.</p>\n\n<p>List slicing is demonstrated in the following python code:</p>\n\n<div class='image-component'>\n```python310\ndef getSubList():\n  l = [1, 4, 9, 10, 23]\n  l1 = l[0:3] # sublist from index 0 to 3\n  l2 = l[3:] # sublist from 3 uptil end\n  return [l1, l2]\n[l1, l2] = getSubList()\nprint(l1)\nprint(l2)\n```\n</div>\n<p>Let’s move on to the next problem.</p>", "summary": {"titleUpdated": true, "title": "", "description": "This lesson will explain how to get the sublist from a list.", "tags": []}, "type": "Lesson"}, {"id": "5651318874046464", "title": "Challenge 2: Appending Value to the End of a List", "is_preview": false, "slug": "challenge-2-appending-value-to-the-end-of-a-list", "text": "## Problem Statement\nGiven an `AppendtoList()` function, create a list named `l` with the following values:\n\n`[1, 4, 9, 10, 23]` \n\n and appends the number `90` at the end of the list.  \n### Input\nA list of numbers\n\n### Output\nAppend the value 90 to the end of the list `l`\n\n### Sample Input\n[1, 4, 9, 10, 23]\n\n### Sample Output\n[1, 4, 9, 10, 23, 90]\n \n\n\n\n**[CanvasAnimation 图表]** - 交互式图表内容\n\n## Coding Challenge\nYou have been provided with some starter code and a return statement that allows you to return the updated list.\n\n**Suggestion:** Check out the [Python documentation on lists](https://docs.python.org/3.5/tutorial/introduction.html#lists) for more details!\n\n```python310\ndef AppendtoList():\n  l = [1, 4, 9, 10, 23]\n  # Write your code here \n  return l\n```\n\nLet's move on to the next lesson to get the detailed review of the above problem.", "mdHtml": "<h2 id=\"problem-statement\">Problem Statement</h2>\n<p>Given an <code>AppendtoList()</code> function, create a list named <code>l</code> with the following values:</p>\n<p><code>[1, 4, 9, 10, 23]</code></p>\n<p>and appends the number <code>90</code> at the end of the list.</p>\n<h3 id=\"input\">Input</h3>\n<p>A list of numbers</p>\n<h3 id=\"output\">Output</h3>\n<p>Append the value 90 to the end of the list <code>l</code></p>\n<h3 id=\"sample-input\">Sample Input</h3>\n<p>[1, 4, 9, 10, 23]</p>\n<h3 id=\"sample-output\">Sample Output</h3>\n<p>[1, 4, 9, 10, 23, 90]</p>\n\n<div class='image-component'>\n**[CanvasAnimation 图表]** - 交互式图表内容\n</div>\n<h2 id=\"coding-challenge\">Coding Challenge</h2>\n<p>You have been provided with some starter code and a return statement that allows you to return the updated list.</p>\n<p><strong>Suggestion:</strong> Check out the <a href=\"https://docs.python.org/3.5/tutorial/introduction.html#lists\">Python documentation on lists</a> for more details!</p>\n\n<div class='image-component'>\n```python310\ndef AppendtoList():\n  l = [1, 4, 9, 10, 23]\n  # Write your code here \n  return l\n```\n</div>\n<p>Let’s move on to the next lesson to get the detailed review of the above problem.</p>", "summary": {"titleUpdated": true, "title": "Challenge 2: Appending value to the end of a list", "description": "In this challenge, you are required to append values to the list.", "tags": []}, "type": "Lesson"}, {"id": "5732392153120768", "title": "Solution Review: Appending Value to the End of a List", "is_preview": false, "slug": "solution-review-appending-value-to-the-end-of-a-list", "text": "## Solution 1: Use the `append()` Function\n\nThis solution is very simple and perhaps the more commonly used method in Python. You simply call the `append` function on your list, and your given arguments automatically get appended to your list automatically. This is what the process looks like: \nThis append operation can be demonstrated using the python code as shown below:\n\n```python310\ndef AppendtoList():\n  l = [1, 4, 9, 10, 23] \n  l.append(90)\n  return l\nl = AppendtoList()\nprint(l)\n```\n\nLists in Python can be concatenated as well; it's similar to the append function in Python.\n## Solution 2: Concatenate Lists Using `+` Operator\nThis solution simply concatenates a list containing the element to be added with the existing list using the `+` operator. \nThis approach is a little more cumbersome, but it achieves the intended results. The following illustration depicts what is happening here:\nThe concatenation operation can be demonstrated using the python code below:\n\n```python310\nl1 = [1, 4, 9, 10, 23]\nprint(l1)\nl1 = l1 + [90]\nprint(l1)\n```\n\nLet's move on to the next lesson to solve another challenge using lists.", "mdHtml": "<h2 id=\"solution-1-use-the-append-function\">Solution 1: Use the <code>append()</code> Function</h2>\n<p>This solution is very simple and perhaps the more commonly used method in Python. You simply call the <code>append</code> function on your list, and your given arguments automatically get appended to your list automatically. This is what the process looks like:</p>\n\n<p>This append operation can be demonstrated using the python code as shown below:</p>\n\n<div class='image-component'>\n```python310\ndef AppendtoList():\n  l = [1, 4, 9, 10, 23] \n  l.append(90)\n  return l\nl = AppendtoList()\nprint(l)\n```\n</div>\n<p>Lists in Python can be concatenated as well; it’s similar to the append function in Python.</p>\n\n<h2 id=\"solution-2-concatenate-lists-using-operator\">Solution 2: Concatenate Lists Using <code>+</code> Operator</h2>\n<p>This solution simply concatenates a list containing the element to be added with the existing list using the <code>+</code> operator.</p>\n\n<p>This approach is a little more cumbersome, but it achieves the intended results. The following illustration depicts what is happening here:</p>\n\n<p>The concatenation operation can be demonstrated using the python code below:</p>\n\n<div class='image-component'>\n```python310\nl1 = [1, 4, 9, 10, 23]\nprint(l1)\nl1 = l1 + [90]\nprint(l1)\n```\n</div>\n<p>Let’s move on to the next lesson to solve another challenge using lists.</p>", "summary": {"titleUpdated": true, "title": "", "description": "This lesson will give you a detailed review of how to append values at the end of a list.", "tags": []}, "type": "Lesson"}, {"id": "5722976108412928", "title": "Challenge 3: Averaging Values in a List", "is_preview": false, "slug": "challenge-3-averaging-values-in-a-list", "text": "## Problem Statement\nGiven a `getAverage()`  function, create a list named `l` with the following values:\n\n`[1, 4, 9, 10, 23]` \n\nCalculate the average value of all values in the list. \n\n### Input\nA list of integers\n\n### Output\nAn average of values in the list\n\n### Sample Input\n[1, 4, 9, 10, 23]\n\n### Sample Output\n9.4\n\n\n\n\n\n**[CanvasAnimation 图表]** - 交互式图表内容\n\n## Coding Exercise\n\nYou have been provided with some starter code and a return statement that allows you to return the calculated average.\n\n**Suggestion:** Check out the [Python documentation on lists](https://docs.python.org/3.5/tutorial/introduction.html#lists) for more details!\n\n```python310\ndef getAverage():\n  l1 = [1, 4, 9, 10, 23]\n  ## Write your code here\n  avg = 0 ## Calculate the average here\n  return avg\n```\n\nLet's move on to the next lesson to get a detailed review on averaging values in a list.", "mdHtml": "<h2 id=\"problem-statement\">Problem Statement</h2>\n<p>Given a <code>getAverage()</code>  function, create a list named <code>l</code> with the following values:</p>\n<p><code>[1, 4, 9, 10, 23]</code></p>\n<p>Calculate the average value of all values in the list.</p>\n<h3 id=\"input\">Input</h3>\n<p>A list of integers</p>\n<h3 id=\"output\">Output</h3>\n<p>An average of values in the list</p>\n<h3 id=\"sample-input\">Sample Input</h3>\n<p>[1, 4, 9, 10, 23]</p>\n<h3 id=\"sample-output\">Sample Output</h3>\n<p>9.4</p>\n\n<div class='image-component'>\n**[CanvasAnimation 图表]** - 交互式图表内容\n</div>\n<h2 id=\"coding-exercise\">Coding Exercise</h2>\n<p>You have been provided with some starter code and a return statement that allows you to return the calculated average.</p>\n<p><strong>Suggestion:</strong> Check out the <a href=\"https://docs.python.org/3.5/tutorial/introduction.html#lists\">Python documentation on lists</a> for more details!</p>\n\n<div class='image-component'>\n```python310\ndef getAverage():\n  l1 = [1, 4, 9, 10, 23]\n  ## Write your code here\n  avg = 0 ## Calculate the average here\n  return avg\n```\n</div>\n<p>Let’s move on to the next lesson to get a detailed review on averaging values in a list.</p>", "summary": {"titleUpdated": true, "title": "Challenge 3: Averaging Values in a List", "description": "In this challenge, you are required to average values in a list in Python.", "tags": []}, "type": "Lesson"}, {"id": "5692672295567360", "title": "Solution Review: Averaging Values in a List", "is_preview": false, "slug": "solution-review-averaging-values-in-a-list", "text": "## Solution: Use the Python `len` and `sum` Functions\n\nThe solution is fairly straightforward due to Python's inbuilt functions. \n* Normally, you'd have to iterate over the entire list to sum each element, but Python already has the `sum` function to do that for you.\n* Similarly, with a single instruction, the `len` function can determine the size of the list. What's left then is just to use the basic formula for calculating the average, and divide the sum with the length of the array to obtain the required average.\n\nFor example:\n\nGiven a list\n```python\nl = [1,4,9,10,23]\nlist_sum = sum(l)\nlist_length = len(l)\naverage = list_sum/length\n```\nThe demonstration is given in python code below:\n\n\n```python310\ndef getAverage():\n  l1 = [1, 4, 9, 10, 23]\n  avg = sum(l1)/len(l1)  \n  return avg\n  \navg = getAverage()\nprint(avg)\n```\n\nNow, let's move on to another challenge of lists.", "mdHtml": "<h2 id=\"solution-use-the-python-len-and-sum-functions\">Solution: Use the Python <code>len</code> and <code>sum</code> Functions</h2>\n<p>The solution is fairly straightforward due to Python’s inbuilt functions.</p>\n<ul>\n<li>Normally, you’d have to iterate over the entire list to sum each element, but Python already has the <code>sum</code> function to do that for you.</li>\n<li>Similarly, with a single instruction, the <code>len</code> function can determine the size of the list. What’s left then is just to use the basic formula for calculating the average, and divide the sum with the length of the array to obtain the required average.</li>\n</ul>\n<p>For example:</p>\n<p>Given a list</p>\n<pre><code class=\"language-python\">l = [1,4,9,10,23]\nlist_sum = sum(l)\nlist_length = len(l)\naverage = list_sum/length\n</code></pre>\n<p>The demonstration is given in python code below:</p>\n\n<div class='image-component'>\n```python310\ndef getAverage():\n  l1 = [1, 4, 9, 10, 23]\n  avg = sum(l1)/len(l1)  \n  return avg\n  \navg = getAverage()\nprint(avg)\n```\n</div>\n<p>Now, let’s move on to another challenge of lists.</p>", "summary": {"titleUpdated": true, "title": "", "description": "This lesson gives a detailed solution review of how to average values in a list.", "tags": []}, "type": "Lesson"}, {"id": "5684080884580352", "title": "Challenge 4: <PERSON><PERSON>ve Sublist From List", "is_preview": false, "slug": "challenge-4-remove-sublist-from-list", "text": "## Problem Statement\nGiven a `removeList()` function, create a list named `l` with the following values:\n\n`[1, 4, 9, 10, 23]` \n\nRemove the sublist `[4, 9]` from list `l`\n\n### Input\nA list\n\n### Output\nThe updated list after the sublist has been removed\n\n### Sample Input\n[1, 4, 9, 10, 23]\n\n### Sample Output\n[1, 10, 23]\n\n\n\n\n**[CanvasAnimation 图表]** - 交互式图表内容\n\n## Coding Exercise\nYou have been provided with some starter code and a return statement that allows you to return the updated list.​\n\n**Suggestion:** You can use the `remove` function in Python.\n\nCheck  [Python documentation on the list](https://docs.python.org/2/tutorial/datastructures.html) for more details!\n\n```python310\ndef removeList():\n  l1 = [1, 4, 9, 10, 23]\n  l2 = [4, 9]\n  ## Write your code here\n  return l1\n```\n\nLet's move on to the detailed review of the above problem in the next lesson.", "mdHtml": "<h2 id=\"problem-statement\">Problem Statement</h2>\n<p>Given a <code>removeList()</code> function, create a list named <code>l</code> with the following values:</p>\n<p><code>[1, 4, 9, 10, 23]</code></p>\n<p>Remove the sublist <code>[4, 9]</code> from list <code>l</code></p>\n<h3 id=\"input\">Input</h3>\n<p>A list</p>\n<h3 id=\"output\">Output</h3>\n<p>The updated list after the sublist has been removed</p>\n<h3 id=\"sample-input\">Sample Input</h3>\n<p>[1, 4, 9, 10, 23]</p>\n<h3 id=\"sample-output\">Sample Output</h3>\n<p>[1, 10, 23]</p>\n\n<div class='image-component'>\n**[CanvasAnimation 图表]** - 交互式图表内容\n</div>\n<h2 id=\"coding-exercise\">Coding Exercise</h2>\n<p>You have been provided with some starter code and a return statement that allows you to return the updated list.​</p>\n<p><strong>Suggestion:</strong> You can use the <code>remove</code> function in Python.</p>\n<p>Check  <a href=\"https://docs.python.org/2/tutorial/datastructures.html\">Python documentation on the list</a> for more details!</p>\n\n<div class='image-component'>\n```python310\ndef removeList():\n  l1 = [1, 4, 9, 10, 23]\n  l2 = [4, 9]\n  ## Write your code here\n  return l1\n```\n</div>\n<p>Let’s move on to the detailed review of the above problem in the next lesson.</p>", "summary": {"titleUpdated": true, "title": "Challenge 4: <PERSON><PERSON><PERSON> Sublist from List", "description": "In this challenge, your task is to remove a sublist from a list. ", "tags": []}, "type": "Lesson"}, {"id": "5768916555005952", "title": "Solution Review: Remove Sublist From List", "is_preview": false, "slug": "solution-review-remove-sublist-from-list", "text": "## Solution 1: Use the `remove()` Function\n\nThe solution is fairly simple; use the `remove` function to delete the elements of the sublist `l2` from `l1`. \n\nTherefore, Python's inbuilt functions make things much more convenient since you don't have to worry about any extraneous implementation level details like you would have to with other languages. Given below is an illustration of how the provided solution would play out. \nThe following python code helps to remove a sublist from a list:\n\n```python310\ndef removeList():\n  l1 = [1, 4, 9, 10, 23]\n  l2 = [4, 9]\n  l1.remove(l2[0])\n  l1.remove(l2[1])\n  return l1\n\nl1 = removeList()\nprint(l1)\n```\n\n## Solution 2: Use the `remove()`Function Within a `for` Loop \n\nThe solution is fairly simple; just iterate over the sublist and use the `remove` function to delete the elements of the sublist from `l1`. \n\n> Don't fret if you are unaware of for loop, we'll cover `for` loops in the next chapter.\n\n```python310\ndef removeList():\n  \n  l1 = [1, 4, 9, 10, 23]\n  l2 = [4, 9]\n  for elem in l2:\n    l1.remove(elem)\n  return l1\n\nl1 = removeList()\nprint(l1)\n```\n\nNow that we have insight on 'Lists', let's move on to 'List Comprehension'.", "mdHtml": "<h2 id=\"solution-1-use-the-remove-function\">Solution 1: Use the <code>remove()</code> Function</h2>\n<p>The solution is fairly simple; use the <code>remove</code> function to delete the elements of the sublist <code>l2</code> from <code>l1</code>.</p>\n<p>Therefore, Python’s inbuilt functions make things much more convenient since you don’t have to worry about any extraneous implementation level details like you would have to with other languages. Given below is an illustration of how the provided solution would play out.</p>\n\n<p>The following python code helps to remove a sublist from a list:</p>\n\n<div class='image-component'>\n```python310\ndef removeList():\n  l1 = [1, 4, 9, 10, 23]\n  l2 = [4, 9]\n  l1.remove(l2[0])\n  l1.remove(l2[1])\n  return l1\n\nl1 = removeList()\nprint(l1)\n```\n</div>\n<h2 id=\"solution-2-use-the-removefunction-within-a-for-loop\">Solution 2: Use the <code>remove()</code>Function Within a <code>for</code> Loop</h2>\n<p>The solution is fairly simple; just iterate over the sublist and use the <code>remove</code> function to delete the elements of the sublist from <code>l1</code>.</p>\n<blockquote>\n<p>Don’t fret if you are unaware of for loop, we’ll cover <code>for</code> loops in the next chapter.</p>\n</blockquote>\n\n<div class='image-component'>\n```python310\ndef removeList():\n  \n  l1 = [1, 4, 9, 10, 23]\n  l2 = [4, 9]\n  for elem in l2:\n    l1.remove(elem)\n  return l1\n\nl1 = removeList()\nprint(l1)\n```\n</div>\n<p>Now that we have insight on ‘Lists’, let’s move on to ‘List Comprehension’.</p>", "summary": {"titleUpdated": true, "title": "", "description": "This lesson gives a detailed review of  how to remove a sublist from a list.", "tags": []}, "type": "Lesson"}, {"id": "4902642186715136", "title": "List Comprehension", "is_preview": false, "slug": "list-comprehension", "text": "## List Comprehensions\n\nList comprehensions are a concise way to create lists. They consist of square brackets containing an expression followed by the `for` keyword; the result will be a list whose results match the expression.\n\nThe general syntax is:\n<img src=\"http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755847741901898054.svg\" alt=\"\" width=\"600\" height=\"400\" style=\"width: 100%; height: auto;\" />\nIn python, here’s how to create a list with the **squared numbers** of another list.\n\n```python310\nprint([x*x for x in [0, 1, 2, 3]])\n```\n\nGiven their flexibility, list comprehensions generally make use of the `range` function that returns a range of numbers. \n \nFor example,\n``` python\nrange(4) # returns values from 0 to 3. \n```\n\n> **Note:** default value of range(n) starts from 0 \n\nIf you want to define the starting value, write the following:\n```python\nrange(startingvalue, n) # returns value from startingvalue to n-1\n```\n> if range iterator is not specified it iterates with increments of 1, but if it is defined it increments by that value.\n\n```python\nrange(startingvalue, n, i) # returns value from startingvalue to n-1 with i increment\n```\n\n\n\n\n\n```python310\nprint([x*x for x in range(4)])\n```\n\n\nSometimes you may want to filter the elements by a given condition; the `if` keyword can be used in those cases.\n<img src=\"http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755847744069471692.svg\" alt=\"\" width=\"600\" height=\"400\" style=\"width: 100%; height: auto;\" />\nThe following python code displays all elements from 0 to 9 which are divisible by 2.\n\n```python310\nprint([x for x in range(10) if x % 2 == 0])\n```\n\n\nThe example above returns all even values in range 0..10. \n\nMore about list comprehensions can be found at [Python Documentation​ on List Comprehension](<https://docs.python.org/3/tutorial/datastructures.html#list-comprehensions>).\n\nNow that the concept of a ​list comprehension is clear, let’s check your knowledge in the upcoming exercises before moving on to the next 'Modules and Functions' chapter.", "mdHtml": "<h2 id=\"list-comprehensions\">List Comprehensions</h2>\n<p>List comprehensions are a concise way to create lists. They consist of square brackets containing an expression followed by the <code>for</code> keyword; the result will be a list whose results match the expression.</p>\n<p>The general syntax is:</p>\n\n<div class='image-component'><img src=\"http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755847741901898054.svg\" alt=\"\" width=\"600\" height=\"400\" style=\"width: 100%; height: auto;\" /></div>\n<p>In python, here’s how to create a list with the <strong>squared numbers</strong> of another list.</p>\n\n<div class='image-component'>\n```python310\nprint([x*x for x in [0, 1, 2, 3]])\n```\n</div>\n<p>Given their flexibility, list comprehensions generally make use of the <code>range</code> function that returns a range of numbers.</p>\n<p>For example,</p>\n<pre><code class=\"language-python\">range(4) # returns values from 0 to 3. \n</code></pre>\n<blockquote>\n<p><strong>Note:</strong> default value of range(n) starts from 0</p>\n</blockquote>\n<p>If you want to define the starting value, write the following:</p>\n<pre><code class=\"language-python\">range(startingvalue, n) # returns value from startingvalue to n-1\n</code></pre>\n<blockquote>\n<p>if range iterator is not specified it iterates with increments of 1, but if it is defined it increments by that value.</p>\n</blockquote>\n<pre><code class=\"language-python\">range(startingvalue, n, i) # returns value from startingvalue to n-1 with i increment\n</code></pre>\n\n<div class='image-component'>\n```python310\nprint([x*x for x in range(4)])\n```\n</div>\n<p>Sometimes you may want to filter the elements by a given condition; the <code>if</code> keyword can be used in those cases.</p>\n\n<div class='image-component'><img src=\"http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755847744069471692.svg\" alt=\"\" width=\"600\" height=\"400\" style=\"width: 100%; height: auto;\" /></div>\n<p>The following python code displays all elements from 0 to 9 which are divisible by 2.</p>\n\n<div class='image-component'>\n```python310\nprint([x for x in range(10) if x % 2 == 0])\n```\n</div>\n<p>The example above returns all even values in range 0…10.</p>\n<p>More about list comprehensions can be found at <a href=\"https://docs.python.org/3/tutorial/datastructures.html#list-comprehensions\">Python Documentation​ on List Comprehension</a>.</p>\n<p>Now that the concept of a ​list comprehension is clear, let’s check your knowledge in the upcoming exercises before moving on to the next ‘Modules and Functions’ chapter.</p>", "summary": {"titleUpdated": true, "title": "List Comprehension", "description": "Now that we are done with lists, let's have a look at list comprehension.", "tags": []}, "type": "Lesson"}, {"id": "5763610794000384", "title": "Challenge 5: List of Squares", "is_preview": false, "slug": "challenge-5-list-of-squares", "text": "## Problem Statement\nGiven a `getSquare()` function, create a list with the squares of the first 10 numbers, i.e., in the range from 1-10.\n\n### Input\nAn empty list\n\n\n### Output\nAn updated list with the square of each value in the list\n\n\n\n\n\n**[CanvasAnimation 图表]** - 交互式图表内容\n\n## Coding Exercise\nYou have been provided with some starter code and a return statement that allows you to return the list.\n\n**Suggestion:** More about list comprehensions can be found at [Python Documentation​ on List Comprehension](<https://docs.python.org/3/tutorial/datastructures.html#list-comprehensions>).\n\n```python310\ndef getSquare():\n  ## Write your code here\n  l1 = [] ## Create your list here\n  return l1\n```\n\nLet's move on to the next lesson to get a detailed review of the solution.", "mdHtml": "<h2 id=\"problem-statement\">Problem Statement</h2>\n<p>Given a <code>getSquare()</code> function, create a list with the squares of the first 10 numbers, i.e., in the range from 1-10.</p>\n<h3 id=\"input\">Input</h3>\n<p>An empty list</p>\n<h3 id=\"output\">Output</h3>\n<p>An updated list with the square of each value in the list</p>\n\n<div class='image-component'>\n**[CanvasAnimation 图表]** - 交互式图表内容\n</div>\n<h2 id=\"coding-exercise\">Coding Exercise</h2>\n<p>You have been provided with some starter code and a return statement that allows you to return the list.</p>\n<p><strong>Suggestion:</strong> More about list comprehensions can be found at <a href=\"https://docs.python.org/3/tutorial/datastructures.html#list-comprehensions\">Python Documentation​ on List Comprehension</a>.</p>\n\n<div class='image-component'>\n```python310\ndef getSquare():\n  ## Write your code here\n  l1 = [] ## Create your list here\n  return l1\n```\n</div>\n<p>Let’s move on to the next lesson to get a detailed review of the solution.</p>", "summary": {"titleUpdated": true, "title": "Challenge 5: List of Squares", "description": "Here is the challenge: you are required to create a list containing squares of numbers.", "tags": []}, "type": "Lesson"}, {"id": "5098895214379008", "title": "Solution Review: List of Squares", "is_preview": false, "slug": "solution-review-list-of-squares", "text": "## Solution: List Comprehension\n\nUsing list comprehension, the solution is reduced to a single, straight forward line of code. You simply use a comprehension that iterates over a range of 1-11 and squares each number. \n<img src=\"http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755847750070799926.svg\" alt=\"\" width=\"600\" height=\"400\" style=\"width: 100%; height: auto;\" />\n\n```python310\ndef getSquare():\n  l1 = [x*x for x in range(1, 11)]\n  return l1\nl1 = getSquare()\nprint(l1)\n```\n\nNow that we have printed a list of squares, let's move on to the next challenge to print a list of cubes.", "mdHtml": "<h2 id=\"solution-list-comprehension\">Solution: List Comprehension</h2>\n<p>Using list comprehension, the solution is reduced to a single, straight forward line of code. You simply use a comprehension that iterates over a range of 1-11 and squares each number.</p>\n\n<div class='image-component'><img src=\"http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755847750070799926.svg\" alt=\"\" width=\"600\" height=\"400\" style=\"width: 100%; height: auto;\" /></div>\n<div class='image-component'>\n```python310\ndef getSquare():\n  l1 = [x*x for x in range(1, 11)]\n  return l1\nl1 = getSquare()\nprint(l1)\n```\n</div>\n<p>Now that we have printed a list of squares, let’s move on to the next challenge to print a list of cubes.</p>", "summary": {"titleUpdated": true, "title": "", "description": "This lesson will give a detailed review of how to print the list of squares using list comprehension.", "tags": []}, "type": "Lesson"}, {"id": "5630758865600512", "title": "Challenge 6: List of Cubes", "is_preview": false, "slug": "challenge-6-list-of-cubes", "text": "## Problem Statement\nGiven a `getCube()` function, create a list with the cubes of the first 20 numbers.\n\n### Input\nAn empty list\n\n\n### Output\nAn updated list with the cube of each value in the list\n\n\n\n**[CanvasAnimation 图表]** - 交互式图表内容\n\n## Coding Exercise\nYou have been provided with some starter code and a return statement that allows you to return the list.\n\n**Suggestion:** More about list comprehensions can be found at [Python Documentation​ on List Comprehension](<https://docs.python.org/3/tutorial/datastructures.html#list-comprehensions>).\n\n```python310\ndef getCube():\n  ## Write your code here \n  l1 = [] ## Make the list here\n  return l1\n```\n\nLet's move on to the next lesson to get the detailed solution review.", "mdHtml": "<h2 id=\"problem-statement\">Problem Statement</h2>\n<p>Given a <code>getCube()</code> function, create a list with the cubes of the first 20 numbers.</p>\n<h3 id=\"input\">Input</h3>\n<p>An empty list</p>\n<h3 id=\"output\">Output</h3>\n<p>An updated list with the cube of each value in the list</p>\n\n<div class='image-component'>\n**[CanvasAnimation 图表]** - 交互式图表内容\n</div>\n<h2 id=\"coding-exercise\">Coding Exercise</h2>\n<p>You have been provided with some starter code and a return statement that allows you to return the list.</p>\n<p><strong>Suggestion:</strong> More about list comprehensions can be found at <a href=\"https://docs.python.org/3/tutorial/datastructures.html#list-comprehensions\">Python Documentation​ on List Comprehension</a>.</p>\n\n<div class='image-component'>\n```python310\ndef getCube():\n  ## Write your code here \n  l1 = [] ## Make the list here\n  return l1\n```\n</div>\n<p>Let’s move on to the next lesson to get the detailed solution review.</p>", "summary": {"titleUpdated": true, "title": "Challenge 6: List of cubes", "description": "In the previous challenge, you created a list containing squares. Now let's try to generate a list containing cubes of numbers.", "tags": []}, "type": "Lesson"}, {"id": "4903319583588352", "title": "Solution Review: List of Cubes", "is_preview": false, "slug": "solution-review-list-of-cubes", "text": "## Solution1: List Comprehension \n\nAs we have already seen in the previous exercise, list comprehensions allow for in-place list creation using a range that mathematical operations can be done on. This solution uses the same approach; we can simply use a list comprehension that iterates over a range of 1-21, and cubes each element as it goes.\n<img src=\"http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755847756004080419.svg\" alt=\"\" width=\"600\" height=\"400\" style=\"width: 100%; height: auto;\" />\n\n```python310\ndef getCube():\n  l1 = [x*x*x for x in range(1, 21)]\n  return l1\nl1 = getCube()\nprint(l1)\n```\n\n## Solution2: List Comprehension \n\nThis solution uses the same approach; we can simply use a list comprehension that iterates over a range of 1-21, and takes the power of 3 of each element in the range.\n\n\n>```\n>x ** y\n>```\n> denotes x raised to power y, i.e., $x^y$\n<img src=\"http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755847757751671999.svg\" alt=\"\" width=\"600\" height=\"400\" style=\"width: 100%; height: auto;\" />\n\n```python310\ndef getCube():\n  l1 = [x**3 for x in range(1,21)]\n  return l1\n\nl1 = getCube()\nprint(l1)\n```\n\n## \nNow, let's move on to the next challenge of list comprehension.", "mdHtml": "<h2 id=\"solution1-list-comprehension\">Solution1: List Comprehension</h2>\n<p>As we have already seen in the previous exercise, list comprehensions allow for in-place list creation using a range that mathematical operations can be done on. This solution uses the same approach; we can simply use a list comprehension that iterates over a range of 1-21, and cubes each element as it goes.</p>\n\n<div class='image-component'><img src=\"http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755847756004080419.svg\" alt=\"\" width=\"600\" height=\"400\" style=\"width: 100%; height: auto;\" /></div>\n<div class='image-component'>\n```python310\ndef getCube():\n  l1 = [x*x*x for x in range(1, 21)]\n  return l1\nl1 = getCube()\nprint(l1)\n```\n</div>\n<h2>Solution2: List Comprehension</h2>\n<p>This solution uses the same approach; we can simply use a list comprehension that iterates over a range of 1-21, and takes the power of 3 of each element in the range.</p>\n<blockquote>\n<pre><code>x ** y\n</code></pre>\n<p>denotes x raised to power y, i.e., <span class=\"katex\"><span class=\"katex-mathml\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><semantics><mrow><msup><mi>x</mi><mi>y</mi></msup></mrow><annotation encoding=\"application/x-tex\">x^y</annotation></semantics></math></span><span class=\"katex-html\" aria-hidden=\"true\"><span class=\"base\"><span class=\"strut\" style=\"height:0.6644em;\"></span><span class=\"mord\"><span class=\"mord mathnormal\">x</span><span class=\"msupsub\"><span class=\"vlist-t\"><span class=\"vlist-r\"><span class=\"vlist\" style=\"height:0.6644em;\"><span style=\"top:-3.063em;margin-right:0.05em;\"><span class=\"pstrut\" style=\"height:2.7em;\"></span><span class=\"sizing reset-size6 size3 mtight\"><span class=\"mord mathnormal mtight\" style=\"margin-right:0.03588em;\">y</span></span></span></span></span></span></span></span></span></span></span></p>\n</blockquote>\n\n<div class='image-component'><img src=\"http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755847757751671999.svg\" alt=\"\" width=\"600\" height=\"400\" style=\"width: 100%; height: auto;\" /></div>\n<div class='image-component'>\n```python310\ndef getCube():\n  l1 = [x**3 for x in range(1,21)]\n  return l1\n\nl1 = getCube()\nprint(l1)\n```\n</div>\n<h2 id=\"\"></h2>\n\n<p>Now, let’s move on to the next challenge of list comprehension.</p>", "summary": {"titleUpdated": true, "title": "", "description": "This lesson gives a detailed review of how to print a list of cubes using a list comprehension.", "tags": []}, "type": "Lesson"}, {"id": "5707106204254208", "title": "Challenge 7: Lists of Even and Odd Numbers", "is_preview": false, "slug": "challenge-7-lists-of-even-and-odd-numbers", "text": "## Problem Statement\nGiven a `ListofEvenOdds()` function, create a list comprehension with all the even numbers from 0 to 20, and another one with all the odd numbers.\n\n### Input\nTwo empty lists\n\n\n### Output\nList 1 with even numbers \n\nList 2 with odd numbers \n\n\n**[CanvasAnimation 图表]** - 交互式图表内容\n\n## Coding Exercise\nYou have been given some starter code and a return statement that allows you to return the lists you create.\n\n**Suggestion:** More about list comprehensions can be found at [Python Documentation​ on List Comprehension](<https://docs.python.org/3/tutorial/datastructures.html#list-comprehensions>).\n\n```python310\ndef ListofEvenOdds():\n  l1 = [] # list of even numbers\n  l2 = [] # list of odd numbers \n  return [l1, l2]\n```\n\nLet's move on to the next lesson for a detailed solution review.", "mdHtml": "<h2 id=\"problem-statement\">Problem Statement</h2>\n<p>Given a <code>ListofEvenOdds()</code> function, create a list comprehension with all the even numbers from 0 to 20, and another one with all the odd numbers.</p>\n<h3 id=\"input\">Input</h3>\n<p>Two empty lists</p>\n<h3 id=\"output\">Output</h3>\n<p>List 1 with even numbers</p>\n<p>List 2 with odd numbers</p>\n\n<div class='image-component'>\n**[CanvasAnimation 图表]** - 交互式图表内容\n</div>\n<h2 id=\"coding-exercise\">Coding Exercise</h2>\n<p>You have been given some starter code and a return statement that allows you to return the lists you create.</p>\n<p><strong>Suggestion:</strong> More about list comprehensions can be found at <a href=\"https://docs.python.org/3/tutorial/datastructures.html#list-comprehensions\">Python Documentation​ on List Comprehension</a>.</p>\n\n<div class='image-component'>\n```python310\ndef ListofEvenOdds():\n  l1 = [] # list of even numbers\n  l2 = [] # list of odd numbers \n  return [l1, l2]\n```\n</div>\n<p>Let’s move on to the next lesson for a detailed solution review.</p>", "summary": {"titleUpdated": true, "title": "Challenge 7: Lists of even and odd numbers", "description": "In this challenge, you are required to create a list containing even and odd numbers.", "tags": []}, "type": "Lesson"}, {"id": "5669367803019264", "title": "Solution Review: Lists of Even and Odd Numbers", "is_preview": false, "slug": "solution-review-lists-of-even-and-odd-numbers", "text": "## Solution 1:​ List Comprehension With Predicate\n\nAs we have already seen in the previous exercise, list comprehensions allow for in-place list creation using a range that mathematical operations can be done on; this solution uses the same approach.\n * Use a list comprehension l1 that iterates over a range of 0-21 and puts an even number in the list if the value is divisible by 2 (using modulus operator). Additionally, use a list comprehension l2 that iterates over a range of 0-21 and puts an odd number in the list if the value is not divisible by 2.\n\n\n<div class=\"flex justify-center sm:flex-row sm:justify-start -mt-4 flex-col\" style=\"gap: 20px;\">\n<div style='flex: 1; width: 50%;'>\n\n\n>**Note:** The statement `a % b` evaluates to the **remainder** of the division of variable `a` by variable `b`.\n\n</div>\n<div style='flex: 1; width: 50%;'>\n![](http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755847763305016124.svg)\n</div>\n</div>\n\n<img src=\"http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755847765339414913.svg\" alt=\"\" width=\"600\" height=\"400\" style=\"width: 100%; height: auto;\" />\n<img src=\"http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755847768333755084.svg\" alt=\"\" width=\"600\" height=\"400\" style=\"width: 100%; height: auto;\" />\n\n```python310\ndef ListofEvenOdds():\n  l1 = []\n  l2 = []  \n  l1 = [x for x in range(0, 21) if (x % 2 == 0)]\n  l2 = [x for x in range(0, 21) if (x % 2 != 0)]\n  return[l1, l2]\n  \nprint(ListofEvenOdds())\n```\n\n## Solution 2:​ List Comprehension \n\n * Use a list comprehension l1 that iterates over a range of 0-21 and puts an even number in the list if the value is divisible by 2 (using modulus operator). Additionally, use a list comprehension l2 that iterates over a range of 0-21 and puts an odd number in the list if the value is not in l1.\n\n```python310\ndef ListofEvenOdds():\n  l1 = []\n  l2 = []  \n  l1 = [x for x in range(0, 21) if (x % 2 == 0)]\n  l2 = [x for x in range(0, 21) if (x not in l1)]\n  return[l1, l2]\n  \nprint(ListofEvenOdds())\n```\n\nNow, let's check your knowledge about list comprehension in the next challenge.", "mdHtml": "<h2 id=\"solution-1-list-comprehension-with-predicate\">Solution 1:​ List Comprehension With Predicate</h2>\n<p>As we have already seen in the previous exercise, list comprehensions allow for in-place list creation using a range that mathematical operations can be done on; this solution uses the same approach.</p>\n<ul>\n<li>Use a list comprehension l1 that iterates over a range of 0-21 and puts an even number in the list if the value is divisible by 2 (using modulus operator). Additionally, use a list comprehension l2 that iterates over a range of 0-21 and puts an odd number in the list if the value is not divisible by 2.</li>\n</ul>\n\n<div class='image-component'>\n<div class=\"flex justify-center sm:flex-row sm:justify-start -mt-4 flex-col\" style=\"gap: 20px;\">\n<div style='flex: 1; width: 50%;'>\n\n\n>**Note:** The statement `a % b` evaluates to the **remainder** of the division of variable `a` by variable `b`.\n\n</div>\n<div style='flex: 1; width: 50%;'>\n![](http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755847763305016124.svg)\n</div>\n</div>\n</div>\n<div class='image-component'><img src=\"http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755847765339414913.svg\" alt=\"\" width=\"600\" height=\"400\" style=\"width: 100%; height: auto;\" /></div>\n<div class='image-component'><img src=\"http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755847768333755084.svg\" alt=\"\" width=\"600\" height=\"400\" style=\"width: 100%; height: auto;\" /></div>\n<div class='image-component'>\n```python310\ndef ListofEvenOdds():\n  l1 = []\n  l2 = []  \n  l1 = [x for x in range(0, 21) if (x % 2 == 0)]\n  l2 = [x for x in range(0, 21) if (x % 2 != 0)]\n  return[l1, l2]\n  \nprint(ListofEvenOdds())\n```\n</div>\n<h2 id=\"solution-2-list-comprehension\">Solution 2:​ List Comprehension</h2>\n<ul>\n<li>Use a list comprehension l1 that iterates over a range of 0-21 and puts an even number in the list if the value is divisible by 2 (using modulus operator). Additionally, use a list comprehension l2 that iterates over a range of 0-21 and puts an odd number in the list if the value is not in l1.</li>\n</ul>\n\n<div class='image-component'>\n```python310\ndef ListofEvenOdds():\n  l1 = []\n  l2 = []  \n  l1 = [x for x in range(0, 21) if (x % 2 == 0)]\n  l2 = [x for x in range(0, 21) if (x not in l1)]\n  return[l1, l2]\n  \nprint(ListofEvenOdds())\n```\n</div>\n<p>Now, let’s check your knowledge about list comprehension in the next challenge.</p>", "summary": {"titleUpdated": true, "title": "", "description": "This lesson gives a detailed review of how to print the list of even and odd numbers using the list comprehension.", "tags": []}, "type": "Lesson"}, {"id": "5667386346700800", "title": "Challenge 8: Sum of Squares of Even Numbers", "is_preview": false, "slug": "challenge-8-sum-of-squares-of-even-numbers", "text": "## Problem Statement\nGiven an `evenSquare()` function, create a list with the squares of the even numbers from 0 to 20. The final output should be the sum of even numbers in the list:\n\n\n### Input\nA list with the square of even numbers from 0-20\n\n\n### Output\nThe sum of the numbers in the list\n\n\n\n**[CanvasAnimation 图表]** - 交互式图表内容\n\n## Coding Exercise\nYou have been given some starter code and a return statement that allows you to return the list you've created.\n\n**Suggestion:** More about list comprehensions can be found at [Python Documentation​ on List Comprehension](<https://docs.python.org/3/tutorial/datastructures.html#list-comprehensions>).\n\n```python310\ndef evenSquareSum():\n  #write code here\n  l1 = []\n  return l1\n```\n\nNow, let's move on to the solution of the above problem.", "mdHtml": "<h2 id=\"problem-statement\">Problem Statement</h2>\n<p>Given an <code>evenSquare()</code> function, create a list with the squares of the even numbers from 0 to 20. The final output should be the sum of even numbers in the list:</p>\n<h3 id=\"input\">Input</h3>\n<p>A list with the square of even numbers from 0-20</p>\n<h3 id=\"output\">Output</h3>\n<p>The sum of the numbers in the list</p>\n\n<div class='image-component'>\n**[CanvasAnimation 图表]** - 交互式图表内容\n</div>\n<h2 id=\"coding-exercise\">Coding Exercise</h2>\n<p>You have been given some starter code and a return statement that allows you to return the list you’ve created.</p>\n<p><strong>Suggestion:</strong> More about list comprehensions can be found at <a href=\"https://docs.python.org/3/tutorial/datastructures.html#list-comprehensions\">Python Documentation​ on List Comprehension</a>.</p>\n\n<div class='image-component'>\n```python310\ndef evenSquareSum():\n  #write code here\n  l1 = []\n  return l1\n```\n</div>\n<p>Now, let’s move on to the solution of the above problem.</p>", "summary": {"titleUpdated": true, "title": "Challenge 8: Squares of Even Numbers", "description": "In this challenge, your task is to create a list of the squares of even numbers.", "tags": []}, "type": "Lesson"}, {"id": "5736025695453184", "title": "Solution Review: Sum of Squares of Even Numbers", "is_preview": false, "slug": "solution-review-sum-of-squares-of-even-numbers", "text": "## Solution: List Comprehension With Predicate \n\nThe solution is similar to what we've implemented in most of our previous challenges. \n* Use a list comprehension that iterates over the range of even numbers and squares each element to obtain a list of even squares\n* Use the `sum(list)` to calculate the sum of even squares  \n<img src=\"http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755847774012224257.svg\" alt=\"\" width=\"600\" height=\"400\" style=\"width: 100%; height: auto;\" />\nOr use the following reference sequence without predicate.\n<img src=\"http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755847775734470672.svg\" alt=\"\" width=\"600\" height=\"400\" style=\"width: 100%; height: auto;\" />\nThe following python code uses a list comprehension to get the sum of squares of even numbers in a list.\n\n```python310\ndef evenSquareSum():\n  even = [x * x for x in range(0, 21, 2)]\n  return sum(even)\n  \nprint(evenSquareSum())\n```\n\nLet's move on to the next challenge.", "mdHtml": "<h2 id=\"solution-list-comprehension-with-predicate\">Solution: List Comprehension With Predicate</h2>\n<p>The solution is similar to what we’ve implemented in most of our previous challenges.</p>\n<ul>\n<li>Use a list comprehension that iterates over the range of even numbers and squares each element to obtain a list of even squares</li>\n<li>Use the <code>sum(list)</code> to calculate the sum of even squares</li>\n</ul>\n\n<div class='image-component'><img src=\"http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755847774012224257.svg\" alt=\"\" width=\"600\" height=\"400\" style=\"width: 100%; height: auto;\" /></div>\n<p>Or use the following reference sequence without predicate.</p>\n\n<div class='image-component'><img src=\"http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755847775734470672.svg\" alt=\"\" width=\"600\" height=\"400\" style=\"width: 100%; height: auto;\" /></div>\n<p>The following python code uses a list comprehension to get the sum of squares of even numbers in a list.</p>\n\n<div class='image-component'>\n```python310\ndef evenSquareSum():\n  even = [x * x for x in range(0, 21, 2)]\n  return sum(even)\n  \nprint(evenSquareSum())\n```\n</div>\n<p>Let’s move on to the next challenge.</p>", "summary": {"titleUpdated": true, "title": "", "description": "This lesson gives a detailed review of how to generate sum for a list of squares of even numbers using a list comprehension.", "tags": []}, "type": "Lesson"}, {"id": "5740332071256064", "title": "Challenge 9: Even Squares Not Divisible By Three", "is_preview": false, "slug": "challenge-9-even-squares-not-divisible-by-three", "text": "## Problem Statement\nGiven a `getSquare()` function, make a list comprehension that returns a list with the **squares** of all **even numbers** from 0 to 20, but **ignores** those numbers that are **divisible by 3**.\n\n### Input\nAn empty list\n### Output\nA list with the square of all even numbers not divisible by 3.\n\n\n\n\n\n\n**[CanvasAnimation 图表]** - 交互式图表内容\n\n## Coding Exercise\nYou have been given some starter code and a return statement that allows you to return the list you've created.\n\n**Suggestion:** More about list comprehensions can be found at [Python Documentation​ on List Comprehension](<https://docs.python.org/3/tutorial/datastructures.html#list-comprehensions>).\n\n```python310\ndef getSquare():\n  ##Write your code here\n  l1 = [] ##Create the list here\n  return l1\n```\n\nNow, let's move on to to the solution review of the above problem in the next lesson.", "mdHtml": "<h2 id=\"problem-statement\">Problem Statement</h2>\n<p>Given a <code>getSquare()</code> function, make a list comprehension that returns a list with the <strong>squares</strong> of all <strong>even numbers</strong> from 0 to 20, but <strong>ignores</strong> those numbers that are <strong>divisible by 3</strong>.</p>\n<h3 id=\"input\">Input</h3>\n<p>An empty list</p>\n<h3 id=\"output\">Output</h3>\n<p>A list with the square of all even numbers not divisible by 3.</p>\n\n<div class='image-component'>\n**[CanvasAnimation 图表]** - 交互式图表内容\n</div>\n<h2 id=\"coding-exercise\">Coding Exercise</h2>\n<p>You have been given some starter code and a return statement that allows you to return the list you’ve created.</p>\n<p><strong>Suggestion:</strong> More about list comprehensions can be found at <a href=\"https://docs.python.org/3/tutorial/datastructures.html#list-comprehensions\">Python Documentation​ on List Comprehension</a>.</p>\n\n<div class='image-component'>\n```python310\ndef getSquare():\n  ##Write your code here\n  l1 = [] ##Create the list here\n  return l1\n```\n</div>\n<p>Now, let’s move on to to the solution review of the above problem in the next lesson.</p>", "summary": {"titleUpdated": true, "title": "Challenge 9: Even Squares Not Divisible By Three", "description": "In this challenge, create a list of even squares that aren't divisible by three.", "tags": []}, "type": "Lesson"}, {"id": "5720804331356160", "title": "Solution Review: Even Squares Not Divisible By Three", "is_preview": false, "slug": "solution-review-even-squares-not-divisible-by-three", "text": "# Solution Review: List Comprehension With Predicate\nUse a list comprehension that iterates over a range of 0-21, increments the number in the range by 2 and squares each remaining number. Also, use a predicate clause to check if the squared number is not divisible by 3, then put the number in the list.\n<img src=\"http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755847781523632839.svg\" alt=\"\" width=\"600\" height=\"400\" style=\"width: 100%; height: auto;\" />\nThe following python code helps to make a list containing even squares that aren't divisible by three:\n\n```python310\ndef getSquare():\n  l1 = [x*x for x in range(0, 21, 2) if x % 3 != 0]\n  return l1\n  \nprint(getSquare())\n```\n\n# Solution 2: List Comprehension With Predicate\nUse a list comprehension that iterates over a range of 0-21, takes the power of each number by 2.  Also, use a predicate clause to check if the squared number is not divisible by 3, and it is divisible by 2, then put the number in the list.\n![](http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755847782579515526.svg)\nThe following python code helps to make a list containing even squares that aren't divisible by three:\n\n```python310\ndef getSquare():\n l1=[ x**2 for x in range(0, 21) if x % 3 != 0 and x % 2 == 0] \n return l1\n \nprint(getSquare())\n```\n\nNow that you have an insight of list comprehension, let's move on to the quiz.", "mdHtml": "<h2 id=\"solution-review-list-comprehension-with-predicate\">Solution Review: List Comprehension With Predicate</h2>\n<p>Use a list comprehension that iterates over a range of 0-21, increments the number in the range by 2 and squares each remaining number. Also, use a predicate clause to check if the squared number is not divisible by 3, then put the number in the list.</p>\n\n<div class='image-component'><img src=\"http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755847781523632839.svg\" alt=\"\" width=\"600\" height=\"400\" style=\"width: 100%; height: auto;\" /></div>\n<p>The following python code helps to make a list containing even squares that aren’t divisible by three:</p>\n\n<div class='image-component'>\n```python310\ndef getSquare():\n  l1 = [x*x for x in range(0, 21, 2) if x % 3 != 0]\n  return l1\n  \nprint(getSquare())\n```\n</div>\n<h2 id=\"solution-2-list-comprehension-with-predicate\">Solution 2: List Comprehension With Predicate</h2>\n<p>Use a list comprehension that iterates over a range of 0-21, takes the power of each number by 2.  Also, use a predicate clause to check if the squared number is not divisible by 3, and it is divisible by 2, then put the number in the list.</p>\n\n<div class='image-component'>![](http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755847782579515526.svg)</div>\n<p>The following python code helps to make a list containing even squares that aren’t divisible by three:</p>\n\n<div class='image-component'>\n```python310\ndef getSquare():\n l1=[ x**2 for x in range(0, 21) if x % 3 != 0 and x % 2 == 0] \n return l1\n \nprint(getSquare())\n```\n</div>\n<p>Now that you have an insight of list comprehension, let’s move on to the quiz.</p>", "summary": {"titleUpdated": true, "title": "Solution Review: Even Squares Not Divisible By Three", "description": "This lesson gives a detailed review of how to make a list of even squares using a list comprehension that aren't divisible by three.", "tags": []}, "type": "Lesson"}, {"id": "5673847051255808", "title": "Quick Quiz on Lists", "is_preview": false, "slug": "quick-quiz-on-lists", "text": "Now that you have learned about lists in Python, what if you want to organize chunks of code? Let's learn about \"Modules and Functions\" in the next chapter.", "mdHtml": "<p>Now that you have learned about lists in Python, what if you want to organize chunks of code? Let’s learn about “Modules and Functions” in the next chapter.</p>", "summary": {"titleUpdated": true, "title": "Quick quiz on Lists", "description": "", "tags": []}, "type": "Quiz", "questions": [{"questionText": "What should be the output of the following code?\n\n```print([5,6,7] + [8,9,10])```", "questionOptions": [{"text": "[13,15,17]", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>[13,15,17]</p>\n"}, {"text": "[5,6,7,8,9,10]", "correct": true, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>[5,6,7,8,9,10]</p>\n"}], "questionTextHtml": "<p>What should be the output of the following code?</p>\n<p><code>print([5,6,7] + [8,9,10])</code></p>\n"}, {"questionText": "Given the list. How can you add an item \"baseball\" in the list?\n\nlist = [\"basketball\",  \"cricket\",  \"badminton\"]", "questionOptions": [{"text": "list.append(\"baseball\")", "correct": true, "explanation": {"mdText": "Append the value at the end of the list", "mdHtml": "<p>Append the value at the end of the list</p>\n"}, "mdHtml": "<p>list.append(“baseball”)</p>\n"}, {"text": "list.insert(\"baseball\")", "correct": false, "explanation": {"mdText": "insert takes two parameters. one is the value and the other is the position to insert.", "mdHtml": "<p>insert takes two parameters. one is the value and the other is the position to insert.</p>\n"}, "mdHtml": "<p>list.insert(“baseball”)</p>\n"}], "questionTextHtml": "<p>Given the list. How can you add an item “baseball” in the list?</p>\n<p>list = [“basketball”,  “cricket”,  “badminton”]</p>\n"}, {"questionText": "What is the output of the following code?\n```python\nS = [x**2 for x in range(10)]\nprint(S)\n```", "questionOptions": [{"text": "[0, 1, 4, 9, 16, 25, 36, 49, 64, 81]", "correct": true, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>[0, 1, 4, 9, 16, 25, 36, 49, 64, 81]</p>\n"}, {"text": "[0, 2, 4, 8, 16, 32, 64, 128, 256, 512]", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>[0, 2, 4, 8, 16, 32, 64, 128, 256, 512]</p>\n"}], "questionTextHtml": "<p>What is the output of the following code?</p>\n<pre><code class=\"language-python\">S = [x**2 for x in range(10)]\nprint(S)\n</code></pre>\n"}, {"questionText": "What is the output of the following code?\n```python\nlist = [n**2 for n in range(10) if n % 2 == 0]\nprint(list)\n```", "questionOptions": [{"text": "[0, 4, 8, 16, 32]", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>[0, 4, 8, 16, 32]</p>\n"}, {"text": "[0, 4, 16, 36, 64]", "correct": true, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>[0, 4, 16, 36, 64]</p>\n"}], "questionTextHtml": "<p>What is the output of the following code?</p>\n<pre><code class=\"language-python\">list = [n**2 for n in range(10) if n % 2 == 0]\nprint(list)\n</code></pre>\n"}]}]}, {"id": "h9vsn6sax", "title": "Modules and Functions", "summary": "Work your way through Python modules, functions, GCD, trigonometry, recursion, and quizzes.", "pages": [{"id": "5147592761540608", "title": "Modules and Functions", "is_preview": true, "slug": "modules-and-functions", "text": "## Modules \n\nA function is a block of code that is used to perform a single action. A module is a Python file containing a set of functions and variables of all types (arrays, dictionaries, objects, etc.) that you want to use in your application.\n\n### Module Creation\nTo create a module, create a python file with a `.py` extension. \n\n### Use a Module\nModules created with a `.py` extension can be used with an `import` statement.\n\nStart up your Python REPL, and let’s use the \"math\" module that provides access to mathematical functions:\n\n\n```python310\nimport math\nprint(math.cos(0.0))\nprint(math.radians(275))\n```\n\nNow, ​let's create a module with a function and use the import statement to call the function:  \n\n```python310\nimport module\nmodule.myCourse(\"Full Speed Python\")\n```\n\n## Functions\nA function is a block of code that contains a sequence of instructions that are executed when the function is invoked. \nData passed in the functions are known as function parameters.\n### Non-Parameterized Function\nA function that does not contain parameters is a non-parameterized function.\n\nThe following defines the \"do_hello\" function that prints two messages when invoked:\n\n\n\n```python310\ndef do_hello():\n  print(\"Hello\")\n  print(\"World\")\ndo_hello()\n```\n\n> `Note:` Make sure that you insert a tab before both print expressions in the previous function. Tabs and spaces in Python are relevant and define that a block of code is somewhat dependent on a previous instruction. For instance, the print expressions are \"inside\" the \"do\\_hello\" function and, therefore, must have a tab.\n\n### Parameterized function\nFunctions that can receive parameters\nare parameterized functions. \n\nThe following Python code uses an \"add_one()\" function that receives a parameter 'val',and it prints the incremented value inside the function:\n\n```python310\ndef add_one(val):\n  print(\"Function got value\",val+1)\n  return\nadd_one(1)\n```\n\n### Parameterized function with a return  statement\nFunctions can also receive parameters and return values (using the \"return\" keyword).\n\nThe following Python code uses an \"add_one()\" function that receives a parameter 'val'; it prints the value passed in the function, and returns the incremented value:\n\n```python310\ndef add_one(val):\n  print(\"Function got value\", val)\n  return val + 1\nvalue = add_one(1)\nprint(value)\n```\n\nNow that the concept of ​modules and functions in python is clear, let’s check your knowledge in the upcoming exercises before moving on to the  'Recursion' lesson.", "mdHtml": "<h2 id=\"modules\">Modules</h2>\n<p>A function is a block of code that is used to perform a single action. A module is a Python file containing a set of functions and variables of all types (arrays, dictionaries, objects, etc.) that you want to use in your application.</p>\n<h3 id=\"module-creation\">Module Creation</h3>\n<p>To create a module, create a python file with a <code>.py</code> extension.</p>\n<h3 id=\"use-a-module\">Use a Module</h3>\n<p>Modules created with a <code>.py</code> extension can be used with an <code>import</code> statement.</p>\n<p>Start up your Python REPL, and let’s use the “math” module that provides access to mathematical functions:</p>\n\n<div class='image-component'>\n```python310\nimport math\nprint(math.cos(0.0))\nprint(math.radians(275))\n```\n</div>\n<p>Now, ​let’s create a module with a function and use the import statement to call the function:</p>\n\n<div class='image-component'>\n```python310\nimport module\nmodule.myCourse(\"Full Speed Python\")\n```\n</div>\n<h2 id=\"functions\">Functions</h2>\n<p>A function is a block of code that contains a sequence of instructions that are executed when the function is invoked.\nData passed in the functions are known as function parameters.</p>\n<h3 id=\"non-parameterized-function\">Non-Parameterized Function</h3>\n<p>A function that does not contain parameters is a non-parameterized function.</p>\n<p>The following defines the “do_hello” function that prints two messages when invoked:</p>\n\n<div class='image-component'>\n```python310\ndef do_hello():\n  print(\"Hello\")\n  print(\"World\")\ndo_hello()\n```\n</div>\n<blockquote>\n<p><code>Note:</code> Make sure that you insert a tab before both print expressions in the previous function. Tabs and spaces in Python are relevant and define that a block of code is somewhat dependent on a previous instruction. For instance, the print expressions are “inside” the “do_hello” function and, therefore, must have a tab.</p>\n</blockquote>\n<h3 id=\"parameterized-function\">Parameterized function</h3>\n<p>Functions that can receive parameters\nare parameterized functions.</p>\n<p>The following Python code uses an “add_one()” function that receives a parameter ‘val’,and it prints the incremented value inside the function:</p>\n\n<div class='image-component'>\n```python310\ndef add_one(val):\n  print(\"Function got value\",val+1)\n  return\nadd_one(1)\n```\n</div>\n<h3 id=\"parameterized-function-with-a-return-statement\">Parameterized function with a return  statement</h3>\n<p>Functions can also receive parameters and return values (using the “return” keyword).</p>\n<p>The following Python code uses an “add_one()” function that receives a parameter ‘val’; it prints the value passed in the function, and returns the incremented value:</p>\n\n<div class='image-component'>\n```python310\ndef add_one(val):\n  print(\"Function got value\", val)\n  return val + 1\nvalue = add_one(1)\nprint(value)\n```\n</div>\n<p>Now that the concept of ​modules and functions in python is clear, let’s check your knowledge in the upcoming exercises before moving on to the  ‘Recursion’ lesson.</p>", "summary": {"titleUpdated": true, "title": "Modules and Functions", "description": "In this chapter,​ we will talk about modules and functions. ", "tags": []}, "type": "Lesson"}, {"id": "5142602009542656", "title": "Challenge 1: Greatest Common Divisor", "is_preview": false, "slug": "challenge-1-greatest-common-divisor", "text": "## Problem Statement\n\nWrite a `findGCD` function that takes in two numbers as input `a` and `b` and finds the greatest common divisor of the two.\n\n### Input\nTwo numbers\n\n### Output\nGCD of numbers\n\n### Sample Input\na = 8, b = 12\n\n### Sample Output\n4\n\n## Coding Exercise\n\n**Suggestion:** Check the [Python library reference](https://docs.python.org/3/library/stdtypes.html#numeric-types-int-float-complex) for more details!\n\nYou have been provided with some starter code that allows you to write your code within a function.\n\n```python310\nimport math \n## Write your function here\ndef findGCD(a, b):\n  return # return the gcd of numbers\n```\n\nNow, let's move on to the detailed solution review of the above problem.", "mdHtml": "<h2 id=\"problem-statement\">Problem Statement</h2>\n<p>Write a <code>findGCD</code> function that takes in two numbers as input <code>a</code> and <code>b</code> and finds the greatest common divisor of the two.</p>\n<h3 id=\"input\">Input</h3>\n<p>Two numbers</p>\n<h3 id=\"output\">Output</h3>\n<p>GCD of numbers</p>\n<h3 id=\"sample-input\">Sample Input</h3>\n<p>a = 8, b = 12</p>\n<h3 id=\"sample-output\">Sample Output</h3>\n<p>4</p>\n<h2 id=\"coding-exercise\">Coding Exercise</h2>\n<p><strong>Suggestion:</strong> Check the <a href=\"https://docs.python.org/3/library/stdtypes.html#numeric-types-int-float-complex\">Python library reference</a> for more details!</p>\n<p>You have been provided with some starter code that allows you to write your code within a function.</p>\n\n<div class='image-component'>\n```python310\nimport math \n## Write your function here\ndef findGCD(a, b):\n  return # return the gcd of numbers\n```\n</div>\n<p>Now, let’s move on to the detailed solution review of the above problem.</p>", "summary": {"titleUpdated": true, "title": "Challenge 1: Greatest Common Divisor", "description": "In this challenge, your task is to find the greatest common divisor of two numbers a and b.", "tags": []}, "type": "Lesson"}, {"id": "5711028985790464", "title": "Solution Review: Greatest Common Divisor", "is_preview": false, "slug": "solution-review-greatest-common-divisor", "text": "## Solution: Import built-in Math Module\n* Import the math library using `import math`\n \n* Use the built-in function `math.gcd(number1,number2)` function to return the  greatest common divisors of two numbers\n\nThe following python code demonstrates\nhow to find the gcd of a number using the built-in math module:\n\n```python310\nimport math \ndef findGCD(a, b): \n  return math.gcd(a, b)\nprint(findGCD(24, 18))\n```\n\nLet’s move on to the next problem.", "mdHtml": "<h2 id=\"solution-import-built-in-math-module\">Solution: Import built-in Math Module</h2>\n<ul>\n<li>\n<p>Import the math library using <code>import math</code></p>\n</li>\n<li>\n<p>Use the built-in function <code>math.gcd(number1,number2)</code> function to return the  greatest common divisors of two numbers</p>\n</li>\n</ul>\n<p>The following python code demonstrates\nhow to find the gcd of a number using the built-in math module:</p>\n\n<div class='image-component'>\n```python310\nimport math \ndef findGCD(a, b): \n  return math.gcd(a, b)\nprint(findGCD(24, 18))\n```\n</div>\n<p>Let’s move on to the next problem.</p>", "summary": {"titleUpdated": true, "title": "", "description": "This lesson will explain how to use the math module to find the greatest common divisor.", "tags": []}, "type": "Lesson"}, {"id": "5640302282932224", "title": "Challenge 2: <PERSON><PERSON> Sine, <PERSON><PERSON><PERSON>, and Tangent of User Input", "is_preview": false, "slug": "challenge-2-calculate-sine-cosine-and-tangent-of-user-input", "text": "## Problem Statement\n\nUse the `calculateSinCosTan()` function; it takes a number `x` as a parameter and shows the result of the sine, cosine, and tangent of the number. \n\n### Input\nA number\n\n### Output\nCalculate the sine, cosine, and tangent of that number\n\n### Sample Input\n0\n\n### Sample Output\n[0, 1, 0]\n\n## Coding Exercise\n\nWrite your code below. It is recommended​ that you try solving the exercise yourself before viewing the solution.\n\n**Suggestion:** Check the [Python library reference](https://docs.python.org/3/library/stdtypes.html#numeric-types-int-float-complex) for more details!\n\n```python310\nimport math\ndef calculateSinCosTan(x):\n  #write your function here\n  sine = 0#calculate sine\n  cos = 0#calculate cosine\n  tan = 0#calculate tangent \n  return [sine, cos, tan]\n```\n\nNow, let’s move on to the detailed solution review of the above problem.", "mdHtml": "<h2 id=\"problem-statement\">Problem Statement</h2>\n<p>Use the <code>calculateSinCosTan()</code> function; it takes a number <code>x</code> as a parameter and shows the result of the sine, cosine, and tangent of the number.</p>\n<h3 id=\"input\">Input</h3>\n<p>A number</p>\n<h3 id=\"output\">Output</h3>\n<p>Calculate the sine, cosine, and tangent of that number</p>\n<h3 id=\"sample-input\">Sample Input</h3>\n<p>0</p>\n<h3 id=\"sample-output\">Sample Output</h3>\n<p>[0, 1, 0]</p>\n<h2 id=\"coding-exercise\">Coding Exercise</h2>\n<p>Write your code below. It is recommended​ that you try solving the exercise yourself before viewing the solution.</p>\n<p><strong>Suggestion:</strong> Check the <a href=\"https://docs.python.org/3/library/stdtypes.html#numeric-types-int-float-complex\">Python library reference</a> for more details!</p>\n\n<div class='image-component'>\n```python310\nimport math\ndef calculateSinCosTan(x):\n  #write your function here\n  sine = 0#calculate sine\n  cos = 0#calculate cosine\n  tan = 0#calculate tangent \n  return [sine, cos, tan]\n```\n</div>\n<p>Now, let’s move on to the detailed solution review of the above problem.</p>", "summary": {"titleUpdated": true, "title": "Challenge 2: <PERSON><PERSON> Sine, <PERSON><PERSON><PERSON>, and Tangent of User Input", "description": "In this challenge, you are required to calculate the sine, cosine, and tangent of variable x.", "tags": []}, "type": "Lesson"}, {"id": "5191183324151808", "title": "Solution Review: Calculate Sine, Cosine and Tangent of User Input", "is_preview": false, "slug": "solution-review-calculate-sine-cosine-and-tangent-of-user-input", "text": "## Solution: Import the Built-In Math Module\n* Import the math library using \n`import math`\n\n* Use the built-in function `math.sin(x)` ,`math.cos(x)`,`math.tan(x)` function to calculate sin,cos, and tangent of variable x \n\nThe following python code demonstrates how to find the sine, cosine and tangent of a number using the built-in math module:\n\n```python310\nimport math\ndef calculateSinCosTan(x):\n  sine = math.sin(x)\n  cos = math.cos(x)\n  tan = math.tan(x)\n  return [sine, cos, tan]\n\nprint(\"sine, cos, tan:\", calculateSinCosTan(-1))\nprint(\"sine, cos, tan:\", calculateSinCosTan(0))\nprint(\"sine, cos, tan:\", calculateSinCosTan(1))\n```\n\nLet’s move on to the next problem.", "mdHtml": "<h2 id=\"solution-import-the-built-in-math-module\">Solution: Import the Built-In Math Module</h2>\n<ul>\n<li>\n<p>Import the math library using\n<code>import math</code></p>\n</li>\n<li>\n<p>Use the built-in function <code>math.sin(x)</code> ,<code>math.cos(x)</code>,<code>math.tan(x)</code> function to calculate sin,cos, and tangent of variable x</p>\n</li>\n</ul>\n<p>The following python code demonstrates how to find the sine, cosine and tangent of a number using the built-in math module:</p>\n\n<div class='image-component'>\n```python310\nimport math\ndef calculateSinCosTan(x):\n  sine = math.sin(x)\n  cos = math.cos(x)\n  tan = math.tan(x)\n  return [sine, cos, tan]\n\nprint(\"sine, cos, tan:\", calculateSinCosTan(-1))\nprint(\"sine, cos, tan:\", calculateSinCosTan(0))\nprint(\"sine, cos, tan:\", calculateSinCosTan(1))\n```\n</div>\n<p>Let’s move on to the next problem.</p>", "summary": {"titleUpdated": true, "title": "", "description": "This lesson will explain how to use the built-in sin, cosine, and tangent function by importing the math module.", "tags": []}, "type": "Lesson"}, {"id": "5715309591789568", "title": "Challenge 3: Compute & Return Maximum", "is_preview": false, "slug": "challenge-3-compute-return-maximum", "text": "## Problem Statement\n\nImplement the `findMaximum` function that receives two numbers as arguments `x` and `y` and returns the maximum of the numbers.\n\n### Input\nTwo numbers x and y\n\n### Output\nThe maximum of two numbers x and y\n\n### Sample Input\n x = 2, y = 3\n\n### Sample Output\n 3\n\n \n## Coding Exercise\nWrite your code below. It is recommended​ that you try solving the exercise yourself before viewing the solution.\n\nUse the \"if\" statement to compare both numbers using\n[Python Documentation on condition statements](https://docs.python.org/3/tutorial/controlflow.html#if-statements).\n\nMake sure you cater to negative inputs as well!\n\n```python310\ndef findMaximum(x, y):\n       #create a variable max2 \n  pass #returns the maximum of two numbers x and y\n```\n\nNow, let’s move on to the detailed solution review of the above problem.", "mdHtml": "<h2 id=\"problem-statement\">Problem Statement</h2>\n<p>Implement the <code>findMaximum</code> function that receives two numbers as arguments <code>x</code> and <code>y</code> and returns the maximum of the numbers.</p>\n<h3 id=\"input\">Input</h3>\n<p>Two numbers x and y</p>\n<h3 id=\"output\">Output</h3>\n<p>The maximum of two numbers x and y</p>\n<h3 id=\"sample-input\">Sample Input</h3>\n<p>x = 2, y = 3</p>\n<h3 id=\"sample-output\">Sample Output</h3>\n<p>3</p>\n<h2 id=\"coding-exercise\">Coding Exercise</h2>\n<p>Write your code below. It is recommended​ that you try solving the exercise yourself before viewing the solution.</p>\n<p>Use the “if” statement to compare both numbers using\n<a href=\"https://docs.python.org/3/tutorial/controlflow.html#if-statements\">Python Documentation on condition statements</a>.</p>\n<p>Make sure you cater to negative inputs as well!</p>\n\n<div class='image-component'>\n```python310\ndef findMaximum(x, y):\n       #create a variable max2 \n  pass #returns the maximum of two numbers x and y\n```\n</div>\n<p>Now, let’s move on to the detailed solution review of the above problem.</p>", "summary": {"titleUpdated": true, "title": "Challenge 3: Compute & Return Greater Sum", "description": "In this lesson, you are required to compute the maximum of two numbers and return the maximum value.", "tags": []}, "type": "Lesson"}, {"id": "5678524673294336", "title": "Solution Review: Compute & Return Maximum", "is_preview": false, "slug": "solution-review-compute-return-maximum", "text": "## Solution 1: Use the `if` Statement\nUse the `if` statement to compare if variable x is greater than y `x>y`.\nIf x is greater than y, then save the value of x in maximum; otherwise, save the value of y.\n\n \n\n\n```python310\ndef findMaximum(x, y):\n  max2 = 0\n  if(x > y):\n    max2 = x\n  else:\n    max2 = y\n  return max2\nprint(findMaximum(3, 2))\n```\n\n## Solution 2: Use the Built-in​ `max` Function\nTo calculate the maximum of numbers n1,n2,n3, use the following built-in max function \n`max(n1,n2,n3,..)`:\n\n\n\n```python310\ndef findMaximum(x, y):\n  max2 = max(x, y) \n  return max2 \nprint(findMaximum(2, 3))\n```\n\nLet’s move on to the next problem.", "mdHtml": "<h2 id=\"solution-1-use-the-if-statement\">Solution 1: Use the <code>if</code> Statement</h2>\n<p>Use the <code>if</code> statement to compare if variable x is greater than y <code>x&gt;y</code>.\nIf x is greater than y, then save the value of x in maximum; otherwise, save the value of y.</p>\n\n<div class='image-component'>\n```python310\ndef findMaximum(x, y):\n  max2 = 0\n  if(x > y):\n    max2 = x\n  else:\n    max2 = y\n  return max2\nprint(findMaximum(3, 2))\n```\n</div>\n<h2 id=\"solution-2-use-the-built-in-max-function\">Solution 2: Use the Built-in​ <code>max</code> Function</h2>\n<p>To calculate the maximum of numbers n1,n2,n3, use the following built-in max function\n<code>max(n1,n2,n3,..)</code>:</p>\n\n<div class='image-component'>\n```python310\ndef findMaximum(x, y):\n  max2 = max(x, y) \n  return max2 \nprint(findMaximum(2, 3))\n```\n</div>\n<p>Let’s move on to the next problem.</p>", "summary": {"titleUpdated": true, "title": "", "description": "This lesson will explain how to compute and return the maximum of two numbers.", "tags": []}, "type": "Lesson"}, {"id": "5676414367956992", "title": "Challenge 4: Check If a Number Is Divisible by Another", "is_preview": false, "slug": "challenge-4-check-if-a-number-is-divisible-by-another", "text": "## Problem Statement\n\nImplement a function named `isDivisible` that receives two parameters (named `x` and `y`) and  only returns **true** if \"x\" can be divided by \"y\"(**and false otherwise**). \n\n> A number is divisible by another when the remainder of the division is zero. Use the modulo operator (\"%\").\n\n### Input\nTwo numbers x and y\n\n### Output\nReturns true if x is divisible by y and false otherwise\n\n### Sample Input\nx = 4, y = 2\n\n### Sample Output\nTrue\n\n \n## Coding Exercise\nWrite your code below. It is recommended​ that you try solving the exercise yourself before viewing the solution.\n\n\n\n\n```python310\ndef isDivisible(x, y):\n  # Write code here!\n pass\n```\n\nNow, let’s move on to the detailed solution review of the above problem.", "mdHtml": "<h2 id=\"problem-statement\">Problem Statement</h2>\n<p>Implement a function named <code>isDivisible</code> that receives two parameters (named <code>x</code> and <code>y</code>) and  only returns <strong>true</strong> if “x” can be divided by “y”(<strong>and false otherwise</strong>).</p>\n<blockquote>\n<p>A number is divisible by another when the remainder of the division is zero. Use the modulo operator (&quot;%&quot;).</p>\n</blockquote>\n<h3 id=\"input\">Input</h3>\n<p>Two numbers x and y</p>\n<h3 id=\"output\">Output</h3>\n<p>Returns true if x is divisible by y and false otherwise</p>\n<h3 id=\"sample-input\">Sample Input</h3>\n<p>x = 4, y = 2</p>\n<h3 id=\"sample-output\">Sample Output</h3>\n<p>True</p>\n<h2 id=\"coding-exercise\">Coding Exercise</h2>\n<p>Write your code below. It is recommended​ that you try solving the exercise yourself before viewing the solution.</p>\n\n<div class='image-component'>\n```python310\ndef isDivisible(x, y):\n  # Write code here!\n pass\n```\n</div>\n<p>Now, let’s move on to the detailed solution review of the above problem.</p>", "summary": {"titleUpdated": true, "title": "Challenge 4: Check if a Number is Divisible by Another", "description": "In this challenge, you are required to check if a number x is divisible by a number y.", "tags": []}, "type": "Lesson"}, {"id": "5744357932007424", "title": "Solution Review: Check if a Number is Divisible by Another", "is_preview": false, "slug": "solution-review-check-if-a-number-is-divisible-by-another", "text": "## Solution: Use the Modulus Operator(%)\nUse `%` to compute `x % y` for calculating the remainder. If the remainder is 0, then x% y returns true (and false otherwise).\n\nThe following python code helps to check whether a number **x** is divisible by a number **y**.\n\n\n```python310\ndef isDivisble(x,y):\n  if  x % y == 0:\n    return True\n  else :\n    return False\n    \nprint(isDivisble(24, 4))\nprint(isDivisble(24, 7))\n```\n\nNow that you have learned about functions let's learn about an important concept regarding functions, i.e., \"Recursion\" in the next lesson.", "mdHtml": "<h2 id=\"solution-use-the-modulus-operator\">Solution: Use the Modulus Operator(%)</h2>\n<p>Use <code>%</code> to compute <code>x % y</code> for calculating the remainder. If the remainder is 0, then x% y returns true (and false otherwise).</p>\n<p>The following python code helps to check whether a number <strong>x</strong> is divisible by a number <strong>y</strong>.</p>\n\n<div class='image-component'>\n```python310\ndef isDivisble(x,y):\n  if  x % y == 0:\n    return True\n  else :\n    return False\n    \nprint(isDivisble(24, 4))\nprint(isDivisble(24, 7))\n```\n</div>\n<p>Now that you have learned about functions let’s learn about an important concept regarding functions, i.e., “Recursion” in the next lesson.</p>", "summary": {"titleUpdated": true, "title": "", "description": "This lesson will explain if one number is divisible by another.", "tags": []}, "type": "Lesson"}, {"id": "5762578928107520", "title": "Recursion", "is_preview": false, "slug": "recursion", "text": "## What Is Recursion? \nRecursion is when a ​function calls itself again and again until it reaches the base condition.\n\n### Parts of a Recursion\nThere are two parts of recursion: `base condition` and `recursive function`.\n\nFor instance, take the example of a Factorial function.\n\nFactorial(!) of a number n is the product of all positive numbers from our chosen number n down to 1.\n\n```python\nfactorial(n) = 1   if n <= 1  #base case\n                        \nfactorial (n) = n * factorial(n-1) #recursive function                      \n```\n \nLet's use an example of factorial of 5.\n```python\n5! = 5 × 4!\n   = 5 × 4 × 3!\n   = 5 × 4 × 3 ×2! \n   = 5 × 4 ×3 ×2 ×1\n   = 120 \n```\nBelow is the call to the function\nfactorial given 5 as a parameter to the function.\nfactorial(5) \n\nIt is calculated recursively in the following manner: \n\n**[CanvasAnimation 图表]** - 交互式图表内容\n\nBasically, the factorial of 5 is 5 times the factorial of 4, etc. Finally, the factorial of 1 (or of zero) is 1 which breaks the recursion. In Python, we could write the following recursive function:\n\n```python310\ndef factorial(n):\n    if(n <= 1):\n        return 1\n    else:\n        return(n * factorial(n-1))\nprint(\"Factorial:\")\nprint(factorial(5))\n```\n\nThe trick with recursive functions is that there must be a “base” case where the recursion must end with a recursive case that iterates towards the base case. In the case of factorial, we know that the factorial of zero is one, and the factorial of a number greater than zero will depend on the factorial of the previous number until it reaches zero.\n\nNow that the concept of 'Recursion' in python is clear, let’s check your knowledge in the upcoming exercises before moving on to the next chapter ---'Iteration and Loops'.", "mdHtml": "<h2>What Is Recursion?</h2>\n<p>Recursion is when a ​function calls itself again and again until it reaches the base condition.</p>\n<h3>Parts of a Recursion</h3>\n<p>There are two parts of recursion: <code>base condition</code> and <code>recursive function</code>.</p>\n<p>For instance, take the example of a Factorial function.</p>\n<p>Factorial(!) of a number n is the product of all positive numbers from our chosen number n down to 1.</p>\n<pre><code class=\"language-python\">factorial(n) = 1   if n &lt;= 1  #base case\n                        \nfactorial (n) = n * factorial(n-1) #recursive function                      \n</code></pre>\n<p>Let’s use an example of factorial of 5.</p>\n<pre><code class=\"language-python\">5! = 5 × 4!\n   = 5 × 4 × 3!\n   = 5 × 4 × 3 ×2! \n   = 5 × 4 ×3 ×2 ×1\n   = 120 \n</code></pre>\n<p>Below is the call to the function\nfactorial given 5 as a parameter to the function.\nfactorial(5)</p>\n<p>It is calculated recursively in the following manner:</p>\n\n<div class='image-component'>\n**[CanvasAnimation 图表]** - 交互式图表内容\n</div>\n<p>Basically, the factorial of 5 is 5 times the factorial of 4, etc. Finally, the factorial of 1 (or of zero) is 1 which breaks the recursion. In Python, we could write the following recursive function:</p>\n\n<div class='image-component'>\n```python310\ndef factorial(n):\n    if(n <= 1):\n        return 1\n    else:\n        return(n * factorial(n-1))\nprint(\"Factorial:\")\nprint(factorial(5))\n```\n</div>\n<p>The trick with recursive functions is that there must be a “base” case where the recursion must end with a recursive case that iterates towards the base case. In the case of factorial, we know that the factorial of zero is one, and the factorial of a number greater than zero will depend on the factorial of the previous number until it reaches zero.</p>\n<p>Now that the concept of ‘Recursion’ in python is clear, let’s check your knowledge in the upcoming exercises before moving on to the next chapter —‘Iteration and Loops’.</p>", "summary": {"titleUpdated": true, "title": "", "description": "Now that we are familiar with functions, let's have a review on the most important concept using a function: recursion.", "tags": []}, "type": "Lesson"}, {"id": "5748638538006528", "title": "Challenge 5: Compute nth Fibonacci Number", "is_preview": false, "slug": "challenge-5-compute-nth-fibonacci-number", "text": "## Problem Statement\n\nImplement the `Fibonacci` function that takes a number `n` and calculates the nth Fibonacci.\n\n>The Fibonacci Sequence is the series of numbers: 0, 1, 1, 2, 3, 5, 8, 13, 21, 34, … \nThe next number is found by adding up the previous two consecutive numbers.\n\n### Input\nAn integer \n\n### Output\nnth fibonacci term\n\n### Sample Input\n  7\n\n### Sample Output\n\n  13\n\n## Coding Exercise\n\nWrite your code in the code widget below. If you don’t get it right, don’t fret; the solution is also given.\n\n\n```python310\ndef fibonacci(n):\n   ## write base case\n   ## write recursive case \n  pass\n```\n\nNow, let’s move on to the detailed solution review of the above problem.", "mdHtml": "<h2>Problem Statement</h2>\n<p>Implement the <code>Fibonacci</code> function that takes a number <code>n</code> and calculates the nth Fibonacci.</p>\n<blockquote>\n<p>The Fibonacci Sequence is the series of numbers: 0, 1, 1, 2, 3, 5, 8, 13, 21, 34, …\nThe next number is found by adding up the previous two consecutive numbers.</p>\n</blockquote>\n<h3>Input</h3>\n<p>An integer</p>\n<h3>Output</h3>\n<p>nth fibonacci term</p>\n<h3>Sample Input</h3>\n<p>7</p>\n<h3>Sample Output</h3>\n<p>13</p>\n<h2>Coding Exercise</h2>\n<p>Write your code in the code widget below. If you don’t get it right, don’t fret; the solution is also given.</p>\n\n<div class='image-component'>\n```python310\ndef fibonacci(n):\n   ## write base case\n   ## write recursive case \n  pass\n```\n</div>\n<p>Now, let’s move on to the detailed solution review of the above problem.</p>", "summary": {"titleUpdated": true, "title": "Challenge 5: Compute Factorial of a Number", "description": "In this challenge, you are required to compute the nth <PERSON><PERSON><PERSON> number.", "tags": []}, "type": "Lesson"}, {"id": "5663921784487936", "title": "Solution Review : Compute nth Fibonacci Number", "is_preview": true, "slug": "solution-review-compute-nth-fibonacci-number", "text": "## Solution: Use Recursion\nThe Fibonacci sequence is obtained by adding the previous two consecutive terms; they are defined by the sequence,\n```python\nfibonacci(0) = 0 # base case \nfibonacci(1) = 1 \n\nfibonacci(n) = fibonacci(n – 1) + fi<PERSON><PERSON><PERSON>(n – 2) for n >= 2 # recursive case\n```\nFor example ,\n```\n0,1,1,2,3,5,8,......\n\nif n=2, f(n)=3\n```\nThe following illustration explains the concept by calculating the **fourth** Fibonacci number. \nThe following python code demonstrates how to find the nth fibonacci number:\n\n```python310\ndef fibonacci(n):\n  if n <= 1:\n       return n\n  else:\n       return(fibon<PERSON><PERSON>(n-1) + fibon<PERSON><PERSON>(n-2))\nprint(fibon<PERSON><PERSON>(4))    \n    \n```\n\nLet’s move on to the next problem.", "mdHtml": "<h2 id=\"solution-use-recursion\">Solution: Use Recursion</h2>\n<p>The Fibonacci sequence is obtained by adding the previous two consecutive terms; they are defined by the sequence,</p>\n<pre><code class=\"language-python\">fibonacci(0) = 0 # base case \nfibonacci(1) = 1 \n\nfibonacci(n) = fibonacci(n – 1) + fi<PERSON>acci(n – 2) for n &gt;= 2 # recursive case\n</code></pre>\n<p>For example ,</p>\n<pre><code>0,1,1,2,3,5,8,......\n\nif n=2, f(n)=3\n</code></pre>\n<p>The following illustration explains the concept by calculating the <strong>fourth</strong> Fibonacci number.</p>\n\n<p>The following python code demonstrates how to find the nth fibonacci number:</p>\n\n<div class='image-component'>\n```python310\ndef fibonacci(n):\n  if n <= 1:\n       return n\n  else:\n       return(fibonacci(n-1) + fi<PERSON><PERSON><PERSON>(n-2))\nprint(fi<PERSON><PERSON><PERSON>(4))    \n    \n```\n</div>\n<p>Let’s move on to the next problem.</p>", "summary": {"titleUpdated": true, "title": "", "description": "This lesson will explain how to compute the nth <PERSON><PERSON><PERSON> number using recursion.", "tags": []}, "type": "Lesson"}, {"id": "5656966756040704", "title": "Challenge 6: <PERSON><PERSON><PERSON>m of First 'n' Natural Numbers", "is_preview": false, "slug": "challenge-6-compute-sum-of-first-n-natural-numbers", "text": "## Problem Statement\nImplement a `sum_N_Numbers` recursive function to compute the sum of the `n` natural numbers (where \\(n\\) is a function parameter). Start by thinking about the base case (the sum of the first 1 integers is?) and then think about the recursive case.\n\n> **Note:** Natural Numbers start from 1, i.e., 1, 2, 3, 4, 5, ....\n\n\n### Input\nA natural number `n`\n\n### Output\nThe sum of all numbers from 1 upto that input natural number `n`\n\n### Sample Input\n  7\n\n### Sample Output\n\n  28\n\n## Coding Exercise\n\nWrite your code in the code widget below. If you don’t get it right, don’t fret; the solution is also given.\n\n\n\n```python310\ndef sum_N_Numbers (n):\n  # Write code here\n  pass\n```\n\nNow, let’s move on to the detailed solution review of the above problem.", "mdHtml": "<h2>Problem Statement</h2>\n<p>Implement a <code>sum_N_Numbers</code> recursive function to compute the sum of the <code>n</code> natural numbers (where (n) is a function parameter). Start by thinking about the base case (the sum of the first 1 integers is?) and then think about the recursive case.</p>\n<blockquote>\n<p><strong>Note:</strong> Natural Numbers start from 1, i.e., 1, 2, 3, 4, 5, …</p>\n</blockquote>\n<h3>Input</h3>\n<p>A natural number <code>n</code></p>\n<h3>Output</h3>\n<p>The sum of all numbers from 1 upto that input natural number <code>n</code></p>\n<h3>Sample Input</h3>\n<p>7</p>\n<h3>Sample Output</h3>\n<p>28</p>\n<h2>Coding Exercise</h2>\n<p>Write your code in the code widget below. If you don’t get it right, don’t fret; the solution is also given.</p>\n\n<div class='image-component'>\n```python310\ndef sum_N_Numbers (n):\n  # Write code here\n  pass\n```\n</div>\n<p>Now, let’s move on to the detailed solution review of the above problem.</p>", "summary": {"titleUpdated": true, "title": "Challenge 6: Compute Sum of First n Numbers", "description": "In this challenge, your task is to compute the sum of the first 'n' natural numbers. ", "tags": []}, "type": "Lesson"}, {"id": "5730579676921856", "title": "Solution Review:  Compute Sum of First n Natural Numbers", "is_preview": false, "slug": "solution-review-compute-sum-of-first-n-natural-numbers", "text": "## Solution: Use Recursion\nThe sum is calculated by adding the sum of previous numbers down to 1.\n```python\nf(1) = 1 # base case\nf(n) = f(n-1) + n # recursive case\n```\nThe recursion stops when n is less than or equal to 1. \nThe following illustration explains how to calculate the sum of the first **n** natural numbers using recursion.\n\nThe following python code demonstrates how to compute the sum of first n numbers using recursion.\n\n```python310\ndef sum_N_Numbers (n):\n  if n <= 1:\n    return n\n  else:\n    return n + sum_N_Numbers (n - 1)\n    \nprint(sum_N_Numbers(5))\n```\n\nNow that you have the insight of modules, functions, and recursion, let's move on to the quiz.", "mdHtml": "<h2 id=\"solution-use-recursion\">Solution: Use Recursion</h2>\n<p>The sum is calculated by adding the sum of previous numbers down to 1.</p>\n<pre><code class=\"language-python\">f(1) = 1 # base case\nf(n) = f(n-1) + n # recursive case\n</code></pre>\n<p>The recursion stops when n is less than or equal to 1.\nThe following illustration explains how to calculate the sum of the first <strong>n</strong> natural numbers using recursion.</p>\n\n<p>The following python code demonstrates how to compute the sum of first n numbers using recursion.</p>\n\n<div class='image-component'>\n```python310\ndef sum_N_Numbers (n):\n  if n <= 1:\n    return n\n  else:\n    return n + sum_N_Numbers (n - 1)\n    \nprint(sum_N_Numbers(5))\n```\n</div>\n<p>Now that you have the insight of modules, functions, and recursion, let’s move on to the quiz.</p>", "summary": {"titleUpdated": true, "title": "", "description": "This lesson will explain how to compute the sum of the first n numbers using recursion.", "tags": []}, "type": "Lesson"}, {"id": "5706847432474624", "title": "Quick Quiz on Modules and Functions", "is_preview": false, "slug": "quick-quiz-on-modules-and-functions", "text": "Now that you have learned about modules and functions in Python, what if you  want a set of instructions or structures to be repeated in a sequence a specified number of times? Let's learn about \"Iterations and Loops\" in the next chapter.", "mdHtml": "<p>Now that you have learned about modules and functions in Python, what if you  want a set of instructions or structures to be repeated in a sequence a specified number of times? Let’s learn about “Iterations and Loops” in the next chapter.</p>", "summary": {"titleUpdated": false, "title": "", "description": "", "tags": []}, "type": "Quiz", "questions": [{"questionText": "What is the output of the following function?\n\n```python\ndef function(fruit = \"Orange\"):\n  print(\"I like \" + fruit)\n\nfunction(\"Banana\")\nfunction(\"Peach\")\nfunction()\nfunction(\"Mango\") \n```", "questionOptions": [{"text": "```python\nI like Ban<PERSON>\nI like Peach\nI like Orange\nI like Mango\n```", "correct": true, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<pre><code class=\"language-python\">I like Banana\nI like Peach\nI like Orange\nI like Mango\n</code></pre>\n"}, {"text": "```python\nI like <PERSON><PERSON>\nI like Peach\nI like Peach\nI like Mango\n```", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<pre><code class=\"language-python\">I like Banana\nI like P<PERSON>\nI like Peach\nI like Mango\n</code></pre>\n"}, {"text": "```python\nI like <PERSON><PERSON>\nI like Peach\nI like Mango\n```", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<pre><code class=\"language-python\">I like Banana\nI like <PERSON><PERSON>\nI like Man<PERSON>\n</code></pre>\n"}], "questionTextHtml": "<p>What is the output of the following function?</p>\n<pre><code class=\"language-python\">def function(fruit = &quot;Orange&quot;):\n  print(&quot;I like &quot; + fruit)\n\nfunction(&quot;Banana&quot;)\nfunction(&quot;Peach&quot;)\nfunction()\nfunction(&quot;Mango&quot;) \n</code></pre>\n"}, {"questionText": "What is the output of the following function?\n```python\ndef function(x):\n  return 5 +x\n\nprint(function(2))\nprint(function(4))\nprint(function(6)) \n```", "questionOptions": [{"text": "```\n7\n9\n11 \n```", "correct": true, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<pre><code>7\n9\n11 \n</code></pre>\n"}, {"text": "```\n5\n5\n5\n```", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<pre><code>5\n5\n5\n</code></pre>\n"}], "questionTextHtml": "<p>What is the output of the following function?</p>\n<pre><code class=\"language-python\">def function(x):\n  return 5 +x\n\nprint(function(2))\nprint(function(4))\nprint(function(6)) \n</code></pre>\n"}, {"questionText": "What is the output of the following code?\n```python\ndef recursion(k):\n  if(k>0):\n    result = k+recursion(k-1)\n  else:\n    result = 0\n  return result\n\nprint(recursion(2))\n```\n", "questionOptions": [{"text": "1", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>1</p>\n"}, {"text": "3", "correct": true, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>3</p>\n"}], "questionTextHtml": "<p>What is the output of the following code?</p>\n<pre><code class=\"language-python\">def recursion(k):\n  if(k&gt;0):\n    result = k+recursion(k-1)\n  else:\n    result = 0\n  return result\n\nprint(recursion(2))\n</code></pre>\n"}]}]}, {"id": "f8fqn60c2", "title": "Iteration & Loops", "summary": "Grasp the fundamentals of loops in Python, automating repetitive tasks with efficiency.", "pages": [{"id": "6196793142411264", "title": "Iteration & Loops", "is_preview": false, "slug": "iteration-loops", "text": "Loops are used in computer programming to automate repetitive tasks.\n\n## Types of Loops in Python\nIn python, there are two types of loops.\n### For Loop\nIn Python, the most common form of iteration is the \"for\" loop.\n\nA `for` loop is used for iterating over a sequence of items.\n\nThe general syntax for creating a `for` loop is:\n```python\nfor index in sequence:\n    statement\n```\nHere,\na sequence can be a list, tuple, dictionary, set, or string. \n> **Note:** The for loop does not require an iterating/ indexing variable to set beforehand. The indexing can be defined in a loop.\n>\n#### Looping Through a List\nThe \"for\" loop allows you to iterate over all items of a list, such that you can do whatever you want with each item.\n\nFor instance, let’s create a list and print the square value of each element.\n\n```python310\nfor value in [0, 1, 2, 3, 4, 5]:\n  print(value * value)\n```\n\n\nIt’s quite easy but very powerful\\! The \"for\" loop is the basis of many things in programming. For instance, you already know about the \"sum(list)\" function which sums all the elements of a list, but here’s an example using the \"for\" loop:\n\n```python310\nmylist = [1,5,7]\nsum = 0\nfor value in mylist:\n  sum = sum + value\nprint(sum)\n```\n\nBasically, you create the variable \"sum\" and keep adding each value as it comes from the list.\n\n#### Looping Using the `range`\nThe `range()` function allows looping through a set of code a specified number of times. The range(n) function returns a sequence of numbers, starting from 0 by default and incrementing by 1 (by default), and ends at a specified number(n) minus 1.\n\n\n\n\n```python310\nfor x in range(6):\n  print(x)\n```\n\nSometimes, instead of the values of a list, you may need to work with the indexes; i.e., not with the values, but the positions of the values in the list. Here’s an example that iterates over a list and returns the indexes and the values for each index:\n\n```python310\nmylist = [1,5,7]\nfor i in range(len(mylist)):\n  print (\"Index:\", i, \"Value:\", mylist[i])\n```\n\n> **Note:** You can see that we are not iterating over the list itself, but iterating over the \"range\" of the length of the list. The range function returns a special list.\n\n\n\n\n\nSo, when you use \"range\" you are not iterating over \"mylist\"; you're iterating but over a list with some numbers that you’ll use as indexes to access individual values on \"mylist\". \n\n\nThere's more about the range function in the [Python documentation](https://docs.python.org/3/tutorial/controlflow.html#the-range-function).\n\n#### Looping Using `enumerate`\nSometimes you may need both things (indexes and values), and you can use the `\"enumerate\"` function:\n\n```python310\nmylist = [1, 5, 7]\n\nfor i, value in enumerate(mylist):\n  print(\"Index:\", i, \"Value:\", value)\n```\n\n> **Note:** The first value on a Python list is always at index 0, while it is greater than 0.\n\n#### Looping Through a String\nStrings contain a sequence of characters so that they can be iterated.\n\n\n```python310\nfor x in \"Full Speed Python\":\n  print(x)\n```\n\n### While Loop\nFinally, we also have the `\"while\"` statement that allows us to repeat a sequence of instructions while a specified condition is true.\n\nThe general syntax for creating a while loop is:\n```python\nwhile(condition):\n    statement\n```\n\nFor instance, the following example starts \"n\" at 10 and **while \"n\" is greater than 0**, it keeps subtracting 1 from \"n\". When \"n\" reaches 0, the condition \"n \\> 0\" is false, and the loop ends:\n\n```python310\nn = 10\nwhile n > 0:\n  print(n)\n  n = n-1\n```\n\nNotice that it never prints 0.\n\nNow that the idea of ​'Iteration and Loops' in python is clear, let’s check your knowledge in the upcoming exercises before moving on to 'Dictionaries' chapter.", "mdHtml": "<p>Loops are used in computer programming to automate repetitive tasks.</p>\n<h2 id=\"types-of-loops-in-python\">Types of Loops in Python</h2>\n<p>In python, there are two types of loops.</p>\n<h3 id=\"for-loop\">For Loop</h3>\n<p>In Python, the most common form of iteration is the “for” loop.</p>\n<p>A <code>for</code> loop is used for iterating over a sequence of items.</p>\n<p>The general syntax for creating a <code>for</code> loop is:</p>\n<pre><code class=\"language-python\">for index in sequence:\n    statement\n</code></pre>\n<p>Here,\na sequence can be a list, tuple, dictionary, set, or string.</p>\n<blockquote>\n<p><strong>Note:</strong> The for loop does not require an iterating/ indexing variable to set beforehand. The indexing can be defined in a loop.</p>\n</blockquote>\n<h4 id=\"looping-through-a-list\">Looping Through a List</h4>\n<p>The “for” loop allows you to iterate over all items of a list, such that you can do whatever you want with each item.</p>\n<p>For instance, let’s create a list and print the square value of each element.</p>\n\n<div class='image-component'>\n```python310\nfor value in [0, 1, 2, 3, 4, 5]:\n  print(value * value)\n```\n</div>\n<p>It’s quite easy but very powerful! The “for” loop is the basis of many things in programming. For instance, you already know about the “sum(list)” function which sums all the elements of a list, but here’s an example using the “for” loop:</p>\n\n<div class='image-component'>\n```python310\nmylist = [1,5,7]\nsum = 0\nfor value in mylist:\n  sum = sum + value\nprint(sum)\n```\n</div>\n<p>Basically, you create the variable “sum” and keep adding each value as it comes from the list.</p>\n<h4 id=\"looping-using-the-range\">Looping Using the <code>range</code></h4>\n<p>The <code>range()</code> function allows looping through a set of code a specified number of times. The range(n) function returns a sequence of numbers, starting from 0 by default and incrementing by 1 (by default), and ends at a specified number(n) minus 1.</p>\n\n<div class='image-component'>\n```python310\nfor x in range(6):\n  print(x)\n```\n</div>\n<p>Sometimes, instead of the values of a list, you may need to work with the indexes; i.e., not with the values, but the positions of the values in the list. Here’s an example that iterates over a list and returns the indexes and the values for each index:</p>\n\n<div class='image-component'>\n```python310\nmylist = [1,5,7]\nfor i in range(len(mylist)):\n  print (\"Index:\", i, \"Value:\", mylist[i])\n```\n</div>\n<blockquote>\n<p><strong>Note:</strong> You can see that we are not iterating over the list itself, but iterating over the “range” of the length of the list. The range function returns a special list.</p>\n</blockquote>\n\n<p>So, when you use “range” you are not iterating over “mylist”; you’re iterating but over a list with some numbers that you’ll use as indexes to access individual values on “mylist”.</p>\n<p>There’s more about the range function in the <a href=\"https://docs.python.org/3/tutorial/controlflow.html#the-range-function\">Python documentation</a>.</p>\n<h4 id=\"looping-using-enumerate\">Looping Using <code>enumerate</code></h4>\n<p>Sometimes you may need both things (indexes and values), and you can use the <code>&quot;enumerate&quot;</code> function:</p>\n\n<div class='image-component'>\n```python310\nmylist = [1, 5, 7]\n\nfor i, value in enumerate(mylist):\n  print(\"Index:\", i, \"Value:\", value)\n```\n</div>\n<blockquote>\n<p><strong>Note:</strong> The first value on a Python list is always at index 0, while it is greater than 0.</p>\n</blockquote>\n<h4 id=\"looping-through-a-string\">Looping Through a String</h4>\n<p>Strings contain a sequence of characters so that they can be iterated.</p>\n\n<div class='image-component'>\n```python310\nfor x in \"Full Speed Python\":\n  print(x)\n```\n</div>\n<h3 id=\"while-loop\">While Loop</h3>\n<p>Finally, we also have the <code>&quot;while&quot;</code> statement that allows us to repeat a sequence of instructions while a specified condition is true.</p>\n<p>The general syntax for creating a while loop is:</p>\n<pre><code class=\"language-python\">while(condition):\n    statement\n</code></pre>\n<p>For instance, the following example starts “n” at 10 and <strong>while “n” is greater than 0</strong>, it keeps subtracting 1 from “n”. When “n” reaches 0, the condition “n &gt; 0” is false, and the loop ends:</p>\n\n<div class='image-component'>\n```python310\nn = 10\nwhile n > 0:\n  print(n)\n  n = n-1\n```\n</div>\n<p>Notice that it never prints 0.</p>\n<p>Now that the idea of ​’Iteration and Loops’ in python is clear, let’s check your knowledge in the upcoming exercises before moving on to ‘Dictionaries’ chapter.</p>", "summary": {"titleUpdated": true, "title": "Iteration & Loops", "description": "In this chapter, we are going to explore iteration and loops in Python.", "tags": []}, "type": "Lesson"}, {"id": "5701995193171968", "title": "Challenge 1: Sum Elements of a List", "is_preview": false, "slug": "challenge-1-sum-elements-of-a-list", "text": "## Problem Statement\nCreate a `sumList` function that receives a `list` as a parameter and returns the sum of all the elements in the list. \n\n### Input\nA list\n\n### Output\nSum of elements in the list\n\n### Sample Input\n[1, 2, 3, 4, 5]\n\n### Sample Output\n15\n\n\n\n**[CanvasAnimation 图表]** - 交互式图表内容\n\n## Coding Exercise\n\nWrite your code below. It is recommended​ that you try solving the exercise yourself before viewing the solution.\n\nFor this section, you may want to consult the \n[Python documentation on Loops](https://docs.python.org/3/tutorial/controlflow.html#for-statements).\n\n```python310\ndef sumList(l):\n pass\n```\n\nNow, let’s move on to the detailed solution review of the above problem.", "mdHtml": "<h2 id=\"problem-statement\">Problem Statement</h2>\n<p>Create a <code>sumList</code> function that receives a <code>list</code> as a parameter and returns the sum of all the elements in the list.</p>\n<h3 id=\"input\">Input</h3>\n<p>A list</p>\n<h3 id=\"output\">Output</h3>\n<p>Sum of elements in the list</p>\n<h3 id=\"sample-input\">Sample Input</h3>\n<p>[1, 2, 3, 4, 5]</p>\n<h3 id=\"sample-output\">Sample Output</h3>\n<p>15</p>\n\n<div class='image-component'>\n**[CanvasAnimation 图表]** - 交互式图表内容\n</div>\n<h2 id=\"coding-exercise\">Coding Exercise</h2>\n<p>Write your code below. It is recommended​ that you try solving the exercise yourself before viewing the solution.</p>\n<p>For this section, you may want to consult the\n<a href=\"https://docs.python.org/3/tutorial/controlflow.html#for-statements\">Python documentation on Loops</a>.</p>\n\n<div class='image-component'>\n```python310\ndef sumList(l):\n pass\n```\n</div>\n<p>Now, let’s move on to the detailed solution review of the above problem.</p>", "summary": {"titleUpdated": true, "title": "Challenge 1: Sum Elements of an Array", "description": "In this challenge, you are required to compute the sum of elements in an array.", "tags": []}, "type": "Lesson"}, {"id": "5701635892314112", "title": "Solution Review:  Sum Elements of a List", "is_preview": false, "slug": "solution-review-sum-elements-of-a-list", "text": "## Solution: Use `for` Loop\nUse the `for` loop to iterate over the list while adding the value stored at each index and storing the value in the variable sum.\nThe following illustration explains how to calculate the sum of variables in a list using a `for` loop.\n\n**[CanvasAnimation 图表]** - 交互式图表内容\n\nThe following python code helps solve the problem.\n\n```python310\ndef sumList(l):\n    sum = 0\n    for x in l:\n        sum += x\n    return sum\nl = [1, 2, 3, 4, 5]\nprint(sumList(l))\n```\n\nLet’s move on to the next problem.", "mdHtml": "<h2 id=\"solution-use-for-loop\">Solution: Use <code>for</code> Loop</h2>\n<p>Use the <code>for</code> loop to iterate over the list while adding the value stored at each index and storing the value in the variable sum.\nThe following illustration explains how to calculate the sum of variables in a list using a <code>for</code> loop.</p>\n\n<div class='image-component'>\n**[CanvasAnimation 图表]** - 交互式图表内容\n</div>\n<p>The following python code helps solve the problem.</p>\n\n<div class='image-component'>\n```python310\ndef sumList(l):\n    sum = 0\n    for x in l:\n        sum += x\n    return sum\nl = [1, 2, 3, 4, 5]\nprint(sumList(l))\n```\n</div>\n<p>Let’s move on to the next problem.</p>", "summary": {"titleUpdated": true, "title": "", "description": "This lesson will explain how to calculate the sum elements of an array using a `for` loop.", "tags": []}, "type": "Lesson"}, {"id": "5113464414535680", "title": "Challenge 2: Find Maximum in a List", "is_preview": false, "slug": "challenge-2-find-maximum-in-a-list", "text": "## Problem Statement 1\nCreate a `findMaximum` function that receives a `list` as a parameter and returns the maximum value in the list. As you iterate over the list, you may want to keep track of the current maximum value in order to keep comparing it with the next elements of the list.\n\n### Input\nA list\n\n### Output\nMaximum number in the list\n\n### Sample Input\n[1, 4, 9, 10, 23]\n\n### Sample Output\nmax = 23\n\n \n\n​\n\n**[CanvasAnimation 图表]** - 交互式图表内容\n\n## Coding Exercise\nWrite your code below. It is recommended​ that you try solving the exercise yourself before viewing the solution.\n\nFor this section, you may want to consult the \n[Python documentation on Loops](https://docs.python.org/3/tutorial/controlflow.html#for-statements).\n\n```python310\ndef findMaximum(list):\n    # write your code here\n    pass\n```\n\n## Problem Statement 2\nModify the previous `findMaximumValueIndex(list)` function such that it returns a list with the first element being the index of the maximum value in the list and the second being the maximum value. Besides keeping the maximum value found so far, you also need to keep the position where it occurred.\n### Input\nA list\n\n### Output\nMaximum number and index of the number in the list\n\n### Sample Input\n[1, 4, 23, 10, 9]\n\n### Sample Output\nmax = 23\n\nindex = 2\n\n## Coding Exercise\nWrite your code below. It is recommended​ that you try solving the exercise yourself before viewing the solution.\n\nFor this section, you may want to consult the \n[Python documentation on Loops](https://docs.python.org/3/tutorial/controlflow.html#for-statements).\n\n**[CanvasAnimation 图表]** - 交互式图表内容\n\n\n```python310\ndef findMaximumValueIndex(list):\n    # write your code here\n  pass\n  # return [index,maximum] #return your index and maximum in the form of a list\n```\n\nNow, let’s move on to the detailed solution review of the above two problems.", "mdHtml": "<h2 id=\"problem-statement-1\">Problem Statement 1</h2>\n<p>Create a <code>findMaximum</code> function that receives a <code>list</code> as a parameter and returns the maximum value in the list. As you iterate over the list, you may want to keep track of the current maximum value in order to keep comparing it with the next elements of the list.</p>\n<h3 id=\"input\">Input</h3>\n<p>A list</p>\n<h3 id=\"output\">Output</h3>\n<p>Maximum number in the list</p>\n<h3 id=\"sample-input\">Sample Input</h3>\n<p>[1, 4, 9, 10, 23]</p>\n<h3 id=\"sample-output\">Sample Output</h3>\n<p>max = 23</p>\n<p>​</p>\n\n<div class='image-component'>\n**[CanvasAnimation 图表]** - 交互式图表内容\n</div>\n<h2 id=\"coding-exercise\">Coding Exercise</h2>\n<p>Write your code below. It is recommended​ that you try solving the exercise yourself before viewing the solution.</p>\n<p>For this section, you may want to consult the\n<a href=\"https://docs.python.org/3/tutorial/controlflow.html#for-statements\">Python documentation on Loops</a>.</p>\n\n<div class='image-component'>\n```python310\ndef findMaximum(list):\n    # write your code here\n    pass\n```\n</div>\n<h2 id=\"problem-statement-2\">Problem Statement 2</h2>\n<p>Modify the previous <code>findMaximumValueIndex(list)</code> function such that it returns a list with the first element being the index of the maximum value in the list and the second being the maximum value. Besides keeping the maximum value found so far, you also need to keep the position where it occurred.</p>\n<h3 id=\"input-2\">Input</h3>\n<p>A list</p>\n<h3 id=\"output-2\">Output</h3>\n<p>Maximum number and index of the number in the list</p>\n<h3 id=\"sample-input-2\">Sample Input</h3>\n<p>[1, 4, 23, 10, 9]</p>\n<h3 id=\"sample-output-2\">Sample Output</h3>\n<p>max = 23</p>\n<p>index = 2</p>\n\n<h2 id=\"coding-exercise-2\">Coding Exercise</h2>\n<p>Write your code below. It is recommended​ that you try solving the exercise yourself before viewing the solution.</p>\n<p>For this section, you may want to consult the\n<a href=\"https://docs.python.org/3/tutorial/controlflow.html#for-statements\">Python documentation on Loops</a>.</p>\n\n<div class='image-component'>\n**[CanvasAnimation 图表]** - 交互式图表内容\n</div>\n<div class='image-component'>\n```python310\ndef findMaximumValueIndex(list):\n    # write your code here\n  pass\n  # return [index,maximum] #return your index and maximum in the form of a list\n```\n</div>\n<p>Now, let’s move on to the detailed solution review of the above two problems.</p>", "summary": {"titleUpdated": true, "title": "Challenge 2: Find Maximum in a List", "description": "In this challenge, find the maximum number in a List.", "tags": []}, "type": "Lesson"}, {"id": "5113749358772224", "title": "Solution Review: Find Maximum in a List", "is_preview": false, "slug": "solution-review-find-maximum-in-a-list", "text": "## Solution for Problem Statement1: Use `for` loop \nThe problem statement 1 states that the task is to find the maximum number in the list.\nThe straight forward solution to the problem is:\n* Save the value of the first index.\n* Use for loop to iterate over the list, then compare the saved value with each value in the list. If any value in the list is greater than the saved value, update the saved value.\n\nThis can be seen more clearly in the following illustration.\n\n**[CanvasAnimation 图表]** - 交互式图表内容\n\nThe following python code helps to find a maximum number in a list using for loop.\n\n```python310\ndef findMaximum(list):\n    \n    maximum = list[0]\n    for x in list:\n      if x > maximum:\n          maximum = x\n    return maximum\n\nlist=[1, 2, 7, 4, 5]\n\nprint(findMaximum(list))\n```\n\nNow, look at the solution below: you are required to print the index of the maximum value and also a maximum of a list.\n## Solution For Problem Statement2: Use `enumerate`\nThe problem statement 2 states that the task is to find the maximum number along with the index of that max number in the list. The straight forward solution to the problem is:\n\n* Save the first value in the list as the maximum.\n* Use for loop to iterate over the enumerate list, then compare the saved value with each value in the list. If any value in the list is greater than the saved value, update the saved value. \n\nThis can be seen more clearly in the following illustration.\n\n**[CanvasAnimation 图表]** - 交互式图表内容\n\nThe following python code helps to find a maximum number in a list and the index of a maximum number using for loop.\n\n```python310\ndef findMaximumValueIndex(list):\n    maximum = list[0]\n    index = 0\n    for i, value in enumerate(list):\n        if value > maximum:\n          maximum = value \n          index = i\n    return [index, maximum]\n\nlist = [1, 2, 7, 4, 5]    \n[index, maximum] = findMaximumValueIndex(list)\n\nprint(\"Index:\", index)\nprint(\"Maximum Value:\", maximum)\n```\n\nLet’s move on to the next problem.", "mdHtml": "<h2 id=\"solution-for-problem-statement1-use-for-loop\">Solution for Problem Statement1: Use <code>for</code> loop</h2>\n<p>The problem statement 1 states that the task is to find the maximum number in the list.\nThe straight forward solution to the problem is:</p>\n<ul>\n<li>Save the value of the first index.</li>\n<li>Use for loop to iterate over the list, then compare the saved value with each value in the list. If any value in the list is greater than the saved value, update the saved value.</li>\n</ul>\n<p>This can be seen more clearly in the following illustration.</p>\n\n<div class='image-component'>\n**[CanvasAnimation 图表]** - 交互式图表内容\n</div>\n<p>The following python code helps to find a maximum number in a list using for loop.</p>\n\n<div class='image-component'>\n```python310\ndef findMaximum(list):\n    \n    maximum = list[0]\n    for x in list:\n      if x > maximum:\n          maximum = x\n    return maximum\n\nlist=[1, 2, 7, 4, 5]\n\nprint(findMaximum(list))\n```\n</div>\n<p>Now, look at the solution below: you are required to print the index of the maximum value and also a maximum of a list.</p>\n\n<h2 id=\"solution-for-problem-statement2-use-enumerate\">Solution For Problem Statement2: Use <code>enumerate</code></h2>\n<p>The problem statement 2 states that the task is to find the maximum number along with the index of that max number in the list. The straight forward solution to the problem is:</p>\n<ul>\n<li>Save the first value in the list as the maximum.</li>\n<li>Use for loop to iterate over the enumerate list, then compare the saved value with each value in the list. If any value in the list is greater than the saved value, update the saved value.</li>\n</ul>\n<p>This can be seen more clearly in the following illustration.</p>\n\n<div class='image-component'>\n**[CanvasAnimation 图表]** - 交互式图表内容\n</div>\n<p>The following python code helps to find a maximum number in a list and the index of a maximum number using for loop.</p>\n\n<div class='image-component'>\n```python310\ndef findMaximumValueIndex(list):\n    maximum = list[0]\n    index = 0\n    for i, value in enumerate(list):\n        if value > maximum:\n          maximum = value \n          index = i\n    return [index, maximum]\n\nlist = [1, 2, 7, 4, 5]    \n[index, maximum] = findMaximumValueIndex(list)\n\nprint(\"Index:\", index)\nprint(\"Maximum Value:\", maximum)\n```\n</div>\n<p>Let’s move on to the next problem.</p>", "summary": {"titleUpdated": true, "title": "", "description": "This lesson will explain how to find the maximum number in a list using for loop.", "tags": []}, "type": "Lesson"}, {"id": "5131520457048064", "title": "Challenge 3: Reverse a List", "is_preview": false, "slug": "challenge-3-reverse-a-list", "text": "## Problem Statement\nImplement a `reverse` function that receives a `list` as a parameter and returns the reverse of that list. You may create an empty list and keep adding the values in reversed order as they come from the original list. \n\n### Input\nA list\n\n### Output\nThe reverse of a list\n\n### Sample Input\n[1, 2, 3, 4, 5]\n\n### Sample Output\n[5, 4, 3, 2, 1]\n\n\n\n**[CanvasAnimation 图表]** - 交互式图表内容\n\n## Coding Exercise\nWrite your code below. It is recommended​ that you try solving the exercise yourself before viewing the solution.\n\nFor this section, you may want to consult the \n[Python documentation on Loops](https://docs.python.org/3/tutorial/controlflow.html#for-statements).\n\n```python310\ndef reverse(list):\n  # Write your code here\n  pass\n  # return new_list # uncomment this line when you write code\n```\n\nNow, let’s move on to the detailed solution review of the above problem.", "mdHtml": "<h2 id=\"problem-statement\">Problem Statement</h2>\n<p>Implement a <code>reverse</code> function that receives a <code>list</code> as a parameter and returns the reverse of that list. You may create an empty list and keep adding the values in reversed order as they come from the original list.</p>\n<h3 id=\"input\">Input</h3>\n<p>A list</p>\n<h3 id=\"output\">Output</h3>\n<p>The reverse of a list</p>\n<h3 id=\"sample-input\">Sample Input</h3>\n<p>[1, 2, 3, 4, 5]</p>\n<h3 id=\"sample-output\">Sample Output</h3>\n<p>[5, 4, 3, 2, 1]</p>\n\n<div class='image-component'>\n**[CanvasAnimation 图表]** - 交互式图表内容\n</div>\n<h2 id=\"coding-exercise\">Coding Exercise</h2>\n<p>Write your code below. It is recommended​ that you try solving the exercise yourself before viewing the solution.</p>\n<p>For this section, you may want to consult the\n<a href=\"https://docs.python.org/3/tutorial/controlflow.html#for-statements\">Python documentation on Loops</a>.</p>\n\n<div class='image-component'>\n```python310\ndef reverse(list):\n  # Write your code here\n  pass\n  # return new_list # uncomment this line when you write code\n```\n</div>\n<p>Now, let’s move on to the detailed solution review of the above problem.</p>", "summary": {"titleUpdated": true, "title": "Challenge 3: Reverse a List", "description": "In this challenge, your task is to reverse the elements in a list.", "tags": []}, "type": "Lesson"}, {"id": "5671811639410688", "title": "Solution Review: Reverse a List", "is_preview": false, "slug": "solution-review-reverse-a-list", "text": "## Solution Review: Use `for` loop\n* Calculate the length of the list using the `len()` function.\n* Create a new list. \n* Run a backward loop starting from len to index 0.\n\nThe following illustration makes it clear. \n\n**[CanvasAnimation 图表]** - 交互式图表内容\n\nThe following python code helps to reverse a list using loop.\n\n```python310\ndef reverse(list):\n    length = len(list)\n    s = length\n\n    new_list = [None]*length\n\n    for item in list:\n        s = s - 1\n        new_list[s] = item\n    return new_list\n    \nlist=[1, 2, 3, 4, 5]\nprint(reverse(list))\n```\n\nLet’s move on to the next problem.", "mdHtml": "<h2 id=\"solution-review-use-for-loop\">Solution Review: Use <code>for</code> loop</h2>\n<ul>\n<li>Calculate the length of the list using the <code>len()</code> function.</li>\n<li>Create a new list.</li>\n<li>Run a backward loop starting from len to index 0.</li>\n</ul>\n<p>The following illustration makes it clear.</p>\n\n<div class='image-component'>\n**[CanvasAnimation 图表]** - 交互式图表内容\n</div>\n<p>The following python code helps to reverse a list using loop.</p>\n\n<div class='image-component'>\n```python310\ndef reverse(list):\n    length = len(list)\n    s = length\n\n    new_list = [None]*length\n\n    for item in list:\n        s = s - 1\n        new_list[s] = item\n    return new_list\n    \nlist=[1, 2, 3, 4, 5]\nprint(reverse(list))\n```\n</div>\n<p>Let’s move on to the next problem.</p>", "summary": {"titleUpdated": true, "title": "", "description": "This lesson will explain how to reverse a list in python using for loop.", "tags": []}, "type": "Lesson"}, {"id": "5671552464977920", "title": "Challenge 4: Check If List Is Sorted", "is_preview": false, "slug": "challenge-4-check-if-list-is-sorted", "text": "## Problem Statement \nMake an `isSorted` function that receives a `list` as a ​parameter and returns true if the list is sorted in ascending order. \n\n\nFor instance, \\[1, 2, 2, 3\\] is ordered while \\[1, 2, 3, 2\\] is not. \n\n### Input\nA list\n\n### Output\nTrue if the list is sorted and False otherwise\n\n\n \n\n\n<div class=\"flex justify-center sm:flex-row sm:justify-start -mt-4 flex-col\" style=\"gap: 20px;\">\n<div style='flex: 1; width: 50%;'>\n![](/api/collection/10370001/5765097389555712/page/5671552464977920/image/4669740184240128)\n</div>\n<div style='flex: 1; width: 50%;'>\n![](/api/collection/10370001/5765097389555712/page/5671552464977920/image/5722095151480832)\n</div>\n</div>\n\n## Coding Exercise\nWrite your code below. It is recommended​ that you try solving the exercise yourself before viewing the solution.\n\nConsult the \n[Python documentation on Loops](https://docs.python.org/3/tutorial/controlflow.html#for-statements) for more details!\n\n```python310\ndef isSorted(list):\n  # write your code here\n  pass\n```\n\nNow let's move on to the detailed solution of the above problem.", "mdHtml": "<h2 id=\"problem-statement\">Problem Statement</h2>\n<p>Make an <code>isSorted</code> function that receives a <code>list</code> as a ​parameter and returns true if the list is sorted in ascending order.</p>\n<p>For instance, [1, 2, 2, 3] is ordered while [1, 2, 3, 2] is not.</p>\n<h3 id=\"input\">Input</h3>\n<p>A list</p>\n<h3 id=\"output\">Output</h3>\n<p>True if the list is sorted and False otherwise</p>\n\n<div class='image-component'>\n<div class=\"flex justify-center sm:flex-row sm:justify-start -mt-4 flex-col\" style=\"gap: 20px;\">\n<div style='flex: 1; width: 50%;'>\n![](/api/collection/10370001/5765097389555712/page/5671552464977920/image/4669740184240128)\n</div>\n<div style='flex: 1; width: 50%;'>\n![](/api/collection/10370001/5765097389555712/page/5671552464977920/image/5722095151480832)\n</div>\n</div>\n</div>\n<h2 id=\"coding-exercise\">Coding Exercise</h2>\n<p>Write your code below. It is recommended​ that you try solving the exercise yourself before viewing the solution.</p>\n<p>Consult the\n<a href=\"https://docs.python.org/3/tutorial/controlflow.html#for-statements\">Python documentation on Loops</a> for more details!</p>\n\n<div class='image-component'>\n```python310\ndef isSorted(list):\n  # write your code here\n  pass\n```\n</div>\n<p>Now let’s move on to the detailed solution of the above problem.</p>", "summary": {"titleUpdated": true, "title": "Challenge 4: Check if List is Sorted", "description": "In this challenge, check whether a list is sorted or not.", "tags": []}, "type": "Lesson"}, {"id": "5175158801170432", "title": "Solution Review: Check If List Is Sorted", "is_preview": false, "slug": "solution-review-check-if-list-is-sorted", "text": "## Solution: Use a Loop \nUse a while loop that iterates over the length of the list and checks if the previous element in the list is less than the next element.\nIf so, a variable flag is set to true.\nIf the flag is false, a list is sorted and vice versa.\n  \n\nThe following illustrations explain the concept of sorting a list.\n\n**Case 1:** If the list is sorted\n**Case 2:** If the list is not sorted\nThe following python code demonstates how to check if the list is sorted.\n\n```python310\ndef isSorted(list):\n  flag = 0\n  i = 1\n  while i < len(list): \n      if(list[i] < list[i - 1]): # compare with the previous element\n          flag = 1\n      i += 1\n      \n  if (not flag) : \n      return True \n  else : \n      return False \nprint(isSorted([1,2,3,4,5]))\nprint(isSorted([1,2,5,4,2]))\n```\n\nNow, let's move on to the next problem.", "mdHtml": "<h2 id=\"solution-use-a-loop\">Solution: Use a Loop</h2>\n<p>Use a while loop that iterates over the length of the list and checks if the previous element in the list is less than the next element.\nIf so, a variable flag is set to true.\nIf the flag is false, a list is sorted and vice versa.</p>\n<p>The following illustrations explain the concept of sorting a list.</p>\n<p><strong>Case 1:</strong> If the list is sorted</p>\n\n<p><strong>Case 2:</strong> If the list is not sorted</p>\n\n<p>The following python code demonstates how to check if the list is sorted.</p>\n\n<div class='image-component'>\n```python310\ndef isSorted(list):\n  flag = 0\n  i = 1\n  while i < len(list): \n      if(list[i] < list[i - 1]): # compare with the previous element\n          flag = 1\n      i += 1\n      \n  if (not flag) : \n      return True \n  else : \n      return False \nprint(isSorted([1,2,3,4,5]))\nprint(isSorted([1,2,5,4,2]))\n```\n</div>\n<p>Now, let’s move on to the next problem.</p>", "summary": {"titleUpdated": true, "title": "", "description": "This lesson will explain how to check if  a list is sorted.", "tags": []}, "type": "Lesson"}, {"id": "5737804483002368", "title": "Challenge 5: Find Duplicates in a List", "is_preview": false, "slug": "challenge-5-find-duplicates-in-a-list", "text": "## Problem Statement\nImplement the `hasDuplicates` function which verifies if a `list` has duplicate values.\n\n\n### Input\nA list\n\n### Output\nTrue if the list has duplicates and False otherwise\n\n\n\n<div class=\"flex justify-center sm:flex-row sm:justify-start -mt-4 flex-col\" style=\"gap: 20px;\">\n<div style='flex: 1; width: 50%;'>\n![](/api/collection/10370001/5765097389555712/page/5737804483002368/image/4597466554957824)\n</div>\n<div style='flex: 1; width: 50%;'>\n![](/api/collection/10370001/5765097389555712/page/5737804483002368/image/5141268269105152)\n</div>\n</div>\n\n## Coding Exercise\nWrite your code below. It is recommended​ that you try solving the exercise yourself before viewing the solution. \n\nSuggestion: You may have to use two `for` loops, where you have to check for duplicates on the rest of the list for each value.\n\nConsult the \n[Python documentation on Loops](https://docs.python.org/3/tutorial/controlflow.html#for-statements) for more details!\n\n```python310\ndef hasDuplicates(list):\n  # write code here\n  pass\n```\n\nNow, let’s move on to the detailed solution review of the above problem.", "mdHtml": "<h2 id=\"problem-statement\">Problem Statement</h2>\n<p>Implement the <code>hasDuplicates</code> function which verifies if a <code>list</code> has duplicate values.</p>\n<h3 id=\"input\">Input</h3>\n<p>A list</p>\n<h3 id=\"output\">Output</h3>\n<p>True if the list has duplicates and False otherwise</p>\n\n<div class='image-component'>\n<div class=\"flex justify-center sm:flex-row sm:justify-start -mt-4 flex-col\" style=\"gap: 20px;\">\n<div style='flex: 1; width: 50%;'>\n![](/api/collection/10370001/5765097389555712/page/5737804483002368/image/4597466554957824)\n</div>\n<div style='flex: 1; width: 50%;'>\n![](/api/collection/10370001/5765097389555712/page/5737804483002368/image/5141268269105152)\n</div>\n</div>\n</div>\n<h2 id=\"coding-exercise\">Coding Exercise</h2>\n<p>Write your code below. It is recommended​ that you try solving the exercise yourself before viewing the solution.</p>\n<p>Suggestion: You may have to use two <code>for</code> loops, where you have to check for duplicates on the rest of the list for each value.</p>\n<p>Consult the\n<a href=\"https://docs.python.org/3/tutorial/controlflow.html#for-statements\">Python documentation on Loops</a> for more details!</p>\n\n<div class='image-component'>\n```python310\ndef hasDuplicates(list):\n  # write code here\n  pass\n```\n</div>\n<p>Now, let’s move on to the detailed solution review of the above problem.</p>", "summary": {"titleUpdated": true, "title": "Challenge 5: Find Duplicates in a List", "description": "In this challenge, your task is to find duplicates in a list.", "tags": []}, "type": "Lesson"}, {"id": "5738108754591744", "title": "Solution Review: Find Duplicates in a List", "is_preview": false, "slug": "solution-review-find-duplicates-in-a-list", "text": "## Solution: Use a Nested `for` Loop\nUse a nested for loop where you have to check for duplicates on the rest of the list for each value. \n* The outer loop selects one element and checks for duplicates over the list--through the inner--loop by comparing values.\n* If a duplicate is found, it sets the flag to true.\n\n\nThe following python code helps to check if the list has duplicate values.\n\n```python310\ndef has_duplicates(list):\n  flag = 0\n  for i in range (len(list)):\n    for j in range (i+1,len(list)):\n        if (list[i] == list[j]):\n          flag = 1\n  if (flag == 1): \n     return True\n  else:\n     return False\n  \nl=[1, 2, 3, 4, 5]  \nprint(has_duplicates(l))\nl=[1, 2, 3, 3, 4]  \nprint(has_duplicates(l))\n```\n\nLet’s move on to the next problem.", "mdHtml": "<h2 id=\"solution-use-a-nested-for-loop\">Solution: Use a Nested <code>for</code> Loop</h2>\n<p>Use a nested for loop where you have to check for duplicates on the rest of the list for each value.</p>\n<ul>\n<li>The outer loop selects one element and checks for duplicates over the list–through the inner–loop by comparing values.</li>\n<li>If a duplicate is found, it sets the flag to true.</li>\n</ul>\n\n<p>The following python code helps to check if the list has duplicate values.</p>\n\n<div class='image-component'>\n```python310\ndef has_duplicates(list):\n  flag = 0\n  for i in range (len(list)):\n    for j in range (i+1,len(list)):\n        if (list[i] == list[j]):\n          flag = 1\n  if (flag == 1): \n     return True\n  else:\n     return False\n  \nl=[1, 2, 3, 4, 5]  \nprint(has_duplicates(l))\nl=[1, 2, 3, 3, 4]  \nprint(has_duplicates(l))\n```\n</div>\n<p>Let’s move on to the next problem.</p>", "summary": {"titleUpdated": true, "title": "", "description": "This lesson will help find duplicates in a list.", "tags": []}, "type": "Lesson"}, {"id": "5088955183661056", "title": "Challenge 6: Print Even/Odd Numbers in Descending Order", "is_preview": false, "slug": "challenge-6-print-even-odd-numbers-in-descending-order", "text": "## Problem Statement\n\nImplement a `printEvenOdd` function that receives a number `n` as parameter and prints--in decreasing order--which numbers are even and which are odd until it reaches 0. For instance, on calling `printEvenOdd(10)` it prints\n\n### Input\nThe input will be a number `n`. We have used `n = 10` in the below output.\n### Output\n```\n        Even number: 10\n        Odd number: 9\n        Even number: 8\n        Odd number: 7\n        Even number: 6\n        Odd number: 5\n        Even number: 4\n        Odd number: 3\n        Even number: 2\n        Odd number: 1\n```\n\n\n \n## Coding Exercise\nPlay with the following code to produce the above output.\n\n```python310\ndef printEvenOdd(n):\r\n print (n)\r\n    \n```\n\nNow, let’s move on to the detailed solution review of the above problem.", "mdHtml": "<h2>Problem Statement</h2>\n<p>Implement a <code>printEvenOdd</code> function that receives a number <code>n</code> as parameter and prints–in decreasing order–which numbers are even and which are odd until it reaches 0. For instance, on calling <code>printEvenOdd(10)</code> it prints</p>\n<h3>Input</h3>\n<p>The input will be a number <code>n</code>. We have used <code>n = 10</code> in the below output.</p>\n<h3>Output</h3>\n<pre><code>        Even number: 10\n        Odd number: 9\n        Even number: 8\n        Odd number: 7\n        Even number: 6\n        Odd number: 5\n        Even number: 4\n        Odd number: 3\n        Even number: 2\n        Odd number: 1\n</code></pre>\n<h2>Coding Exercise</h2>\n<p>Play with the following code to produce the above output.</p>\n\n<div class='image-component'>\n```python310\ndef printEvenOdd(n):\r\n print (n)\r\n    \n```\n</div>\n<p>Now, let’s move on to the detailed solution review of the above problem.</p>", "summary": {"titleUpdated": true, "title": "Challenge 6: Print Even/Odd Numbers in Descending Order", "description": "Now that we have practiced for loop, let's learn to print even/odd numbers in descending order using a while loop. ", "tags": []}, "type": "Lesson"}, {"id": "5651590933381120", "title": "Solution Review: Print Even/Odd Numbers in Descending Order", "is_preview": false, "slug": "solution-review-print-even-odd-numbers-in-descending-order", "text": "## Solution: Use a `while` Loop\nUse a `while` loop that does the following in each iteration:\n* checks if the number is divisible by 2 using the modulus\noperator. If yes, then it prints it as even (and otherwise, it's odd).\n* it decrements the input value n \n\nIt does this until the number n is greater than 0. \n\n\n```python310\ndef printEvenOdd(n):\n  while(n > 0):\n    if (n % 2 == 0):\n      print (\"Even number:\",n)\n    else:\n      print (\"Odd number:\",n)\n    n = n - 1 \n\nprintEvenOdd(10)   \n```\n\nNow that you have solved the challenges, let's move on to the quiz on Iteration and Loops.", "mdHtml": "<h2 id=\"solution-use-a-while-loop\">Solution: Use a <code>while</code> Loop</h2>\n<p>Use a <code>while</code> loop that does the following in each iteration:</p>\n<ul>\n<li>checks if the number is divisible by 2 using the modulus\noperator. If yes, then it prints it as even (and otherwise, it’s odd).</li>\n<li>it decrements the input value n</li>\n</ul>\n<p>It does this until the number n is greater than 0.</p>\n\n<div class='image-component'>\n```python310\ndef printEvenOdd(n):\n  while(n > 0):\n    if (n % 2 == 0):\n      print (\"Even number:\",n)\n    else:\n      print (\"Odd number:\",n)\n    n = n - 1 \n\nprintEvenOdd(10)   \n```\n</div>\n<p>Now that you have solved the challenges, let’s move on to the quiz on Iteration and Loops.</p>", "summary": {"titleUpdated": true, "title": "", "description": "This lesson will help print even/odd numbers in descending order using a loop.", "tags": []}, "type": "Lesson"}, {"id": "5764971224891392", "title": "Quick Quiz on Iteration & Loops", "is_preview": false, "slug": "quick-quiz-on-iteration-loops", "text": "Now that you have learned about iterations and loops, what if you want to store groups of objects? Let's learn about \"Dictionaries\" in the next chapter.", "mdHtml": "<p>Now that you have learned about iterations and loops, what if you want to store groups of objects? Let’s learn about “Dictionaries” in the next chapter.</p>", "summary": {"titleUpdated": false, "title": "", "description": "", "tags": []}, "type": "Quiz", "questions": [{"questionText": "What is the output of the following code?\n```python\nfor a in \"Python\":\n  print (a)\n ``` ", "questionOptions": [{"text": "Python", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>Python</p>\n"}, {"text": "P\n\ny\n\nt\n\nh\n\no\n\nn", "correct": true, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>P</p>\n<p>y</p>\n<p>t</p>\n<p>h</p>\n<p>o</p>\n<p>n</p>\n"}], "questionTextHtml": "<p>What is the output of the following code?</p>\n<pre><code class=\"language-python\">for a in &quot;Python&quot;:\n  print (a)\n</code></pre>\n"}, {"questionText": "What is the output of the following code?\n\n```python\nLanguages = [\"Python\", \"Java\", \"R\"]\nfor x in Languages:\n  if x == \"Java\":\n    break\n  print(x) \n```", "questionOptions": [{"text": "Python Java R", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>Python Java R</p>\n"}, {"text": "Python Java", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>Python Java</p>\n"}, {"text": "Python", "correct": true, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>Python</p>\n"}], "questionTextHtml": "<p>What is the output of the following code?</p>\n<pre><code class=\"language-python\">Languages = [&quot;Python&quot;, &quot;Java&quot;, &quot;R&quot;]\nfor x in Languages:\n  if x == &quot;Java&quot;:\n    break\n  print(x) \n</code></pre>\n"}, {"questionText": "What is the output of the following code?\n```python\ni = 1\nwhile i < 7:\n  print(i)\n  if (i == 3):\n    break\n  i += 1\n ```", "questionOptions": [{"text": "```\n1\n2\n3\n```", "correct": true, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<pre><code>1\n2\n3\n</code></pre>\n"}, {"text": "```\n1\n2\n3\n4\n5\n6\n```", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<pre><code>1\n2\n3\n4\n5\n6\n</code></pre>\n"}], "questionTextHtml": "<p>What is the output of the following code?</p>\n<pre><code class=\"language-python\">i = 1\nwhile i &lt; 7:\n  print(i)\n  if (i == 3):\n    break\n  i += 1\n</code></pre>\n"}, {"questionText": "What is the output of the following code?\n```python\ni = 1\nwhile i <= 7:\n  i += 1\n  if (i == 3):\n    continue\n  print(i)\n ```", "questionOptions": [{"text": "```\n1\n2\n3\n```", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<pre><code>1\n2\n3\n</code></pre>\n"}, {"text": "```\n2\n4\n5\n6\n7\n8\n```\n", "correct": true, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<pre><code>2\n4\n5\n6\n7\n8\n</code></pre>\n"}], "questionTextHtml": "<p>What is the output of the following code?</p>\n<pre><code class=\"language-python\">i = 1\nwhile i &lt;= 7:\n  i += 1\n  if (i == 3):\n    continue\n  print(i)\n</code></pre>\n"}]}]}, {"id": "jb5el0i9p", "title": "Dictionaries", "summary": "Take a closer look at Python dictionaries, their operations, and practical coding challenges.", "pages": [{"id": "5721958738034688", "title": "Dictionaries", "is_preview": false, "slug": "dictionaries", "text": "## Dictionaries\nDictionaries are data structures that index values by a given key `(key-value pairs)`. \n\nDictionaries are written with curly brackets `{}`, and they have keys and values.\n\nThe general syntax for creating a dictionary is:\n\n```python\nDictionaryName {\n\nkey1: value1,\nkey2: value2,\n.\n.\n.\nkeyN: valueN,\n}\n```\n\\\nEvery key in a dictionary must be **unique** so that we know which value to return for a given key; however, dictionaries are NOT sorted. What makes dictionaries useful is that we assign a **key** to each value, instead of a numerical index like we do for a list. \n\nHere is the visual example of a dictionary named `Student` with keys as student names and values as student ages.\n<img src=\"http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755847848514561446.svg\" alt=\"\" width=\"600\" height=\"400\" style=\"width: 100%; height: auto;\" />\n### Create and Print a Dictionary\nThe following example shows a dictionary that indexes students' ages by name.\n\n```python310\nages = {\n    \"<PERSON>\": 10,\n    \"<PERSON>\": 11,\n    \"<PERSON>\": 9,\n    \"<PERSON>\": 10,\n    \"<PERSON>\": 10,\n    \"<PERSON>\": 11,\n    \"<PERSON>\": 12,\n    \"<PERSON>\": 10,\n}\n# print one item\nprint(\"Get age of peter\")\nprint(ages[\"<PERSON>\"])\n\n## print the whole dictionary\nprint(\"Get age of all persons\")\nfor key, value in ages.items(): \n    print(key, value) \n```\n\nCall dictionary with no parameters using the `dict` keyword\n```python\nnew_dict = dict()\n```\nor simply write dictionary name followed by equal to `=` and `{}`\n``` \nnew_dict = {}\n\n```python310\nages = dict()\nages['Peter'] = 12\nages['Susan'] = 13\nfor key, value in ages.items(): \n    print(key, value) \n```\n\n>**Note:** The order in which the keys are inserted is not maintained when the elements are printed on the console. It changes every time when the code is run.\n\nYou can create an **ordered dictionary** which preserves the order in which the keys are inserted. This is done by importing the `OrderedDictionary` from the `collections` library, and call the `OrderedDictionary()` built-in method.\n\n1. `from collections import OrderedDict` \n2. dictionary_name = `OrderedDict()`\n\n\n\n```python310\nfrom collections import OrderedDict \n\nages = OrderedDict()\n\nages['Peter'] = 12\nages['Susan'] = 10\nages['Maria'] = 5\n  \nfor key, value in ages.items(): \n    print(key, value) \n```\n\n### Accessing Items of a Dictionary\n\nHowever, dictionary keys can be [immutable](https://docs.python.org/3/tutorial/datastructures.html#dictionaries)  object and don’t necessarily need to be strings.\n\n```python310\nd = {\n    0: [0, 0, 0],\n    1: [1, 1, 1],\n    2: [2, 2, 2],\n}\n\nprint(d[2])\n```\n\n### Loop Through the Dictionary\nUsing `for` loop, ​we can iterate through the loop.\n\n#### Loop to Get All Keys\nTo get all keys from the dictionary​, use the following syntax:\n\n> 📝Note that the dictionary isn’t necessarily printed in the order it was saved.\n\n```python310\nages = {\n    \"Peter\": 10,\n    \"Isabel\": 11,\n    \"Anna\": 9,\n    \"Thomas\": 10,\n    \"Bob\": 10,\n    \"Joseph\": 11,\n    \"Maria\": 12,\n    \"Gabriel\": 10,\n}\n\nfor x in ages:\n  print(x)\n```\n\n#### Loop to Get All Values\nTo get all values from the dictionary​ use the following syntax:\n\n```python310\nages = {\n    \"Peter\": 10,\n    \"Isabel\": 11,\n    \"Anna\": 9,\n    \"Thomas\": 10,\n    \"Bob\": 10,\n    \"Joseph\": 11,\n    \"Maria\": 12,\n    \"Gabriel\": 10,\n}\n\nfor x in ages:\n  print(ages[x])\n```\n\nAnother method to return values of a dictionary is to use `values()` function:\n\n```python310\nages = {\n    \"Peter\": 10,\n    \"Isabel\": 11,\n    \"Anna\": 9,\n    \"Thomas\": 10,\n    \"Bob\": 10,\n    \"Joseph\": 11,\n    \"Maria\": 12,\n    \"Gabriel\": 10,\n}\n\nfor x in ages.values():\n  print(x)\n```\n\n#### Loop to Get Both Keys and Values\n\nIt is possible to iterate over the contents of a dictionary using `items()`, like this:\n\n```python310\nages = {\n    \"Peter\": 10,\n    \"Isabel\": 11,\n    \"Anna\": 9,\n    \"Thomas\": 10,\n    \"Bob\": 10,\n    \"Joseph\": 11,\n    \"Maria\": 12,\n    \"Gabriel\": 10,\n}\n\nfor name, age in ages.items():\n  print(name, age)\n```\n\n### Nested Dictionary\nA dictionary can be made within a dictionary, and you can also use other dictionaries as values.\n<img src=\"http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755847850575022779.svg\" alt=\"\" width=\"600\" height=\"400\" style=\"width: 100%; height: auto;\" />\nThe following python code demonstrates the concept of nested dictionary. \n\n```python310\nstudents = {\n    \"Peter\": {\"age\": 10, \"address\": \"Lisbon\"},\n    \"Isabel\": {\"age\": 11, \"address\": \"Sesimbra\"},\n    \"Anna\": {\"age\": 9, \"address\": \"Lisbon\"},\n}\nprint(students)\nprint(students['Peter'])\nprint(students['Peter']['address'])\n```\n\n#### Looping Through a Nested Dictionary\nWe can loop through the nested dictionary using a nested for loop.\n\n```python310\nstudents = {\n    \"Peter\": {\"age\": 10, \"address\": \"Lisbon\"},\n    \"Isabel\": {\"age\": 11, \"address\": \"Sesimbra\"},\n    \"Anna\": {\"age\": 9, \"address\": \"Lisbon\"},\n}\n\nfor p_id, p_info in students.items():\n    print(\"\\nPerson Name:\", p_id)\n    for key in p_info:\n        print(key + ':', p_info[key])\n```\n\nThis is quite useful to structure hierarchical information.\n\nNow that the idea of \"Dictionaries\" in python is clear, let’s check your knowledge in the upcoming exercises before moving on to the next chapter---'Classes'.", "mdHtml": "<h1 id=\"dictionaries\">Dictionaries</h1>\n<p>Dictionaries are data structures that index values by a given key <code>(key-value pairs)</code>.</p>\n<p>Dictionaries are written with curly brackets <code>{}</code>, and they have keys and values.</p>\n<p>The general syntax for creating a dictionary is:</p>\n<pre><code class=\"language-python\">DictionaryName {\n\nkey1: value1,\nkey2: value2,\n.\n.\n.\nkeyN: valueN,\n}\n</code></pre>\n<p><br />\nEvery key in a dictionary must be <strong>unique</strong> so that we know which value to return for a given key; however, dictionaries are NOT sorted. What makes dictionaries useful is that we assign a <strong>key</strong> to each value, instead of a numerical index like we do for a list.</p>\n<p>Here is the visual example of a dictionary named <code>Student</code> with keys as student names and values as student ages.</p>\n\n<div class='image-component'><img src=\"http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755847848514561446.svg\" alt=\"\" width=\"600\" height=\"400\" style=\"width: 100%; height: auto;\" /></div>\n<h2 id=\"create-and-print-a-dictionary\">Create and Print a Dictionary</h2>\n<p>The following example shows a dictionary that indexes students’ ages by name.</p>\n\n<div class='image-component'>\n```python310\nages = {\n    \"Peter\": 10,\n    \"Isabel\": 11,\n    \"Anna\": 9,\n    \"Thomas\": 10,\n    \"Bob\": 10,\n    \"Joseph\": 11,\n    \"Maria\": 12,\n    \"Gabriel\": 10,\n}\n# print one item\nprint(\"Get age of peter\")\nprint(ages[\"Peter\"])\n\n## print the whole dictionary\nprint(\"Get age of all persons\")\nfor key, value in ages.items(): \n    print(key, value) \n```\n</div>\n<p>Call dictionary with no parameters using the <code>dict</code> keyword</p>\n<pre><code class=\"language-python\">new_dict = dict()\n</code></pre>\n<p>or simply write dictionary name followed by equal to <code>=</code> and <code>{}</code></p>\n<pre><code>new_dict = {}</code></pre>\n\n<div class='image-component'>\n```python310\nages = dict()\nages['Peter'] = 12\nages['Susan'] = 13\nfor key, value in ages.items(): \n    print(key, value) \n```\n</div>\n<blockquote>\n<p><strong>Note:</strong> The order in which the keys are inserted is not maintained when the elements are printed on the console. It changes every time when the code is run.</p>\n</blockquote>\n<p>You can create an <strong>ordered dictionary</strong> which preserves the order in which the keys are inserted. This is done by importing the <code>OrderedDictionary</code> from the <code>collections</code> library, and call the <code>OrderedDictionary()</code> built-in method.</p>\n<ol>\n<li><code>from collections import OrderedDict</code></li>\n<li>dictionary_name = <code>OrderedDict()</code></li>\n</ol>\n\n<div class='image-component'>\n```python310\nfrom collections import OrderedDict \n\nages = OrderedDict()\n\nages['Peter'] = 12\nages['Susan'] = 10\nages['Maria'] = 5\n  \nfor key, value in ages.items(): \n    print(key, value) \n```\n</div>\n<h2 id=\"accessing-items-of-a-dictionary\">Accessing Items of a Dictionary</h2>\n\n<p>However, dictionary keys can be <a href=\"https://docs.python.org/3/tutorial/datastructures.html#dictionaries\">immutable</a>  object and don’t necessarily need to be strings.</p>\n\n<div class='image-component'>\n```python310\nd = {\n    0: [0, 0, 0],\n    1: [1, 1, 1],\n    2: [2, 2, 2],\n}\n\nprint(d[2])\n```\n</div>\n<h2 id=\"loop-through-the-dictionary\">Loop Through the Dictionary</h2>\n<p>Using <code>for</code> loop, ​we can iterate through the loop.</p>\n\n<h3 id=\"loop-to-get-all-keys\">Loop to Get All Keys</h3>\n<p>To get all keys from the dictionary​, use the following syntax:</p>\n<blockquote>\n<p>📝Note that the dictionary isn’t necessarily printed in the order it was saved.</p>\n</blockquote>\n\n<div class='image-component'>\n```python310\nages = {\n    \"Peter\": 10,\n    \"Isabel\": 11,\n    \"Anna\": 9,\n    \"Thomas\": 10,\n    \"Bob\": 10,\n    \"Joseph\": 11,\n    \"Maria\": 12,\n    \"Gabriel\": 10,\n}\n\nfor x in ages:\n  print(x)\n```\n</div>\n<h3 id=\"loop-to-get-all-values\">Loop to Get All Values</h3>\n<p>To get all values from the dictionary​ use the following syntax:</p>\n\n<div class='image-component'>\n```python310\nages = {\n    \"Peter\": 10,\n    \"Isabel\": 11,\n    \"Anna\": 9,\n    \"Thomas\": 10,\n    \"Bob\": 10,\n    \"Joseph\": 11,\n    \"Maria\": 12,\n    \"Gabriel\": 10,\n}\n\nfor x in ages:\n  print(ages[x])\n```\n</div>\n<p>Another method to return values of a dictionary is to use <code>values()</code> function:</p>\n\n<div class='image-component'>\n```python310\nages = {\n    \"Peter\": 10,\n    \"Isabel\": 11,\n    \"Anna\": 9,\n    \"Thomas\": 10,\n    \"Bob\": 10,\n    \"Joseph\": 11,\n    \"Maria\": 12,\n    \"Gabriel\": 10,\n}\n\nfor x in ages.values():\n  print(x)\n```\n</div>\n<h3 id=\"loop-to-get-both-keys-and-values\">Loop to Get Both Keys and Values</h3>\n<p>It is possible to iterate over the contents of a dictionary using <code>items()</code>, like this:</p>\n\n<div class='image-component'>\n```python310\nages = {\n    \"Peter\": 10,\n    \"Isabel\": 11,\n    \"Anna\": 9,\n    \"Thomas\": 10,\n    \"Bob\": 10,\n    \"Joseph\": 11,\n    \"Maria\": 12,\n    \"Gabriel\": 10,\n}\n\nfor name, age in ages.items():\n  print(name, age)\n```\n</div>\n<h2 id=\"nested-dictionary\">Nested Dictionary</h2>\n<p>A dictionary can be made within a dictionary, and you can also use other dictionaries as values.</p>\n\n<div class='image-component'><img src=\"http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755847850575022779.svg\" alt=\"\" width=\"600\" height=\"400\" style=\"width: 100%; height: auto;\" /></div>\n<p>The following python code demonstrates the concept of nested dictionary.</p>\n\n<div class='image-component'>\n```python310\nstudents = {\n    \"Peter\": {\"age\": 10, \"address\": \"Lisbon\"},\n    \"Isabel\": {\"age\": 11, \"address\": \"Sesimbra\"},\n    \"Anna\": {\"age\": 9, \"address\": \"Lisbon\"},\n}\nprint(students)\nprint(students['Peter'])\nprint(students['Peter']['address'])\n```\n</div>\n<h3 id=\"looping-through-a-nested-dictionary\">Looping Through a Nested Dictionary</h3>\n<p>We can loop through the nested dictionary using a nested for loop.</p>\n\n<div class='image-component'>\n```python310\nstudents = {\n    \"Peter\": {\"age\": 10, \"address\": \"Lisbon\"},\n    \"Isabel\": {\"age\": 11, \"address\": \"Sesimbra\"},\n    \"Anna\": {\"age\": 9, \"address\": \"Lisbon\"},\n}\n\nfor p_id, p_info in students.items():\n    print(\"\\nPerson Name:\", p_id)\n    for key in p_info:\n        print(key + ':', p_info[key])\n```\n</div>\n<p>This is quite useful to structure hierarchical information.</p>\n<p>Now that the idea of “Dictionaries” in python is clear, let’s check your knowledge in the upcoming exercises before moving on to the next chapter—‘Classes’.</p>", "summary": {"titleUpdated": true, "title": "Dictionaries", "description": "In this chapter, we will work with Python dictionaries.", "tags": []}, "type": "Lesson"}, {"id": "5632809712484352", "title": "Challenge 1: <PERSON><PERSON><PERSON> of a Dictionary", "is_preview": false, "slug": "challenge-1-determine-size-of-a-dictionary", "text": "## Problem Statement\nGiven a `lengthDictionary` function that takes `Students` dictionary as a parameter, find how many students are in the dictionary.\n\n### Input\nA dictionary\n\n### Output\nThe size of the dictionary\n\n\n\n \n\n<img src=\"http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755847853927987786.svg\" alt=\"\" width=\"600\" height=\"400\" style=\"width: 100%; height: auto;\" />\n## Coding Exercise\nWrite your code below. It is recommended​ that you try solving the exercise yourself before viewing the solution.\n\nUse\n[Python documentation on dictionary](https://docs.python.org/3/library/stdtypes.html#mapping-types-dict) to solve the exercise. \n\nTake the following Python dictionary:\n\n```python310\ndef lengthDictionary(Students):\n  return # return length of dictionary\n```\n\nNow, let’s move on to the detailed solution review of the above problem in the next lesson.", "mdHtml": "<h2 id=\"problem-statement\">Problem Statement</h2>\n<p>Given a <code>lengthDictionary</code> function that takes <code>Students</code> dictionary as a parameter, find how many students are in the dictionary.</p>\n<h3 id=\"input\">Input</h3>\n<p>A dictionary</p>\n<h3 id=\"output\">Output</h3>\n<p>The size of the dictionary</p>\n\n<div class='image-component'><img src=\"http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755847853927987786.svg\" alt=\"\" width=\"600\" height=\"400\" style=\"width: 100%; height: auto;\" /></div>\n<h2 id=\"coding-exercise\">Coding Exercise</h2>\n<p>Write your code below. It is recommended​ that you try solving the exercise yourself before viewing the solution.</p>\n<p>Use\n<a href=\"https://docs.python.org/3/library/stdtypes.html#mapping-types-dict\">Python documentation on dictionary</a> to solve the exercise.</p>\n<p>Take the following Python dictionary:</p>\n\n<div class='image-component'>\n```python310\ndef lengthDictionary(Students):\n  return # return length of dictionary\n```\n</div>\n<p>Now, let’s move on to the detailed solution review of the above problem in the next lesson.</p>", "summary": {"titleUpdated": true, "title": "Challenge 1: <PERSON><PERSON><PERSON> of a Dictionary", "description": "In this challenge, your task is to calculate the size of a dictionary", "tags": []}, "type": "Lesson"}, {"id": "5720283566571520", "title": "Solution Review : Det<PERSON><PERSON> of a Dictionary", "is_preview": false, "slug": "solution-review-determine-size-of-a-dictionary", "text": "## Solution: Use `len()` function\nUse the `len(dictionary)` to get the length of the dictionary.\n\nThe following python code helps to calculate the size of a dictionary.\n\n```python310\ndef lengthDictionary(Students):\n  return len(Students)\nStudents = {\n     \"<PERSON>\": 10,\n     \"<PERSON>\": 11,\n     \"<PERSON>\": 9,\n     \"<PERSON>\": 10,\n     \"<PERSON>\": 10,\n     \"<PERSON>\": 11,\n     \"<PERSON>\": 12,\n     \"<PERSON>\": 10,\n}\nprint(lengthDictionary(Students))\n```\n\nNow, let's move on to the next problem.", "mdHtml": "<h2 id=\"solution-use-len-function\">Solution: Use <code>len()</code> function</h2>\n<p>Use the <code>len(dictionary)</code> to get the length of the dictionary.</p>\n<p>The following python code helps to calculate the size of a dictionary.</p>\n\n<div class='image-component'>\n```python310\ndef lengthDictionary(Students):\n  return len(Students)\nStudents = {\n     \"<PERSON>\": 10,\n     \"<PERSON>\": 11,\n     \"<PERSON>\": 9,\n     \"<PERSON>\": 10,\n     \"<PERSON>\": 10,\n     \"<PERSON>\": 11,\n     \"<PERSON>\": 12,\n     \"<PERSON>\": 10,\n}\nprint(lengthDictionary(Students))\n```\n</div>\n<p>Now, let’s move on to the next problem.</p>", "summary": {"titleUpdated": true, "title": "", "description": "This lesson will explain how to find the size of a dictionary.", "tags": []}, "type": "Lesson"}, {"id": "5706064674684928", "title": "Challenge 2: Average of Values of Keys in a Dictionary", "is_preview": false, "slug": "challenge-2-average-of-values-of-keys-in-a-dictionary", "text": "## Problem Statement\nImplement a `calculateAvg` function that receives the `ages` dictionary as a ​parameter, and returns the average age of the students. Traverse all items in the dictionary using the \"items\" method above.\n\n\n### Input\nA dictionary\n\n### Output\nThe average age of all students in the dictionary\n\n\n<img src=\"http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755847858956468676.svg\" alt=\"\" width=\"600\" height=\"400\" style=\"width: 100%; height: auto;\" />\n## Coding Exercise\nWrite your code below. It is recommended​ that you try solving the exercise yourself before viewing the solution.\nUse\n[Python documentation on dictionary](https://docs.python.org/3/library/stdtypes.html#mapping-types-dict) to solve the exercise. \n\nTake the following Python dictionary:\n\n```python310\ndef calculateAvg(ages):\n  return # return the average of ages \n```\n\nNow, let’s move on to the detailed solution review of the above problem.", "mdHtml": "<h2 id=\"problem-statement\">Problem Statement</h2>\n<p>Implement a <code>calculateAvg</code> function that receives the <code>ages</code> dictionary as a ​parameter, and returns the average age of the students. Traverse all items in the dictionary using the “items” method above.</p>\n<h3 id=\"input\">Input</h3>\n<p>A dictionary</p>\n<h3 id=\"output\">Output</h3>\n<p>The average age of all students in the dictionary</p>\n\n<div class='image-component'><img src=\"http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755847858956468676.svg\" alt=\"\" width=\"600\" height=\"400\" style=\"width: 100%; height: auto;\" /></div>\n<h2 id=\"coding-exercise\">Coding Exercise</h2>\n<p>Write your code below. It is recommended​ that you try solving the exercise yourself before viewing the solution.\nUse\n<a href=\"https://docs.python.org/3/library/stdtypes.html#mapping-types-dict\">Python documentation on dictionary</a> to solve the exercise.</p>\n<p>Take the following Python dictionary:</p>\n\n<div class='image-component'>\n```python310\ndef calculateAvg(ages):\n  return # return the average of ages \n```\n</div>\n<p>Now, let’s move on to the detailed solution review of the above problem.</p>", "summary": {"titleUpdated": true, "title": "Challenge 2: Average of Values on a Given Key", "description": "In this challenge, you are required to calculate the average of values in a dictionary.", "tags": []}, "type": "Lesson"}, {"id": "5687160778784768", "title": "Solution Review : Average of Values of Keys in a Dictionary", "is_preview": false, "slug": "solution-review-average-of-values-of-keys-in-a-dictionary", "text": "## Solution: Use the `sum()` and `len()` function\n* Use the `sum(dictionary.values())` function to calculate the sum of values in a dictionary.\n* ​Use `len(dictionary)` to calculate the length of dictionary, then divide the sum by length to calculate the average.\n\nThis concept is demonstrated by the following python code.\n\n```python310\ndef calculateAvg(ages):\n  length=len(ages)\n  return(sum(ages.values())/length)\nages = {\n     \"<PERSON>\": 10,\n     \"<PERSON>\": 11,\n     \"<PERSON>\": 9,\n     \"<PERSON>\": 10,\n     \"<PERSON>\": 10,\n     \"<PERSON>\": 11,\n     \"<PERSON>\": 12,\n     \"<PERSON>\": 10,\n }\nprint(calculateAvg(ages))\n```\n\nNow, let's move on to the next problem.", "mdHtml": "<h2 id=\"solution-use-the-sum-and-len-function\">Solution: Use the <code>sum()</code> and <code>len()</code> function</h2>\n<ul>\n<li>Use the <code>sum(dictionary.values())</code> function to calculate the sum of values in a dictionary.</li>\n<li>​Use <code>len(dictionary)</code> to calculate the length of dictionary, then divide the sum by length to calculate the average.</li>\n</ul>\n<p>This concept is demonstrated by the following python code.</p>\n\n<div class='image-component'>\n```python310\ndef calculateAvg(ages):\n  length=len(ages)\n  return(sum(ages.values())/length)\nages = {\n     \"<PERSON>\": 10,\n     \"<PERSON>\": 11,\n     \"<PERSON>\": 9,\n     \"<PERSON>\": 10,\n     \"<PERSON>\": 10,\n     \"<PERSON>\": 11,\n     \"<PERSON>\": 12,\n     \"<PERSON>\": 10,\n }\nprint(calculateAvg(ages))\n```\n</div>\n<p>Now, let’s move on to the next problem.</p>", "summary": {"titleUpdated": true, "title": "", "description": "This lesson will explain how to find an average of values of keys' values in a dictionary.", "tags": []}, "type": "Lesson"}, {"id": "5666138658701312", "title": "Challenge 3: Return Key With Maximum Value", "is_preview": false, "slug": "challenge-3-return-key-with-maximum-value", "text": "## Problem Statement\n\nImplement an `oldestStudent` function that receives the `ages` dictionary as a parameter, and returns the name of the oldest student.\n\n### Input\nA dictionary\n\n### Output\nThe key associated with highest age, i.e., the name of the oldest student\n\n\nUse\n[Python documentation on dictionary](https://docs.python.org/3/library/stdtypes.html#mapping-types-dict) to solve the exercise.\n\nTake the following Python dictionary:\n<img src=\"http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755847863945378645.svg\" alt=\"\" width=\"600\" height=\"400\" style=\"width: 100%; height: auto;\" />\n## Coding Exercise\nWrite your code below. It is recommended​ that you try solving the exercise yourself before viewing the solution.\n\nUse\n[Python documentation on dictionary](https://docs.python.org/3/library/stdtypes.html#mapping-types-dict) to solve the exercise. \n\nTake the following Python dictionary:\n\n```python310\ndef oldestStudent(ages):\n#write code here\n  pass\n```\n\nNow, let’s move on to the detailed solution review of the above problem.", "mdHtml": "<h2 id=\"problem-statement\">Problem Statement</h2>\n<p>Implement an <code>oldestStudent</code> function that receives the <code>ages</code> dictionary as a parameter, and returns the name of the oldest student.</p>\n<h3 id=\"input\">Input</h3>\n<p>A dictionary</p>\n<h3 id=\"output\">Output</h3>\n<p>The key associated with highest age, i.e., the name of the oldest student</p>\n<p>Use\n<a href=\"https://docs.python.org/3/library/stdtypes.html#mapping-types-dict\">Python documentation on dictionary</a> to solve the exercise.</p>\n<p>Take the following Python dictionary:</p>\n\n<div class='image-component'><img src=\"http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755847863945378645.svg\" alt=\"\" width=\"600\" height=\"400\" style=\"width: 100%; height: auto;\" /></div>\n<h2 id=\"coding-exercise\">Coding Exercise</h2>\n<p>Write your code below. It is recommended​ that you try solving the exercise yourself before viewing the solution.</p>\n<p>Use\n<a href=\"https://docs.python.org/3/library/stdtypes.html#mapping-types-dict\">Python documentation on dictionary</a> to solve the exercise.</p>\n<p>Take the following Python dictionary:</p>\n\n<div class='image-component'>\n```python310\ndef oldestStudent(ages):\n#write code here\n  pass\n```\n</div>\n<p>Now, let’s move on to the detailed solution review of the above problem.</p>", "summary": {"titleUpdated": true, "title": "Challenge 3: Return Key With Maximum Value", "description": "Let's learn to find a key with the maximum value.", "tags": []}, "type": "Lesson"}, {"id": "5679550096736256", "title": "Solution Review : Return Key With a Maximum Value", "is_preview": false, "slug": "solution-review-return-key-with-a-maximum-value", "text": "## Solution: Use `max()` Function\n* Create a list containing values and another list containing keys.\n* Get the maximum index of values using the max() built-in function. Then, using `key[value.index(max(value))]`, get the key corresponding to the maximum value.\n\nThe following python code illustrates the concept.\n\n```python310\ndef oldestStudent(ages):\n  \n  value = list(ages.values())\n  key = list(ages.keys())\n  return key[value.index(max(value))]\n\nages = {\n     \"<PERSON>\": 10,\n     \"<PERSON>\": 11,\n     \"<PERSON>\": 9,\n     \"<PERSON>\": 10,\n     \"<PERSON>\": 10,\n     \"<PERSON>\": 11,\n     \"<PERSON>\": 12,\n     \"<PERSON>\": 10,\n  }\nprint(oldestStudent(ages))\n```\n\nLet's move on to the next problem.", "mdHtml": "<h2>Solution: Use <code>max()</code> Function</h2>\n<ul>\n<li>Create a list containing values and another list containing keys.</li>\n<li>Get the maximum index of values using the max() built-in function. Then, using <code>key[value.index(max(value))]</code>, get the key corresponding to the maximum value.</li>\n</ul>\n<p>The following python code illustrates the concept.</p>\n\n<div class='image-component'>\n```python310\ndef oldestStudent(ages):\n  \n  value = list(ages.values())\n  key = list(ages.keys())\n  return key[value.index(max(value))]\n\nages = {\n     \"<PERSON>\": 10,\n     \"<PERSON>\": 11,\n     \"<PERSON>\": 9,\n     \"<PERSON>\": 10,\n     \"<PERSON>\": 10,\n     \"<PERSON>\": 11,\n     \"<PERSON>\": 12,\n     \"<PERSON>\": 10,\n  }\nprint(oldestStudent(ages))\n```\n</div>\n<p>Let’s move on to the next problem.</p>", "summary": {"titleUpdated": true, "title": "", "description": "This lesson will explain how to find a key with a maximum value.", "tags": []}, "type": "Lesson"}, {"id": "5742692155785216", "title": "Challenge 4: Increment Dictionary Values", "is_preview": false, "slug": "challenge-4-increment-dictionary-values", "text": "## Problem Statement\nImplement an `updateAges` function that receives `ages` dictionary and a number `n` and returns a new dictionary where each student is \\(n\\) years older. \n\n\n### Input\nA dictionary\n\n### Output\nReturns a copy of sorted `ages` where each student is `n` years older.\n\n\n<img src=\"http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755847868565560527.svg\" alt=\"\" width=\"600\" height=\"400\" style=\"width: 100%; height: auto;\" />\n## Coding Exercise\n\nWrite your code below. It is recommended​ that you try solving the exercise yourself before viewing the solution.\n\nUse\n[Python documentation on dictionary](https://docs.python.org/3/library/stdtypes.html#mapping-types-dict) to solve the exercise. \n\n```python310\ndef updateAges(ages, n):\n  # Write your code here\n  pass\n```\n\nNow, let’s move on to the detailed solution review of the above problem.", "mdHtml": "<h2 id=\"problem-statement\">Problem Statement</h2>\n<p>Implement an <code>updateAges</code> function that receives <code>ages</code> dictionary and a number <code>n</code> and returns a new dictionary where each student is (n) years older.</p>\n<h3 id=\"input\">Input</h3>\n<p>A dictionary</p>\n<h3 id=\"output\">Output</h3>\n<p>Returns a copy of sorted <code>ages</code> where each student is <code>n</code> years older.</p>\n\n<div class='image-component'><img src=\"http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755847868565560527.svg\" alt=\"\" width=\"600\" height=\"400\" style=\"width: 100%; height: auto;\" /></div>\n<h2 id=\"coding-exercise\">Coding Exercise</h2>\n<p>Write your code below. It is recommended​ that you try solving the exercise yourself before viewing the solution.</p>\n<p>Use\n<a href=\"https://docs.python.org/3/library/stdtypes.html#mapping-types-dict\">Python documentation on dictionary</a> to solve the exercise.</p>\n\n<div class='image-component'>\n```python310\ndef updateAges(ages, n):\n  # Write your code here\n  pass\n```\n</div>\n<p>Now, let’s move on to the detailed solution review of the above problem.</p>", "summary": {"titleUpdated": true, "title": "Challenge 4: Create a Sub-dictionary", "description": "In this challenge, you are required to increment values in a dictionary.", "tags": []}, "type": "Lesson"}, {"id": "5753509433573376", "title": "Solution Review:  Increment Dictionary Values", "is_preview": false, "slug": "solution-review-increment-dictionary-values", "text": "## Solution: Loop Over the Dictionary and Increment Value\nUse a for loop to iterate over the length of the dictionary, incrementing the value of age in the dictionary with a number `n` and then saving the value in a new dictionary.\n\nThe following illustration explains the concept of iterating the values of a dictionary containing only 3 students.\n\n\n\n\n**[CanvasAnimation 图表]** - 交互式图表内容\n\nThe following python code demonstrates the concept.\n\n```python310\ndef updateAges(ages, n):\n  new_ages = {}\n  for word in ages:\n    new_ages[word] = ages[word] + n\n  return new_ages\nages = {\n      \"<PERSON>\": 10,\n      \"<PERSON>\": 11,\n      \"<PERSON>\": 9,\n      \"<PERSON>\": 10,\n      \"<PERSON>\": 10,\n      \"<PERSON>\": 11,\n      \"<PERSON>\": 12,\n      \"<PERSON>\": 10,\n   }\nnew_ages = updateAges(ages, 10)\nprint(new_ages)\n```\n\nLet's move on to the next problem.", "mdHtml": "<h2 id=\"solution-loop-over-the-dictionary-and-increment-value\">Solution: Loop Over the Dictionary and Increment Value</h2>\n<p>Use a for loop to iterate over the length of the dictionary, incrementing the value of age in the dictionary with a number <code>n</code> and then saving the value in a new dictionary.</p>\n<p>The following illustration explains the concept of iterating the values of a dictionary containing only 3 students.</p>\n\n<div class='image-component'>\n**[CanvasAnimation 图表]** - 交互式图表内容\n</div>\n<p>The following python code demonstrates the concept.</p>\n\n<div class='image-component'>\n```python310\ndef updateAges(ages, n):\n  new_ages = {}\n  for word in ages:\n    new_ages[word] = ages[word] + n\n  return new_ages\nages = {\n      \"<PERSON>\": 10,\n      \"<PERSON>\": 11,\n      \"<PERSON>\": 9,\n      \"<PERSON>\": 10,\n      \"<PERSON>\": 10,\n      \"<PERSON>\": 11,\n      \"<PERSON>\": 12,\n      \"<PERSON>\": 10,\n   }\nnew_ages = updateAges(ages, 10)\nprint(new_ages)\n```\n</div>\n<p>Let’s move on to the next problem.</p>", "summary": {"titleUpdated": true, "title": "", "description": "This lesson will explain how to increment values in a dictionary.", "tags": []}, "type": "Lesson"}, {"id": "6219916709462016", "title": "Challenge 5: Size of a Dictionary Within a Dictionary", "is_preview": false, "slug": "challenge-5-size-of-a-dictionary-within-a-dictionary", "text": "## Problem Statement​\nGiven a `totalStudents` function, your task is to calculate how many students are in the `students` dictionary. \n\n### Input\nA nested dictionary\n\n### Output\nThe size of the nested dictionary\n\n\n<img src=\"http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755847873939347585.svg\" alt=\"\" width=\"600\" height=\"400\" style=\"width: 100%; height: auto;\" />\n## Coding Exercise\nUse\n[Python documentation on dictionary](https://docs.python.org/3/library/stdtypes.html#mapping-types-dict) to solve the exercise. \n\nTake the following dictionary:\n\n```python310\ndef totalStudents(students):\n  # write your code here\n  pass # return the number of students in the dictionary\n```\n\nNow, let’s move on to the detailed solution review of the above problem.", "mdHtml": "<h2 id=\"problem-statement\">Problem Statement​</h2>\n<p>Given a <code>totalStudents</code> function, your task is to calculate how many students are in the <code>students</code> dictionary.</p>\n<h3 id=\"input\">Input</h3>\n<p>A nested dictionary</p>\n<h3 id=\"output\">Output</h3>\n<p>The size of the nested dictionary</p>\n\n<div class='image-component'><img src=\"http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755847873939347585.svg\" alt=\"\" width=\"600\" height=\"400\" style=\"width: 100%; height: auto;\" /></div>\n<h2 id=\"coding-exercise\">Coding Exercise</h2>\n<p>Use\n<a href=\"https://docs.python.org/3/library/stdtypes.html#mapping-types-dict\">Python documentation on dictionary</a> to solve the exercise.</p>\n<p>Take the following dictionary:</p>\n\n<div class='image-component'>\n```python310\ndef totalStudents(students):\n  # write your code here\n  pass # return the number of students in the dictionary\n```\n</div>\n<p>Now, let’s move on to the detailed solution review of the above problem.</p>", "summary": {"titleUpdated": true, "title": "Challenge 5: Size of a Dictionary Within a Dictionary", "description": "In this challenge, your task is to calculate the size of the dictionary within a dictionary.", "tags": []}, "type": "Lesson"}, {"id": "6221836962496512", "title": "Solution Review: Size of a Dictionary Within a Dictionary", "is_preview": false, "slug": "solution-review-size-of-a-dictionary-within-a-dictionary", "text": "## Solution 1: Use`len()` Function\nTo calculate the number of students in the dictionary, get the length of the total keys in the dictionary using `len(student.keys())`\n\nThe following python code demonstrates the concept.\n\n```python310\ndef totalStudents(students):\n  return(len(students.keys()))\n\nstudents = {\n      \"Peter\": {\"age\": 10, \"address\": \"Lisbon\"},\n      \"Isabel\": {\"age\": 11, \"address\": \"Sesimbra\"},\n      \"Anna\": {\"age\": 9, \"address\": \"Lisbon\"},\n}\nprint(totalStudents(students))\n```\n\n## Solution 2: Use `len()` Function\nThe solution1 looks more easy to understand but the simple `len` function can also return the correct result using `len(students)`.\n\nThe following python code demonstrates the concept.\n\n```python310\ndef totalStudents(students):\n  return len(students)\n\nstudents = {\n      \"Peter\": {\"age\": 10, \"address\": \"Lisbon\"},\n      \"Isabel\": {\"age\": 11, \"address\": \"Sesimbra\"},\n      \"Anna\": {\"age\": 9, \"address\": \"Lisbon\"},\n}\nprint(totalStudents(students))\n```\n\nLet's move on to the detailed solution of the above problem.", "mdHtml": "<h2 id=\"solution-1-uselen-function\">Solution 1: Use<code>len()</code> Function</h2>\n<p>To calculate the number of students in the dictionary, get the length of the total keys in the dictionary using <code>len(student.keys())</code></p>\n<p>The following python code demonstrates the concept.</p>\n\n<div class='image-component'>\n```python310\ndef totalStudents(students):\n  return(len(students.keys()))\n\nstudents = {\n      \"Peter\": {\"age\": 10, \"address\": \"Lisbon\"},\n      \"Isabel\": {\"age\": 11, \"address\": \"Sesimbra\"},\n      \"Anna\": {\"age\": 9, \"address\": \"Lisbon\"},\n}\nprint(totalStudents(students))\n```\n</div>\n<h2 id=\"solution-2-use-len-function\">Solution 2: Use <code>len()</code> Function</h2>\n<p>The solution1 looks more easy to understand but the simple <code>len</code> function can also return the correct result using <code>len(students)</code>.</p>\n<p>The following python code demonstrates the concept.</p>\n\n<div class='image-component'>\n```python310\ndef totalStudents(students):\n  return len(students)\n\nstudents = {\n      \"Peter\": {\"age\": 10, \"address\": \"Lisbon\"},\n      \"Isabel\": {\"age\": 11, \"address\": \"Sesimbra\"},\n      \"Anna\": {\"age\": 9, \"address\": \"Lisbon\"},\n}\nprint(totalStudents(students))\n```\n</div>\n<p>Let’s move on to the detailed solution of the above problem.</p>", "summary": {"titleUpdated": true, "title": "", "description": "This lesson will explain how to determine the size of a dictionary within a dictionary.", "tags": []}, "type": "Lesson"}, {"id": "5183186766135296", "title": "Challenge 6: Average Values Within Multiple Dictionaries", "is_preview": false, "slug": "challenge-6-average-values-within-multiple-dictionaries", "text": "## Problem Statement\nImplement a `calculateAverageAge` function that receives the `students` dictionary and returns the average age.\n\n### Input\nA nested dictionary\n\n### Output\nThe average age of students\n\n<img src=\"http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755847879542966639.svg\" alt=\"\" width=\"600\" height=\"400\" style=\"width: 100%; height: auto;\" />\n## Coding Exercise\nWrite your code below. It is recommended​ that you try solving the exercise yourself before viewing the solution.\n\nUse\n[Python documentation on dictionary](https://docs.python.org/3/library/stdtypes.html#mapping-types-dict) to solve the exercise. \n\nTake the following dictionary:\n\n```python310\ndef calculateAverageAge(students):\n  # Write code here\n pass   \n```\n\nNow, let’s move on to the detailed solution review of the above problem.", "mdHtml": "<h2 id=\"problem-statement\">Problem Statement</h2>\n<p>Implement a <code>calculateAverageAge</code> function that receives the <code>students</code> dictionary and returns the average age.</p>\n<h3 id=\"input\">Input</h3>\n<p>A nested dictionary</p>\n<h3 id=\"output\">Output</h3>\n<p>The average age of students</p>\n\n<div class='image-component'><img src=\"http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755847879542966639.svg\" alt=\"\" width=\"600\" height=\"400\" style=\"width: 100%; height: auto;\" /></div>\n<h2 id=\"coding-exercise\">Coding Exercise</h2>\n<p>Write your code below. It is recommended​ that you try solving the exercise yourself before viewing the solution.</p>\n<p>Use\n<a href=\"https://docs.python.org/3/library/stdtypes.html#mapping-types-dict\">Python documentation on dictionary</a> to solve the exercise.</p>\n<p>Take the following dictionary:</p>\n\n<div class='image-component'>\n```python310\ndef calculateAverageAge(students):\n  # Write code here\n pass   \n```\n</div>\n<p>Now, let’s move on to the detailed solution review of the above problem.</p>", "summary": {"titleUpdated": true, "title": "Challenge 6: Average Values of a Single Key Within Multiple Dictionaries", "description": "In this challenge, you are required to average values of a single key within multiple dictionaries.", "tags": []}, "type": "Lesson"}, {"id": "5646994244632576", "title": "Solution Review:  Average Values Within Multiple Dictionaries", "is_preview": false, "slug": "solution-review-average-values-within-multiple-dictionaries", "text": "## Solution: Use `for` loop\n\n* Iterate over `students.values()` using for loop to get the age of each student, and add it in a variable.\n* When the for loop terminates, divide the sum of ages with total  keys in the dictionary using `len(student.keys())`\n\nThe following python code demonstrates the concept.\n\n```python310\ndef calculateAverageAge(students):\n  add_age = 0\n  for thing in students.values():\n      age = thing['age']\n      add_age = add_age + age\n    \n  return(add_age / len(students.keys()))\n\nstudents = {\n      \"Peter\": {\"age\": 10, \"address\": \"Lisbon\"},\n      \"Isabel\": {\"age\": 11, \"address\": \"Sesimbra\"},\n      \"Anna\": {\"age\": 9, \"address\": \"Lisbon\"},\n      \"Gibrael\": {\"age\": 10, \"address\": \"Sesimbra\"},\n      \"Susan\": {\"age\": 11, \"address\": \"Lisbon\"},\n      \"Charles\": {\"age\": 9, \"address\": \"Sesimbra\"},\n  }\nprint(calculateAverageAge(students))\n```\n\nNow, let's move on to the next problem.", "mdHtml": "<h2 id=\"solution-use-for-loop\">Solution: Use <code>for</code> loop</h2>\n<ul>\n<li>Iterate over <code>students.values()</code> using for loop to get the age of each student, and add it in a variable.</li>\n<li>When the for loop terminates, divide the sum of ages with total  keys in the dictionary using <code>len(student.keys())</code></li>\n</ul>\n<p>The following python code demonstrates the concept.</p>\n\n<div class='image-component'>\n```python310\ndef calculateAverageAge(students):\n  add_age = 0\n  for thing in students.values():\n      age = thing['age']\n      add_age = add_age + age\n    \n  return(add_age / len(students.keys()))\n\nstudents = {\n      \"Peter\": {\"age\": 10, \"address\": \"Lisbon\"},\n      \"Isabel\": {\"age\": 11, \"address\": \"Sesimbra\"},\n      \"Anna\": {\"age\": 9, \"address\": \"Lisbon\"},\n      \"Gibrael\": {\"age\": 10, \"address\": \"Sesimbra\"},\n      \"Susan\": {\"age\": 11, \"address\": \"Lisbon\"},\n      \"<PERSON>\": {\"age\": 9, \"address\": \"Sesi<PERSON>ra\"},\n  }\nprint(calculateAverage<PERSON>ge(students))\n```\n</div>\n<p>Now, let’s move on to the next problem.</p>", "summary": {"titleUpdated": true, "title": "", "description": "This lesson will explain how to find average values of a single key with multiple dictionaries.", "tags": []}, "type": "Lesson"}, {"id": "5649474185592832", "title": "Challenge 7: Keys Matching in Multiple Dictionaries", "is_preview": false, "slug": "challenge-7-keys-matching-in-multiple-dictionaries", "text": "## Problem Statement\nImplement a `find_students` function that receives the `students` dictionary and an address, and returns a list with names of all students whose address matches the address in the argument. For instance, invoking `find_students('Lisbon')` should return <PERSON> and <PERSON>. Also, note that the names should be printed in alphabetical order.\n\n### Input\nA nested dictionary `students` and `address`\n\n### Output\nThe keys matching the `address` in the sorted order, i.e., the name of the students living in a particular area\n\n\n\n<img src=\"http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755847884517013370.svg\" alt=\"\" width=\"600\" height=\"400\" style=\"width: 100%; height: auto;\" />\n## Coding Exercise\nUse\n[Python documentation on dictionary](https://docs.python.org/3/library/stdtypes.html#mapping-types-dict) to solve the exercise.\n\nTake the following dictionary:\n\n```python310\ndef find_students(address, students):\n  # Write code here        \n  pass\n```\n\nNow, let’s move on to the detailed solution review of the above problem.", "mdHtml": "<h2 id=\"problem-statement\">Problem Statement</h2>\n<p>Implement a <code>find_students</code> function that receives the <code>students</code> dictionary and an address, and returns a list with names of all students whose address matches the address in the argument. For instance, invoking <code>find_students('Lisbon')</code> should return <PERSON> and <PERSON>. Also, note that the names should be printed in alphabetical order.</p>\n<h3 id=\"input\">Input</h3>\n<p>A nested dictionary <code>students</code> and <code>address</code></p>\n<h3 id=\"output\">Output</h3>\n<p>The keys matching the <code>address</code> in the sorted order, i.e., the name of the students living in a particular area</p>\n\n<div class='image-component'><img src=\"http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755847884517013370.svg\" alt=\"\" width=\"600\" height=\"400\" style=\"width: 100%; height: auto;\" /></div>\n<h2 id=\"coding-exercise\">Coding Exercise</h2>\n<p>Use\n<a href=\"https://docs.python.org/3/library/stdtypes.html#mapping-types-dict\">Python documentation on dictionary</a> to solve the exercise.</p>\n<p>Take the following dictionary:</p>\n\n<div class='image-component'>\n```python310\ndef find_students(address, students):\n  # Write code here        \n  pass\n```\n</div>\n<p>Now, let’s move on to the detailed solution review of the above problem.</p>", "summary": {"titleUpdated": true, "title": "Challenge 7: Return Keys Matching a Given Value From Multiple Dictionaries", "description": "In this challenge, you are required to find keys matching a given value from multiple dictionaries.", "tags": []}, "type": "Lesson"}, {"id": "5712209028055040", "title": "Solution Review: Keys Matching in Multiple Dictionaries", "is_preview": false, "slug": "solution-review-keys-matching-in-multiple-dictionaries", "text": "## Solution 1: Use a Nested `for` Loop \n* The outer for loop iterates over the outer dictionary using `students.items() `\n* The inner for loop iterates over the subdictionary values using `subdict.values()`\n* If the subitem matches with the address in the parameter, the key is saved in a list.\nReturn the sorted key using `sorted(list)`.\n\nThe following python code demonstrates the concept:\n\n```python310\ndef find_students(address, students):\n  names = []\n  for key, subdict in students.items():\n       for sublist in subdict.values():\n          if (sublist == address):\n             names.append(key)\n  return sorted(names)\n\nstudents = {\n      \"Peter\": {\"age\": 10, \"address\": \"Lisbon\"},\n      \"Isabel\": {\"age\": 11, \"address\": \"Sesimbra\"},\n      \"Anna\": {\"age\": 9, \"address\": \"Lisbon\"},\n}\nprint(find_students(\"Lisbon\", students))\n```\n\n## Solution 2: Use a `for` Loop\n* A for loop iterators over the dictionary using `key` and `value` to keep track of items of the dictionary.\n  \n   * If the value of the address in the dictionary is equal to the given address the corresponding keys get appended to `names`.\n\n    * The sorted `names` are returned \n\n```python310\ndef find_students(address, students):\n  names = []\n  for key, value in students.items():\n    if value[\"address\"] == address:\n      names.append(key)\n  return sorted(names)\n\nstudents = {\n      \"<PERSON>\": {\"age\": 10, \"address\": \"Lisbon\"},\n      \"Isabel\": {\"age\": 11, \"address\": \"Sesimbra\"},\n      \"Anna\": {\"age\": 9, \"address\": \"Lisbon\"},\n}\nprint(find_students(\"Lisbon\", students))\n```\n\nNow that you have an insight on dictionaries, let's move on to the quiz before moving on to the next chapter.", "mdHtml": "<h2 id=\"solution-1-use-a-nested-for-loop\">Solution 1: Use a Nested <code>for</code> Loop</h2>\n<ul>\n<li>The outer for loop iterates over the outer dictionary using <code>students.items() </code></li>\n<li>The inner for loop iterates over the subdictionary values using <code>subdict.values()</code></li>\n<li>If the subitem matches with the address in the parameter, the key is saved in a list.\nReturn the sorted key using <code>sorted(list)</code>.</li>\n</ul>\n<p>The following python code demonstrates the concept:</p>\n\n<div class='image-component'>\n```python310\ndef find_students(address, students):\n  names = []\n  for key, subdict in students.items():\n       for sublist in subdict.values():\n          if (sublist == address):\n             names.append(key)\n  return sorted(names)\n\nstudents = {\n      \"Peter\": {\"age\": 10, \"address\": \"Lisbon\"},\n      \"Isabel\": {\"age\": 11, \"address\": \"Sesimbra\"},\n      \"Anna\": {\"age\": 9, \"address\": \"Lisbon\"},\n}\nprint(find_students(\"Lisbon\", students))\n```\n</div>\n<h2 id=\"solution-2-use-a-for-loop\">Solution 2: Use a <code>for</code> Loop</h2>\n<ul>\n<li>\n<p>A for loop iterators over the dictionary using <code>key</code> and <code>value</code> to keep track of items of the dictionary.</p>\n<ul>\n<li>\n<p>If the value of the address in the dictionary is equal to the given address the corresponding keys get appended to <code>names</code>.</p>\n</li>\n<li>\n<p>The sorted <code>names</code> are returned</p>\n</li>\n</ul>\n</li>\n</ul>\n\n<div class='image-component'>\n```python310\ndef find_students(address, students):\n  names = []\n  for key, value in students.items():\n    if value[\"address\"] == address:\n      names.append(key)\n  return sorted(names)\n\nstudents = {\n      \"Peter\": {\"age\": 10, \"address\": \"Lisbon\"},\n      \"Isabel\": {\"age\": 11, \"address\": \"Sesimbra\"},\n      \"Anna\": {\"age\": 9, \"address\": \"Lisbon\"},\n}\nprint(find_students(\"Lisbon\", students))\n```\n</div>\n<p>Now that you have an insight on dictionaries, let’s move on to the quiz before moving on to the next chapter.</p>", "summary": {"titleUpdated": true, "title": "", "description": "This lesson will explain how to find keys matching a given value with in as a parameter to the function within multiple dictionaries.", "tags": []}, "type": "Lesson"}, {"id": "5670800174612480", "title": "Quick Quiz on Dictionaries", "is_preview": false, "slug": "quick-quiz-on-dictionaries", "text": "Now that you have learned about Dictionaries, what if you want to create a blueprint for creating objects in which you can define the object's attributes and behavior? Let's learn about \"Classes\" in the next chapter.", "mdHtml": "<p>Now that you have learned about Dictionaries, what if you want to create a blueprint for creating objects in which you can define the object’s attributes and behavior? Let’s learn about “Classes” in the next chapter.</p>", "summary": {"titleUpdated": false, "title": "", "description": "", "tags": []}, "type": "Quiz", "questions": [{"questionText": "What is the output of the following code?\n```python\nDict = { 'Dict1': {'Fruitname': 'Mango', 'season': 'Summer'}, \n         'Dict2': {'Fruitname': 'Orange', 'season': 'Winter'}}\nprint(Dict['Dict1']['Fruitname'])\nprint(Dict['Dict2']['season'])  \n  ```", "questionOptions": [{"text": "Mango\n\nWinter", "correct": true, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p><PERSON><PERSON></p>\n<p>Winter</p>\n"}, {"text": "Orange\n\nWinter", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>Orange</p>\n<p>Winter</p>\n"}], "questionTextHtml": "<p>What is the output of the following code?</p>\n<pre><code class=\"language-python\">Dict = { 'Dict1': {'Fruitname': 'Mango', 'season': 'Summer'}, \n         'Dict2': {'Fruitname': 'Orange', 'season': 'Winter'}}\nprint(Dict['Dict1']['Fruitname'])\nprint(Dict['Dict2']['season'])  \n</code></pre>\n"}, {"questionText": "What is the output of the following code?\n```python\nDict1 =\t{\n  \"FruitName\": \"Mango\",\n  \"season\": \"Spring\",\n}\nDict1[\"season\"] = \"Summer\"\nprint(sorted(Dict1.values()))\n```\n  ", "questionOptions": [{"text": "['Mango', 'Summer']", "correct": true, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>[‘Mango’, ‘Summer’]</p>\n"}, {"text": "['Mango', 'Spring']", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>[‘Mango’, ‘Spring’]</p>\n"}], "questionTextHtml": "<p>What is the output of the following code?</p>\n<pre><code class=\"language-python\">Dict1 =\t{\n  &quot;FruitName&quot;: &quot;Mango&quot;,\n  &quot;season&quot;: &quot;Spring&quot;,\n}\nDict1[&quot;season&quot;] = &quot;Summer&quot;\nprint(sorted(Dict1.values()))\n</code></pre>\n"}, {"questionText": "What is the output of the following code?\n```python\nDict1 =\t{\n  \"FruitName\": \"Mango\",\n  \"season\": \"Spring\",\n}\nDict1.pop(\"season\")\nprint(Dict1.values())\n```", "questionOptions": [{"text": "dict_values(['Mango'])", "correct": true, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>dict_values([‘Mango’])</p>\n"}, {"text": "\ndict_values(['Spring', 'Mango'])", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>dict_values([‘Spring’, ‘Mango’])</p>\n"}], "questionTextHtml": "<p>What is the output of the following code?</p>\n<pre><code class=\"language-python\">Dict1 =\t{\n  &quot;FruitName&quot;: &quot;Mango&quot;,\n  &quot;season&quot;: &quot;Spring&quot;,\n}\nDict1.pop(&quot;season&quot;)\nprint(Dict1.values())\n</code></pre>\n"}, {"questionText": "What is the output of the following code?\n```python\nDict1 =\t{\n  \"FruitName\": \"Mango\",\n  \"season\": \"Spring\",\n}\nDict1.clear()\nprint(Dict1)\n```", "questionOptions": [{"text": "{}", "correct": true, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>{}</p>\n"}, {"text": "['Mango','Spring']", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>[‘Mango’,‘Spring’]</p>\n"}], "questionTextHtml": "<p>What is the output of the following code?</p>\n<pre><code class=\"language-python\">Dict1 =\t{\n  &quot;FruitName&quot;: &quot;Mango&quot;,\n  &quot;season&quot;: &quot;Spring&quot;,\n}\nDict1.clear()\nprint(Dict1)\n</code></pre>\n"}]}]}, {"id": "8qg5n33dc", "title": "Classes", "summary": "Explore class concepts, including implementation, methods, inheritance, and practical challenges.", "pages": [{"id": "5097599342215168", "title": "Classes", "is_preview": false, "slug": "classes", "text": "# Classes\n\nIn object-oriented programming (OOP), a class is a structure that allows you to group together a set of properties (called **attributes**) and functions (called **methods**) to manipulate those properties. Take the following class that defines a person with the `name` and `age` properties and the `greet()` method.\n\n```python310\nclass Person:\n\n      def __init__(self, name, age): # class constructor\n          self.name = name # instance variable\n          self.age = age # instance variable\n\n      def greet(self): # class function to print a greeting\n          print(\"Hello, my name is %s!\" % self.name)\n```\n\nMost classes will need the constructor method (`__init__`) to **initialize** the class’s attributes. In the above code snippet, the constructor of the class receives the person’s name and age and stores that information in the **class’s instance** (referenced by the `self` keyword). Finally, the `greet()` method prints the name of the person as stored in a **specific class instance (object)**.\n\nHere’s how we can instantiate two objects:\n\n```python310\nclass Person:\n\n     def __init__(self, name, age): # class constructor\n         self.name = name # instance variable\n         self.age = age # instance variable\n\n     def greet(self): # class function to print a greeting\n         print(\"Hello, my name is %s!\" % self.name)\n        \n\na = Person(\"<PERSON>\", 20) # instantiation\nb = Person(\"<PERSON>\", 19) # instantiation\n\na.greet() # call a's greet method\nb.greet() # call b's greet method\n\nprint(a.name)\nprint(a.age)  # We can also access the attributes of an object\n\nprint(b.name)\nprint(b.age)  # We can also access the attributes of an object\n```\n\nLet's solve an exercise to practice the concepts you just learnt.", "mdHtml": "<h1 id=\"classes\">Classes</h1>\n<p>In object-oriented programming (OOP), a class is a structure that allows you to group together a set of properties (called <strong>attributes</strong>) and functions (called <strong>methods</strong>) to manipulate those properties. Take the following class that defines a person with the <code>name</code> and <code>age</code> properties and the <code>greet()</code> method.</p>\n\n<div class='image-component'>\n```python310\nclass Person:\n\n      def __init__(self, name, age): # class constructor\n          self.name = name # instance variable\n          self.age = age # instance variable\n\n      def greet(self): # class function to print a greeting\n          print(\"Hello, my name is %s!\" % self.name)\n```\n</div>\n<p>Most classes will need the constructor method (<code>__init__</code>) to <strong>initialize</strong> the class’s attributes. In the above code snippet, the constructor of the class receives the person’s name and age and stores that information in the <strong>class’s instance</strong> (referenced by the <code>self</code> keyword). Finally, the <code>greet()</code> method prints the name of the person as stored in a <strong>specific class instance (object)</strong>.</p>\n<p>Here’s how we can instantiate two objects:</p>\n\n<div class='image-component'>\n```python310\nclass Person:\n\n     def __init__(self, name, age): # class constructor\n         self.name = name # instance variable\n         self.age = age # instance variable\n\n     def greet(self): # class function to print a greeting\n         print(\"Hello, my name is %s!\" % self.name)\n        \n\na = Person(\"Peter\", 20) # instantiation\nb = Person(\"Anna\", 19) # instantiation\n\na.greet() # call a's greet method\nb.greet() # call b's greet method\n\nprint(a.name)\nprint(a.age)  # We can also access the attributes of an object\n\nprint(b.name)\nprint(b.age)  # We can also access the attributes of an object\n```\n</div>\n<p>Let’s solve an exercise to practice the concepts you just learnt.</p>", "summary": {"titleUpdated": true, "title": "Classes", "description": "This lesson introduces object-oriented programming using classes, attributes, and methods.", "tags": []}, "type": "Lesson"}, {"id": "5734808743313408", "title": "Challenge 1: Implement a Rectangle Class", "is_preview": false, "slug": "challenge-1-implement-a-rectangle-class", "text": "# Problem Statement\n\n1. Implement a class named Rectangle to store the coordinates of a rectangle given the top-left corner (x1, y1) and the bottom-right corner (x2, y2).\n\n2. Implement the class constructor with the parameters (x1, y1, x2, y2) and store them in the **class instance** using the `self` keyword.\n\n## Input\nGiven a class `Rectangle`\n\n## Output\nImplement the class constructor and output if the rectangle can be created with the given the coordinates.\n\n\n## Sample Input\nx1 = 2, y1 = 7, x2 = 8, y2 = 4\n\n## Sample Output\nRectangle(2, 7, 8, 4) created\n\n\n**[CanvasAnimation 图表]** - 交互式图表内容\n\n# Coding Exercise\nUse the [Python documentation on classes](https://docs.python.org/3/tutorial/classes.html) to solve the following exercise.\n\n```python310\nclass Rectangle:\n  def __init__(self, x1, y1, x2, y2): # class constructor\n    return -1 # Add and replace your code here\n```\n\nLet's discuss the solution for this exercise in the next lesson.", "mdHtml": "<h2 id=\"problem-statement\">Problem Statement</h2>\n<ol>\n<li>\n<p>Implement a class named Rectangle to store the coordinates of a rectangle given the top-left corner (x1, y1) and the bottom-right corner (x2, y2).</p>\n</li>\n<li>\n<p>Implement the class constructor with the parameters (x1, y1, x2, y2) and store them in the <strong>class instance</strong> using the <code>self</code> keyword.</p>\n</li>\n</ol>\n<h3 id=\"input\">Input</h3>\n<p>Given a class <code>Rectangle</code></p>\n<h3 id=\"output\">Output</h3>\n<p>Implement the class constructor and output if the rectangle can be created with the given the coordinates.</p>\n<h3 id=\"sample-input\">Sample Input</h3>\n<p>x1 = 2, y1 = 7, x2 = 8, y2 = 4</p>\n<h3 id=\"sample-output\">Sample Output</h3>\n<p>Rectangle(2, 7, 8, 4) created</p>\n\n<div class='image-component'>\n**[CanvasAnimation 图表]** - 交互式图表内容\n</div>\n<h2 id=\"coding-exercise\">Coding Exercise</h2>\n<p>Use the <a href=\"https://docs.python.org/3/tutorial/classes.html\">Python documentation on classes</a> to solve the following exercise.</p>\n\n<div class='image-component'>\n```python310\nclass Rectangle:\n  def __init__(self, x1, y1, x2, y2): # class constructor\n    return -1 # Add and replace your code here\n```\n</div>\n<p>Let’s discuss the solution for this exercise in the next lesson.</p>", "summary": {"titleUpdated": true, "title": "Challenge 1: Implement a Rectangle Class", "description": "This lesson covers a basic exercise on classes and constructors.", "tags": []}, "type": "Lesson"}, {"id": "6330934500524032", "title": "Solution Review: Implement a Rectangle Class", "is_preview": false, "slug": "solution-review-implement-a-rectangle-class", "text": "# Solution:\nThe solution to the exercise in the previous lesson was very simple. It just had a constructor with four attributes as `x1`, `y1`, `x2`, and `y2` as the coordinates of the rectangle. The constructor also checks if the **top-left** and **bottom-right** coordinates of the rectangle are correct, i.e., x1 < x2 and y1 > y2. To check the code, we created an object `r` with the coordinates (2, 7, 8, 4). The solution is shown below:\n\n```python310\nclass Rectangle:\n  def __init__(self, x1, y1, x2, y2): # class constructor\n    if x1 < x2 and y1 > y2:\n      self.x1 = x1 # instance variable\n      self.y1 = y1 # instance variable\n      self.x2 = x2 # instance variable\n      self.y2 = y2 # instance variable\n    else:\n      print(\"Incorrect coordinates of the rectangle!\")\n        \nr = Rectangle(2, 7, 8, 4)\n```\n\nThe creation of the rectangle `r` is illustrated in the following figure:\n\n**[CanvasAnimation 图表]** - 交互式图表内容\n\nNow, let's move on to the next exercise and implement the getter methods of the `Rectangle` class.", "mdHtml": "<h2 id=\"solution\">Solution:</h2>\n<p>The solution to the exercise in the previous lesson was very simple. It just had a constructor with four attributes as <code>x1</code>, <code>y1</code>, <code>x2</code>, and <code>y2</code> as the coordinates of the rectangle. The constructor also checks if the <strong>top-left</strong> and <strong>bottom-right</strong> coordinates of the rectangle are correct, i.e., x1 &lt; x2 and y1 &gt; y2. To check the code, we created an object <code>r</code> with the coordinates (2, 7, 8, 4). The solution is shown below:</p>\n\n<div class='image-component'>\n```python310\nclass Rectangle:\n  def __init__(self, x1, y1, x2, y2): # class constructor\n    if x1 < x2 and y1 > y2:\n      self.x1 = x1 # instance variable\n      self.y1 = y1 # instance variable\n      self.x2 = x2 # instance variable\n      self.y2 = y2 # instance variable\n    else:\n      print(\"Incorrect coordinates of the rectangle!\")\n        \nr = Rectangle(2, 7, 8, 4)\n```\n</div>\n<p>The creation of the rectangle <code>r</code> is illustrated in the following figure:</p>\n\n<div class='image-component'>\n**[CanvasAnimation 图表]** - 交互式图表内容\n</div>\n<p>Now, let’s move on to the next exercise and implement the getter methods of the <code>Rectangle</code> class.</p>", "summary": {"titleUpdated": true, "title": "Solution Review: Implement a Rectangle Class", "description": "This lesson covers the Python code to create a simple class with a constructor and attributes.", "tags": []}, "type": "Lesson"}, {"id": "5694470410469376", "title": "Challenge 2: Implement Getter Methods", "is_preview": false, "slug": "challenge-2-implement-getter-methods", "text": "## Problem Statement\nImplement the `width()` and `height()` methods which return, respectively, the width and height of a rectangle. The tests that follow will create two objects---instances of `Rectangle` to test the calculations.\n\n### Input\nA class `Rectangle` with constructor having the rectangle coordinates x1, y1, x2, and y2 respectively\n\n### Output\nThe width and height of the rectangle\n\n### Sample Input\nx1 = 2, y1 = 7, x2 = 8, y2 = 4 \n\n### Sample Output\nheight = 3, width = 6\n\n**[CanvasAnimation 图表]** - 交互式图表内容\n\n## Coding Exercise\n\n\nWrite your code below. It is recommended​ that you try solving the exercise yourself before viewing the solution.\n\nUse the [Python documentation on classes](https://docs.python.org/3/tutorial/classes.html) to solve the following exercise.\n\n```python310\nclass Rectangle:\n  def __init__(self, x1, y1, x2, y2): # class constructor\n    if x1 < x2 and y1 > y2:\n      self.x1 = x1 # class variable\n      self.y1 = y1 # class variable\n      self.x2 = x2 # class variable\n      self.y2 = y2 # class variable\n    else:\n      print(\"Incorrect coordinates of the rectangle!\")\n        \n  # write your code here\n  \n```\n\nNow, let's discuss the solution in the next lesson.", "mdHtml": "<h2>Problem Statement</h2>\n<p>Implement the <code>width()</code> and <code>height()</code> methods which return, respectively, the width and height of a rectangle. The tests that follow will create two objects—instances of <code>Rectangle</code> to test the calculations.</p>\n<h3>Input</h3>\n<p>A class <code>Rectangle</code> with constructor having the rectangle coordinates x1, y1, x2, and y2 respectively</p>\n<h3>Output</h3>\n<p>The width and height of the rectangle</p>\n<h3>Sample Input</h3>\n<p>x1 = 2, y1 = 7, x2 = 8, y2 = 4</p>\n<h3>Sample Output</h3>\n<p>height = 3, width = 6</p>\n\n<div class='image-component'>\n**[CanvasAnimation 图表]** - 交互式图表内容\n</div>\n<h2 id=\"coding-exercise\">Coding Exercise</h2>\n<p>Write your code below. It is recommended​ that you try solving the exercise yourself before viewing the solution.</p>\n<p>Use the <a href=\"https://docs.python.org/3/tutorial/classes.html\">Python documentation on classes</a> to solve the following exercise.</p>\n\n<div class='image-component'>\n```python310\nclass Rectangle:\n  def __init__(self, x1, y1, x2, y2): # class constructor\n    if x1 < x2 and y1 > y2:\n      self.x1 = x1 # class variable\n      self.y1 = y1 # class variable\n      self.x2 = x2 # class variable\n      self.y2 = y2 # class variable\n    else:\n      print(\"Incorrect coordinates of the rectangle!\")\n        \n  # write your code here\n  \n```\n</div>\n<p>Now, let’s discuss the solution in the next lesson.</p>", "summary": {"titleUpdated": true, "title": "Challenge 2: Implement Getter Functions", "description": "This lesson covers a challenge on getter functions and how to create them.", "tags": []}, "type": "Lesson"}, {"id": "5750558791041024", "title": "Solution Review: Implement Getter Methods", "is_preview": false, "slug": "solution-review-implement-getter-methods", "text": "## Solution:\nThe getter methods are written in the lines 11-15 in the following code playground. The `width()` and `height()` methods simply subtract the x and y coordinates respectively and return the result. \n\nThis solution is illustrated in the following figure:\n\n**[CanvasAnimation 图表]** - 交互式图表内容\n\nThe following code illustrates the concept:\n\n```python310\nclass Rectangle:\n  def __init__(self, x1, y1, x2, y2): # class constructor\n    if x1<x2 and y1>y2:\n      self.x1 = x1 # class variable\n      self.y1 = y1 # class variable\n      self.x2 = x2 # class variable\n      self.y2 = y2 # class variable\n    else:\n      print(\"Incorrect coordinates of the rectangle!\")\n        \n  def width(self):\n    return self.x2-self.x1\n      \n  def height(self):\n    return self.y1-self.y2\n  \nrectangle = Rectangle(2, 7, 8, 4)\nprint(rectangle.width())\nprint(rectangle.height())\n```\n\nNow, let's expand this challenge by implementing the area and perimeter methods in the next lesson.", "mdHtml": "<h2 id=\"solution\">Solution:</h2>\n<p>The getter methods are written in the lines 11-15 in the following code playground. The <code>width()</code> and <code>height()</code> methods simply subtract the x and y coordinates respectively and return the result.</p>\n<p>This solution is illustrated in the following figure:</p>\n\n<div class='image-component'>\n**[CanvasAnimation 图表]** - 交互式图表内容\n</div>\n<p>The following code illustrates the concept:</p>\n\n<div class='image-component'>\n```python310\nclass Rectangle:\n  def __init__(self, x1, y1, x2, y2): # class constructor\n    if x1<x2 and y1>y2:\n      self.x1 = x1 # class variable\n      self.y1 = y1 # class variable\n      self.x2 = x2 # class variable\n      self.y2 = y2 # class variable\n    else:\n      print(\"Incorrect coordinates of the rectangle!\")\n        \n  def width(self):\n    return self.x2-self.x1\n      \n  def height(self):\n    return self.y1-self.y2\n  \nrectangle = Rectangle(2, 7, 8, 4)\nprint(rectangle.width())\nprint(rectangle.height())\n```\n</div>\n<p>Now, let’s expand this challenge by implementing the area and perimeter methods in the next lesson.</p>", "summary": {"titleUpdated": true, "title": "Challenge 2: Implement Getter Functions", "description": "This lesson gives the solution to the previous exercise - implementing the getter methods to calculate the width and height of a rectangle.", "tags": []}, "type": "Lesson"}, {"id": "5767725372669952", "title": "Challenge 3: Implement Area and Perimeter Member Methods", "is_preview": false, "slug": "challenge-3-implement-area-and-perimeter-member-methods", "text": "## Problem Statement\n\nImplement the `area()` and `perimeter()` methods to return the area and perimeter of the rectangle respectively, where\n$$ Area = width*height $$\n$$ Perimeter = 2*width + 2*height $$\n\n\n\n### Input\nA class `Rectangle` with constructor having the rectangle coordinates x1, y1, x2, and y2 respectively\n\n### Output\nThe area and perimeter of the rectangle\n\n### Sample Input\nx1 = 2, y1 = 7, x2 = 5, y2 = 3 \n\n### Sample Output\nArea = 12, Perimeter = 14\n\n \n## Coding Exercise\nWrite your code below. It is recommended​ that you try solving the exercise yourself before viewing the solution.\n\n\n\n```python310\nclass Rectangle:\n  def __init__(self, x1, y1, x2, y2): # class constructor\n    if x1<x2 and y1>y2:\n      self.x1 = x1 # class variable\n      self.y1 = y1 # class variable\n      self.x2 = x2 # class variable\n      self.y2 = y2 # class variable\n    else:\n      print(\"Incorrect coordinates of the rectangle!\")\n        \n  def width(self):\n    return self.x2-self.x1\n      \n  def height(self):\n    return self.y1-self.y2\n  \n  #write your code here\n```\n\nIn the next lesson, we will discuss the solution to this challenge.", "mdHtml": "<h2>Problem Statement</h2>\n<p>Implement the <code>area()</code> and <code>perimeter()</code> methods to return the area and perimeter of the rectangle respectively, where</p>\n<p class=\"katex-block \"><span class=\"katex-display\"><span class=\"katex\"><span class=\"katex-mathml\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\" display=\"block\"><semantics><mrow><mi>A</mi><mi>r</mi><mi>e</mi><mi>a</mi><mo>=</mo><mi>w</mi><mi>i</mi><mi>d</mi><mi>t</mi><mi>h</mi><mo>∗</mo><mi>h</mi><mi>e</mi><mi>i</mi><mi>g</mi><mi>h</mi><mi>t</mi></mrow><annotation encoding=\"application/x-tex\">Area = width*height \n</annotation></semantics></math></span><span class=\"katex-html\" aria-hidden=\"true\"><span class=\"base\"><span class=\"strut\" style=\"height:0.6833em;\"></span><span class=\"mord mathnormal\">A</span><span class=\"mord mathnormal\">re</span><span class=\"mord mathnormal\">a</span><span class=\"mspace\" style=\"margin-right:0.2778em;\"></span><span class=\"mrel\">=</span><span class=\"mspace\" style=\"margin-right:0.2778em;\"></span></span><span class=\"base\"><span class=\"strut\" style=\"height:0.6944em;\"></span><span class=\"mord mathnormal\" style=\"margin-right:0.02691em;\">w</span><span class=\"mord mathnormal\">i</span><span class=\"mord mathnormal\">d</span><span class=\"mord mathnormal\">t</span><span class=\"mord mathnormal\">h</span><span class=\"mspace\" style=\"margin-right:0.2222em;\"></span><span class=\"mbin\">∗</span><span class=\"mspace\" style=\"margin-right:0.2222em;\"></span></span><span class=\"base\"><span class=\"strut\" style=\"height:0.8889em;vertical-align:-0.1944em;\"></span><span class=\"mord mathnormal\">h</span><span class=\"mord mathnormal\">e</span><span class=\"mord mathnormal\">i</span><span class=\"mord mathnormal\" style=\"margin-right:0.03588em;\">g</span><span class=\"mord mathnormal\">h</span><span class=\"mord mathnormal\">t</span></span></span></span></span></p>\n<p class=\"katex-block \"><span class=\"katex-display\"><span class=\"katex\"><span class=\"katex-mathml\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\" display=\"block\"><semantics><mrow><mi>P</mi><mi>e</mi><mi>r</mi><mi>i</mi><mi>m</mi><mi>e</mi><mi>t</mi><mi>e</mi><mi>r</mi><mo>=</mo><mn>2</mn><mo>∗</mo><mi>w</mi><mi>i</mi><mi>d</mi><mi>t</mi><mi>h</mi><mo>+</mo><mn>2</mn><mo>∗</mo><mi>h</mi><mi>e</mi><mi>i</mi><mi>g</mi><mi>h</mi><mi>t</mi></mrow><annotation encoding=\"application/x-tex\">Perimeter = 2*width + 2*height \n</annotation></semantics></math></span><span class=\"katex-html\" aria-hidden=\"true\"><span class=\"base\"><span class=\"strut\" style=\"height:0.6833em;\"></span><span class=\"mord mathnormal\" style=\"margin-right:0.13889em;\">P</span><span class=\"mord mathnormal\" style=\"margin-right:0.02778em;\">er</span><span class=\"mord mathnormal\">im</span><span class=\"mord mathnormal\">e</span><span class=\"mord mathnormal\">t</span><span class=\"mord mathnormal\" style=\"margin-right:0.02778em;\">er</span><span class=\"mspace\" style=\"margin-right:0.2778em;\"></span><span class=\"mrel\">=</span><span class=\"mspace\" style=\"margin-right:0.2778em;\"></span></span><span class=\"base\"><span class=\"strut\" style=\"height:0.6444em;\"></span><span class=\"mord\">2</span><span class=\"mspace\" style=\"margin-right:0.2222em;\"></span><span class=\"mbin\">∗</span><span class=\"mspace\" style=\"margin-right:0.2222em;\"></span></span><span class=\"base\"><span class=\"strut\" style=\"height:0.7778em;vertical-align:-0.0833em;\"></span><span class=\"mord mathnormal\" style=\"margin-right:0.02691em;\">w</span><span class=\"mord mathnormal\">i</span><span class=\"mord mathnormal\">d</span><span class=\"mord mathnormal\">t</span><span class=\"mord mathnormal\">h</span><span class=\"mspace\" style=\"margin-right:0.2222em;\"></span><span class=\"mbin\">+</span><span class=\"mspace\" style=\"margin-right:0.2222em;\"></span></span><span class=\"base\"><span class=\"strut\" style=\"height:0.6444em;\"></span><span class=\"mord\">2</span><span class=\"mspace\" style=\"margin-right:0.2222em;\"></span><span class=\"mbin\">∗</span><span class=\"mspace\" style=\"margin-right:0.2222em;\"></span></span><span class=\"base\"><span class=\"strut\" style=\"height:0.8889em;vertical-align:-0.1944em;\"></span><span class=\"mord mathnormal\">h</span><span class=\"mord mathnormal\">e</span><span class=\"mord mathnormal\">i</span><span class=\"mord mathnormal\" style=\"margin-right:0.03588em;\">g</span><span class=\"mord mathnormal\">h</span><span class=\"mord mathnormal\">t</span></span></span></span></span></p>\n<h3>Input</h3>\n<p>A class <code>Rectangle</code> with constructor having the rectangle coordinates x1, y1, x2, and y2 respectively</p>\n<h3>Output</h3>\n<p>The area and perimeter of the rectangle</p>\n<h3>Sample Input</h3>\n<p>x1 = 2, y1 = 7, x2 = 5, y2 = 3</p>\n<h3>Sample Output</h3>\n<p>Area = 12, Perimeter = 14</p>\n<h2>Coding Exercise</h2>\n<p>Write your code below. It is recommended​ that you try solving the exercise yourself before viewing the solution.</p>\n\n<div class='image-component'>\n```python310\nclass Rectangle:\n  def __init__(self, x1, y1, x2, y2): # class constructor\n    if x1<x2 and y1>y2:\n      self.x1 = x1 # class variable\n      self.y1 = y1 # class variable\n      self.x2 = x2 # class variable\n      self.y2 = y2 # class variable\n    else:\n      print(\"Incorrect coordinates of the rectangle!\")\n        \n  def width(self):\n    return self.x2-self.x1\n      \n  def height(self):\n    return self.y1-self.y2\n  \n  #write your code here\n```\n</div>\n<p>In the next lesson, we will discuss the solution to this challenge.</p>", "summary": {"titleUpdated": true, "title": "Challenge 3: Implement Area and Circumference Member Functions", "description": "Solve an exercise to practice your Python classes, especially the member methods inside a class.", "tags": []}, "type": "Lesson"}, {"id": "6234761592832000", "title": "Solution Review: Implement Area and Perimeter Methods", "is_preview": false, "slug": "solution-review-implement-area-and-perimeter-methods", "text": "## Solution:\n\nThe `area()` and `perimeter()` methods are written in lines 17-21. They simply take the values of `width()` and `height()` and perform the following calculations on them:\n$$ Area = width*height $$\n$$ Perimeter = 2*width + 2*height $$\n\nThis is shown in the code below:\n\n\n```python310\nclass Rectangle:\n  def __init__(self, x1, y1, x2, y2): # class constructor\n    if x1<x2 and y1>y2:\n      self.x1 = x1 # class variable\n      self.y1 = y1 # class variable\n      self.x2 = x2 # class variable\n      self.y2 = y2 # class variable\n    else:\n      print(\"Incorrect coordinates of the rectangle!\")\n        \n  def width(self):\n    return self.x2-self.x1\n      \n  def height(self):\n    return self.y1-self.y2\n  \n  def area(self):\n    return self.width()*self.height()\n  \n  def perimeter(self):\n    return 2 * self.width() + 2 * self.height()\n\n# test your code\nr = Rectangle (2, 7, 8, 4)\nprint(\"Area: \" + str(r.area()))\nprint(\"Perimeter: \" + str(r.perimeter()))\n```\n\nIn the next lesson, you will do an exercise on the print method of classes.", "mdHtml": "<h2>Solution:</h2>\n<p>The <code>area()</code> and <code>perimeter()</code> methods are written in lines 17-21. They simply take the values of <code>width()</code> and <code>height()</code> and perform the following calculations on them:</p>\n<p class=\"katex-block \"><span class=\"katex-display\"><span class=\"katex\"><span class=\"katex-mathml\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\" display=\"block\"><semantics><mrow><mi>A</mi><mi>r</mi><mi>e</mi><mi>a</mi><mo>=</mo><mi>w</mi><mi>i</mi><mi>d</mi><mi>t</mi><mi>h</mi><mo>∗</mo><mi>h</mi><mi>e</mi><mi>i</mi><mi>g</mi><mi>h</mi><mi>t</mi></mrow><annotation encoding=\"application/x-tex\">Area = width*height \n</annotation></semantics></math></span><span class=\"katex-html\" aria-hidden=\"true\"><span class=\"base\"><span class=\"strut\" style=\"height:0.6833em;\"></span><span class=\"mord mathnormal\">A</span><span class=\"mord mathnormal\">re</span><span class=\"mord mathnormal\">a</span><span class=\"mspace\" style=\"margin-right:0.2778em;\"></span><span class=\"mrel\">=</span><span class=\"mspace\" style=\"margin-right:0.2778em;\"></span></span><span class=\"base\"><span class=\"strut\" style=\"height:0.6944em;\"></span><span class=\"mord mathnormal\" style=\"margin-right:0.02691em;\">w</span><span class=\"mord mathnormal\">i</span><span class=\"mord mathnormal\">d</span><span class=\"mord mathnormal\">t</span><span class=\"mord mathnormal\">h</span><span class=\"mspace\" style=\"margin-right:0.2222em;\"></span><span class=\"mbin\">∗</span><span class=\"mspace\" style=\"margin-right:0.2222em;\"></span></span><span class=\"base\"><span class=\"strut\" style=\"height:0.8889em;vertical-align:-0.1944em;\"></span><span class=\"mord mathnormal\">h</span><span class=\"mord mathnormal\">e</span><span class=\"mord mathnormal\">i</span><span class=\"mord mathnormal\" style=\"margin-right:0.03588em;\">g</span><span class=\"mord mathnormal\">h</span><span class=\"mord mathnormal\">t</span></span></span></span></span></p>\n<p class=\"katex-block \"><span class=\"katex-display\"><span class=\"katex\"><span class=\"katex-mathml\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\" display=\"block\"><semantics><mrow><mi>P</mi><mi>e</mi><mi>r</mi><mi>i</mi><mi>m</mi><mi>e</mi><mi>t</mi><mi>e</mi><mi>r</mi><mo>=</mo><mn>2</mn><mo>∗</mo><mi>w</mi><mi>i</mi><mi>d</mi><mi>t</mi><mi>h</mi><mo>+</mo><mn>2</mn><mo>∗</mo><mi>h</mi><mi>e</mi><mi>i</mi><mi>g</mi><mi>h</mi><mi>t</mi></mrow><annotation encoding=\"application/x-tex\">Perimeter = 2*width + 2*height \n</annotation></semantics></math></span><span class=\"katex-html\" aria-hidden=\"true\"><span class=\"base\"><span class=\"strut\" style=\"height:0.6833em;\"></span><span class=\"mord mathnormal\" style=\"margin-right:0.13889em;\">P</span><span class=\"mord mathnormal\" style=\"margin-right:0.02778em;\">er</span><span class=\"mord mathnormal\">im</span><span class=\"mord mathnormal\">e</span><span class=\"mord mathnormal\">t</span><span class=\"mord mathnormal\" style=\"margin-right:0.02778em;\">er</span><span class=\"mspace\" style=\"margin-right:0.2778em;\"></span><span class=\"mrel\">=</span><span class=\"mspace\" style=\"margin-right:0.2778em;\"></span></span><span class=\"base\"><span class=\"strut\" style=\"height:0.6444em;\"></span><span class=\"mord\">2</span><span class=\"mspace\" style=\"margin-right:0.2222em;\"></span><span class=\"mbin\">∗</span><span class=\"mspace\" style=\"margin-right:0.2222em;\"></span></span><span class=\"base\"><span class=\"strut\" style=\"height:0.7778em;vertical-align:-0.0833em;\"></span><span class=\"mord mathnormal\" style=\"margin-right:0.02691em;\">w</span><span class=\"mord mathnormal\">i</span><span class=\"mord mathnormal\">d</span><span class=\"mord mathnormal\">t</span><span class=\"mord mathnormal\">h</span><span class=\"mspace\" style=\"margin-right:0.2222em;\"></span><span class=\"mbin\">+</span><span class=\"mspace\" style=\"margin-right:0.2222em;\"></span></span><span class=\"base\"><span class=\"strut\" style=\"height:0.6444em;\"></span><span class=\"mord\">2</span><span class=\"mspace\" style=\"margin-right:0.2222em;\"></span><span class=\"mbin\">∗</span><span class=\"mspace\" style=\"margin-right:0.2222em;\"></span></span><span class=\"base\"><span class=\"strut\" style=\"height:0.8889em;vertical-align:-0.1944em;\"></span><span class=\"mord mathnormal\">h</span><span class=\"mord mathnormal\">e</span><span class=\"mord mathnormal\">i</span><span class=\"mord mathnormal\" style=\"margin-right:0.03588em;\">g</span><span class=\"mord mathnormal\">h</span><span class=\"mord mathnormal\">t</span></span></span></span></span></p>\n<p>This is shown in the code below:</p>\n\n<div class='image-component'>\n```python310\nclass Rectangle:\n  def __init__(self, x1, y1, x2, y2): # class constructor\n    if x1<x2 and y1>y2:\n      self.x1 = x1 # class variable\n      self.y1 = y1 # class variable\n      self.x2 = x2 # class variable\n      self.y2 = y2 # class variable\n    else:\n      print(\"Incorrect coordinates of the rectangle!\")\n        \n  def width(self):\n    return self.x2-self.x1\n      \n  def height(self):\n    return self.y1-self.y2\n  \n  def area(self):\n    return self.width()*self.height()\n  \n  def perimeter(self):\n    return 2 * self.width() + 2 * self.height()\n\n# test your code\nr = Rectangle (2, 7, 8, 4)\nprint(\"Area: \" + str(r.area()))\nprint(\"Perimeter: \" + str(r.perimeter()))\n```\n</div>\n<p>In the next lesson, you will do an exercise on the print method of classes.</p>", "summary": {"titleUpdated": true, "title": "Challenge 3: Implement Area and Circumference Member Functions", "description": "This lesson discusses how to calculate the area and perimeter of a rectangle.", "tags": []}, "type": "Lesson"}, {"id": "5635337300738048", "title": "Challenge 4: Implement a Print Method", "is_preview": false, "slug": "challenge-4-implement-a-print-method", "text": "## Problem Statement\n\nImplement a function in the Rectangle class `__str__` method, such that when you print one of the objects using the `print()` command, it prints the coordinates as `x1, y1, x2, y2`. \n\nFor instance, the code\n```python\nrectangle = Rectangle(2, 3, 5, 7)\nprint(rectangle)\n```\nshould print\n```\n2, 3, 5, 7\n```\n\n### Input\nA class `Rectangle` with constructor having the rectangle coordinates x1, y1, x2, and y2 respectively\n\n### Output\nPrint the coordinates of the rectangle\n\n\n### Sample Input\n\nx1 = 2, y1 = 3, x2 = 5, y2 = 7\n\n### Sample Output\n2, 3, 5, 7\n\n\n\n\n\n## Coding Exercise\nWrite your code below. It is recommended​ that you try solving the exercise yourself before viewing the solution.\n\nUse the [Python documentation on classes](https://docs.python.org/3/tutorial/classes.html) to solve the following exercise.\n\n```python310\nclass Rectangle:\n  def __init__(self, x1, y1, x2, y2): # class constructor\n    self.x1 = x1 # class variable\n    self.y1 = y1 # class variable\n    self.x2 = x2 # class variable\n    self.y2 = y2 # class variable        \n  #write your code here\n```\n\nIn the next lesson, we will discuss the solution to this exercise.", "mdHtml": "<h2 id=\"problem-statement\">Problem Statement</h2>\n<p>Implement a function in the Rectangle class <code>__str__</code> method, such that when you print one of the objects using the <code>print()</code> command, it prints the coordinates as <code>x1, y1, x2, y2</code>.</p>\n<p>For instance, the code</p>\n<pre><code class=\"language-python\">rectangle = Rectangle(2, 3, 5, 7)\nprint(rectangle)\n</code></pre>\n<p>should print</p>\n<pre><code>2, 3, 5, 7\n</code></pre>\n<h3 id=\"input\">Input</h3>\n<p>A class <code>Rectangle</code> with constructor having the rectangle coordinates x1, y1, x2, and y2 respectively</p>\n<h3 id=\"output\">Output</h3>\n<p>Print the coordinates of the rectangle</p>\n<h3 id=\"sample-input\">Sample Input</h3>\n<p>x1 = 2, y1 = 3, x2 = 5, y2 = 7</p>\n<h3 id=\"sample-output\">Sample Output</h3>\n<p>2, 3, 5, 7</p>\n<h2 id=\"coding-exercise\">Coding Exercise</h2>\n<p>Write your code below. It is recommended​ that you try solving the exercise yourself before viewing the solution.</p>\n<p>Use the <a href=\"https://docs.python.org/3/tutorial/classes.html\">Python documentation on classes</a> to solve the following exercise.</p>\n\n<div class='image-component'>\n```python310\nclass Rectangle:\n  def __init__(self, x1, y1, x2, y2): # class constructor\n    self.x1 = x1 # class variable\n    self.y1 = y1 # class variable\n    self.x2 = x2 # class variable\n    self.y2 = y2 # class variable        \n  #write your code here\n```\n</div>\n<p>In the next lesson, we will discuss the solution to this exercise.</p>", "summary": {"titleUpdated": true, "title": "Challenge 4: Implement a Print Function", "description": "In this exercise, you will modify the rectangle class such that the print method actually prints values instead of addresses.", "tags": []}, "type": "Lesson"}, {"id": "5658887009075200", "title": "Solution Review: Implement a Print Method", "is_preview": false, "slug": "solution-review-implement-a-print-method", "text": "## Solution:\nIn Python, and in many other languages for that matter, if we make a class and print an instance of that class the output may vary every time. It prints the address of the object in memory. Consider the following code:\n\n```python310\nclass Rectangle:\n  def __init__(self, x1, y1, x2, y2): # class constructor\n    if x1 < x2 and y1 > y2:\n      self.x1 = x1 # class variable\n      self.y1 = y1 # class variable\n      self.x2 = x2 # class variable\n      self.y2 = y2 # class variable\n    else:\n      print(\"Incorrect coordinates of the rectangle!\")\n        \n  \n# test your code\nr = Rectangle (2, 7, 8, 4)\nprint (r)\n```\n\nHowever, python has a built-in method `__str__` used for the string representation of an object. `__repr__` is another built-in method which is similar to `__str__`. Both of them can be overridden for any class and there are only minor differences. \n\n`str()`:\n1. makes the object readable\n2. generates output for end-user\n\n`repr()`:\n1. needs code that reproduces the object\n2. generates output for developer\n\n\nIf both methods are defined in a class, `__str__` is used.\n\n\n\nIn the previous exercise, you had to implement the `__str__` method in the `Rectangle` class; therefore, when you print one of the objects using the `print()` command, it prints the coordinates as `x1, y1, x2, y2`. \n\n### Using `__str__`\n**Lines 11 and 12** in the code below show how this is done.\n\n```python310\nclass Rectangle:\n  def __init__(self, x1, y1, x2, y2): # class constructor\n    if x1 < x2 and y1 > y2:\n      self.x1 = x1 # class variable\n      self.y1 = y1 # class variable\n      self.x2 = x2 # class variable\n      self.y2 = y2 # class variable\n    else:\n      print(\"Incorrect coordinates of the rectangle!\")\n        \n  def __str__(self):\n    return(str(self.x1) + ', ' + str(self.y1) + ', ' + str(self.x2) + ', '+str(self.y2))\n  \n# test your code\nr = Rectangle (2, 7, 8, 4)\nprint (r)\n```\n\n### Using `__repr__`\n**Lines 11 and 12** in the code below show how this is done.\n\n```python310\nclass Rectangle:\n  def __init__(self, x1, y1, x2, y2): # class constructor\n    if x1 < x2 and y1 > y2:\n      self.x1 = x1 # class variable\n      self.y1 = y1 # class variable\n      self.x2 = x2 # class variable\n      self.y2 = y2 # class variable\n    else:\n      print(\"Incorrect coordinates of the rectangle!\")\n        \n  def __repr__(self):\n    return(str(self.x1) + ', ' + str(self.y1) + ', ' + str(self.x2) + ', '+str(self.y2))\n  \n# test your code\nr = Rectangle (2, 7, 8, 4)\nprint (r)\n```\n\nNow that you know the basic concepts of classes, let's move on to another concept in object-oriented programming - inheritance.", "mdHtml": "<h2 id=\"solution\">Solution:</h2>\n<p>In Python, and in many other languages for that matter, if we make a class and print an instance of that class the output may vary every time. It prints the address of the object in memory. Consider the following code:</p>\n\n<div class='image-component'>\n```python310\nclass Rectangle:\n  def __init__(self, x1, y1, x2, y2): # class constructor\n    if x1 < x2 and y1 > y2:\n      self.x1 = x1 # class variable\n      self.y1 = y1 # class variable\n      self.x2 = x2 # class variable\n      self.y2 = y2 # class variable\n    else:\n      print(\"Incorrect coordinates of the rectangle!\")\n        \n  \n# test your code\nr = Rectangle (2, 7, 8, 4)\nprint (r)\n```\n</div>\n<p>However, python has a built-in method <code>__str__</code> used for the string representation of an object. <code>__repr__</code> is another built-in method which is similar to <code>__str__</code>. Both of them can be overridden for any class and there are only minor differences.</p>\n<p><code>str()</code>:</p>\n<ol>\n<li>makes the object readable</li>\n<li>generates output for end-user</li>\n</ol>\n<p><code>repr()</code>:</p>\n<ol>\n<li>needs code that reproduces the object</li>\n<li>generates output for developer</li>\n</ol>\n<p>If both methods are defined in a class, <code>__str__</code> is used.</p>\n<p>In the previous exercise, you had to implement the <code>__str__</code> method in the <code>Rectangle</code> class; therefore, when you print one of the objects using the <code>print()</code> command, it prints the coordinates as <code>x1, y1, x2, y2</code>.</p>\n<h3 id=\"using-__str__\">Using <code>__str__</code></h3>\n<p><strong>Lines 11 and 12</strong> in the code below show how this is done.</p>\n\n<div class='image-component'>\n```python310\nclass Rectangle:\n  def __init__(self, x1, y1, x2, y2): # class constructor\n    if x1 < x2 and y1 > y2:\n      self.x1 = x1 # class variable\n      self.y1 = y1 # class variable\n      self.x2 = x2 # class variable\n      self.y2 = y2 # class variable\n    else:\n      print(\"Incorrect coordinates of the rectangle!\")\n        \n  def __str__(self):\n    return(str(self.x1) + ', ' + str(self.y1) + ', ' + str(self.x2) + ', '+str(self.y2))\n  \n# test your code\nr = Rectangle (2, 7, 8, 4)\nprint (r)\n```\n</div>\n<h3 id=\"using-__repr__\">Using <code>__repr__</code></h3>\n<p><strong>Lines 11 and 12</strong> in the code below show how this is done.</p>\n\n<div class='image-component'>\n```python310\nclass Rectangle:\n  def __init__(self, x1, y1, x2, y2): # class constructor\n    if x1 < x2 and y1 > y2:\n      self.x1 = x1 # class variable\n      self.y1 = y1 # class variable\n      self.x2 = x2 # class variable\n      self.y2 = y2 # class variable\n    else:\n      print(\"Incorrect coordinates of the rectangle!\")\n        \n  def __repr__(self):\n    return(str(self.x1) + ', ' + str(self.y1) + ', ' + str(self.x2) + ', '+str(self.y2))\n  \n# test your code\nr = Rectangle (2, 7, 8, 4)\nprint (r)\n```\n</div>\n<p>Now that you know the basic concepts of classes, let’s move on to another concept in object-oriented programming - inheritance.</p>", "summary": {"titleUpdated": true, "title": "Challenge 4: Implement a Print Function", "description": "This lesson discusses the __str__ method in Python for the string representation of an object.", "tags": []}, "type": "Lesson"}, {"id": "5703462461374464", "title": "Inheritance", "is_preview": false, "slug": "inheritance", "text": "## Class Inheritance\n\nInheritance is an essential part of object-oriented programming. Inheritance is a process in which a **subclass** can inherit the attributes and methods of another class, allowing it to rewrite some of the **super class**’s functionalities. For instance, the `Person` class in the first [lesson](https://www.educative.io/courses/full-speed-python/classes/) we could permit us to create a subclass for people at 10 years of age.\n\n**[CanvasAnimation 图表]** - 交互式图表内容\n\nThe following codes demonstrates how this works in Python:\n\n```python310\nclass Person:\n      def __init__(self, name, age): # Person's constructor\n          self.name = name # Person's attribute\n          self.age = age # Person's attribute\n\n      def greet(self): # Person's method\n          print(\"Hello, my name is %s!\" % self.name)\n\nclass TenYearOldPerson(Person): # TenYearOldPerson inherits from Person\n\n      def __init__(self, name): # TenYearOld<PERSON>erson's constructor\n          Person.__init__(self, name, 10) # accesses Person's constructor\n\n      def greet(self): # rewrites the greet method\n          print(\"I don't talk to strangers!!\")\n\ntyo = TenYearOldPerson(\"Jack\") # instance of TenYearOldPerson\ntyo.greet() # call greet method of the TenYearOldPerson\n```\n\n\nThe indication that the `TenYearOldPerson` class is a subclass of `Person` is given on line 9. Then, we rewrote the constructor of the subclass only to receive the name of the person, but we will eventually call the super class’s constructor with the name of the 10-year-old and the age hardcoded as 10. Finally, we reimplemented the `greet` method.\n\nNow that you know the basics of inheritance, let's move on to slightly more advanced concepts of inheritance in the next lesson.", "mdHtml": "<h2 id=\"class-inheritance\">Class Inheritance</h2>\n<p>Inheritance is an essential part of object-oriented programming. Inheritance is a process in which a <strong>subclass</strong> can inherit the attributes and methods of another class, allowing it to rewrite some of the <strong>super class</strong>’s functionalities. For instance, the <code>Person</code> class in the first <a href=\"https://www.educative.io/collection/page/10370001/5765097389555712/5097599342215168/\">lesson</a> we could permit us to create a subclass for people at 10 years of age.</p>\n\n<div class='image-component'>\n**[CanvasAnimation 图表]** - 交互式图表内容\n</div>\n<p>The following codes demonstrates how this works in Python:</p>\n\n<div class='image-component'>\n```python310\nclass Person:\n      def __init__(self, name, age): # Person's constructor\n          self.name = name # Person's attribute\n          self.age = age # Person's attribute\n\n      def greet(self): # Person's method\n          print(\"Hello, my name is %s!\" % self.name)\n\nclass TenYearOldPerson(Person): # TenYearOld<PERSON>erson inherits from Person\n\n      def __init__(self, name): # TenYearOld<PERSON>erson's constructor\n          Person.__init__(self, name, 10) # accesses Person's constructor\n\n      def greet(self): # rewrites the greet method\n          print(\"I don't talk to strangers!!\")\n\ntyo = TenYearOldPerson(\"Jack\") # instance of TenYearOldPerson\ntyo.greet() # call greet method of the TenYearOldPerson\n```\n</div>\n<p>The indication that the <code>TenYearOldPerson</code> class is a subclass of <code>Person</code> is given on line 9. Then, we rewrote the constructor of the subclass only to receive the name of the person, but we will eventually call the super class’s constructor with the name of the 10-year-old and the age hardcoded as 10. Finally, we reimplemented the <code>greet</code> method.</p>\n<p>Now that you know the basics of inheritance, let’s move on to slightly more advanced concepts of inheritance in the next lesson.</p>", "summary": {"titleUpdated": true, "title": "Inheritance", "description": "This lesson discusses inheritance, i.e., one class inheriting attributes and methods of another class.", "tags": []}, "type": "Lesson"}, {"id": "5636219245428736", "title": "Multi-Level Inheritance", "is_preview": true, "slug": "multi-level-inheritance", "text": "## Introduction\nIn addition to single-level inheritance, Python also supports multi-level inheritance. This means that you can create a hierarchy of classes, each inheriting from its superclass. The following figure illustrates an example of multi-level inheritance.\n\n**[CanvasAnimation 图表]** - 交互式图表内容\n\nThe following hierarchy is clear from the above figure:\n\n|**Class**|**Superclass**|**Relation**|\n|-|-|-|\n|Carnivore|Mammal|Carnivore *is a* Mammal|\n|Mammal|Animal|Mammal *is an* Animal|\n|Animal|-|-|\n\n## Implementation\nSyntactically, multi-level inheritance in Python is quite similar to single-level inheritance. Consider the following basic code snippet: \n\n```python310\nclass Animal ():\n  def __init__(self, name, food, characteristic): # Animal's constructor\n    self.name = name # Animal's attribute\n    self.characteristic = characteristic # Animal's attribute\n    self.food = food # Animal's attribute\n    print (\"I am a \" + str(self.name) + \".\")\n    \nclass Mammal (Animal): # Mammal inherits from Animal\n  def __init__(self, name, food): # Mammal's constructor\n    Animal.__init__(self, name, food, \"warm blooded\") # Animal's constructor\n    print (\"I am warm blooded.\")\n    \nclass Carnivore (Mammal): # Carnivore inherits from Mammal\n  def __init__(self, name): # Carnivore's constructor\n    Mammal.__init__(self, name, \"meat\") # Mammal's constructor \n    print (\"I eat meat.\")\n\nlion = Carnivore(\"lion\") # lion is an instance of Carnivore\n```\n\nHowever, if we move the `print` method in a separate method, the subclass can not only access the method of its parent class---but also override it if needed. This behavior is shown in the highlighted lines of the following code:\n\n```python310\nclass Animal ():\n  def __init__(self, name, food, characteristic):\n    self.name = name\n    self.characteristic = characteristic\n    self.food = food\n  def printer(self):\n    print (\"I am a \" + str(self.name) + \".\")\n    \nclass Mammal (Animal):\n  def __init__(self, name, food):\n    Animal.__init__(self, name, food, \"warm blooded\")\n  def printer(self):\n    print (\"I am warm blooded.\")\n    \nclass Carnivore (Mammal):\n  def __init__(self, name):\n    Mammal.__init__(self, name, \"meat\")\n  def printer(self):\n    print (\"I eat meat.\")\n\nlion = Carnivore(\"lion\")\nlion.printer()\n```\n\nNow if you remove the `printer()` method of the `Carnivore` class in lines 18, 19, the `lion` object will be able to access the `printer()` method of its super/parent class `Mammal`. The output will then become:\n```python\nI am warm blooded.\n```\nIf you also remove the `printer()` method in 12, 13 of the `Mammal` class, the `lion` object will access the `printer()` method of its superclass `Animal`. It will then print:\n```python\nI am a lion.\n```\nIn the next lesson, we will discuss multiple inheritance; it's slightly different from multi-level inheritance.", "mdHtml": "<h2 id=\"introduction\">Introduction</h2>\n<p>In addition to single-level inheritance, Python also supports multi-level inheritance. This means that you can create a hierarchy of classes, each inheriting from its superclass. The following figure illustrates an example of multi-level inheritance.</p>\n\n<div class='image-component'>\n**[CanvasAnimation 图表]** - 交互式图表内容\n</div>\n<p>The following hierarchy is clear from the above figure:</p>\n<table>\n<thead>\n<tr>\n<th><strong>Class</strong></th>\n<th><strong>Superclass</strong></th>\n<th><strong>Relation</strong></th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>Carnivore</td>\n<td>Mammal</td>\n<td>Carnivore <em>is a</em> Mammal</td>\n</tr>\n<tr>\n<td>Mammal</td>\n<td>Animal</td>\n<td>Mammal <em>is an</em> Animal</td>\n</tr>\n<tr>\n<td>Animal</td>\n<td>-</td>\n<td>-</td>\n</tr>\n</tbody>\n</table>\n<h2 id=\"implementation\">Implementation</h2>\n<p>Syntactically, multi-level inheritance in Python is quite similar to single-level inheritance. Consider the following basic code snippet:</p>\n\n<div class='image-component'>\n```python310\nclass Animal ():\n  def __init__(self, name, food, characteristic): # Animal's constructor\n    self.name = name # Animal's attribute\n    self.characteristic = characteristic # Animal's attribute\n    self.food = food # Animal's attribute\n    print (\"I am a \" + str(self.name) + \".\")\n    \nclass Mammal (Animal): # Mammal inherits from Animal\n  def __init__(self, name, food): # Mammal's constructor\n    Animal.__init__(self, name, food, \"warm blooded\") # Animal's constructor\n    print (\"I am warm blooded.\")\n    \nclass Carnivore (Mammal): # Carnivore inherits from Mammal\n  def __init__(self, name): # Carnivore's constructor\n    Mammal.__init__(self, name, \"meat\") # Mammal's constructor \n    print (\"I eat meat.\")\n\nlion = Carnivore(\"lion\") # lion is an instance of Carnivore\n```\n</div>\n<p>However, if we move the <code>print</code> method in a separate method, the subclass can not only access the method of its parent class—but also override it if needed. This behavior is shown in the highlighted lines of the following code:</p>\n\n<div class='image-component'>\n```python310\nclass Animal ():\n  def __init__(self, name, food, characteristic):\n    self.name = name\n    self.characteristic = characteristic\n    self.food = food\n  def printer(self):\n    print (\"I am a \" + str(self.name) + \".\")\n    \nclass Mammal (Animal):\n  def __init__(self, name, food):\n    Animal.__init__(self, name, food, \"warm blooded\")\n  def printer(self):\n    print (\"I am warm blooded.\")\n    \nclass Carnivore (Mammal):\n  def __init__(self, name):\n    Mammal.__init__(self, name, \"meat\")\n  def printer(self):\n    print (\"I eat meat.\")\n\nlion = Carnivore(\"lion\")\nlion.printer()\n```\n</div>\n<p>Now if you remove the <code>printer()</code> method of the <code>Carnivore</code> class in lines 18, 19, the <code>lion</code> object will be able to access the <code>printer()</code> method of its super/parent class <code>Mammal</code>. The output will then become:</p>\n<pre><code class=\"language-python\">I am warm blooded.\n</code></pre>\n<p>If you also remove the <code>printer()</code> method in 12, 13 of the <code>Mammal</code> class, the <code>lion</code> object will access the <code>printer()</code> method of its superclass <code>Animal</code>. It will then print:</p>\n<pre><code class=\"language-python\">I am a lion.\n</code></pre>\n<p>In the next lesson, we will discuss multiple inheritance; it’s slightly different from multi-level inheritance.</p>", "summary": {"titleUpdated": true, "title": "", "description": "This lesson talks about a hierarchy of classes when one class inherits from its parent class, and that parent class inherits from its parent class, and so on.", "tags": []}, "type": "Lesson"}, {"id": "5715594536026112", "title": "Multiple Inheritance", "is_preview": false, "slug": "multiple-inheritance", "text": "In Python, a class can inherit **attributes** and **methods** from **more than one class**. Critics of multiple inheritance say that it is confusing and complex, like the [diamond problem](https://en.wikipedia.org/wiki/Multiple_inheritance), which is out of the scope of this lesson.\n\nThe idea that multiple inheritance is somehow \"bad\" is based on the fact that some languages either do not support multiple inheritance (like Java) or do not have a very good implementation for it. Python, however, has a very sophisticated and well-structured approach to multiple inheritance. \n\nConsider the following code snippet where `ChildClass` inherits from `ParentClass1`, `ParentClass2`, `ParentClass3`, and so on.\n\n```python3\nclass ChildClass (ParentClass1, ParentClass2, ParentClass3, ...):\n  pass\n```\n\nThis behavior is illustrated in the following figure:\n\n**[CanvasAnimation 图表]** - 交互式图表内容\n\nNow, let's discuss a real-world example where a `Person` can either be a `Student` or a `Teacher`. Additionally, a `Person` can be a `TA`. A `TA` inherits the attributes and methods of both the `Student` and `Teacher` class because, essentially, a `TA` is a `Student` who is performing some of the duties that a `Teacher` performs. This hierarchy is shown in the following figure:\n\n**[CanvasAnimation 图表]** - 交互式图表内容\n\nConsider the following code:\n\n```python310\nclass Person:\n  def __init__(self, name):\n      self.name = name\n\n  def greet(self):\n    print(\"Hi, I am \" + self.name + \".\")\n\n\nclass Student (Person): # Student inherits from Person class\n  def __init__(self, name, rollNumber):\n    self.name = name # Attribute inherited from the Person class\n    self.rollNumber = rollNumber # Student's attribute\n    Person.__init__(self, name) # Person's constructor\n\n  def report(self): # Student's method\n    print(\"My roll number is \" + self.rollNumber + \".\")\n\nclass Teacher (Person): # Teacher inherits from Person class\n  def __init__(self, name, course):\n    self.name = name # Attribute inherited from the Person class\n    self.course = course # Teacher's attribute\n    Person.__init__(self, name) # Person's constructor   \n\n  def introduce(self): # Teacher's method\n    print(\"I teach \" + self.course + \".\")\n\nclass TA (Student, Teacher): # TA inherits from Student and Teacher class\n  def __init__(self, name, rollNumber, course, grade):\n    self.name = name # Attribute inherited from the Person class\n    self.rollNumber = rollNumber # Attribute inherited from the Student class\n    self.course = course # Attribute inherited from the Teacher class\n    self.grade = grade # TA's attribute\n    \n  def details(self): # TA's method\n    if self.grade==\"A*\" or self.grade==\"A\" or self.grade==\"A-\": # if person is elligible for TAship\n      Person.greet(self) # can access Person's greet method\n      Student.report(self) # can access Student's report method\n      Teacher.introduce(self) # can access Teacher's introduce method\n      print (\"I got an \" + self.grade + \" in \" + self.course + \".\")\n    else: # person is not elligible for TAship\n      print(self.name + \", you can not apply for TAship.\")\n    \nta = TA('Ali', '13K-1234', 'Data Structures' ,'A') # TA object\nta.details()\n\n#uncomment any of the following lines of code and see how they work\n# ta.greet()\n# ta.report()\n# ta.introduce()\n\nprint(\"\\n\")\n\nta2 = TA('Ahmed', '14K-5678', 'Algorithms' ,'B')\nta2.details()\n\n```\n\nNow that you get the basic idea, let's discuss a better way to access methods and attributes of the superclass in the next lesson.", "mdHtml": "<p>In Python, a class can inherit <strong>attributes</strong> and <strong>methods</strong> from <strong>more than one class</strong>. Critics of multiple inheritance say that it is confusing and complex, like the <a href=\"https://en.wikipedia.org/wiki/Multiple_inheritance\">diamond problem</a>, which is out of the scope of this lesson.</p>\n<p>The idea that multiple inheritance is somehow “bad” is based on the fact that some languages either do not support multiple inheritance (like Java) or do not have a very good implementation for it. Python, however, has a very sophisticated and well-structured approach to multiple inheritance.</p>\n<p>Consider the following code snippet where <code>ChildClass</code> inherits from <code>ParentClass1</code>, <code>ParentClass2</code>, <code>ParentClass3</code>, and so on.</p>\n\n<div class='image-component'>\n```python3\nclass ChildClass (ParentClass1, ParentClass2, ParentClass3, ...):\n  pass\n```\n</div>\n<p>This behavior is illustrated in the following figure:</p>\n\n<div class='image-component'>\n**[CanvasAnimation 图表]** - 交互式图表内容\n</div>\n<p>Now, let’s discuss a real-world example where a <code>Person</code> can either be a <code>Student</code> or a <code>Teacher</code>. Additionally, a <code>Person</code> can be a <code>TA</code>. A <code>TA</code> inherits the attributes and methods of both the <code>Student</code> and <code>Teacher</code> class because, essentially, a <code>TA</code> is a <code>Student</code> who is performing some of the duties that a <code>Teacher</code> performs. This hierarchy is shown in the following figure:</p>\n\n<div class='image-component'>\n**[CanvasAnimation 图表]** - 交互式图表内容\n</div>\n<p>Consider the following code:</p>\n\n<div class='image-component'>\n```python310\nclass Person:\n  def __init__(self, name):\n      self.name = name\n\n  def greet(self):\n    print(\"Hi, I am \" + self.name + \".\")\n\n\nclass Student (Person): # Student inherits from Person class\n  def __init__(self, name, rollNumber):\n    self.name = name # Attribute inherited from the Person class\n    self.rollNumber = rollNumber # Student's attribute\n    Person.__init__(self, name) # Person's constructor\n\n  def report(self): # Student's method\n    print(\"My roll number is \" + self.rollNumber + \".\")\n\nclass Teacher (Person): # Teacher inherits from Person class\n  def __init__(self, name, course):\n    self.name = name # Attribute inherited from the Person class\n    self.course = course # Teacher's attribute\n    Person.__init__(self, name) # Person's constructor   \n\n  def introduce(self): # Teacher's method\n    print(\"I teach \" + self.course + \".\")\n\nclass TA (Student, Teacher): # TA inherits from Student and Teacher class\n  def __init__(self, name, rollNumber, course, grade):\n    self.name = name # Attribute inherited from the Person class\n    self.rollNumber = rollNumber # Attribute inherited from the Student class\n    self.course = course # Attribute inherited from the Teacher class\n    self.grade = grade # TA's attribute\n    \n  def details(self): # TA's method\n    if self.grade==\"A*\" or self.grade==\"A\" or self.grade==\"A-\": # if person is elligible for TAship\n      Person.greet(self) # can access Person's greet method\n      Student.report(self) # can access Student's report method\n      Teacher.introduce(self) # can access Teacher's introduce method\n      print (\"I got an \" + self.grade + \" in \" + self.course + \".\")\n    else: # person is not elligible for TAship\n      print(self.name + \", you can not apply for TAship.\")\n    \nta = TA('Ali', '13K-1234', 'Data Structures' ,'A') # TA object\nta.details()\n\n#uncomment any of the following lines of code and see how they work\n# ta.greet()\n# ta.report()\n# ta.introduce()\n\nprint(\"\\n\")\n\nta2 = TA('Ahmed', '14K-5678', 'Algorithms' ,'B')\nta2.details()\n\n```\n</div>\n<p>Now that you get the basic idea, let’s discuss a better way to access methods and attributes of the superclass in the next lesson.</p>", "summary": {"titleUpdated": true, "title": "", "description": "", "tags": []}, "type": "Lesson"}, {"id": "6318330918993920", "title": "Super Method", "is_preview": true, "slug": "super-method", "text": "## Introduction\nAs we discussed before, you can access the constructor and methods of the parent class in the child class using \n```\nParentClassName.__init__(self, methodParameter)\n```\nand\n```\nParentClassName.methodName(self, methodParameter)\n```\nrespectively. \n\nThis method tends to get confusing when the hierarchy has multiple levels because you have to remember the exact names of the parent classes and where to use them. An easier way to do this is by using the `super()` method.\n\nThe built-in `super` function allows the child class (or subclass) to access inherited methods of the parent class (or superclass) that have been overwritten in the child class.\n\nAccording to the [official Python documentation](https://docs.python.org/2/library/functions.html#super):\n\n\n> Super is used to return a proxy object that delegates method calls to a parent or sibling class of type. This is useful for accessing inherited methods that have been overridden in a class.\n\n## Using the `super` Function\n\nThe `super` method can be used in multiple scenarios.\n\n### Single Inheritance \nWe can use `super` in single inheritance to refer to the parent (or super) class or multiple classes without explicitly naming them. This is just a shortcut and helps make your code maintainable and easy to understand.\n\n\n### Multiple Inheritance\nAs discussed [earlier](https://www.educative.io/courses/full-speed-python/multiple-inheritance), it is possible in Python for a class to inherit from multiple parent classes. You can use the `super` method for multiple or collaborative inheritance. This can be done only in Python because some languages do not support inheritance from multiple classes. \n\n> **Note:** `super()` is not limited to use inside methods. You can specify the appropriate references by specifying the arguments of the `super()` method.\n\nIn Python 3 and above, the syntax for `super` is:\n\n```\nsuper().methodName(args)\n```\n\nNow consider the following code snippet where the `Student` class inherits from (or \"is a\") `Person`.\n\n```python310\nclass Person(object): # Super class\n  def __init__(self, name):\n    self.name = name\n  def greet(self):\n    print (\"Hi, I'm \" + self.name + \".\") # Super class does something\n\nclass Student(Person): # Subclass inheriting from the super class\n  def __init__(self, name, degree):\n    self.name = name\n    self.degree = degree\n    Person.__init__(self, name) # calls constructor of super class\n  def greet(self):\n    Person.greet(self) # calls method of super class\n    print (\"I am a \" + self.degree + \" student.\")\n  \nstudent = Student(\"Ali\", \"PhD\") # Create an object of the subclass\nstudent.greet()\n```\n\nNotice that when you create a `Student` object, it can access the `Parent` class's `greet()` method and write its own too. Now we will modify the above code to include the `super` method. \n\n```python310\nclass Person(object): # Super class\n  def __init__(self, name):\n    self.name = name\n  def greet(self):\n    print (\"Hi, I'm \" + self.name + \".\") # Super class does something\n\nclass Student(Person): # Subclass inheriting from the super class\n  def __init__(self, name, degree):\n    self.name = name\n    self.degree = degree\n    super().__init__(name) # calls constructor of super class\n  def greet(self):\n    super().greet() # calls method of super class\n    print (\"I am a \" + self.degree + \" student.\")\n  \nstudent = Student(\"Ali\", \"PhD\") # Create an object of the subclass\nstudent.greet()\n```\n\nNotice in the above code playground that when we called the parent class's methods `super().__init__(name)` and `super().greet()`, we didn't explicitly add `self` as a parameter to these methods. \n\nLet's solve some exercises on this concept.", "mdHtml": "<h2 id=\"introduction\">Introduction</h2>\n<p>As we discussed before, you can access the constructor and methods of the parent class in the child class using</p>\n<pre><code>ParentClassName.__init__(self, methodParameter)\n</code></pre>\n<p>and</p>\n<pre><code>ParentClassName.methodName(self, methodParameter)\n</code></pre>\n<p>respectively.</p>\n<p>This method tends to get confusing when the hierarchy has multiple levels because you have to remember the exact names of the parent classes and where to use them. An easier way to do this is by using the <code>super()</code> method.</p>\n<p>The built-in <code>super</code> function allows the child class (or subclass) to access inherited methods of the parent class (or superclass) that have been overwritten in the child class.</p>\n<p>According to the <a href=\"https://docs.python.org/2/library/functions.html#super\">official Python documentation</a>:</p>\n<blockquote>\n<p>Super is used to return a proxy object that delegates method calls to a parent or sibling class of type. This is useful for accessing inherited methods that have been overridden in a class.</p>\n</blockquote>\n<h2 id=\"using-the-super-function\">Using the <code>super</code> Function</h2>\n<p>The <code>super</code> method can be used in multiple scenarios.</p>\n<h3 id=\"single-inheritance\">Single Inheritance</h3>\n<p>We can use <code>super</code> in single inheritance to refer to the parent (or super) class or multiple classes without explicitly naming them. This is just a shortcut and helps make your code maintainable and easy to understand.</p>\n<h3 id=\"multiple-inheritance\">Multiple Inheritance</h3>\n<p>As discussed <a href=\"https://www.educative.io/courses/full-speed-python/multiple-inheritance\">earlier</a>, it is possible in Python for a class to inherit from multiple parent classes. You can use the <code>super</code> method for multiple or collaborative inheritance. This can be done only in Python because some languages do not support inheritance from multiple classes.</p>\n<blockquote>\n<p><strong>Note:</strong> <code>super()</code> is not limited to use inside methods. You can specify the appropriate references by specifying the arguments of the <code>super()</code> method.</p>\n</blockquote>\n<p>In Python 3 and above, the syntax for <code>super</code> is:</p>\n<pre><code>super().methodName(args)\n</code></pre>\n<p>Now consider the following code snippet where the <code>Student</code> class inherits from (or “is a”) <code>Person</code>.</p>\n\n<div class='image-component'>\n```python310\nclass Person(object): # Super class\n  def __init__(self, name):\n    self.name = name\n  def greet(self):\n    print (\"Hi, I'm \" + self.name + \".\") # Super class does something\n\nclass Student(Person): # Subclass inheriting from the super class\n  def __init__(self, name, degree):\n    self.name = name\n    self.degree = degree\n    Person.__init__(self, name) # calls constructor of super class\n  def greet(self):\n    Person.greet(self) # calls method of super class\n    print (\"I am a \" + self.degree + \" student.\")\n  \nstudent = Student(\"Ali\", \"PhD\") # Create an object of the subclass\nstudent.greet()\n```\n</div>\n<p>Notice that when you create a <code>Student</code> object, it can access the <code>Parent</code> class’s <code>greet()</code> method and write its own too. Now we will modify the above code to include the <code>super</code> method.</p>\n\n<div class='image-component'>\n```python310\nclass Person(object): # Super class\n  def __init__(self, name):\n    self.name = name\n  def greet(self):\n    print (\"Hi, I'm \" + self.name + \".\") # Super class does something\n\nclass Student(Person): # Subclass inheriting from the super class\n  def __init__(self, name, degree):\n    self.name = name\n    self.degree = degree\n    super().__init__(name) # calls constructor of super class\n  def greet(self):\n    super().greet() # calls method of super class\n    print (\"I am a \" + self.degree + \" student.\")\n  \nstudent = Student(\"Ali\", \"PhD\") # Create an object of the subclass\nstudent.greet()\n```\n</div>\n<p>Notice in the above code playground that when we called the parent class’s methods <code>super().__init__(name)</code> and <code>super().greet()</code>, we didn’t explicitly add <code>self</code> as a parameter to these methods.</p>\n<p>Let’s solve some exercises on this concept.</p>", "summary": {"titleUpdated": true, "title": "", "description": "This lesson covers multiple use-cases of the super method in Python and how it makes inheritance easier.", "tags": []}, "type": "Lesson"}, {"id": "5730956560302080", "title": "Challenge 5: Inheritance", "is_preview": false, "slug": "challenge-5-inheritance", "text": "## Problem Statement\nThe code for the `Rectangle` class is implemented below:\n\n1. Create a `Square` class as a ​subclass of `Rectangle`.\n\n2. Implement the `Square` constructor. The constructor should have only the `x1`, `y1` coordinates and the `length` of a side. Notice which arguments you’ll have to use when you invoke the `Rectangle` constructor while using `super`.\n\nThe following test cases will calculate the area of the square to check that the `Square` class correctly inherits attributes and methods from `Rectangle`. \n\n\n### Input\nThe coordinates and the length of the square\n\n### Output\nArea of the square\n\n### Sample Input\nSquare([2, 3, 5])\n\nx1 = 2, y1 = 3, length = 5\n\n### Sample Output\nArea = 25\n\n \n\n## Coding Exercise\nWrite your code below. It is recommended​ that you try solving the exercise yourself before viewing the solution.\n\n```python310\nclass Rectangle:\n  def __init__(self, x1, y1, x2, y2): # class constructor\n    self.x1 = x1 # class variable\n    self.y1 = y1 # class variable\n    self.x2 = x2 # class variable\n    self.y2 = y2 # class variable\n        \n  def width(self):\n    return self.x2 - self.x1\n      \n  def height(self):\n    return self.y2 - self.y1\n  \n  def area(self):\n    return self.width() * self.height()\n  \n#class Square():\n  #write your code here\n```\n\nThe next lesson discusses the solution of this exercise.", "mdHtml": "<h2 id=\"problem-statement\">Problem Statement</h2>\n<p>The code for the <code>Rectangle</code> class is implemented below:</p>\n<ol>\n<li>\n<p>Create a <code>Square</code> class as a ​subclass of <code>Rectangle</code>.</p>\n</li>\n<li>\n<p>Implement the <code>Square</code> constructor. The constructor should have only the <code>x1</code>, <code>y1</code> coordinates and the <code>length</code> of a side. Notice which arguments you’ll have to use when you invoke the <code>Rectangle</code> constructor while using <code>super</code>.</p>\n</li>\n</ol>\n<p>The following test cases will calculate the area of the square to check that the <code>Square</code> class correctly inherits attributes and methods from <code>Rectangle</code>.</p>\n<h3 id=\"input\">Input</h3>\n<p>The coordinates and the length of the square</p>\n<h3 id=\"output\">Output</h3>\n<p>Area of the square</p>\n<h3 id=\"sample-input\">Sample Input</h3>\n<p>Square([2, 3, 5])</p>\n<p>x1 = 2, y1 = 3, length = 5</p>\n<h3 id=\"sample-output\">Sample Output</h3>\n<p>Area = 25</p>\n\n<h2 id=\"coding-exercise\">Coding Exercise</h2>\n<p>Write your code below. It is recommended​ that you try solving the exercise yourself before viewing the solution.</p>\n\n<div class='image-component'>\n```python310\nclass Rectangle:\n  def __init__(self, x1, y1, x2, y2): # class constructor\n    self.x1 = x1 # class variable\n    self.y1 = y1 # class variable\n    self.x2 = x2 # class variable\n    self.y2 = y2 # class variable\n        \n  def width(self):\n    return self.x2 - self.x1\n      \n  def height(self):\n    return self.y2 - self.y1\n  \n  def area(self):\n    return self.width() * self.height()\n  \n#class Square():\n  #write your code here\n```\n</div>\n<p>The next lesson discusses the solution of this exercise.</p>", "summary": {"titleUpdated": true, "title": "Exercises on Inheritance", "description": "Solve an exercise on inheritance to brush up on the previous inheritance concepts.", "tags": []}, "type": "Lesson"}, {"id": "5190559480152064", "title": "Solution Review: Inheritance", "is_preview": false, "slug": "solution-review-inheritance", "text": "## Solution:\nNotice that in line 18 `Square` inherits from `Rectangle` and in line 22, it accesses the constructor of the `Rectangle` class using `super()`. If you remove `super().__init__(x1, y1, x2, y2)` from line 22, then code will give an error.\n\nIn the end, we created two instances of the `Square` class and calculated their area to test if the inheritance code is correct.\n\n```python310\nclass Rectangle:\n  def __init__(self, x1, y1, x2, y2): # class constructor\n    self.x1 = x1 # class variable\n    self.y1 = y1 # class variable\n    self.x2 = x2 # class variable\n    self.y2 = y2 # class variable\n        \n  def width(self):\n    return self.x2 - self.x1\n      \n  def height(self):\n    return self.y2 - self.y1\n  \n  def area(self):\n    return self.width() * self.height()\n  \n#write your code here\nclass Square(Rectangle):\n  def __init__(self, x1, y1, length):\n    x2 = x1 + length\n    y2 = y1 + length\n    super().__init__(x1, y1, x2, y2)\n    \n# test your code here\nsquare = Square (2, 7, 7)\nprint(\"Length: \" + str(square.width()) + \", Area: \" + str(square.area()))\nsquare2 = Square (1, 3, 5)\nprint(\"Length: \" + str(square2.width()) + \", Area: \" + str(square2.area()))\n```\n\nIn the next chapter, we will study a new concept - iterators in Python.", "mdHtml": "<h2 id=\"solution\">Solution:</h2>\n<p>Notice that in line 18 <code>Square</code> inherits from <code>Rectangle</code> and in line 22, it accesses the constructor of the <code>Rectangle</code> class using <code>super()</code>. If you remove <code>super().__init__(x1, y1, x2, y2)</code> from line 22, then code will give an error.</p>\n<p>In the end, we created two instances of the <code>Square</code> class and calculated their area to test if the inheritance code is correct.</p>\n\n<div class='image-component'>\n```python310\nclass Rectangle:\n  def __init__(self, x1, y1, x2, y2): # class constructor\n    self.x1 = x1 # class variable\n    self.y1 = y1 # class variable\n    self.x2 = x2 # class variable\n    self.y2 = y2 # class variable\n        \n  def width(self):\n    return self.x2 - self.x1\n      \n  def height(self):\n    return self.y2 - self.y1\n  \n  def area(self):\n    return self.width() * self.height()\n  \n#write your code here\nclass Square(Rectangle):\n  def __init__(self, x1, y1, length):\n    x2 = x1 + length\n    y2 = y1 + length\n    super().__init__(x1, y1, x2, y2)\n    \n# test your code here\nsquare = Square (2, 7, 7)\nprint(\"Length: \" + str(square.width()) + \", Area: \" + str(square.area()))\nsquare2 = Square (1, 3, 5)\nprint(\"Length: \" + str(square2.width()) + \", Area: \" + str(square2.area()))\n```\n</div>\n<p>In the next chapter, we will study a new concept - iterators in Python.</p>", "summary": {"titleUpdated": true, "title": "Exercises on Inheritance", "description": "This lesson discusses the solution for the inheritance problem in the previous lesson.", "tags": []}, "type": "Lesson"}, {"id": "5649027374776320", "title": "Quick Quiz on Classes", "is_preview": false, "slug": "quick-quiz-on-classes", "text": "Now that you have learned about Classes, Let’s learn about an iteration protocol, i.e., “Iterators” in the next chapter.", "mdHtml": "<p>Now that you have learned about Classes, Let’s learn about an iteration protocol, i.e., “Iterators” in the next chapter.</p>", "summary": {"titleUpdated": false, "title": "", "description": "", "tags": []}, "type": "Quiz", "questions": [{"questionText": "In Python `__init__ ` is", "questionOptions": [{"text": "the method associated with extending a list with another list", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>the method associated with extending a list with another list</p>\n"}, {"text": "the name of the constructor method for a class", "correct": true, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>the name of the constructor method for a class</p>\n"}], "questionTextHtml": "<p>In Python <code>__init__ </code> is</p>\n"}, {"questionText": "With the following code, which of the options is true?\n```python\nclass Person:\n  def __init__(self, fname, lname):\n    self.firstname = fname\n    self.lastname = lname\n\n  def printname(self):\n    print(self.firstname, self.lastname)\n\nclass Student(Person):\n  def __init__(self, fname, lname, year):\n    super().__init__(fname, lname)\n    self.batch= year\n\nx = Student(\"Alex\", \"Vice\", 2019)\nx = Student(\"Anna\", \"Bate\", 2018)\nprint(x.batch)\n ```", "questionOptions": [{"text": "2019", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>2019</p>\n"}, {"text": "2018", "correct": true, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>2018</p>\n"}], "questionTextHtml": "<p>With the following code, which of the options is true?</p>\n<pre><code class=\"language-python\">class Person:\n  def __init__(self, fname, lname):\n    self.firstname = fname\n    self.lastname = lname\n\n  def printname(self):\n    print(self.firstname, self.lastname)\n\nclass Student(Person):\n  def __init__(self, fname, lname, year):\n    super().__init__(fname, lname)\n    self.batch= year\n\nx = Student(&quot;Alex&quot;, &quot;Vice&quot;, 2019)\nx = Student(&quot;<PERSON>&quot;, &quot;<PERSON><PERSON>&quot;, 2018)\nprint(x.batch)\n</code></pre>\n"}]}]}, {"id": "jrhdxc3v7", "title": "Iterators", "summary": "Master the steps to creating and using custom iterators in Python with practical challenges.", "pages": [{"id": "5715121150099456", "title": "Iterators", "is_preview": false, "slug": "iterators", "text": "## Iterators\n\nAs we saw previously, in Python we use the \"for\" loop and \"while\" to iterate over the contents of objects:\n\n```python310\nfor value in [0, 1, 2, 3, 4, 5]:\n     print(value)\n```\n\n\nObjects that can be used with a for loop are called iterators. An iterator is, therefore, an object that follows the iteration protocol.\n\nThe built-in `iter` method can be used to build iterator objects, while the `next` method can be used to gradually iterate over their content:\n\n```python310\nmy_iter = iter([1, 2, 3])\nprint (next(my_iter))\nprint (next(my_iter))\nprint (next(my_iter))\n```\n\nIf there are no more elements, the iterator raises a \"StopIteration\" exception.\n\n```python310\nmy_iter = iter([1, 2, 3])\nnext(my_iter)\nnext(my_iter)\nnext(my_iter)\n```\n\n### Iterator Classes\n\nIterators can be implemented as classes; you just need to implement the `__next__` and `__iter__` methods. Here’s an example of a class that mimics the `range` function, returning all values from `a` to `b`:\n\n```python310\nclass MyRange:\n\n    def __init__(self, a, b):\n        self.a = a\n        self.b = b\n\n    def __iter__(self): # returns the iterator object itself\n        return self\n\n    def __next__(self): # returns the next item in the sequence\n        if self.a < self.b:\n            value = self.a\n            self.a += 1\n            return value\n        else:\n            raise StopIteration\n```\n\nBasically, on every call to `next`, it moves forward the internal variable `a` and returns its value. When it reaches `b`, it raises the StopIteration exception. You can observe this behavior by uncommenting the last line.\n\n```python310\nclass MyRange:\n  def __init__(self, a, b):\n    self.a = a\n    self.b = b\n\n  def __iter__(self):# returns the iterator object itself\n    return self\n\n  def next(self):\n    if self.a < self.b:# returns the next item in the sequence\n      value = self.a\n      self.a += 1\n      return value\n    else:\n      raise StopIteration\n         \nmyrange = MyRange(1, 4)\nprint (myrange.next())\nprint (myrange.next())\nprint (myrange.next())\n##print (myrange.next())\n```\n\n\nBut most importantly, you can use the iterator class in a \"for\" loop:\n\n```python310\nclass MyRange:\n    def __init__(self, a, b):\n        self.a = a\n        self.b = b\n\n    def __iter__(self): # returns the iterator object itself\n        return self\n\n    def __next__(self): # returns the next item in the sequence\n        if self.a < self.b:\n            value = self.a\n            self.a += 1\n            return value\n        else:\n            raise StopIteration\nfor value in MyRange(1, 4):\n     print(value)\n```\n\nNow that you know the basics, let's solve some challenges on iterators.", "mdHtml": "<h1 id=\"iterators\">Iterators</h1>\n<p>As we saw previously, in Python we use the “for” loop and “while” to iterate over the contents of objects:</p>\n\n<div class='image-component'>\n```python310\nfor value in [0, 1, 2, 3, 4, 5]:\n     print(value)\n```\n</div>\n<p>Objects that can be used with a for loop are called iterators. An iterator is, therefore, an object that follows the iteration protocol.</p>\n<p>The built-in <code>iter</code> method can be used to build iterator objects, while the <code>next</code> method can be used to gradually iterate over their content:</p>\n\n<div class='image-component'>\n```python310\nmy_iter = iter([1, 2, 3])\nprint (next(my_iter))\nprint (next(my_iter))\nprint (next(my_iter))\n```\n</div>\n<p>If there are no more elements, the iterator raises a “StopIteration” exception.</p>\n\n<div class='image-component'>\n```python310\nmy_iter = iter([1, 2, 3])\nnext(my_iter)\nnext(my_iter)\nnext(my_iter)\n```\n</div>\n<h2 id=\"iterator-classes\">Iterator Classes</h2>\n<p>Iterators can be implemented as classes; you just need to implement the <code>__next__</code> and <code>__iter__</code> methods. Here’s an example of a class that mimics the <code>range</code> function, returning all values from <code>a</code> to <code>b</code>:</p>\n\n<div class='image-component'>\n```python310\nclass MyRange:\n\n    def __init__(self, a, b):\n        self.a = a\n        self.b = b\n\n    def __iter__(self): # returns the iterator object itself\n        return self\n\n    def __next__(self): # returns the next item in the sequence\n        if self.a < self.b:\n            value = self.a\n            self.a += 1\n            return value\n        else:\n            raise StopIteration\n```\n</div>\n<p>Basically, on every call to <code>next</code>, it moves forward the internal variable <code>a</code> and returns its value. When it reaches <code>b</code>, it raises the StopIteration exception. You can observe this behavior by uncommenting the last line.</p>\n\n<div class='image-component'>\n```python310\nclass MyRange:\n  def __init__(self, a, b):\n    self.a = a\n    self.b = b\n\n  def __iter__(self):# returns the iterator object itself\n    return self\n\n  def next(self):\n    if self.a < self.b:# returns the next item in the sequence\n      value = self.a\n      self.a += 1\n      return value\n    else:\n      raise StopIteration\n         \nmyrange = MyRange(1, 4)\nprint (myrange.next())\nprint (myrange.next())\nprint (myrange.next())\n##print (myrange.next())\n```\n</div>\n<p>But most importantly, you can use the iterator class in a “for” loop:</p>\n\n<div class='image-component'>\n```python310\nclass MyRange:\n    def __init__(self, a, b):\n        self.a = a\n        self.b = b\n\n    def __iter__(self): # returns the iterator object itself\n        return self\n\n    def __next__(self): # returns the next item in the sequence\n        if self.a < self.b:\n            value = self.a\n            self.a += 1\n            return value\n        else:\n            raise StopIteration\nfor value in MyRange(1, 4):\n     print(value)\n```\n</div>\n<p>Now that you know the basics, let’s solve some challenges on iterators.</p>", "summary": {"titleUpdated": true, "title": "Iterators", "description": "This lesson will discuss built-in iterators in Python, and teach you to build your own custom iterator class.", "tags": []}, "type": "Lesson"}, {"id": "5651905137082368", "title": "Challenge 1: Return Even Numbers From 1 to n", "is_preview": false, "slug": "challenge-1-return-even-numbers-from-1-to-n", "text": "## Problem Statement\n\nPrint a list of even numbers from 1 to \\(n\\). You just need to edit the `next` method to return all the positive even numbers from 1 to \\(n\\). The following test cases will test your code using\n```python\nmyrange = MyRange(n) # n is an integer\nprint(myrange.next())\n```\n### Input\nA number `n`\n\n### Output\nThe range of positive even number uptil n\n\n### Sample Input\n8\n\n### Sample Output\n[2, 4, 6, 8]\n\n \n## Coding Exercise\nWrite your code below. It is recommended​ that you try solving the exercise yourself before viewing the solution.\n\n```python310\nclass MyRange:\n  def __init__(self, n):\n    self.n = n\n\n  def __iter__(self):\n    return self\n\n  def next(self):\n    pass\n    \nmyrange = MyRange(8)\nprint (myrange.next())\n```\n\nIn the next lesson, we will discuss the solution to this challenge.", "mdHtml": "<h2>Problem Statement</h2>\n<p>Print a list of even numbers from 1 to (n). You just need to edit the <code>next</code> method to return all the positive even numbers from 1 to (n). The following test cases will test your code using</p>\n<pre><code class=\"language-python\">myrange = MyRange(n) # n is an integer\nprint(myrange.next())\n</code></pre>\n<h3>Input</h3>\n<p>A number <code>n</code></p>\n<h3>Output</h3>\n<p>The range of positive even number uptil n</p>\n<h3>Sample Input</h3>\n<p>8</p>\n<h3>Sample Output</h3>\n<p>[2, 4, 6, 8]</p>\n<h2>Coding Exercise</h2>\n<p>Write your code below. It is recommended​ that you try solving the exercise yourself before viewing the solution.</p>\n\n<div class='image-component'>\n```python310\nclass MyRange:\n  def __init__(self, n):\n    self.n = n\n\n  def __iter__(self):\n    return self\n\n  def next(self):\n    pass\n    \nmyrange = MyRange(8)\nprint (myrange.next())\n```\n</div>\n<p>In the next lesson, we will discuss the solution to this challenge.</p>", "summary": {"titleUpdated": true, "title": "Challenge 1: Return Even Numbers From 1 to n", "description": "Make your own iterator class to return a list of even numbers.", "tags": []}, "type": "Lesson"}, {"id": "5166707379273728", "title": "Solution Review: Return Even Numbers From 1 to n", "is_preview": false, "slug": "solution-review-return-even-numbers-from-1-to-n", "text": "## Solution\nNotice (in the code below) that the `next` method in lines 8-13 makes a list, then appends the even numbers in that range to the list using a `for` loop.\n\n```python310\nclass MyRange:\n  def __init__(self, n):\n    self.n = n\n\n  def __iter__(self):\n    return self\n\n  def next(self):\n    evenArray = [] # next method returns this list\n    for i in range(1, self.n+1):\n      if i % 2 is 0: # checks if number is even\n        value = i\n        evenArray.append(i) # adds the even number to the list\n    return evenArray\n    \nmyrange = MyRange(8)\nprint (myrange.next())\n```\n\nThe figure below illustrates how this is done.\nNow, let's try to output numbers from n down to 0 in the next lesson.", "mdHtml": "<h2>Solution</h2>\n<p>Notice (in the code below) that the <code>next</code> method in lines 8-13 makes a list, then appends the even numbers in that range to the list using a <code>for</code> loop.</p>\n\n<div class='image-component'>\n```python310\nclass MyRange:\n  def __init__(self, n):\n    self.n = n\n\n  def __iter__(self):\n    return self\n\n  def next(self):\n    evenArray = [] # next method returns this list\n    for i in range(1, self.n+1):\n      if i % 2 is 0: # checks if number is even\n        value = i\n        evenArray.append(i) # adds the even number to the list\n    return evenArray\n    \nmyrange = MyRange(8)\nprint (myrange.next())\n```\n</div>\n<p>The figure below illustrates how this is done.</p>\n\n<p>Now, let’s try to output numbers from n down to 0 in the next lesson.</p>", "summary": {"titleUpdated": true, "title": "Challenge 1: Return Even Numbers From 1 to n", "description": "Make your own iterator class to return a list of even numbers.", "tags": []}, "type": "Lesson"}, {"id": "5718356871086080", "title": "Challenge 2: Return Numbers From n Down to 0", "is_preview": false, "slug": "challenge-2-return-numbers-from-n-down-to-0", "text": "## Problem Statement\n\nEdit the following class, such that it returns all numbers from `n` down to 0. \n\n### Input\nA number `n`\n\n### Output\nThe range of numbers from n down to 0\n\n### Sample Input\n8\n\n### Sample Output\n[8, 7, 6, 5, 4, 3, 2, 1, 0]\n\n \n## Coding Exercise\nWrite your code below. It is recommended​ that you try solving the exercise yourself before viewing the solution.\n\n\n\n```python310\nclass MyRange:\n  def __init__(self, n):\n    self.n = n\n\n  def __iter__(self):\n    return self\n\n  def next(self):\n    #write your code here\n    pass\n```\n\nThe next lesson discusses the solution in detail.", "mdHtml": "<h2 id=\"problem-statement\">Problem Statement</h2>\n<p>Edit the following class, such that it returns all numbers from <code>n</code> down to 0.</p>\n<h3 id=\"input\">Input</h3>\n<p>A number <code>n</code></p>\n<h3 id=\"output\">Output</h3>\n<p>The range of numbers from n down to 0</p>\n<h3 id=\"sample-input\">Sample Input</h3>\n<p>8</p>\n<h3 id=\"sample-output\">Sample Output</h3>\n<p>[8, 7, 6, 5, 4, 3, 2, 1, 0]</p>\n<h2 id=\"coding-exercise\">Coding Exercise</h2>\n<p>Write your code below. It is recommended​ that you try solving the exercise yourself before viewing the solution.</p>\n\n<div class='image-component'>\n```python310\nclass MyRange:\n  def __init__(self, n):\n    self.n = n\n\n  def __iter__(self):\n    return self\n\n  def next(self):\n    #write your code here\n    pass\n```\n</div>\n<p>The next lesson discusses the solution in detail.</p>", "summary": {"titleUpdated": false, "title": "Challenge 2: Return Numbers From n Down to 0", "description": "Solve a challenge to iterate through a list in reverse order.", "tags": []}, "type": "Lesson"}, {"id": "5647985845207040", "title": "Solution Review: Return Numbers From n to 0", "is_preview": false, "slug": "solution-review-return-numbers-from-n-to-0", "text": "## Solution: Use Iterator\nIn this problem, you only had to edit the `next` method to include a reverse for loop; `for `i` in range(self.n, -1, -1)` where `i` is decremented once in every iteration. In line 11 of the code below, you append the value of `i` to the list in every iteration and return the list at the end of the method. In lines 14 and 15, you can test your code by creating an instance of the MyRange class and calling the next method.\n\n```python310\nclass MyRange:\n  def __init__(self, n):\n    self.n = n\n\n  def __iter__(self):\n    return self\n\n  def next(self):\n    myArray = []\n    for i in range(self.n, -1, -1): # from n to 0\n      myArray.append(i) # adds the even number to the list\n    return myArray\n  \nmyRange = MyRange(8)\nprint(myRange.next())\n```\n\nThe next lesson includes a slightly trickier exercise to brush up your concepts on Python iterators.", "mdHtml": "<h2 id=\"solution-use-iterator\">Solution: Use Iterator</h2>\n<p>In this problem, you only had to edit the <code>next</code> method to include a reverse for loop; <code>for </code>i<code> in range(self.n, -1, -1)</code> where <code>i</code> is decremented once in every iteration. In line 11 of the code below, you append the value of <code>i</code> to the list in every iteration and return the list at the end of the method. In lines 14 and 15, you can test your code by creating an instance of the MyRange class and calling the next method.</p>\n\n<div class='image-component'>\n```python310\nclass MyRange:\n  def __init__(self, n):\n    self.n = n\n\n  def __iter__(self):\n    return self\n\n  def next(self):\n    myArray = []\n    for i in range(self.n, -1, -1): # from n to 0\n      myArray.append(i) # adds the even number to the list\n    return myArray\n  \nmyRange = MyRange(8)\nprint(myRange.next())\n```\n</div>\n<p>The next lesson includes a slightly trickier exercise to brush up your concepts on Python iterators.</p>", "summary": {"titleUpdated": true, "title": "Challenge 2: Return Numbers From n Down to 0", "description": "This lesson covers the Python code to return a list of numbers from n down to 0 using iterators.", "tags": []}, "type": "Lesson"}, {"id": "5691006519345152", "title": "Challenge 3: Return Sequence of <PERSON><PERSON><PERSON><PERSON> Numbers", "is_preview": false, "slug": "challenge-3-return-sequence-of-fibonacci-numbers", "text": "## Problem Statement\nEdit the following iterator class to return the Fibonacci sequence from the first element up to the `n`th element. \n\n>The **Fibonacci Sequence** is the series of numbers in which the next term is found by adding the two previous terms:\n>```\n> 0, 1, 1, 2, 3, 5, 8, 13, 21, 34, ...\n>```\n> Here ,\n> the number 0 is the first term, 1 is the second term, 1 is the third term and so on...\n\n\n### Input\nA number `n`\n\n### Output\nThe range of fibonacci numbers from 0 to n\n\n### Sample Input\n8\n\n### Sample Output\n[0, 1, 1, 2, 3, 5, 8, 13]\n\n \n## Coding Exercise\nWrite your code below. It is recommended​ that you try solving the exercise yourself before viewing the solution.\n\n\n\n```python310\nclass MyRange:\n  def __init__(self, n):\n    self.n = n\n\n  def __iter__(self):\n    return self\n\n  def next(self):\n    #write your code here\n    pass\n```\n\nThe next lesson discusses the solution to this challenge in detail.", "mdHtml": "<h2 id=\"problem-statement\">Problem Statement</h2>\n<p>Edit the following iterator class to return the Fibonacci sequence from the first element up to the <code>n</code>th element.</p>\n<blockquote>\n<p>The <strong>Fibonacci Sequence</strong> is the series of numbers in which the next term is found by adding the two previous terms:</p>\n<pre><code>0, 1, 1, 2, 3, 5, 8, 13, 21, 34, ...\n</code></pre>\n<p>Here ,\nthe number 0 is the first term, 1 is the second term, 1 is the third term and so on…</p>\n</blockquote>\n<h3 id=\"input\">Input</h3>\n<p>A number <code>n</code></p>\n<h3 id=\"output\">Output</h3>\n<p>The range of fibonacci numbers from 0 to n</p>\n<h3 id=\"sample-input\">Sample Input</h3>\n<p>8</p>\n<h3 id=\"sample-output\">Sample Output</h3>\n<p>[0, 1, 1, 2, 3, 5, 8, 13]</p>\n<h2 id=\"coding-exercise\">Coding Exercise</h2>\n<p>Write your code below. It is recommended​ that you try solving the exercise yourself before viewing the solution.</p>\n\n<div class='image-component'>\n```python310\nclass MyRange:\n  def __init__(self, n):\n    self.n = n\n\n  def __iter__(self):\n    return self\n\n  def next(self):\n    #write your code here\n    pass\n```\n</div>\n<p>The next lesson discusses the solution to this challenge in detail.</p>", "summary": {"titleUpdated": false, "title": "Challenge 3: Return Sequence of <PERSON><PERSON><PERSON><PERSON> Numbers", "description": "Use iterators to return a list containing the <PERSON><PERSON><PERSON><PERSON> sequence.", "tags": []}, "type": "Lesson"}, {"id": "5176913832181760", "title": "Solution Review: Return Sequence of <PERSON><PERSON><PERSON><PERSON> Numbers", "is_preview": false, "slug": "solution-review-return-sequence-of-fibonacci-numbers", "text": "## Solution:\nThe first and second elements of the Fi<PERSON><PERSON>ci sequence are 0 and 1 respectively. Each successive element is obtained by adding the two elements before it. For instance, the 3rd element is obtained by adding the 1st and 2nd elements, the 4th is obtained by adding the 2nd and 3rd elements, and so on. Thus we can conclude the following for `myArray` in a Fibonacci sequence:\n$$ myArray[i] = myArray[i-2] + myArray[i-1]$$\nWe used this logic in lines 11 to 14 of the following code to calculate the Fibonacci sequence up to a certain range `n`.\n\n```python310\nclass MyRange:\n  def __init__(self, n):\n    self.n = n\n\n  def __iter__(self):\n    return self\n\n  def next(self):\n    myArray = []\n    for i in range(self.n): # from n to 0\n      if i == 0 or i == 1:\n        myArray.append(i) # adds the even number to the list\n      else:\n        myArray.append(myArray[i-2] + myArray[i-1])\n    return myArray\n  \nmyrange = MyRange(8)\nprint(myrange.next())\n```\n\nThis code is illustrated in the figure below:\nNow that you've learnt and practised iterators, let's discuss generators in the next chapter.", "mdHtml": "<h2>Solution:</h2>\n<p>The first and second elements of the Fibonacci sequence are 0 and 1 respectively. Each successive element is obtained by adding the two elements before it. For instance, the 3rd element is obtained by adding the 1st and 2nd elements, the 4th is obtained by adding the 2nd and 3rd elements, and so on. Thus we can conclude the following for <code>myArray</code> in a Fibonacci sequence:</p>\n<p class=\"katex-block \"><span class=\"katex-display\"><span class=\"katex\"><span class=\"katex-mathml\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\" display=\"block\"><semantics><mrow><mi>m</mi><mi>y</mi><mi>A</mi><mi>r</mi><mi>r</mi><mi>a</mi><mi>y</mi><mo stretchy=\"false\">[</mo><mi>i</mi><mo stretchy=\"false\">]</mo><mo>=</mo><mi>m</mi><mi>y</mi><mi>A</mi><mi>r</mi><mi>r</mi><mi>a</mi><mi>y</mi><mo stretchy=\"false\">[</mo><mi>i</mi><mo>−</mo><mn>2</mn><mo stretchy=\"false\">]</mo><mo>+</mo><mi>m</mi><mi>y</mi><mi>A</mi><mi>r</mi><mi>r</mi><mi>a</mi><mi>y</mi><mo stretchy=\"false\">[</mo><mi>i</mi><mo>−</mo><mn>1</mn><mo stretchy=\"false\">]</mo></mrow><annotation encoding=\"application/x-tex\">myArray[i] = myArray[i-2] + myArray[i-1]\n</annotation></semantics></math></span><span class=\"katex-html\" aria-hidden=\"true\"><span class=\"base\"><span class=\"strut\" style=\"height:1em;vertical-align:-0.25em;\"></span><span class=\"mord mathnormal\">m</span><span class=\"mord mathnormal\" style=\"margin-right:0.03588em;\">y</span><span class=\"mord mathnormal\">A</span><span class=\"mord mathnormal\" style=\"margin-right:0.02778em;\">rr</span><span class=\"mord mathnormal\">a</span><span class=\"mord mathnormal\" style=\"margin-right:0.03588em;\">y</span><span class=\"mopen\">[</span><span class=\"mord mathnormal\">i</span><span class=\"mclose\">]</span><span class=\"mspace\" style=\"margin-right:0.2778em;\"></span><span class=\"mrel\">=</span><span class=\"mspace\" style=\"margin-right:0.2778em;\"></span></span><span class=\"base\"><span class=\"strut\" style=\"height:1em;vertical-align:-0.25em;\"></span><span class=\"mord mathnormal\">m</span><span class=\"mord mathnormal\" style=\"margin-right:0.03588em;\">y</span><span class=\"mord mathnormal\">A</span><span class=\"mord mathnormal\" style=\"margin-right:0.02778em;\">rr</span><span class=\"mord mathnormal\">a</span><span class=\"mord mathnormal\" style=\"margin-right:0.03588em;\">y</span><span class=\"mopen\">[</span><span class=\"mord mathnormal\">i</span><span class=\"mspace\" style=\"margin-right:0.2222em;\"></span><span class=\"mbin\">−</span><span class=\"mspace\" style=\"margin-right:0.2222em;\"></span></span><span class=\"base\"><span class=\"strut\" style=\"height:1em;vertical-align:-0.25em;\"></span><span class=\"mord\">2</span><span class=\"mclose\">]</span><span class=\"mspace\" style=\"margin-right:0.2222em;\"></span><span class=\"mbin\">+</span><span class=\"mspace\" style=\"margin-right:0.2222em;\"></span></span><span class=\"base\"><span class=\"strut\" style=\"height:1em;vertical-align:-0.25em;\"></span><span class=\"mord mathnormal\">m</span><span class=\"mord mathnormal\" style=\"margin-right:0.03588em;\">y</span><span class=\"mord mathnormal\">A</span><span class=\"mord mathnormal\" style=\"margin-right:0.02778em;\">rr</span><span class=\"mord mathnormal\">a</span><span class=\"mord mathnormal\" style=\"margin-right:0.03588em;\">y</span><span class=\"mopen\">[</span><span class=\"mord mathnormal\">i</span><span class=\"mspace\" style=\"margin-right:0.2222em;\"></span><span class=\"mbin\">−</span><span class=\"mspace\" style=\"margin-right:0.2222em;\"></span></span><span class=\"base\"><span class=\"strut\" style=\"height:1em;vertical-align:-0.25em;\"></span><span class=\"mord\">1</span><span class=\"mclose\">]</span></span></span></span></span></p>\n<p>We used this logic in lines 11 to 14 of the following code to calculate the Fibonacci sequence up to a certain range <code>n</code>.</p>\n\n<div class='image-component'>\n```python310\nclass MyRange:\n  def __init__(self, n):\n    self.n = n\n\n  def __iter__(self):\n    return self\n\n  def next(self):\n    myArray = []\n    for i in range(self.n): # from n to 0\n      if i == 0 or i == 1:\n        myArray.append(i) # adds the even number to the list\n      else:\n        myArray.append(myArray[i-2] + myArray[i-1])\n    return myArray\n  \nmyrange = MyRange(8)\nprint(myrange.next())\n```\n</div>\n<p>This code is illustrated in the figure below:</p>\n\n<p>Now that you’ve learnt and practised iterators, let’s discuss generators in the next chapter.</p>", "summary": {"titleUpdated": true, "title": "Challenge 3: Return Sequence of <PERSON><PERSON><PERSON><PERSON> Numbers", "description": "This lesson discusses how you can use iterators to return return a <PERSON><PERSON><PERSON><PERSON> sequence.", "tags": []}, "type": "Lesson"}]}, {"id": "60pfwfvk8", "title": "Generators", "summary": "Explore Python generators for creating efficient iterators with practical challenges.", "pages": [{"id": "5683063514202112", "title": "Generators", "is_preview": false, "slug": "generators", "text": "## Generators\n\nIf you read the previous chapter, you know that iterators are objects that are regularly used with `for` loops. In other words, iterators are objects that implement the iteration protocol. A Python generator is a convenient way to implement an iterator. Instead of a class, a generator is a function which returns a value each time the `yield` keyword is used. Here’s an example of a generator to count the values between two numbers:\n\n```python310\ndef myrange(a, b):\n  while a < b:\n    yield a\n    a += 1\na = myrange(2, 4) # call the generator function which returns an object\nprint (next(a)) # iterate through items using next\nprint (next(a))\n```\n\n\nLike iterators, generators can be used with the `for` loop:\n\n```python310\ndef myrange(a, b):\n    while a < b:\n      yield a\n      a += 1\nfor value in myrange(1, 4):\n  print(value)\n```\n\nUnder the hood, generators behave similarly to iterators. As can be seen in the example below, uncommenting the **line 9** should give an error:\n> **Why error?**\n>\n>Since on **line 6**, the range is defined from 1 to 3, so generating the next number will give an error.\n\n```python310\ndef myrange(a, b):\n    while a < b:\n      yield a\n      a += 1\n\nseq = myrange(1,3)\nprint(next(seq))\nprint(next(seq)) \n##next(seq) \n```\n\nThe interesting thing about generators is the `yield` keyword. The `yield` keyword works much like the `return` keyword, but---unlike `return`---it allows the function to eventually resume its execution. In other words, each time the next value of a generator is needed, Python wakes up the function and resumes its execution from the `yield` line as if the function had never exited.\n\nGenerator functions can use other functions inside. For instance, it is very common to use the `range` function to iterate over a sequence of numbers:\n\n```python310\ndef squares(n):\n  for value in range(n):\n    yield value * value\n\nsqr = squares(8)\nprint(next(sqr))\nprint(next(sqr))\nprint(next(sqr))\n```\n\nNow, let's solve some exercises to practice your concept of generators.", "mdHtml": "<h1 id=\"generators\">Generators</h1>\n<p>If you read the previous chapter, you know that iterators are objects that are regularly used with <code>for</code> loops. In other words, iterators are objects that implement the iteration protocol. A Python generator is a convenient way to implement an iterator. Instead of a class, a generator is a function which returns a value each time the <code>yield</code> keyword is used. Here’s an example of a generator to count the values between two numbers:</p>\n\n<div class='image-component'>\n```python310\ndef myrange(a, b):\n  while a < b:\n    yield a\n    a += 1\na = myrange(2, 4) # call the generator function which returns an object\nprint (next(a)) # iterate through items using next\nprint (next(a))\n```\n</div>\n<p>Like iterators, generators can be used with the <code>for</code> loop:</p>\n\n<div class='image-component'>\n```python310\ndef myrange(a, b):\n    while a < b:\n      yield a\n      a += 1\nfor value in myrange(1, 4):\n  print(value)\n```\n</div>\n<p>Under the hood, generators behave similarly to iterators. As can be seen in the example below, uncommenting the <strong>line 9</strong> should give an error:</p>\n<blockquote>\n<p><strong>Why error?</strong></p>\n<p>Since on <strong>line 6</strong>, the range is defined from 1 to 3, so generating the next number will give an error.</p>\n</blockquote>\n\n<div class='image-component'>\n```python310\ndef myrange(a, b):\n    while a < b:\n      yield a\n      a += 1\n\nseq = myrange(1,3)\nprint(next(seq))\nprint(next(seq)) \n##next(seq) \n```\n</div>\n<p>The interesting thing about generators is the <code>yield</code> keyword. The <code>yield</code> keyword works much like the <code>return</code> keyword, but—unlike <code>return</code>—it allows the function to eventually resume its execution. In other words, each time the next value of a generator is needed, Python wakes up the function and resumes its execution from the <code>yield</code> line as if the function had never exited.</p>\n<p>Generator functions can use other functions inside. For instance, it is very common to use the <code>range</code> function to iterate over a sequence of numbers:</p>\n\n<div class='image-component'>\n```python310\ndef squares(n):\n  for value in range(n):\n    yield value * value\n\nsqr = squares(8)\nprint(next(sqr))\nprint(next(sqr))\nprint(next(sqr))\n```\n</div>\n<p>Now, let’s solve some exercises to practice your concept of generators.</p>", "summary": {"titleUpdated": false, "title": "Generators", "description": "This lesson introduces generators and how to use them in Python.", "tags": []}, "type": "Lesson"}, {"id": "5644449073856512", "title": "Challenge 1: Yield Odd Numbers From 1 to n", "is_preview": false, "slug": "challenge-1-yield-odd-numbers-from-1-to-n", "text": "## Problem Statement\nCreate a generator to yield all the odd numbers from 1 to `n`. \n\n### Input\nA number `n`\n\n### Output\nAll odd numbers from 1 uptil n\n\n### Sample Input\n8\n\n### Sample Output\n1, 3, 5, 7\n\n \n## Coding Exercise\nWrite your code below. It is recommended​ that you try solving the exercise yourself before viewing the solution.\n\n```python310\ndef odd(n):\n  # write your code here\n  pass\n```\n\nThe next lesson discusses the solution to this exercise.", "mdHtml": "<h2 id=\"problem-statement\">Problem Statement</h2>\n<p>Create a generator to yield all the odd numbers from 1 to <code>n</code>.</p>\n<h3 id=\"input\">Input</h3>\n<p>A number <code>n</code></p>\n<h3 id=\"output\">Output</h3>\n<p>All odd numbers from 1 uptil n</p>\n<h3 id=\"sample-input\">Sample Input</h3>\n<p>8</p>\n<h3 id=\"sample-output\">Sample Output</h3>\n<p>1, 3, 5, 7</p>\n<h2 id=\"coding-exercise\">Coding Exercise</h2>\n<p>Write your code below. It is recommended​ that you try solving the exercise yourself before viewing the solution.</p>\n\n<div class='image-component'>\n```python310\ndef odd(n):\n  # write your code here\n  pass\n```\n</div>\n<p>The next lesson discusses the solution to this exercise.</p>", "summary": {"titleUpdated": false, "title": "Challenge 2: <PERSON>eld Odd Numbers From 1 to n", "description": "Practice your concepts of generators by solving the exercise below.", "tags": []}, "type": "Lesson"}, {"id": "5726948818944000", "title": "Solution Review: Yield <PERSON> Numbers From 1 to n", "is_preview": false, "slug": "solution-review-yield-odd-numbers-from-1-to-n", "text": "## Solution:\n\nIn line 3 of the following code, you can see that the generator checks for each integer in the sequence 1 to `n` to see if the value is not divisible by 2 (i.e., it's odd). If it's odd, it returns that value using the `yield` keyword.\n\nLines 6 and 7 simply have a loop that prints the odd numbers from 1 to 8.\n\n```python310\ndef odd(n):\n  for value in range(n + 1):\n    if value % 2 is not 0:\n      yield value\n\nfor j in odd(8):\n  print(j)\n```\n\nThe next lesson has another exercise on generators for your practice.", "mdHtml": "<h2 id=\"solution\">Solution:</h2>\n<p>In line 3 of the following code, you can see that the generator checks for each integer in the sequence 1 to <code>n</code> to see if the value is not divisible by 2 (i.e., it’s odd). If it’s odd, it returns that value using the <code>yield</code> keyword.</p>\n<p>Lines 6 and 7 simply have a loop that prints the odd numbers from 1 to 8.</p>\n\n<div class='image-component'>\n```python310\ndef odd(n):\n  for value in range(n + 1):\n    if value % 2 is not 0:\n      yield value\n\nfor j in odd(8):\n  print(j)\n```\n</div>\n<p>The next lesson has another exercise on generators for your practice.</p>", "summary": {"titleUpdated": true, "title": "Challenge 2: <PERSON>eld Odd Numbers From 1 to n", "description": "This lesson discusses how you can create generators to return a list of odd numbers.", "tags": []}, "type": "Lesson"}, {"id": "5710282332569600", "title": "Challenge 2: Yield Numbers From n Down to 0", "is_preview": false, "slug": "challenge-2-yield-numbers-from-n-down-to-0", "text": "## Problem Statement\nImplement a generator `reverse(n)` that returns All numbers from `n` down to 0.\n\n### Input\nA number `n`\n\n### Output\nAll numbers from `n` down to 0.\n\n### Sample Input\n8\n\n### Sample Output\n8, 7, 6, 5, 4, 3, 2, 1, 0\n\n \n## Coding Exercise\nWrite your code below. It is recommended​ that you try solving the exercise yourself before viewing the solution.\n\n\n```python310\ndef reverse(n):\n  # write your code here\n  pass\n```\n\nThe next lesson discusses the solution to this exercise.", "mdHtml": "<h2 id=\"problem-statement\">Problem Statement</h2>\n<p>Implement a generator <code>reverse(n)</code> that returns All numbers from <code>n</code> down to 0.</p>\n<h3 id=\"input\">Input</h3>\n<p>A number <code>n</code></p>\n<h3 id=\"output\">Output</h3>\n<p>All numbers from <code>n</code> down to 0.</p>\n<h3 id=\"sample-input\">Sample Input</h3>\n<p>8</p>\n<h3 id=\"sample-output\">Sample Output</h3>\n<p>8, 7, 6, 5, 4, 3, 2, 1, 0</p>\n<h2 id=\"coding-exercise\">Coding Exercise</h2>\n<p>Write your code below. It is recommended​ that you try solving the exercise yourself before viewing the solution.</p>\n\n<div class='image-component'>\n```python310\ndef reverse(n):\n  # write your code here\n  pass\n```\n</div>\n<p>The next lesson discusses the solution to this exercise.</p>", "summary": {"titleUpdated": false, "title": "Challenge 3: <PERSON>eld Numbers From n Down to 0", "description": "Practice an exercise on generators to return all numbers from n to 0 in descending order.", "tags": []}, "type": "Lesson"}, {"id": "5699392308772864", "title": "Solution Review: Yield Numbers from n Down to 0", "is_preview": false, "slug": "solution-review-yield-numbers-from-n-down-to-0", "text": "## Solution:\n\nIn this problem, all you need to do is add a reverse loop that starts from n and goes to 0, with the `value` being decremented by 1 in every iteration. Lines 5 and 6 simply print the numbers in the sequence n to 0.\n\n```python310\ndef reverse(n):\n  for value in range(n, -1, -1):\n    yield value\n\nfor i in reverse(8):\n  print(i)\n```\n\nThe next lesson has another challenge to brush up your concepts on generators.", "mdHtml": "<h2 id=\"solution\">Solution:</h2>\n<p>In this problem, all you need to do is add a reverse loop that starts from n and goes to 0, with the <code>value</code> being decremented by 1 in every iteration. Lines 5 and 6 simply print the numbers in the sequence n to 0.</p>\n\n<div class='image-component'>\n```python310\ndef reverse(n):\n  for value in range(n, -1, -1):\n    yield value\n\nfor i in reverse(8):\n  print(i)\n```\n</div>\n<p>The next lesson has another challenge to brush up your concepts on generators.</p>", "summary": {"titleUpdated": false, "title": "Challenge 3: <PERSON>eld Numbers From n Down to 0", "description": "This lesson discusses how you can use Python generators to print a sequence starting from an integer n down to 0.", "tags": []}, "type": "Lesson"}, {"id": "5680921936134144", "title": "Challenge 3: <PERSON><PERSON> From 1st to Nth Number", "is_preview": false, "slug": "challenge-3-yield-fibonacci-sequence-from-1st-to-nth-number", "text": "## Problem Statement\nCreate a generator to return the Fibonacci sequence starting from the first element up to `n`. \n\n>The **Fibonacci Sequence** is the series of numbers in which the next term is found by adding the two previous terms:\n>```\n> 0, 1, 1, 2, 3, 5, 8, 13, 21, 34, ...\n>```\n> Here ,\n> the number 0 is the first term, 1 is the second term, 1 is the third term and so on...\n\n\n### Input\nA number `n`\n\n### Output\nThe range of fibonacci numbers from 0 to n\n\n### Sample Input\n8\n\n### Sample Output\n[0, 1, 1, 2, 3, 5, 8, 13]\n\n \n## Coding Exercise\nWrite your code below. It is recommended​ that you try solving the exercise yourself before viewing the solution.\n\n\n\n```python310\ndef fibonacci(n):\n  # write your code here\n  pass\n```\n\nThe next lesson discusses the solution to this exercise.", "mdHtml": "<h2 id=\"problem-statement\">Problem Statement</h2>\n<p>Create a generator to return the Fibonacci sequence starting from the first element up to <code>n</code>.</p>\n<blockquote>\n<p>The <strong>Fibonacci Sequence</strong> is the series of numbers in which the next term is found by adding the two previous terms:</p>\n<pre><code>0, 1, 1, 2, 3, 5, 8, 13, 21, 34, ...\n</code></pre>\n<p>Here ,\nthe number 0 is the first term, 1 is the second term, 1 is the third term and so on…</p>\n</blockquote>\n<h3 id=\"input\">Input</h3>\n<p>A number <code>n</code></p>\n<h3 id=\"output\">Output</h3>\n<p>The range of fibonacci numbers from 0 to n</p>\n<h3 id=\"sample-input\">Sample Input</h3>\n<p>8</p>\n<h3 id=\"sample-output\">Sample Output</h3>\n<p>[0, 1, 1, 2, 3, 5, 8, 13]</p>\n<h2 id=\"coding-exercise\">Coding Exercise</h2>\n<p>Write your code below. It is recommended​ that you try solving the exercise yourself before viewing the solution.</p>\n\n<div class='image-component'>\n```python310\ndef fibonacci(n):\n  # write your code here\n  pass\n```\n</div>\n<p>The next lesson discusses the solution to this exercise.</p>", "summary": {"titleUpdated": true, "title": "Challenge 4: <PERSON><PERSON> From the 1st to nth Number", "description": "Practice an exercise on generators to return a sequence of fi<PERSON><PERSON>ci numbers.", "tags": []}, "type": "Lesson"}, {"id": "5705473982464000", "title": "Solution Review: <PERSON><PERSON> Sequence From 1st to Nth Number", "is_preview": false, "slug": "solution-review-yield-fibonacci-sequence-from-1st-to-nth-number", "text": "## Solution: \nThe first and second terms of the <PERSON><PERSON><PERSON><PERSON> sequence are 0 and 1 respectively. Each successive term in the sequence is then calculated by adding the two preceding terms. That is, the nth term is calculated by adding the (n-2)th and (n-1)th terms. Thus, we need to store the already computed sequence in a list to be able to calculate each new element in the sequence. The code snippet below uses this logic to return the <PERSON><PERSON><PERSON><PERSON> sequence. Lines 12 and 13 simply use a for loop to print the first 8 terms in the sequence.\n\n```python310\ndef fibonacci(n):\n  myArray = []\n  for i in range(n):\n    if i is 0 or i is 1:\n      myArray.append(i)\n      yield i\n    else:\n      x = myArray[i-2] + myArray[i-1]\n      myArray.append(x)\n      yield x\n      \nfor i in fibonacci(8):\n  print(i)\n```\n\nNow that you've learnt and practised generators, let's move on to asynchronous programming in the next chapter.", "mdHtml": "<h2 id=\"solution\">Solution:</h2>\n<p>The first and second terms of the Fibonacci sequence are 0 and 1 respectively. Each successive term in the sequence is then calculated by adding the two preceding terms. That is, the nth term is calculated by adding the (n-2)th and (n-1)th terms. Thus, we need to store the already computed sequence in a list to be able to calculate each new element in the sequence. The code snippet below uses this logic to return the Fibonacci sequence. Lines 12 and 13 simply use a for loop to print the first 8 terms in the sequence.</p>\n\n<div class='image-component'>\n```python310\ndef fibonacci(n):\n  myArray = []\n  for i in range(n):\n    if i is 0 or i is 1:\n      myArray.append(i)\n      yield i\n    else:\n      x = myArray[i-2] + myArray[i-1]\n      myArray.append(x)\n      yield x\n      \nfor i in fibonacci(8):\n  print(i)\n```\n</div>\n<p>Now that you’ve learnt and practised generators, let’s move on to asynchronous programming in the next chapter.</p>", "summary": {"titleUpdated": true, "title": "Challenge 4: <PERSON><PERSON> From the 1st to nth Number", "description": "This lesson discusses how you can use generators to return the first n terms of the <PERSON><PERSON><PERSON><PERSON> sequence.", "tags": []}, "type": "Lesson"}]}, {"id": "w6hgs4y8x", "title": "Asynchronous Programming", "summary": "Walk through asynchronous Python programming, components, task execution, and practical exercises.", "pages": [{"id": "5702573268926464", "title": "Asynchronous Programming", "is_preview": false, "slug": "asynchronous-programming", "text": "## Synchronous Programming\n\nSynchronous program execution is quite simple: a program starts at the first line, then each line is executed until the program reaches the end. Each time a function is called, the program waits for the function to return before continuing to the next line.\n\n\n## Why Asynchronous Programming?\nAsynchronous programming allows you to write concurrent code that runs in a single thread.\n### Concurrency\nImagine you are reading a novel while painting. Even if it seems like you are doing both tasks at the same time, what you are doing is switching between the two tasks; while you wait for the paint to dry you are reading your novel, but while you are painting you pause your reading. This is called concurrency.\n\nThe illustration below shows the context switching between the tasks.\nSingle thread allows you to decide where the scheduler will switch from one task to another, which means that sharing data between tasks is safer and easier.\n\n### Minimum Memory Usage\nEvery time a new thread is created, some memory is used to allow context switching. If we use async programming, this is not a problem since the code runs in a single thread.\n\nLet's learn about the components of an asynchronous code in the next lesson.", "mdHtml": "<h2 id=\"synchronous-programming\">Synchronous Programming</h2>\n<p>Synchronous program execution is quite simple: a program starts at the first line, then each line is executed until the program reaches the end. Each time a function is called, the program waits for the function to return before continuing to the next line.</p>\n\n<h2 id=\"why-asynchronous-programming\">Why Asynchronous Programming?</h2>\n<p>Asynchronous programming allows you to write concurrent code that runs in a single thread.</p>\n<h3 id=\"concurrency\">Concurrency</h3>\n<p>Imagine you are reading a novel while painting. Even if it seems like you are doing both tasks at the same time, what you are doing is switching between the two tasks; while you wait for the paint to dry you are reading your novel, but while you are painting you pause your reading. This is called concurrency.</p>\n<p>The illustration below shows the context switching between the tasks.</p>\n\n<p>Single thread allows you to decide where the scheduler will switch from one task to another, which means that sharing data between tasks is safer and easier.</p>\n<h3 id=\"minimum-memory-usage\">Minimum Memory Usage</h3>\n<p>Every time a new thread is created, some memory is used to allow context switching. If we use async programming, this is not a problem since the code runs in a single thread.</p>\n<p>Let’s learn about the components of an asynchronous code in the next lesson.</p>", "summary": {"titleUpdated": false, "title": "", "description": "So far we have been doing synchronous programming. In this lesson, we will learn about what is asynchronous programming and why it is important.", "tags": []}, "type": "Lesson"}, {"id": "5711601290182656", "title": "Components of an Asynchronous Code", "is_preview": false, "slug": "components-of-an-asynchronous-code", "text": "## Write an Asynchronous Code \nTo write asynchronous code in python,\nimport the library using `import asyncio`.\n\n## Components\nAsyncio has 3 main components: \n1. coroutines \n2. event loop  \n3. future\n\n\n### Coroutine\nA coroutine is the result of an asynchronous function which can be declared using the keyword `async` before def.\n\n\n```python3\nasync def my_function(argument):\n  pass\n```\n\nWhen we declare a function using the async keyword the function is not executed; instead, a coroutine object is returned.\n\nTo call a coroutine, write\n\n```python3\nmy_coroutine = my_function(argument)\n```\n\nThere are two ways to read the output of an async function from a coroutine.\nThe first way is to use the `await` keyword, which is only possible inside async functions and will wait for the coroutine to terminate and return the result.\n\n```python3\nresult = await my_function(argument)\n```\n\nThe second way is to add it to an event loop.\n\n### Event Loop\nThe event loop is the object which executes our asynchronous code and decides how to switch between async functions. After creating an event loop we can add multiple coroutines to it; these coroutines will all be running concurrently when `run_until_complete` or `run_forever` is called.\n\n```python3\nloop = asyncio.new_event_loop()  # create loop\nfuture = loop.create_task(my_coroutine) # add coroutine to the loop\nloop.run_until_complete(future) # add coroutine to the loop concurrently\nloop.close() # close the loop\n```\n\n### Future\nA future is an object that works as a placeholder for the output of an asynchronous function, and it gives us information about the function state.\nA future is created when we add a coroutine to an event loop. There are two ways to this:\n\n```python3\nfuture = loop.create_task(my_coroutine)\n```\n\nThe method adds a coroutine to the loop and returns a task which is a subtype of the future. \n\nNow that you have learned the components of asynchronous programming, let's move on to execute tasks using asynchronous programming.", "mdHtml": "<h2 id=\"write-an-asynchronous-code\">Write an Asynchronous Code</h2>\n<p>To write asynchronous code in python,\nimport the library using <code>import asyncio</code>.</p>\n<h2 id=\"components\">Components</h2>\n<p>Asyncio has 3 main components:</p>\n<ol>\n<li>coroutines</li>\n<li>event loop</li>\n<li>future</li>\n</ol>\n<h3 id=\"coroutine\">Coroutine</h3>\n<p>A coroutine is the result of an asynchronous function which can be declared using the keyword <code>async</code> before def.</p>\n\n<div class='image-component'>\n```python3\nasync def my_function(argument):\n  pass\n```\n</div>\n<p>When we declare a function using the async keyword the function is not executed; instead, a coroutine object is returned.</p>\n<p>To call a coroutine, write</p>\n\n<div class='image-component'>\n```python3\nmy_coroutine = my_function(argument)\n```\n</div>\n<p>There are two ways to read the output of an async function from a coroutine.\nThe first way is to use the <code>await</code> keyword, which is only possible inside async functions and will wait for the coroutine to terminate and return the result.</p>\n\n<div class='image-component'>\n```python3\nresult = await my_function(argument)\n```\n</div>\n<p>The second way is to add it to an event loop.</p>\n<h3 id=\"event-loop\">Event Loop</h3>\n<p>The event loop is the object which executes our asynchronous code and decides how to switch between async functions. After creating an event loop we can add multiple coroutines to it; these coroutines will all be running concurrently when <code>run_until_complete</code> or <code>run_forever</code> is called.</p>\n\n<div class='image-component'>\n```python3\nloop = asyncio.new_event_loop()  # create loop\nfuture = loop.create_task(my_coroutine) # add coroutine to the loop\nloop.run_until_complete(future) # add coroutine to the loop concurrently\nloop.close() # close the loop\n```\n</div>\n<h3 id=\"future\">Future</h3>\n<p>A future is an object that works as a placeholder for the output of an asynchronous function, and it gives us information about the function state.\nA future is created when we add a coroutine to an event loop. There are two ways to this:</p>\n\n<div class='image-component'>\n```python3\nfuture = loop.create_task(my_coroutine)\n```\n</div>\n<p>The method adds a coroutine to the loop and returns a task which is a subtype of the future.</p>\n<p>Now that you have learned the components of asynchronous programming, let’s move on to execute tasks using asynchronous programming.</p>", "summary": {"titleUpdated": true, "title": "", "description": "In this lesson, we will learn about the components of the asynchronous code.", "tags": []}, "type": "Lesson"}, {"id": "5633843188989952", "title": "Execute Single and Multiple Tasks", "is_preview": false, "slug": "execute-single-and-multiple-tasks", "text": "In **asynchronous programming**, the execution of a function is usually non-blocking. In other words, each time you call a function it returns immediately. However, that function does not necessarily get executed right away. Instead, there is usually a mechanism (called the \"scheduler\") which is responsible for the future execution of the function.\n\nThe problem with asynchronous programming is that a program may end before any asynchronous function starts. A common solution for this is for asynchronous functions to return \"futures\" or \"promises\". These are objects that represent the state of execution of an async function. Finally, asynchronous programming frameworks typically have mechanisms to block or wait for those async functions to end based on those \"future\" objects.\n\n## Co-operative Multitasking\nSince Python 3.6, the `asyncio` module combined with the `async` and `await` keyword allows us to implement what is called *co-operative multitasking programs*. In this type of programming, a coroutine function voluntarily yields control to another coroutine function when idle or when waiting for some input.\n\nAsynchronous functions are declared with `async def`.\n\n\n```python3\nimport asyncio\nasync def functionName():\n  await asyncio.sleep(1) \n  return  \n```\n\n## Execute a Single Task\nTo call an asynchronous function once, do the following:-\n\n1. Create an event loop\n2. Run async function and wait for completion\n3. Close the loop\n\n```python3\n# Create an event loop\nloop = asyncio.get_event_loop()\n\n# Run async function and wait for completion\nresults = loop.run_until_complete(functionName())\n\n# Close the loop\nloop.close()\n```\n\nConsider the following asynchronous function that squares a number and sleeps for one second before returning. (Ignore the `await` keyword for now.)\n\n```python310\nimport asyncio\n\nasync def square(x):\n    print('Square', x)\n    await asyncio.sleep(1)\n    print('End square', x)\n    return x * x\n\n# Create event loop\nloop = asyncio.get_event_loop()\n\n# Run async function and wait for completion\nresults = loop.run_until_complete(square(1))\nprint(results)\n\n# Close the loop\nloop.close()\n```\n\n\nThe event loop for [asynchronous Programming](https://docs.python.org/3/library/asyncio-eventloop.html) is, among other things, the Python mechanism that schedules the execution of asynchronous functions. We use the loop to run the function until completion. This is a synchronizing mechanism that makes sure the next print statement doesn't execute until we have some results.\n\n## Execute Multiple Tasks\nThe previous example is not a good example of asynchronous programming because we don't need that much complexity to execute only one function. However, imagine that you would need to execute the `square(x)` function three times, like this:\n\n```python\nsquare(1)\nsquare(2)\nsquare(3)\n```\n\nTo call an asynchronous function to execute multiple tasks, do the following:-\n1.Create an event loop\n2.Run async function and wait for completion\n\n\n```python\n# Create event loop\nloop = asyncio.get_event_loop()\n\n# Run async function and wait for completion\nresults = loop.run_until_complete(asyncio.gather(\nfunctionName()\nfunctionName()\n.\n.\n.\nfunctionName())\n\n# Close the loop\nloop.close()\n```\n\nSince the `square()` function has a sleep function inside, the total execution time of this program would be 3 seconds. However, given that the computer is going to be idle for a full second each time the function is executed, why can't we start the next call while the previous is sleeping? Here's how we do it:\n\n```python310\nimport asyncio\n\nasync def square(x):\n    print('Square', x)\n    await asyncio.sleep(1)\n    print('End square', x)\n    return x * x\n\n# Create event loop\nloop = asyncio.get_event_loop()\n\n# Run async function and wait for completion\nresults = loop.run_until_complete(asyncio.gather(\n    square(1),\n    square(2),\n    square(3)\n))\nprint(results)\n\n# Close the loop\nloop.close()\n```\n\n\nBasically, we  use ``asyncio.gather(*tasks)`` to inform the loop to wait for all tasks to finish. Since the coroutines will start at almost the same time, the program will run for only 1 second. Asyncio **gather()** won't necessarily run the coroutines by order, although it will return an ordered list of results.\n\n\nSometimes results may be needed as soon as they are available. For that, we can use a second coroutine that deals with each result using ``asyncio.as_completed()``:\n\n```python310\nimport asyncio\n\nasync def square(x):\n    print('Square', x)\n    await asyncio.sleep(1)\n    print('End square', x)\n    return x * x\n\n# Create event loop\nloop = asyncio.get_event_loop()\n\nasync def when_done(tasks):\n    for res in asyncio.as_completed(tasks):\n        print('Result:', await res)\n\nloop.run_until_complete(when_done([\n    square(1),\n    square(2),\n    square(3)\n]))\n```\n\n\nFinally, async coroutines can call **other async coroutine functions** with the **await** keyword:\n\n```python310\nimport asyncio\n\nasync def compute_square(x):\n    await asyncio.sleep(1)\n    return x * x\n\nasync def square(x):\n    print('Square', x)\n    res = await compute_square(x)\n    print('End square', x)\n    return res\n  \n# Create event loop\nloop = asyncio.get_event_loop()\n\nasync def when_done(tasks):\n    for res in asyncio.as_completed(tasks):\n        print('Result:', await res)\n\nloop.run_until_complete(when_done([\n    square(1),\n    square(2),\n    square(3)\n]))\n```\n\nNow that you have a clear concept of asynchronous programming, let's test your knowledge in the upcoming exercises.", "mdHtml": "<p>In <strong>asynchronous programming</strong>, the execution of a function is usually non-blocking. In other words, each time you call a function it returns immediately. However, that function does not necessarily get executed right away. Instead, there is usually a mechanism (called the “scheduler”) which is responsible for the future execution of the function.</p>\n<p>The problem with asynchronous programming is that a program may end before any asynchronous function starts. A common solution for this is for asynchronous functions to return “futures” or “promises”. These are objects that represent the state of execution of an async function. Finally, asynchronous programming frameworks typically have mechanisms to block or wait for those async functions to end based on those “future” objects.</p>\n<h3 id=\"co-operative-multitasking\">Co-operative Multitasking</h3>\n<p>Since Python 3.6, the <code>asyncio</code> module combined with the <code>async</code> and <code>await</code> keyword allows us to implement what is called <em>co-operative multitasking programs</em>. In this type of programming, a coroutine function voluntarily yields control to another coroutine function when idle or when waiting for some input.</p>\n<p>Asynchronous functions are declared with <code>async def</code>.</p>\n\n<div class='image-component'>\n```python3\nimport asyncio\nasync def functionName():\n  await asyncio.sleep(1) \n  return  \n```\n</div>\n<h3 id=\"execute-a-single-task\">Execute a Single Task</h3>\n<p>To call an asynchronous function once, do the following:-</p>\n<ol>\n<li>Create an event loop</li>\n<li>Run async function and wait for completion</li>\n<li>Close the loop</li>\n</ol>\n\n<div class='image-component'>\n```python3\n# Create an event loop\nloop = asyncio.get_event_loop()\n\n# Run async function and wait for completion\nresults = loop.run_until_complete(functionName())\n\n# Close the loop\nloop.close()\n```\n</div>\n<p>Consider the following asynchronous function that squares a number and sleeps for one second before returning. (Ignore the <code>await</code> keyword for now.)</p>\n\n<div class='image-component'>\n```python310\nimport asyncio\n\nasync def square(x):\n    print('Square', x)\n    await asyncio.sleep(1)\n    print('End square', x)\n    return x * x\n\n# Create event loop\nloop = asyncio.get_event_loop()\n\n# Run async function and wait for completion\nresults = loop.run_until_complete(square(1))\nprint(results)\n\n# Close the loop\nloop.close()\n```\n</div>\n<p>The event loop for <a href=\"https://docs.python.org/3/library/asyncio-eventloop.html\">asynchronous Programming</a> is, among other things, the Python mechanism that schedules the execution of asynchronous functions. We use the loop to run the function until completion. This is a synchronizing mechanism that makes sure the next print statement doesn’t execute until we have some results.</p>\n<h3 id=\"execute-multiple-tasks\">Execute Multiple Tasks</h3>\n<p>The previous example is not a good example of asynchronous programming because we don’t need that much complexity to execute only one function. However, imagine that you would need to execute the <code>square(x)</code> function three times, like this:</p>\n\n<div class='image-component'>\n```python\nsquare(1)\nsquare(2)\nsquare(3)\n```\n</div>\n<p>To call an asynchronous function to execute multiple tasks, do the following:-\n1.Create an event loop\n2.Run async function and wait for completion</p>\n<pre><code class=\"language-python\"># Create event loop\nloop = asyncio.get_event_loop()\n\n# Run async function and wait for completion\nresults = loop.run_until_complete(asyncio.gather(\nfunctionName()\nfunctionName()\n.\n.\n.\nfunctionName())\n\n# Close the loop\nloop.close()\n</code></pre>\n<p>Since the <code>square()</code> function has a sleep function inside, the total execution time of this program would be 3 seconds. However, given that the computer is going to be idle for a full second each time the function is executed, why can’t we start the next call while the previous is sleeping? Here’s how we do it:</p>\n\n<div class='image-component'>\n```python310\nimport asyncio\n\nasync def square(x):\n    print('Square', x)\n    await asyncio.sleep(1)\n    print('End square', x)\n    return x * x\n\n# Create event loop\nloop = asyncio.get_event_loop()\n\n# Run async function and wait for completion\nresults = loop.run_until_complete(asyncio.gather(\n    square(1),\n    square(2),\n    square(3)\n))\nprint(results)\n\n# Close the loop\nloop.close()\n```\n</div>\n<p>Basically, we  use <code>asyncio.gather(*tasks)</code> to inform the loop to wait for all tasks to finish. Since the coroutines will start at almost the same time, the program will run for only 1 second. Asyncio <strong>gather()</strong> won’t necessarily run the coroutines by order, although it will return an ordered list of results.</p>\n<p>Sometimes results may be needed as soon as they are available. For that, we can use a second coroutine that deals with each result using <code>asyncio.as_completed()</code>:</p>\n\n<div class='image-component'>\n```python310\nimport asyncio\n\nasync def square(x):\n    print('Square', x)\n    await asyncio.sleep(1)\n    print('End square', x)\n    return x * x\n\n# Create event loop\nloop = asyncio.get_event_loop()\n\nasync def when_done(tasks):\n    for res in asyncio.as_completed(tasks):\n        print('Result:', await res)\n\nloop.run_until_complete(when_done([\n    square(1),\n    square(2),\n    square(3)\n]))\n```\n</div>\n<p>Finally, async coroutines can call <strong>other async coroutine functions</strong> with the <strong>await</strong> keyword:</p>\n\n<div class='image-component'>\n```python310\nimport asyncio\n\nasync def compute_square(x):\n    await asyncio.sleep(1)\n    return x * x\n\nasync def square(x):\n    print('Square', x)\n    res = await compute_square(x)\n    print('End square', x)\n    return res\n  \n# Create event loop\nloop = asyncio.get_event_loop()\n\nasync def when_done(tasks):\n    for res in asyncio.as_completed(tasks):\n        print('Result:', await res)\n\nloop.run_until_complete(when_done([\n    square(1),\n    square(2),\n    square(3)\n]))\n```\n</div>\n<p>Now that you have a clear concept of asynchronous programming, let’s test your knowledge in the upcoming exercises.</p>", "summary": {"titleUpdated": true, "title": "Asynchronous Programming", "description": "This lesson will teach you cooperative multitasking to execute single and multiple tasks using python.", "tags": []}, "type": "Lesson"}, {"id": "5699235676684288", "title": "Challenge 1: Implement an Asynchronous Function", "is_preview": false, "slug": "challenge-1-implement-an-asynchronous-function", "text": "## Problem Statement\n\nImplement an asynchronous coroutine function to add two variables and sleep for the duration of the sum. Use the `asyncio` loop to call the function with two numbers.\n\n### Input\nTwo number n1 and n2\n\n### Output\nThe sum of two numbers\n\n### Sample Input\nn1 = 1, n2 = 2\n\n### Sample Output\n3\n\n \n## Coding Exercise\nWrite your code below. It is recommended​ that you try solving the exercise yourself before viewing the solution.\nNow, let's move on to the detailed solution of the above problem.", "mdHtml": "<h2 id=\"problem-statement\">Problem Statement</h2>\n<p>Implement an asynchronous coroutine function to add two variables and sleep for the duration of the sum. Use the <code>asyncio</code> loop to call the function with two numbers.</p>\n<h3 id=\"input\">Input</h3>\n<p>Two number n1 and n2</p>\n<h3 id=\"output\">Output</h3>\n<p>The sum of two numbers</p>\n<h3 id=\"sample-input\">Sample Input</h3>\n<p>n1 = 1, n2 = 2</p>\n<h3 id=\"sample-output\">Sample Output</h3>\n<p>3</p>\n<h2 id=\"coding-exercise\">Coding Exercise</h2>\n<p>Write your code below. It is recommended​ that you try solving the exercise yourself before viewing the solution.</p>\n\n<p>Now, let’s move on to the detailed solution of the above problem.</p>", "summary": {"titleUpdated": true, "title": "Challenge 1: Implement an Asynchronous Function", "description": "In this challenge, you are required to implement an asynchronous coroutine function.", "tags": []}, "type": "Lesson"}, {"id": "5732139823792128", "title": "Solution Review: Implement an Asynchronous Function", "is_preview": false, "slug": "solution-review-implement-an-asynchronous-function", "text": "## Solution: Import the `asyncio` Library and Call the Asynchronous Coroutine \n* Import the library `import asyncio`\n* Define the function\n\n  Asynchronous functions are declared with \n     **async def**.\n   \n\n```python3\nimport asyncio\nasync def sumNumbers(n1,n2):\n  await asyncio.sleep(1) \n  return  \n```\n\n* Call the asynchronous coroutine\n  1. Create an event loop\n     ```python\n      loop = asyncio.get_event_loop()\n     ```\n  2. Run async function and wait for completion\n     ```python\n     results = loop.run_until_complete(functionName())\n     ```\n  3. Close the loop\n     ```python\n      loop.close()\n     ```\n\nThe following python code explains the concept.\n\n```python310\nimport asyncio\n\nasync def sum(n1,n2):\n    print('Sum numbers', n1, '+', n2)\n    await asyncio.sleep(1)\n    print('End Sum', n1, '+', n2)\n    return n1 + n2\n\n# Create event loop\nloop = asyncio.get_event_loop()\nn1 = 1\nn2 = 2\n# Run async function and wait for completion\nresults = loop.run_until_complete(sum(n1, n2))\nprint(\"Sum of two numbers:\", n1, \"+\", n2, \"=\", results)\n\n# Close the loop\nloop.close()\n```\n\nLet's move on to the next problem.", "mdHtml": "<h2 id=\"solution-import-the-asyncio-library-and-call-the-asynchronous-coroutine\">Solution: Import the <code>asyncio</code> Library and Call the Asynchronous Coroutine</h2>\n<ul>\n<li>\n<p>Import the library <code>import asyncio</code></p>\n</li>\n<li>\n<p>Define the function</p>\n<p>Asynchronous functions are declared with\n<strong>async def</strong>.</p>\n</li>\n</ul>\n\n<div class='image-component'>\n```python3\nimport asyncio\nasync def sumNumbers(n1,n2):\n  await asyncio.sleep(1) \n  return  \n```\n</div>\n<ul>\n<li>Call the asynchronous coroutine\n<ol>\n<li>Create an event loop<pre><code class=\"language-python\"> loop = asyncio.get_event_loop()\n</code></pre>\n</li>\n<li>Run async function and wait for completion<pre><code class=\"language-python\">results = loop.run_until_complete(functionName())\n</code></pre>\n</li>\n<li>Close the loop<pre><code class=\"language-python\"> loop.close()\n</code></pre>\n</li>\n</ol>\n</li>\n</ul>\n<p>The following python code explains the concept.</p>\n\n<div class='image-component'>\n```python310\nimport asyncio\n\nasync def sum(n1,n2):\n    print('Sum numbers', n1, '+', n2)\n    await asyncio.sleep(1)\n    print('End Sum', n1, '+', n2)\n    return n1 + n2\n\n# Create event loop\nloop = asyncio.get_event_loop()\nn1 = 1\nn2 = 2\n# Run async function and wait for completion\nresults = loop.run_until_complete(sum(n1, n2))\nprint(\"Sum of two numbers:\", n1, \"+\", n2, \"=\", results)\n\n# Close the loop\nloop.close()\n```\n</div>\n<p>Let’s move on to the next problem.</p>", "summary": {"titleUpdated": true, "title": "", "description": "This lesson will explain how to implement an asynchronous function to calculate the sum of two numbers n1 and n2. ", "tags": []}, "type": "Lesson"}, {"id": "5765687410688000", "title": "Challenge 2: Multiple Asynchronous Calls", "is_preview": false, "slug": "challenge-2-multiple-asynchronous-calls", "text": "## Problem Statement\n\nChange the previous program to schedule the execution of two calls to the sum function.\n\n### Input\nTwo number n1 and n2\n\n### Output\nThe sum of two numbers in the order of the execution.\n\n \n## Coding Exercise\nWrite your code below. It is recommended​ that you try solving the exercise yourself before viewing the solution.\nNow, let’s move on to the detailed solution review of the above problem.", "mdHtml": "<h2 id=\"problem-statement\">Problem Statement</h2>\n<p>Change the previous program to schedule the execution of two calls to the sum function.</p>\n<h3 id=\"input\">Input</h3>\n<p>Two number n1 and n2</p>\n<h3 id=\"output\">Output</h3>\n<p>The sum of two numbers in the order of the execution.</p>\n<h2 id=\"coding-exercise\">Coding Exercise</h2>\n<p>Write your code below. It is recommended​ that you try solving the exercise yourself before viewing the solution.</p>\n\n<p>Now, let’s move on to the detailed solution review of the above problem.</p>", "summary": {"titleUpdated": true, "title": "Challenge 2: Multiple Asynchronous Calls ", "description": "In this challenge, you are required to call the asynchronous function multiple times.", "tags": []}, "type": "Lesson"}, {"id": "5693244599959552", "title": "Solution Review: Multiple Asynchronous Calls", "is_preview": false, "slug": "solution-review-multiple-asynchronous-calls", "text": "## Solution: Import the `asyncio` Library and Call the Asynchronous Coroutine \n* Import the library `import asyncio`\n* Define the function\n\n  Asynchronous functions are declared with \n     **async def**.\n   ```python\n   import asyncio\n   async def sum(n1, n2):\n   await asyncio.sleep(1) \n   return  \n   ```\n* Call the asynchronous coroutine\n  1. Create an event loop\n     ```python\n      loop = asyncio.get_event_loop()\n     ```\n  2. Run async function and wait for completion\n     ```python\n      results = loop.run_until_complete(asyncio.gather(\n      sum(n1, n2)\n      sum(n1, n2)\n      sum(n1, n2))\n\n     ```\n  3. Close the loop\n     ```python\n      loop.close()\n     ```\n\nThe following python code explains the concept.\n\n```python310\nimport asyncio\n\nasync def sum(n1, n2):\n    print('Sum numbers', n1, '+', n2)\n    await asyncio.sleep(1)\n    print('End Sum', n1, '+', n2)\n    return n1 + n2\n\n# Create event loop\nloop = asyncio.get_event_loop()\n\n# Run async function and wait for completion\nresults = loop.run_until_complete(asyncio.gather(\n    sum(1, 2),\n    sum(2, 3),\n    sum(3, 4)\n))\nprint(results)\n\n# Close the loop\nloop.close()\n```\n\nNow that you have an insight into asynchronous programming, let’s move on to the quiz.", "mdHtml": "<h2 id=\"solution-import-the-asyncio-library-and-call-the-asynchronous-coroutine\">Solution: Import the <code>asyncio</code> Library and Call the Asynchronous Coroutine</h2>\n<ul>\n<li>\n<p>Import the library <code>import asyncio</code></p>\n</li>\n<li>\n<p>Define the function</p>\n<p>Asynchronous functions are declared with\n<strong>async def</strong>.</p>\n<pre><code class=\"language-python\">import asyncio\nasync def sum(n1, n2):\nawait asyncio.sleep(1) \nreturn  \n</code></pre>\n</li>\n<li>\n<p>Call the asynchronous coroutine</p>\n<ol>\n<li>Create an event loop<pre><code class=\"language-python\"> loop = asyncio.get_event_loop()\n</code></pre>\n</li>\n<li>Run async function and wait for completion<pre><code class=\"language-python\"> results = loop.run_until_complete(asyncio.gather(\n sum(n1, n2)\n sum(n1, n2)\n sum(n1, n2))\n\n</code></pre>\n</li>\n<li>Close the loop<pre><code class=\"language-python\"> loop.close()\n</code></pre>\n</li>\n</ol>\n</li>\n</ul>\n<p>The following python code explains the concept.</p>\n\n<div class='image-component'>\n```python310\nimport asyncio\n\nasync def sum(n1, n2):\n    print('Sum numbers', n1, '+', n2)\n    await asyncio.sleep(1)\n    print('End Sum', n1, '+', n2)\n    return n1 + n2\n\n# Create event loop\nloop = asyncio.get_event_loop()\n\n# Run async function and wait for completion\nresults = loop.run_until_complete(asyncio.gather(\n    sum(1, 2),\n    sum(2, 3),\n    sum(3, 4)\n))\nprint(results)\n\n# Close the loop\nloop.close()\n```\n</div>\n<p>Now that you have an insight into asynchronous programming, let’s move on to the quiz.</p>", "summary": {"titleUpdated": false, "title": "", "description": "This lesson will give a detailed review of how to call an asynchronous function multiple times.", "tags": []}, "type": "Lesson"}, {"id": "5768200369209344", "title": "Quick Quiz on Asynchronous Programming", "is_preview": false, "slug": "quick-quiz-on-asynchronous-programming", "text": "", "mdHtml": "", "summary": {"titleUpdated": false, "title": "", "description": "", "tags": []}, "type": "Quiz", "questions": [{"questionText": "Which of the following statements allow you to execute an asynchronous function `myPrint(x)` once?", "questionOptions": [{"text": "```python\nimport asyncio\nasync def myPrint(x):\n  print(x)\n  await asyncio.sleep(1) \n  return\n\nloop = asyncio.get_event_loop()\nresults = loop.run_until_complete(myPrint(1))\nloop.close()\n```\n", "correct": true, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<pre><code class=\"language-python\">import asyncio\nasync def myPrint(x):\n  print(x)\n  await asyncio.sleep(1) \n  return\n\nloop = asyncio.get_event_loop()\nresults = loop.run_until_complete(myPrint(1))\nloop.close()\n</code></pre>\n"}, {"text": "```python\nimport asyncio\nasync def myPrint(x):\n  print(x)\n  await asyncio.sleep(1) \n  return\n\nloop = asyncio.get_event_loop()\nresults = loop.run_until_complete(asyncio.gather(myPrint(1))\n                                                 myPrint(2))\nloop.close()\n```", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<pre><code class=\"language-python\">import asyncio\nasync def myPrint(x):\n  print(x)\n  await asyncio.sleep(1) \n  return\n\nloop = asyncio.get_event_loop()\nresults = loop.run_until_complete(asyncio.gather(myPrint(1))\n                                                 myPrint(2))\nloop.close()\n</code></pre>\n"}], "questionTextHtml": "<p>Which of the following statements allow you to execute an asynchronous function <code>myPrint(x)</code> once?</p>\n"}, {"questionText": "Which of the following statements allow you to execute an asynchronous function `myPrint(x)` multiple times?", "questionOptions": [{"text": "```python\nimport asyncio\nasync def myPrint(x):\n  print(x)\n  await asyncio.sleep(1) \n  return\n\nloop = asyncio.get_event_loop()\nresults = loop.run_until_complete(myPrint(1))\nloop.close()\n```", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<pre><code class=\"language-python\">import asyncio\nasync def myPrint(x):\n  print(x)\n  await asyncio.sleep(1) \n  return\n\nloop = asyncio.get_event_loop()\nresults = loop.run_until_complete(myPrint(1))\nloop.close()\n</code></pre>\n"}, {"text": "```python\nimport asyncio\nasync def myPrint(x):\n  print(x)\n  await asyncio.sleep(1) \n  return\n\nloop = asyncio.get_event_loop()\nresults = loop.run_until_complete(asyncio.gather(myPrint(1), myPrint(2)))\nloop.close()\n```", "correct": true, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<pre><code class=\"language-python\">import asyncio\nasync def myPrint(x):\n  print(x)\n  await asyncio.sleep(1) \n  return\n\nloop = asyncio.get_event_loop()\nresults = loop.run_until_complete(asyncio.gather(myPrint(1), myPrint(2)))\nloop.close()\n</code></pre>\n"}], "questionTextHtml": "<p>Which of the following statements allow you to execute an asynchronous function <code>myPrint(x)</code> multiple times?</p>\n"}]}]}]}]}