{"deletion_time": null, "ai_assistant_enabled": false, "certificate_price": 19.0, "is_recommended_by_reader": false, "is_reader_subscribed": false, "pinned_by_reader": false, "is_published": true, "first_published_time": "2022-09-26T08:40:28.136663", "last_published_time": "2025-08-08T11:50:44.758970", "timestamp": "2025-08-20T14:35:28.283457", "total_courses": 1, "price": null, "skills": [], "tags": [], "aggregated_widget_stats": {"projects": 1, "SlateHTML": 118, "codeRunnableCount": 29, "codeSnippetCount": 24, "illustrations": 75, "MxGraphWidget": 5, "WebpackBin": 26, "Code": 21, "EditorCode": 4, "Columns": 6, "DrawIOWidget": 14, "CanvasAnimation": 11, "SpoilerEditor": 2, "Quiz": 4, "Sandpack": 2}, "url_slug": "building-a-jamstack-application-with-nextjs-and-strapi-cms", "authors": [], "read_time": 28800.0, "learner_tags": ["grow-my-skillset", "javascript", "web-development", "strapi", "nextjs", "jamstack"], "level_one_learner_tags": ["grow-my-skillset", "javascript", "web-development", "strapi", "nextjs", "jamstack"], "courses": [{"title": "Building a Jamstack Application with Next.js and Strapi CMS", "summary": "Jamstack proposes an architecture for creating flexible, secure, and performance-efficient web applications. This course follows a tutorial-based approach, through which you will learn how to build a complete Jamstack application with Next.js and Strapi.\n\nThis course will introduce you to Jamstack and how it differs from standard web applications. you will then learn about Next.js and how to create static websites. Along the way, you will create a Recipes application with a list of recipes available through TheMealDB API. You will also integrate Strapi as a headless CMS, enabling you to authenticate users and allow them to save their favorite recipes. You will conclude the course by working on a project where you will be using Strapi and Next.js to authenticate users and allow them to create, retrieve, update, and delete notes for themselves.\n\nBy the end of this course, you will have a thorough command of Strapi and Next.js to create flexible, secure, and performance-efficient web applications.", "brief_summary": "Build performance-efficient web apps with Next.js and Strapi. Learn static site creation, user authentication, and headless CMS integration in a modern Jamstack architecture.", "whatYouWillLearn": ["A thorough understanding of the core concepts related to Next.js", "Familiarity with Static Site Generation with Next.js", "Hands-on experience building a Jamstack application with Next.js and Strapi CMS", "Familiarity with core components of Strapi including Content Manager, Content-Type Builder, and Strapi API endpoints", "Hands-on experience building a Notes application project using Strapi and Next.js"], "target_audience": "beginner", "cover_image_url": "", "creation_time": "2025-08-08T11:35:42.275757", "modified_time": "2025-08-20T00:09:00.032151", "published_time": "2025-08-08T11:50:44.758970", "categories": [{"id": "nr9usk8m8", "title": "Introduction", "summary": "Get familiar with Jamstack architecture, Next.js static sites, and Strapi CMS usage.", "pages": [{"id": "5874907641806848", "title": "Introduction to the Course", "is_preview": true, "slug": "introduction-to-the-course", "text": "<div class=\"flex justify-center sm:flex-row sm:justify-start -mt-4 flex-col\" style=\"gap: 20px;\">\n<div style='flex: 1; width: 70%;'>\nThis course follows a tutorial-based approach, through which we'll learn how to build a complete Jamstack application with Next.js and Strapi. We'll create a Recipes application with a list of recipes available through TheMealDB API. We'll also integrate Strapi as a headless CMS, enabling us to authenticate users and allow them to save their favorite recipes.\n</div>\n<div style='flex: 1; width: 30%;'>\n**[DrawIOWidget]**\n</div>\n</div>\n\n![](http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755671628787104272.svg)\n![](http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755671631606985008.svg)", "mdHtml": "<div class='image-component'>\n<div class=\"flex justify-center sm:flex-row sm:justify-start -mt-4 flex-col\" style=\"gap: 20px;\">\n<div style='flex: 1; width: 70%;'>\nThis course follows a tutorial-based approach, through which we'll learn how to build a complete Jamstack application with Next.js and Strapi. We'll create a Recipes application with a list of recipes available through TheMealDB API. We'll also integrate Strapi as a headless CMS, enabling us to authenticate users and allow them to save their favorite recipes.\n</div>\n<div style='flex: 1; width: 30%;'>\n**[DrawIOWidget]**\n</div>\n</div>\n</div>\n<div class='image-component'>![](http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755671628787104272.svg)</div>\n<div class='image-component'>![](http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755671631606985008.svg)</div>", "summary": {"titleUpdated": true, "title": "Introduction to the Course", "description": "Get introduced to the course as well as its structure, audience, and prerequisites. ", "tags": []}, "type": "Lesson"}, {"id": "5948311275307008", "title": "What We'll Be Building", "is_preview": true, "slug": "what-well-be-building", "text": "![The home page of the application](http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755671634436565930.svg)\n![The meal details page](http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755671636803042782.svg)", "mdHtml": "<div class='image-component'>![The home page of the application](http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755671634436565930.svg)</div>\n<div class='image-component'>![The meal details page](http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755671636803042782.svg)</div>", "summary": {"titleUpdated": true, "title": "What We'll Be Building", "description": "Take a look at the application that we'll build in this course.", "tags": []}, "type": "Lesson"}]}, {"id": "ucwine9rd", "title": "Getting Started with Jamstack", "summary": "Look at Jamstack architecture, static site generation, JavaScript with APIs, and headless CMS.", "pages": [{"id": "5705786133315584", "title": "What is Jamstack?", "is_preview": true, "slug": "what-is-jamstack", "text": "<img src=\"http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755671640074525301.svg\" alt=\"A traditional web application\" width=\"442\" height=\"141\" style=\"width: 100%; height: auto;\" />\n<img src=\"http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755671642659383946.svg\" alt=\"An advanced web application process\" width=\"811\" height=\"311\" style=\"width: 100%; height: auto;\" />\n<img src=\"http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755671646955306446.svg\" alt=\"A typical Jamstack application\" width=\"441\" height=\"141\" style=\"width: 100%; height: auto;\" />", "mdHtml": "<div class='image-component'><img src=\"http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755671640074525301.svg\" alt=\"A traditional web application\" width=\"442\" height=\"141\" style=\"width: 100%; height: auto;\" /></div>\n<div class='image-component'><img src=\"http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755671642659383946.svg\" alt=\"An advanced web application process\" width=\"811\" height=\"311\" style=\"width: 100%; height: auto;\" /></div>\n<div class='image-component'><img src=\"http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755671646955306446.svg\" alt=\"A typical Jamstack application\" width=\"441\" height=\"141\" style=\"width: 100%; height: auto;\" /></div>", "summary": {"titleUpdated": true, "title": "What is Jamstack?", "description": "Get introduced to Jamstack and the advantages of using it.", "tags": []}, "type": "Lesson"}, {"id": "6116248318640128", "title": "Markup and Static Site Generators", "is_preview": false, "slug": "markup-and-static-site-generators", "text": "", "mdHtml": "", "summary": {"titleUpdated": true, "title": "Markup and Statitc Site Generators", "description": "Learn about markup and static site generators.", "tags": []}, "type": "Lesson"}, {"id": "5741369400492032", "title": "JavaScript and APIs", "is_preview": false, "slug": "javascript-and-apis", "text": "```javascript\nimport fetch from \"node-fetch\";\n\nconst endpointUrl = new URL('https://www.thecocktaildb.com/api/json/v1/1/random.php');\nconst options = {\n  method: 'GET'\n};\n\nasync function fetchQuotes() {\n  try {\n    const response = await fetch(endpointUrl, options);\n    printResponse(response);\n  } catch (error) {\n    printError(error);\n  }\n}\n\nfetchQuotes();\n```", "mdHtml": "<div class='image-component'>\n```javascript\nimport fetch from \"node-fetch\";\n\nconst endpointUrl = new URL('https://www.thecocktaildb.com/api/json/v1/1/random.php');\nconst options = {\n  method: 'GET'\n};\n\nasync function fetchQuotes() {\n  try {\n    const response = await fetch(endpointUrl, options);\n    printResponse(response);\n  } catch (error) {\n    printError(error);\n  }\n}\n\nfetchQuotes();\n```\n</div>", "summary": {"titleUpdated": true, "title": "", "description": "Learn about APIs and JavaScript, as well as how they fit into Jamstack.", "tags": []}, "type": "Lesson"}, {"id": "6127532795756544", "title": "Headless Content Management Systems", "is_preview": false, "slug": "headless-content-management-systems", "text": "<img src=\"http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755671651850456998.svg\" alt=\"A traditional CMS\" width=\"497\" height=\"141\" style=\"width: 100%; height: auto;\" />\n<img src=\"http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755671654985342070.svg\" alt=\"A headless CMS\" width=\"431\" height=\"221\" style=\"width: 100%; height: auto;\" />", "mdHtml": "<div class='image-component'><img src=\"http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755671651850456998.svg\" alt=\"A traditional CMS\" width=\"497\" height=\"141\" style=\"width: 100%; height: auto;\" /></div>\n<div class='image-component'><img src=\"http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755671654985342070.svg\" alt=\"A headless CMS\" width=\"431\" height=\"221\" style=\"width: 100%; height: auto;\" /></div>", "summary": {"titleUpdated": true, "title": "Headless Content Management System", "description": "Learn about the headless C<PERSON> and get an introduction to <PERSON><PERSON><PERSON>.", "tags": []}, "type": "Lesson"}, {"id": "6572031392088064", "title": "Quiz: Getting Started with Jam<PERSON>ck", "is_preview": false, "slug": "quiz-getting-started-with-jamstack", "text": "", "mdHtml": "", "summary": {"titleUpdated": true, "title": "Quiz - Introduction to <PERSON><PERSON><PERSON>", "description": "Test your knowledge of Jamstack.", "tags": []}, "type": "Quiz", "questions": [{"questionText": "Which option is not a benefit of using Jamstack? ", "questionOptions": [{"text": "Security", "id": "SyMQcm8JQptKL1Q0m_yj6", "correct": false, "explanation": {"mdText": "Jamstack improves security by removing multiple moving parts, which makes it harder to attack.", "mdHtml": "<p>Jamstack improves security by removing multiple moving parts, which makes it harder to attack.</p>\n"}, "mdHtml": "<p>Security</p>\n"}, {"text": "Performance", "id": "IRx4o8X1Kt7Mm95r6i0RE", "correct": false, "explanation": {"mdText": "Jamstack improves performance by having pre-rendered pages ready to be served.", "mdHtml": "<p>Jamstack improves performance by having pre-rendered pages ready to be served.</p>\n"}, "mdHtml": "<p>Performance</p>\n"}, {"text": "Maintainability", "id": "Pn1XHZYBiSVYe1ogBXSTH", "correct": false, "explanation": {"mdText": "Jamstack improves maintainability by reducing the pieces of infrastructure. This means that lesser parts have to be maintained in running an application.", "mdHtml": "<p>Jamstack improves maintainability by reducing the pieces of infrastructure. This means that lesser parts have to be maintained in running an application.</p>\n"}, "mdHtml": "<p>Maintainability</p>\n"}, {"text": "Usability ", "id": "TKPAByMX0g4RQrqMevuz4", "correct": true, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>Usability</p>\n"}], "id": "0_question_0", "questionTextHtml": "<p>Which option is not a benefit of using Jamstack?</p>\n"}, {"id": "_i9AFvGGoL6jeuwOlhPG_", "questionText": "What is the role of Next.js in a Jamstack application?", "questionOptions": [{"text": "Next.js is the front-end technology being used in this course, and it's the markup part of a Jamstack application.\n", "id": "D6EaM9FFglWN8RiXWmDkf", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>Next.js is the front-end technology being used in this course, and it’s the markup part of a Jamstack application.</p>\n"}, {"text": "We can call an API endpoint from a Next.js application, making this the JavaScript and API part of a Jamstack application.", "id": "jnZE374CJFxlxOnBh9txE", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>We can call an API endpoint from a Next.js application, making this the JavaScript and API part of a Jamstack application.</p>\n"}, {"text": "All of the above", "id": "sj2jPvIEDL8TQpEIuvLj-", "correct": true, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>All of the above</p>\n"}], "multipleAnswers": false, "questionTextHtml": "<p>What is the role of Next.js in a Jamstack application?</p>\n"}, {"id": "ACMYwOFKq6BpaNr5iePcT", "questionText": "**(True or False)** The static web pages from the early days of the internet can be considered an example of a Jamstack application.\n", "questionOptions": [{"text": "True", "id": "ONElP4smRjMjLqtNYmavF", "correct": true, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>True</p>\n"}, {"text": "False", "id": "K1Oy1JJgTuE1vOJ2bVLxZ", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>False</p>\n"}], "multipleAnswers": false, "questionTextHtml": "<p><strong>(True or False)</strong> The static web pages from the early days of the internet can be considered an example of a Jamstack application.</p>\n"}, {"id": "boejmeTWOvDnrPJ9208F1", "questionText": "**(True or False)** Any Jamstack application must include all three core components: JavaScript, API, and markup.\n", "questionOptions": [{"text": "True", "id": "WqgPh0_AAWyFjGsKKGOg-", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>True</p>\n"}, {"text": "False", "id": "SIRN5QQ0ligJqHp_RQB7u", "correct": true, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>False</p>\n"}], "multipleAnswers": false, "questionTextHtml": "<p><strong>(True or False)</strong> Any Jamstack application must include all three core components: JavaScript, API, and markup.</p>\n"}, {"id": "3tMm3nA20xCU6eKJC0DTS", "questionText": "Which content type can Jamstack be used for?", "questionOptions": [{"text": "Static content because it uses pre-rendered files.", "id": "LD6tBPnv4HVectjbLe_aB", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>Static content because it uses pre-rendered files.</p>\n"}, {"text": "Dynamic content because it uses API calls.", "id": "4BqoV7DuQM_jNEGrStyW0", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>Dynamic content because it uses API calls.</p>\n"}, {"text": "All of the above", "id": "fGEaYK-5kBLkaoWQy8xdY", "correct": true, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>All of the above</p>\n"}], "multipleAnswers": false, "questionTextHtml": "<p>Which content type can Jamstack be used for?</p>\n"}, {"id": "F8R5-Lko1ra2XU5ALiT0n", "questionText": "Which library or framework is required for a Jamstack application?", "questionOptions": [{"text": "React", "id": "MqEHkQ4qwEIeYVweqykWC", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>React</p>\n"}, {"text": "Next.js", "id": "kg_j3MnyourspStAse1SR", "correct": true, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>Next.js</p>\n"}, {"text": "Vue.js", "id": "k9rj-g3i7tqrjsFFXUcWE", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>Vue.js</p>\n"}, {"text": "There is no requirement to use a specific JavaScript framework or library.", "id": "0evFOiQiU162ZHucof8JZ", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>There is no requirement to use a specific JavaScript framework or library.</p>\n"}], "multipleAnswers": false, "questionTextHtml": "<p>Which library or framework is required for a Jamstack application?</p>\n"}, {"id": "60TJtSyOxur99i-MsDENG", "questionText": "Which statement is incorrect regarding APIs? ", "questionOptions": [{"text": "It allows different software systems to communicate with each other. For instance, we can consume data from a weather API in our Next.js application.", "id": "AUBe4PV5bb-yW2wL4dU6n", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>It allows different software systems to communicate with each other. For instance, we can consume data from a weather API in our Next.js application.</p>\n"}, {"text": "It allows for programmatic access to data exposed by an application.", "id": "8RKPRd2JwX9KHcjnQYTRj", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>It allows for programmatic access to data exposed by an application.</p>\n"}, {"text": "APIs allow for a Jamstack application to access and consume dynamic content.", "id": "774jXJZ6gn7zCI4wv_wT-", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>APIs allow for a Jamstack application to access and consume dynamic content.</p>\n"}, {"text": "None of the above", "id": "VDhv-G7aZy5YFYcKZs8A-", "correct": true, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>None of the above</p>\n"}], "multipleAnswers": false, "questionTextHtml": "<p>Which statement is incorrect regarding APIs?</p>\n"}, {"id": "vn-U0aUiBASs6290O-YKU", "questionText": "Which statement is incorrect regarding content management systems (CMS)?", "questionOptions": [{"text": "They allow for the creation and management of digital content. ", "id": "iuVNJDxtOwa2k_yTyoGG2", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>They allow for the creation and management of digital content.</p>\n"}, {"text": "Traditional systems include both the content and presentation layers.", "id": "Nv1r9A0EdjYK_vo9yPE_6", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>Traditional systems include both the content and presentation layers.</p>\n"}, {"text": "WordPress is an example of a headless CMS.", "id": "tbUIjsMl_wJiVtyV0NWSx", "correct": true, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>WordPress is an example of a headless CMS.</p>\n"}, {"text": "None of the above", "id": "4gVXwR8Wq7jkRnrZZnwrR", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>None of the above</p>\n"}], "multipleAnswers": false, "questionTextHtml": "<p>Which statement is incorrect regarding content management systems (CMS)?</p>\n"}]}]}, {"id": "xcr85ytu5", "title": "Basics of Next.js", "summary": "Explore the essentials of building with Next.js, from routing and dynamic routing to asset management.", "pages": [{"id": "****************", "title": "Introduction to Next.js", "is_preview": true, "slug": "introduction-to-nextjs", "text": "", "mdHtml": "", "summary": {"titleUpdated": true, "title": "Introduction to Next.js", "description": "Get introduced to Next.js and its project directory structure. ", "tags": []}, "type": "Lesson"}, {"id": "****************", "title": "Routing", "is_preview": false, "slug": "routing", "text": "- The `/pages/index.js` file opens on the `/` path.\n- The `/pages/about.js` file opens on the `/about` path.\n- The `/pages/collections/index.js` file corresponds to the `/collections` path.\n- The `/pages/collections/collections1.js` file corresponds to the `/collections/collection1` path.", "mdHtml": "<ul>\n<li>The <code>/pages/index.js</code> file opens on the <code>/</code> path.</li>\n<li>The <code>/pages/about.js</code> file opens on the <code>/about</code> path.</li>\n<li>The <code>/pages/collections/index.js</code> file corresponds to the <code>/collections</code> path.</li>\n<li>The <code>/pages/collections/collections1.js</code> file corresponds to the <code>/collections/collection1</code> path.</li>\n</ul>", "summary": {"titleUpdated": true, "title": "Routing ", "description": "Learn how to create routes and navigate to different pages in our application.", "tags": []}, "type": "Lesson"}, {"id": "****************", "title": "Dynamic Routing", "is_preview": false, "slug": "dynamic-routing", "text": "```javascript\nimport { useRouter } from 'next/router'\n\nexport default function Collection() {\n  const router = useRouter()\n  const {collection} = router.query;\n\n  // Use collection for API call\n  const data = await fetch (`URL/${collection}`)\n\n  return (\n    // Use the data from the API to display into HTML\n    <div>\n      {data}\n    </div>\n  )\n}\n\n```", "mdHtml": "<div class='image-component'>\n```javascript\nimport { useRouter } from 'next/router'\n\nexport default function Collection() {\n  const router = useRouter()\n  const {collection} = router.query;\n\n  // Use collection for API call\n  const data = await fetch (`URL/${collection}`)\n\n  return (\n    // Use the data from the API to display into HTML\n    <div>\n      {data}\n    </div>\n  )\n}\n\n```\n</div>", "summary": {"titleUpdated": true, "title": "Dynamic Routing", "description": "Learn how to create dynamic routes in a Next.js application.", "tags": []}, "type": "Lesson"}, {"id": "****************", "title": "Challenge: Routing", "is_preview": false, "slug": "challenge-routing", "text": "- Use the `Link` component of `next/link` to navigate between routes.\n- Remember to import and add the header to all of the pages. \n- You can also use the `useRouter` hook from `next/router` to handle navigation.", "mdHtml": "<ul>\n<li>Use the <code>Link</code> component of <code>next/link</code> to navigate between routes.</li>\n<li>Remember to import and add the header to all of the pages.</li>\n<li>You can also use the <code>useRouter</code> hook from <code>next/router</code> to handle navigation.</li>\n</ul>", "summary": {"titleUpdated": true, "title": "Challenge: Routing", "description": "This challenge that will help you get hands-on experience with Next.js.", "tags": []}, "type": "Lesson"}, {"id": "****************", "title": "Solution: Routing", "is_preview": false, "slug": "solution-routing", "text": "", "mdHtml": "", "summary": {"titleUpdated": true, "title": "Solution: Routing", "description": "Let's discuss the solution to the Routing challenge.", "tags": []}, "type": "Lesson"}, {"id": "****************", "title": "Assets and Metadata", "is_preview": false, "slug": "assets-and-metadata", "text": "```javascript\n/** @type {import('next').NextConfig} */\nconst nextConfig = {\n  reactStrictMode: true,\n  swcMinify: true,\n  \n  images: {\n    domains: ['www.domain-name1.com', 'www.domain-name2.com']\n  }\n}\n\nmodule.exports = nextConfig\n\n```", "mdHtml": "<div class='image-component'>\n```javascript\n/** @type {import('next').NextConfig} */\nconst nextConfig = {\n  reactStrictMode: true,\n  swcMinify: true,\n  \n  images: {\n    domains: ['www.domain-name1.com', 'www.domain-name2.com']\n  }\n}\n\nmodule.exports = nextConfig\n\n```\n</div>", "summary": {"titleUpdated": true, "title": "Assets and Metadata", "description": "Learn how to add and use assets in our applications and add metadata to a page.", "tags": []}, "type": "Lesson"}, {"id": "****************", "title": "Quiz: Basics of Next.js", "is_preview": false, "slug": "quiz-basics-of-nextjs", "text": "", "mdHtml": "", "summary": {"titleUpdated": true, "title": "Quiz -  Basics of Next.js", "description": "Test your knowledge of Next.js.", "tags": []}, "type": "Quiz", "questions": [{"questionText": "Which Next.js feature makes it an excellent candidate for building Jamstack applications? ", "questionOptions": [{"text": "File-system based routing", "id": "K10z71uFlbzujeRRkCk70", "correct": false, "explanation": {"mdText": "This is just a way to make routing easier when using React components. ", "mdHtml": "<p>This is just a way to make routing easier when using React components.</p>\n"}, "mdHtml": "<p>File-system based routing</p>\n"}, {"text": "Static site ", "id": "Fcw_F-l0bvre6-u-psQWZ", "correct": true, "explanation": {"mdText": "Static site generation lets us prebuild pages and deploy them for faster delivery.", "mdHtml": "<p>Static site generation lets us prebuild pages and deploy them for faster delivery.</p>\n"}, "mdHtml": "<p>Static site</p>\n"}, {"text": "Server-side rendering", "id": "fyjzUFvlh67ZzPdyHnDRn", "correct": false, "explanation": {"mdText": "Rendering on the server is not a good option for creating prebuilt pages because it will always create a new page on request.", "mdHtml": "<p>Rendering on the server is not a good option for creating prebuilt pages because it will always create a new page on request.</p>\n"}, "mdHtml": "<p>Server-side rendering</p>\n"}, {"text": "Separate development and production environments", "id": "_W_6yqr3AoiVrF0QhdcZH", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>Separate development and production environments</p>\n"}], "id": "0_question_0", "questionTextHtml": "<p>Which Next.js feature makes it an excellent candidate for building Jamstack applications?</p>\n"}, {"id": "XHkyRUA70u39vsu6Fjfq7", "questionText": "**(True or False)** When compared to React, Next.js provides out-of-the-box support for routing. ", "questionOptions": [{"text": "True", "id": "hH2erjDCHh2ufq7oT5aO1", "correct": true, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>True</p>\n"}, {"text": "False", "id": "IkIzDdr0tsTDngGgg2FIJ", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>False</p>\n"}], "multipleAnswers": false, "questionTextHtml": "<p><strong>(True or False)</strong> When compared to React, Next.js provides out-of-the-box support for routing.</p>\n"}, {"id": "NTC0hh4jYn5eG-3-CIBnD", "questionText": "What is the recommended way to bootstrap a new Next.js application? ", "questionOptions": [{"text": "We can use create-react-app to create a React application first. Once routing is added to it, it becomes a Next.js application.", "id": "jH6eBgi27DLHqEawsVmW0", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>We can use create-react-app to create a React application first. Once routing is added to it, it becomes a Next.js application.</p>\n"}, {"text": "We can use the `npx create-next-app@latest` command to create a new application.\n", "id": "D7xAT-BXo0Qiugysha3lz", "correct": true, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>We can use the <code>npx create-next-app@latest</code> command to create a new application.</p>\n"}, {"text": "We can use the `npx create-react-app` command to create a new React application and then install the Next.js plugin.", "id": "hfIgtx9l5XyjBKMdalJRI", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>We can use the <code>npx create-react-app</code> command to create a new React application and then install the Next.js plugin.</p>\n"}], "multipleAnswers": false, "questionTextHtml": "<p>What is the recommended way to bootstrap a new Next.js application?</p>\n"}, {"id": "vrK8cQhF9uHTLhni21rE7", "questionText": "**(True or False)** After bootstrapping a new Next.js application using `create-next-app`, is it required to use the `/styles` folder for storing our project's `.css` files?", "questionOptions": [{"text": "True", "id": "_VK9qjdWFXfXpsN2VQMG2", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>True</p>\n"}, {"text": "False", "id": "87fTnt5D91r-jMXpBtxlW", "correct": true, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>False</p>\n"}], "multipleAnswers": false, "questionTextHtml": "<p><strong>(True or False)</strong> After bootstrapping a new Next.js application using <code>create-next-app</code>, is it required to use the <code>/styles</code> folder for storing our project’s <code>.css</code> files?</p>\n"}, {"id": "Z5xjhPfnubzoy63MaDjEb", "questionText": "Once we've bootstrapped a new Next.js application using `create-next-app`, where can we find public resources such as images and logos used in the application? ", "questionOptions": [{"text": "The `/styles` folder", "id": "E1A9LIUTYYSOXHZaIrguD", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>The <code>/styles</code> folder</p>\n"}, {"text": "The `/pages` folder", "id": "KhbxZP3i9jNutqo6tzxwS", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>The <code>/pages</code> folder</p>\n"}, {"text": "The `/external` folder", "id": "hBqEAXLRtpOzYnao8XuNI", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>The <code>/external</code> folder</p>\n"}, {"text": "None of the above", "id": "hE40voKdy561TZJlphFe3", "correct": true, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>None of the above</p>\n"}], "multipleAnswers": false, "questionTextHtml": "<p>Once we’ve bootstrapped a new Next.js application using <code>create-next-app</code>, where can we find public resources such as images and logos used in the application?</p>\n"}, {"id": "fqPblQIFJe9nE9l2TbSvZ", "questionText": "After we've bootstrapped a new Next.js application using `create-next-app`, which folder can we use for routing?", "questionOptions": [{"text": "The `/styles` folder", "id": "xpxtAld-A-pnlh1jZuzkn", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>The <code>/styles</code> folder</p>\n"}, {"text": "The `/pages` folder", "id": "-N5fzXYJLBc2z9dwzswZ9", "correct": true, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>The <code>/pages</code> folder</p>\n"}, {"text": "The `/external` folder", "id": "ycqnabOSAlqCX6Udlwkq4", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>The <code>/external</code> folder</p>\n"}, {"text": "The `/public` folder", "id": "DgKaaPqhBVaAmPChgLRKn", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>The <code>/public</code> folder</p>\n"}], "multipleAnswers": false, "questionTextHtml": "<p>After we’ve bootstrapped a new Next.js application using <code>create-next-app</code>, which folder can we use for routing?</p>\n"}, {"id": "e0UTaPX4dID333jSaI4Yw", "questionText": "We know that Next.js automatically creates a route for all the files present in the `/pages` directory. What would be the route for a file available at the path `/pages/demo/index.js`?", "questionOptions": [{"text": "`/pages`", "id": "hKmcXbzjlSxQTbtcjk6Q1", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p><code>/pages</code></p>\n"}, {"text": "`/`", "id": "yM9uYbXCgTG0k7EZZ8S0u", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p><code>/</code></p>\n"}, {"text": "`/demo`", "id": "cxPn-WxLEQty8Q-_WrjDQ", "correct": true, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p><code>/demo</code></p>\n"}, {"text": "`/demo/index`", "id": "SmijzjFypKkGC_0P4uvF8", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p><code>/demo/index</code></p>\n"}], "multipleAnswers": false, "questionTextHtml": "<p>We know that Next.js automatically creates a route for all the files present in the <code>/pages</code> directory. What would be the route for a file available at the path <code>/pages/demo/index.js</code>?</p>\n"}, {"id": "Y6O-RwgM0t9B2BZfbbZi3", "questionText": "We know that Next.js automatically handles routing inside the `/pages` directory. What would be the route for a file available at the path `/pages/demo.js`?\n", "questionOptions": [{"text": "`/demo`", "id": "CfuyS1m5zoZQ_xrE9MOOX", "correct": true, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p><code>/demo</code></p>\n"}, {"text": "`/pages/demo`", "id": "s5T0z8kq9nExkMiYJ7HHj", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p><code>/pages/demo</code></p>\n"}, {"text": "`/pages/demo/index1`", "id": "DrHbLZggvRTiqj-xIETlK", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p><code>/pages/demo/index1</code></p>\n"}], "multipleAnswers": false, "questionTextHtml": "<p>We know that Next.js automatically handles routing inside the <code>/pages</code> directory. What would be the route for a file available at the path <code>/pages/demo.js</code>?</p>\n"}]}]}, {"id": "5nirt0r84", "title": "Static Site Generation with Next.js", "summary": "Grasp the fundamentals of static site generation with Next.js, including pre-rendering and dynamic routes.", "pages": [{"id": "****************", "title": "Pre-rendering", "is_preview": false, "slug": "pre-rendering", "text": "![How a pre-rendered website loads](http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755671670718108679.svg)\n![A regular web page](http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755671672263502964.svg)\n![How a statically generated web page works](http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755671674781259506.svg)\n![How a server-side rendered page works](http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755671676982468939.svg)", "mdHtml": "<div class='image-component'>![How a pre-rendered website loads](http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755671670718108679.svg)</div>\n<div class='image-component'>![A regular web page](http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755671672263502964.svg)</div>\n<div class='image-component'>![How a statically generated web page works](http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755671674781259506.svg)</div>\n<div class='image-component'>![How a server-side rendered page works](http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755671676982468939.svg)</div>", "summary": {"titleUpdated": true, "title": "Pre-rendering", "description": "Learn about pre-rendering and how it makes Next.js unique. ", "tags": []}, "type": "Lesson"}, {"id": "4952746728095744", "title": "Static Generation", "is_preview": false, "slug": "static-generation", "text": "```javascript\n// Page function will take the props as a parameter from the getStaticProps function\nexport default function Home(props){\n  \n  return (\n    <div>\n      {props}\n    </div>\n  )\n}\n\nexport async function getStaticProps(){\n  // Get data from an external source\n  const data = await fetch(URL)\n\n  // Return the data fetched from the external source\n  return {\n    props: {\n      data\n    }\n  }  \n}\n```", "mdHtml": "<div class='image-component'>\n```javascript\n// Page function will take the props as a parameter from the getStaticProps function\nexport default function Home(props){\n  \n  return (\n    <div>\n      {props}\n    </div>\n  )\n}\n\nexport async function getStaticProps(){\n  // Get data from an external source\n  const data = await fetch(URL)\n\n  // Return the data fetched from the external source\n  return {\n    props: {\n      data\n    }\n  }  \n}\n```\n</div>", "summary": {"titleUpdated": true, "title": "Static Generation", "description": "Learn how to create a static website with Next.js.", "tags": []}, "type": "Lesson"}, {"id": "6392875518787584", "title": "Static Generation of Dynamic Routes", "is_preview": false, "slug": "static-generation-of-dynamic-routes", "text": "```javascript\nexport async function getStaticPaths(){\n  // return the data fetched from the external source\n  return {\n    paths: [\n      {\n        params: {\n          categoryName: 'Category1'  \n        },\n        params: {\n          categoryName: 'Category2'  \n        },\n        // ...\n      }\n    ],\n    fallback: false,\n  }\n}\n\nexport async function getStaticProps( {params} ){\n  // params comes from the getStaticPath function\n  const data = await fetch(URL)\n\n  return {\n    props: {\n      data\n    }\n  }\n}\n```", "mdHtml": "<div class='image-component'>\n```javascript\nexport async function getStaticPaths(){\n  // return the data fetched from the external source\n  return {\n    paths: [\n      {\n        params: {\n          categoryName: 'Category1'  \n        },\n        params: {\n          categoryName: 'Category2'  \n        },\n        // ...\n      }\n    ],\n    fallback: false,\n  }\n}\n\nexport async function getStaticProps( {params} ){\n  // params comes from the getStaticPath function\n  const data = await fetch(URL)\n\n  return {\n    props: {\n      data\n    }\n  }\n}\n```\n</div>", "summary": {"titleUpdated": true, "title": "Static Generation of Dynamic Routes", "description": "Learn how to statically generate dynamically routed files in Next.js.\n ", "tags": []}, "type": "Lesson"}, {"id": "5500884122599424", "title": "The fallback Option", "is_preview": false, "slug": "the-fallback-option", "text": "```javascript\nexport async function getStaticPaths() {\n    return {\n      paths: [\n        {params: {\n            meal: '52772'\n        }},\n        {params: {\n            meal: '53050'\n        }},\n      ],\n      fallback: 'blocking',\n    }\n}\n```", "mdHtml": "<div class='image-component'>\n```javascript\nexport async function getStaticPaths() {\n    return {\n      paths: [\n        {params: {\n            meal: '52772'\n        }},\n        {params: {\n            meal: '53050'\n        }},\n      ],\n      fallback: 'blocking',\n    }\n}\n```\n</div>", "summary": {"titleUpdated": true, "title": "The \"fallback\" Option", "description": "Learn what the fallback option of the getStaticPaths method does.", "tags": []}, "type": "Lesson"}, {"id": "6116869134352384", "title": "Adding the Search Functionality", "is_preview": false, "slug": "adding-the-search-functionality", "text": "```javascript\nconst [options, setOptions] = useState([])\n\n// Button click handler\nconst getSearchResults = async () => {\n  const data = await (\n    await fetch(`https://www.themealdb.com/api/json/v1/1/search.php?s=${searchVal}`)\n  ).json()\n  setOptions(data.meals)\n}\n\n// Enter press handler\nconst enterPressHandler = (event) => {\n  if (event.key === 'Enter') {\n    event.preventDefault()\n    getSearchResults()\n  }\n}\n```\n\n\n```javascript\nexport default function Home() {\n  const [searchVal, setSearchVal] = useState(\"\")\n\n    return(\n      <> \n        <Head>\n            <title>Home | Recipes App</title>\n            <meta name=\"description\" content=\"Homepage of the Recipes application\" />\n          </Head>\n          <Header />\n          <main className={styles.main}>\n          <div className={styles.searchbarDiv}>\n            <TextField fullWidth\n              placeholder='Search...'\n              onChange={(event) => {setSearchVal(event.target.value)}}\n              onKeyDown={enterPressHandler}\n              InputProps= {{\n                endAdornment:<IconButton onClick={getSearchResults} ><SearchOutlined /></IconButton>\n              }}\n            />\n            </div>\n          </main>\n      </>\n    )\n}\n```\n\n\n```javascript\n<div className={styles.searchResult}>\n    {options == null ? <div>No results found</div> : <MealCard meals={options} />}\n</div>\n```", "mdHtml": "<div class='image-component'>\n```javascript\nconst [options, setOptions] = useState([])\n\n// Button click handler\nconst getSearchResults = async () => {\n  const data = await (\n    await fetch(`https://www.themealdb.com/api/json/v1/1/search.php?s=${searchVal}`)\n  ).json()\n  setOptions(data.meals)\n}\n\n// Enter press handler\nconst enterPressHandler = (event) => {\n  if (event.key === 'Enter') {\n    event.preventDefault()\n    getSearchResults()\n  }\n}\n```\n</div>\n<div class='image-component'>\n```javascript\nexport default function Home() {\n  const [searchVal, setSearchVal] = useState(\"\")\n\n    return(\n      <> \n        <Head>\n            <title>Home | Recipes App</title>\n            <meta name=\"description\" content=\"Homepage of the Recipes application\" />\n          </Head>\n          <Header />\n          <main className={styles.main}>\n          <div className={styles.searchbarDiv}>\n            <TextField fullWidth\n              placeholder='Search...'\n              onChange={(event) => {setSearchVal(event.target.value)}}\n              onKeyDown={enterPressHandler}\n              InputProps= {{\n                endAdornment:<IconButton onClick={getSearchResults} ><SearchOutlined /></IconButton>\n              }}\n            />\n            </div>\n          </main>\n      </>\n    )\n}\n```\n</div>\n<div class='image-component'>\n```javascript\n<div className={styles.searchResult}>\n    {options == null ? <div>No results found</div> : <MealCard meals={options} />}\n</div>\n```\n</div>", "summary": {"titleUpdated": true, "title": "Adding Search Functionality", "description": "Learn how to add the search functionality in Next.js.", "tags": []}, "type": "Lesson"}, {"id": "6675176773386240", "title": "Deploying a Static Site on Netlify", "is_preview": false, "slug": "deploying-a-static-site-on-netlify", "text": "**[CanvasAnimation 图表]** - 交互式图表内容\n\n\n**[CanvasAnimation 图表]** - 交互式图表内容\n\n\n**[CanvasAnimation 图表]** - 交互式图表内容", "mdHtml": "<div class='image-component'>\n**[CanvasAnimation 图表]** - 交互式图表内容\n</div>\n<div class='image-component'>\n**[CanvasAnimation 图表]** - 交互式图表内容\n</div>\n<div class='image-component'>\n**[CanvasAnimation 图表]** - 交互式图表内容\n</div>", "summary": {"titleUpdated": true, "title": "", "description": "Learn how to generate a static Next.js website and deploy it on Netlify. ", "tags": []}, "type": "Lesson"}, {"id": "4926982964117504", "title": "Server-Side Rendering", "is_preview": false, "slug": "server-side-rendering", "text": "", "mdHtml": "", "summary": {"titleUpdated": true, "title": "Server-Side Rendering", "description": "Learn about server-side rendering and how to use it in a Next.js application.", "tags": []}, "type": "Lesson"}, {"id": "4603613914857472", "title": "Quiz: Static Site Generation with Next.js", "is_preview": false, "slug": "quiz-static-site-generation-with-nextjs", "text": "", "mdHtml": "", "summary": {"titleUpdated": true, "title": "Quiz - Static Site Generation with Next.js", "description": "Test your knowledge of static site generation.", "tags": []}, "type": "Quiz", "questions": [{"questionText": "Which statement is true about pre-rendering? \n", "questionOptions": [{"text": "Pre-rendering is the process of generating the HTML of a page before it has to be loaded on the browser. ", "id": "Lo-anoAxOzeoNjIszE082", "correct": true, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>Pre-rendering is the process of generating the HTML of a page before it has to be loaded on the browser.</p>\n"}, {"text": "Pre-rendering improves performance.", "id": "KvkgVrLzyl6xbf4j9mcGZ", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>Pre-rendering improves performance.</p>\n"}], "id": "0_question_0", "questionTextHtml": "<p>Which statement is true about pre-rendering?</p>\n"}, {"id": "ezQNezi5diVC8ICBE7ocC", "questionText": "Consider the case of a web application with some static content such as \"Contact Us\" and \"About\" pages. Which type of pre-rendering is more suitable in this case? \n", "questionOptions": [{"text": "Static generation", "id": "DGXUh23-mi8luWSQIk1rv", "correct": true, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>Static generation</p>\n"}, {"text": "Server-side rendering", "id": "qOmEZbxBL4LTSYZTnNVfd", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>Server-side rendering</p>\n"}, {"text": "Pre-rendering ", "id": "28wNGUB9ntRZ-4h88Ih8c", "correct": false, "explanation": {"mdText": "Pre-rendering is not needed because the content is only static.", "mdHtml": "<p>Pre-rendering is not needed because the content is only static.</p>\n"}, "mdHtml": "<p>Pre-rendering</p>\n"}], "multipleAnswers": false, "questionTextHtml": "<p>Consider the case of a web application with some static content such as “Contact Us” and “About” pages. Which type of pre-rendering is more suitable in this case?</p>\n"}, {"id": "qJK0V_CImZ7qz3Qu96SMf", "questionText": "Consider the case of a web application that displays the latest stock prices from an exchange. Which type of pre-rendering is more suitable in this case? \n", "questionOptions": [{"text": "Static generation", "id": "zT8iwMwreqLwLEKEjqgSo", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>Static generation</p>\n"}, {"text": "Server-side rendering", "id": "m0JKSdPbsN7ysGpumleBu", "correct": true, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>Server-side rendering</p>\n"}, {"text": "Pre-rendering", "id": "PcX7cTPOoUken3ZLHqwOZ", "correct": false, "explanation": {"mdText": "Pre-rendering is not needed because the content is rapidly changing.", "mdHtml": "<p>Pre-rendering is not needed because the content is rapidly changing.</p>\n"}, "mdHtml": "<p>Pre-rendering</p>\n"}], "multipleAnswers": false, "questionTextHtml": "<p>Consider the case of a web application that displays the latest stock prices from an exchange. Which type of pre-rendering is more suitable in this case?</p>\n"}, {"id": "lJY_CzRPuo576abDvksbF", "questionText": "**(True or False)** Static generation can only be used for pages that don't use data from an external source.  ", "questionOptions": [{"text": "True", "id": "kk_uAadxcbn0xK8AE-ISQ", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>True</p>\n"}, {"text": "False", "id": "zfNB_9hWKhJtLYK8Y7SpG", "correct": true, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>False</p>\n"}], "multipleAnswers": false, "questionTextHtml": "<p><strong>(True or False)</strong> Static generation can only be used for pages that don’t use data from an external source.</p>\n"}, {"id": "nwhRuPkQNymVQSbc2Zga4", "questionText": "**(Select all that apply.)** What is the output of the `getStaticPaths()` function?", "questionOptions": [{"text": "This is a user-defined function, so we can decide what is returned.", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "id": "0_question_4_option_0", "mdHtml": "<p>This is a user-defined function, so we can decide what is returned.</p>\n"}, {"text": "The `getStaticPaths()` function returns a list of all possible paths for that dynamic route so that the pages can be pre-rendered during build time.", "correct": true, "explanation": {"mdText": "", "mdHtml": ""}, "id": "0_question_4_option_1", "mdHtml": "<p>The <code>getStaticPaths()</code> function returns a list of all possible paths for that dynamic route so that the pages can be pre-rendered during build time.</p>\n"}, {"text": "This returns the `fallback` option, which can have one of the following three values: `true`, `false`, or `blocking`.", "correct": true, "explanation": {"mdText": "", "mdHtml": ""}, "id": "0_question_4_option_2", "mdHtml": "<p>This returns the <code>fallback</code> option, which can have one of the following three values: <code>true</code>, <code>false</code>, or <code>blocking</code>.</p>\n"}], "multipleAnswers": true, "questionTextHtml": "<p><strong>(Select all that apply.)</strong> What is the output of the <code>getStaticPaths()</code> function?</p>\n"}, {"id": "ng2GzP7lsISwb2r_r4C9n", "questionText": "Consider the case of a path that isn't returned by the `getStaticPaths` function. What would be the behavior for path access with the `fallback` option set to `true`? ", "questionOptions": [{"text": "On the first request, a 404 page would be rendered and on every subsequent request for that page, the page generated on the first request is served as if it was pre-rendered during build time.\n", "id": "QuwEUW-q0G2S_FcIh67bt", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>On the first request, a 404 page would be rendered and on every subsequent request for that page, the page generated on the first request is served as if it was pre-rendered during build time.</p>\n"}, {"text": "The first request will take a long time to process as the page is being rendered at the server. On every subsequent request for that page, the page generated on the first request will be served as if it was pre-rendered during build time.", "id": "0JeNfGirxrgwIqKzKoUsm", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>The first request will take a long time to process as the page is being rendered at the server. On every subsequent request for that page, the page generated on the first request will be served as if it was pre-rendered during build time.</p>\n"}, {"text": "None of the above", "id": "CgUJaQ0cpNuWbw4L6_0ay", "correct": true, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>None of the above</p>\n"}], "multipleAnswers": false, "questionTextHtml": "<p>Consider the case of a path that isn’t returned by the <code>getStaticPaths</code> function. What would be the behavior for path access with the <code>fallback</code> option set to <code>true</code>?</p>\n"}, {"id": "hOf7ZYga_vWtGmUQAG4dE", "questionText": "Consider the case of a path not returned by the `getStaticPaths` function. What would be the behavior for path access with the fallback option set to `blocking`?\n", "questionOptions": [{"text": "On the first request, a 404 page would be rendered and on every subsequent request for that page, the page generated on the first request is served as if it was pre-rendered during build time.", "id": "zhlsbWsZdQ6gzRsj_WJCi", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>On the first request, a 404 page would be rendered and on every subsequent request for that page, the page generated on the first request is served as if it was pre-rendered during build time.</p>\n"}, {"text": "The first request takes a long time to process because the page is being rendered at the server. On every subsequent request for that page, the page generated on the first request is served as if it was pre-rendered during build time.", "id": "6VeHFlQX4PWWACJGKAFZ8", "correct": true, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>The first request takes a long time to process because the page is being rendered at the server. On every subsequent request for that page, the page generated on the first request is served as if it was pre-rendered during build time.</p>\n"}, {"text": "None of the above", "id": "Om-36gBpOHqmoqLnvW2js", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>None of the above</p>\n"}], "multipleAnswers": false, "questionTextHtml": "<p>Consider the case of a path not returned by the <code>getStaticPaths</code> function. What would be the behavior for path access with the fallback option set to <code>blocking</code>?</p>\n"}]}]}, {"id": "fz24xggai", "title": "Using <PERSON><PERSON><PERSON> as a Headless CMS", "summary": "Solve problems in setting up and managing content with Strapi CMS for Next.js apps.", "pages": [{"id": "6719686517719040", "title": "Getting Started with <PERSON><PERSON><PERSON>", "is_preview": true, "slug": "getting-started-with-strapi", "text": "**[CanvasAnimation 图表]** - 交互式图表内容", "mdHtml": "<div class='image-component'>\n**[CanvasAnimation 图表]** - 交互式图表内容\n</div>", "summary": {"titleUpdated": true, "title": "Getting Started with <PERSON><PERSON><PERSON>", "description": "Get introduced to St<PERSON><PERSON> and learn how to create an admin user that will be able to manage the application.", "tags": []}, "type": "Lesson"}, {"id": "5809211671052288", "title": "The Content Manager and Content-Types", "is_preview": false, "slug": "the-content-manager-and-content-types", "text": "![The Strapi Content Manager](http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755671698234064999.svg)\n![Collection types in Strapi](http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755671700594222361.svg)\n\n**[CanvasAnimation 图表]** - 交互式图表内容\n\n![The single type view](http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755671704285616848.svg)", "mdHtml": "<div class='image-component'>![The Strapi Content Manager](http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755671698234064999.svg)</div>\n<div class='image-component'>![Collection types in Strapi](http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755671700594222361.svg)</div>\n<div class='image-component'>\n**[CanvasAnimation 图表]** - 交互式图表内容\n</div>\n<div class='image-component'>![The single type view](http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755671704285616848.svg)</div>", "summary": {"titleUpdated": true, "title": "The Content Manager and Content Types", "description": "Learn about Strapi's content-types and how to manage these collections with the Content Manager.", "tags": []}, "type": "Lesson"}, {"id": "6661006124384256", "title": "Strapi's Content-Type Builder", "is_preview": false, "slug": "strapis-content-type-builder", "text": "**[CanvasAnimation 图表]** - 交互式图表内容\n\n\n**[CanvasAnimation 图表]** - 交互式图表内容", "mdHtml": "<div class='image-component'>\n**[CanvasAnimation 图表]** - 交互式图表内容\n</div>\n<div class='image-component'>\n**[CanvasAnimation 图表]** - 交互式图表内容\n</div>", "summary": {"titleUpdated": true, "title": "Strapi Content-Type Builder", "description": "Learn about content-types and the Content-type Builder that Strapi provides. ", "tags": []}, "type": "Lesson"}, {"id": "6527848149155840", "title": "Relational Fields in Strapi", "is_preview": false, "slug": "relational-fields-in-strapi", "text": "![Pop-up for creating a relation in a collection](http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755671711065562616.svg)\n\n**[CanvasAnimation 图表]** - 交互式图表内容", "mdHtml": "<div class='image-component'>![Pop-up for creating a relation in a collection](http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755671711065562616.svg)</div>\n<div class='image-component'>\n**[CanvasAnimation 图表]** - 交互式图表内容\n</div>", "summary": {"titleUpdated": true, "title": "Relational Fields in Strapi", "description": "Learn how to manage relational fields with the Strapi admin panel.", "tags": []}, "type": "Lesson"}, {"id": "5254260520648704", "title": "Using the Strapi API Endpoints", "is_preview": false, "slug": "using-the-strapi-api-endpoints", "text": "```javascript\n{\n  \"kind\": \"collectionType\",\n  \"collectionName\": \"recipes\",\n  \"info\": {\n    \"singularName\": \"recipe\",\n    \"pluralName\": \"recipes\",\n    \"displayName\": \"Recipe\"\n  },\n  \"options\": {\n    \"draftAndPublish\": true\n  },\n  \"pluginOptions\": {},\n  \"attributes\": {\n    \"strMeal\": {\n      \"type\": \"string\"\n    },\n    \"strThumb\": {\n      \"type\": \"string\"\n    },\n    \"strArea\": {\n      \"type\": \"string\"\n    },\n    \"strCategory\": {\n      \"type\": \"string\"\n    },\n    \"strInstructions\": {\n      \"type\": \"text\"\n    },\n    \"strTags\": {\n      \"type\": \"string\"\n    },\n    \"strIngredient1\": {\n      \"type\": \"string\"\n    },\n    \"strIngredient2\": {\n      \"type\": \"string\"\n    },\n    \"strIngredient3\": {\n      \"type\": \"string\"\n    },\n    \"strIngredient4\": {\n      \"type\": \"string\"\n    },\n    \"strIngredient5\": {\n      \"type\": \"string\"\n    },\n    \"strIngredient6\": {\n      \"type\": \"string\"\n    },\n    \"strIngredient7\": {\n      \"type\": \"string\"\n    },\n    \"strIngredient8\": {\n      \"type\": \"string\"\n    },\n    \"strIngredient9\": {\n      \"type\": \"string\"\n    },\n    \"strIngredient10\": {\n      \"type\": \"string\"\n    },\n    \"strIngredient11\": {\n      \"type\": \"string\"\n    },\n    \"strIngredient12\": {\n      \"type\": \"string\"\n    },\n    \"strIngredient13\": {\n      \"type\": \"string\"\n    },\n    \"strIngredient14\": {\n      \"type\": \"string\"\n    },\n    \"strIngredient15\": {\n      \"type\": \"string\"\n    },\n    \"strIngredient16\": {\n      \"type\": \"string\"\n    },\n    \"strIngredient17\": {\n      \"type\": \"string\"\n    },\n    \"strIngredient18\": {\n      \"type\": \"string\"\n    },\n    \"strIngredient19\": {\n      \"type\": \"string\"\n    },\n    \"strIngredient20\": {\n      \"type\": \"string\"\n    },\n    \"strMeasure1\": {\n      \"type\": \"string\"\n    },\n    \"strMeasure2\": {\n      \"type\": \"string\"\n    },\n    \"strMeasure3\": {\n      \"type\": \"string\"\n    },\n    \"strMeasure4\": {\n      \"type\": \"string\"\n    },\n    \"strMeasure5\": {\n      \"type\": \"string\"\n    },\n    \"strMeasure6\": {\n      \"type\": \"string\"\n    },\n    \"strMeasure7\": {\n      \"type\": \"string\"\n    },\n    \"strMeasure8\": {\n      \"type\": \"string\"\n    },\n    \"strMeasure9\": {\n      \"type\": \"string\"\n    },\n    \"strMeasure10\": {\n      \"type\": \"string\"\n    },\n    \"strMeasure11\": {\n      \"type\": \"string\"\n    },\n    \"strMeasure12\": {\n      \"type\": \"string\"\n    },\n    \"strMeasure13\": {\n      \"type\": \"string\"\n    },\n    \"strMeasure14\": {\n      \"type\": \"string\"\n    },\n    \"strMeasure15\": {\n      \"type\": \"string\"\n    },\n    \"strMeasure16\": {\n      \"type\": \"string\"\n    },\n    \"strMeasure17\": {\n      \"type\": \"string\"\n    },\n    \"strMeasure18\": {\n      \"type\": \"string\"\n    },\n    \"strMeasure19\": {\n      \"type\": \"string\"\n    },\n    \"strMeasure20\": {\n      \"type\": \"string\"\n    },\n    \"idMeal\": {\n      \"type\": \"integer\",\n      \"unique\": true,\n      \"required\": true\n    }\n  }\n}\n\n```\n\n\n```javascript\n'use strict';\n\n/**\n * recipe router\n */\n\nconst { createCoreRouter } = require('@strapi/strapi').factories;\n\nmodule.exports = createCoreRouter('api::recipe.recipe');\n\n```\n\n\n```javascript\n'use strict';\n\n/**\n * recipe controller\n */\n\nconst { createCoreController } = require('@strapi/strapi').factories;\n\nmodule.exports = createCoreController('api::recipe.recipe');\n\n```\n\n\n```javascript\n'use strict';\n\n/**\n * recipe service\n */\n\nconst { createCoreService } = require('@strapi/strapi').factories;\n\nmodule.exports = createCoreService('api::recipe.recipe');\n\n```\n\n\n**[CanvasAnimation 图表]** - 交互式图表内容", "mdHtml": "<div class='image-component'>\n```javascript\n{\n  \"kind\": \"collectionType\",\n  \"collectionName\": \"recipes\",\n  \"info\": {\n    \"singularName\": \"recipe\",\n    \"pluralName\": \"recipes\",\n    \"displayName\": \"Recipe\"\n  },\n  \"options\": {\n    \"draftAndPublish\": true\n  },\n  \"pluginOptions\": {},\n  \"attributes\": {\n    \"strMeal\": {\n      \"type\": \"string\"\n    },\n    \"strThumb\": {\n      \"type\": \"string\"\n    },\n    \"strArea\": {\n      \"type\": \"string\"\n    },\n    \"strCategory\": {\n      \"type\": \"string\"\n    },\n    \"strInstructions\": {\n      \"type\": \"text\"\n    },\n    \"strTags\": {\n      \"type\": \"string\"\n    },\n    \"strIngredient1\": {\n      \"type\": \"string\"\n    },\n    \"strIngredient2\": {\n      \"type\": \"string\"\n    },\n    \"strIngredient3\": {\n      \"type\": \"string\"\n    },\n    \"strIngredient4\": {\n      \"type\": \"string\"\n    },\n    \"strIngredient5\": {\n      \"type\": \"string\"\n    },\n    \"strIngredient6\": {\n      \"type\": \"string\"\n    },\n    \"strIngredient7\": {\n      \"type\": \"string\"\n    },\n    \"strIngredient8\": {\n      \"type\": \"string\"\n    },\n    \"strIngredient9\": {\n      \"type\": \"string\"\n    },\n    \"strIngredient10\": {\n      \"type\": \"string\"\n    },\n    \"strIngredient11\": {\n      \"type\": \"string\"\n    },\n    \"strIngredient12\": {\n      \"type\": \"string\"\n    },\n    \"strIngredient13\": {\n      \"type\": \"string\"\n    },\n    \"strIngredient14\": {\n      \"type\": \"string\"\n    },\n    \"strIngredient15\": {\n      \"type\": \"string\"\n    },\n    \"strIngredient16\": {\n      \"type\": \"string\"\n    },\n    \"strIngredient17\": {\n      \"type\": \"string\"\n    },\n    \"strIngredient18\": {\n      \"type\": \"string\"\n    },\n    \"strIngredient19\": {\n      \"type\": \"string\"\n    },\n    \"strIngredient20\": {\n      \"type\": \"string\"\n    },\n    \"strMeasure1\": {\n      \"type\": \"string\"\n    },\n    \"strMeasure2\": {\n      \"type\": \"string\"\n    },\n    \"strMeasure3\": {\n      \"type\": \"string\"\n    },\n    \"strMeasure4\": {\n      \"type\": \"string\"\n    },\n    \"strMeasure5\": {\n      \"type\": \"string\"\n    },\n    \"strMeasure6\": {\n      \"type\": \"string\"\n    },\n    \"strMeasure7\": {\n      \"type\": \"string\"\n    },\n    \"strMeasure8\": {\n      \"type\": \"string\"\n    },\n    \"strMeasure9\": {\n      \"type\": \"string\"\n    },\n    \"strMeasure10\": {\n      \"type\": \"string\"\n    },\n    \"strMeasure11\": {\n      \"type\": \"string\"\n    },\n    \"strMeasure12\": {\n      \"type\": \"string\"\n    },\n    \"strMeasure13\": {\n      \"type\": \"string\"\n    },\n    \"strMeasure14\": {\n      \"type\": \"string\"\n    },\n    \"strMeasure15\": {\n      \"type\": \"string\"\n    },\n    \"strMeasure16\": {\n      \"type\": \"string\"\n    },\n    \"strMeasure17\": {\n      \"type\": \"string\"\n    },\n    \"strMeasure18\": {\n      \"type\": \"string\"\n    },\n    \"strMeasure19\": {\n      \"type\": \"string\"\n    },\n    \"strMeasure20\": {\n      \"type\": \"string\"\n    },\n    \"idMeal\": {\n      \"type\": \"integer\",\n      \"unique\": true,\n      \"required\": true\n    }\n  }\n}\n\n```\n</div>\n<div class='image-component'>\n```javascript\n'use strict';\n\n/**\n * recipe router\n */\n\nconst { createCoreRouter } = require('@strapi/strapi').factories;\n\nmodule.exports = createCoreRouter('api::recipe.recipe');\n\n```\n</div>\n<div class='image-component'>\n```javascript\n'use strict';\n\n/**\n * recipe controller\n */\n\nconst { createCoreController } = require('@strapi/strapi').factories;\n\nmodule.exports = createCoreController('api::recipe.recipe');\n\n```\n</div>\n<div class='image-component'>\n```javascript\n'use strict';\n\n/**\n * recipe service\n */\n\nconst { createCoreService } = require('@strapi/strapi').factories;\n\nmodule.exports = createCoreService('api::recipe.recipe');\n\n```\n</div>\n<div class='image-component'>\n**[CanvasAnimation 图表]** - 交互式图表内容\n</div>", "summary": {"titleUpdated": true, "title": "Using Strapi's APIs", "description": "Learn how to consume the API endpoints that <PERSON><PERSON><PERSON> creates with each collection. ", "tags": []}, "type": "Lesson"}, {"id": "6465089717927936", "title": "Quiz: Using <PERSON><PERSON><PERSON> as a Headless CMS", "is_preview": false, "slug": "quiz-using-strapi-as-a-headless-cms", "text": "", "mdHtml": "", "summary": {"titleUpdated": false, "title": "Quiz - <PERSON><PERSON><PERSON>", "description": "Test your knowledge of Strapi and headless content management systems.", "tags": []}, "type": "Quiz", "questions": [{"questionText": "Why are some CMS called \"headless\" CMS?", "questionOptions": [{"text": "Because they don’t have any front-end technology related to it.", "id": "DXy-cyao3QxGnsqo97qI0", "correct": true, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>Because they don’t have any front-end technology related to it.</p>\n"}, {"text": "Because they provide a complete web development package.", "id": "6GPbUFv2lLwdYFjOX2rEW", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>Because they provide a complete web development package.</p>\n"}, {"text": "None of the above", "id": "gDhty1xISsMn_FHcR1BUb", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>None of the above</p>\n"}], "id": "0_question_0", "questionTextHtml": "<p>Why are some CMS called “headless” CMS?</p>\n"}, {"id": "x-IYbdEm7sSlv32YHXetH", "questionText": "**(Select all that apply.)** Which option is a content-type in Strapi?", "questionOptions": [{"text": "Collection type", "id": "3UlrKBeRQhOlsZ80k-pvC", "correct": true, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>Collection type</p>\n"}, {"text": "TypeBuilder", "id": "hHd1TQ1j095yVs2JFaDao", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>TypeBuilder</p>\n"}, {"text": "Single type", "id": "wkwWAQtMkRO3up4YOXVmj", "correct": true, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>Single type</p>\n"}, {"text": "All of the above", "id": "P0Dn8YUf5Z6cpP6Dm8Wbq", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>All of the above</p>\n"}], "multipleAnswers": true, "questionTextHtml": "<p><strong>(Select all that apply.)</strong> Which option is a content-type in Strapi?</p>\n"}, {"id": "0y978CndJV7GkWJKNISH2", "questionText": "Which statement about relational fields in Strapi is correct? ", "questionOptions": [{"text": "We can define any relationship between any two content-types. ", "id": "08ajY5GG2UhTXIVWahDm8", "correct": true, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>We can define any relationship between any two content-types.</p>\n"}, {"text": "We can only define one-way relationships between single types, that is, content-type A has one content-type B.", "id": "evuwR35d4OirTbz1m_tCt", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>We can only define one-way relationships between single types, that is, content-type A has one content-type B.</p>\n"}, {"text": "We can only define one-to-one relationships between single types, that is, content-type A has and belongs to one content-type B.", "id": "6mgaxabAj6LIB2ZVv0Ygs", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>We can only define one-to-one relationships between single types, that is, content-type A has and belongs to one content-type B.</p>\n"}, {"text": "None of the above", "id": "G5KdXYtXUk6Al1NrpsO-F", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>None of the above</p>\n"}], "multipleAnswers": false, "questionTextHtml": "<p>Which statement about relational fields in Strapi is correct?</p>\n"}, {"id": "J6qmIpoR_nQrlNP4lTPlJ", "questionText": "We learned that, whenever we create a collection type in our Content-type Builder, <PERSON><PERSON><PERSON> automatically generates a folder with the name of that collection. What is that's folder's path? ", "questionOptions": [{"text": "It can be anywhere in the project directory. The exact path doesn't matter because the endpoints are also automatically generated.", "id": "aE2bNfPTCIKgnxZ9OBEUj", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>It can be anywhere in the project directory. The exact path doesn’t matter because the endpoints are also automatically generated.</p>\n"}, {"text": "This folder is created in the `/api` subfolder, under the content-types folder.", "id": "t-4b7TYDqevSVmx2GSf1K", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>This folder is created in the <code>/api</code> subfolder, under the content-types folder.</p>\n"}, {"text": "None of the above", "id": "MwkruynQzt98K3paX4ynF", "correct": true, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>None of the above</p>\n"}], "multipleAnswers": false, "questionTextHtml": "<p>We learned that, whenever we create a collection type in our Content-type Builder, <PERSON><PERSON><PERSON> automatically generates a folder with the name of that collection. What is that’s folder’s path?</p>\n"}, {"id": "F3_iVjrwggshM64Vbc7iL", "questionText": "We learned that <PERSON><PERSON><PERSON> automatically generates a folder with the name of the user-created collection type. It also creates some subfolders, including the `content-types` folder with a file named `schema.json`.  Based on this, which statement is true? ", "questionOptions": [{"text": "The name of the file is always `schema.json`.", "id": "nQ2hH6sI1WARkoPCAGilO", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>The name of the file is always <code>schema.json</code>.</p>\n"}, {"text": "It contains all the information <PERSON><PERSON><PERSON> requires about the content that is saved in this content-type.", "id": "A9Sii2qcwzTZn68wZRjD2", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>It contains all the information <PERSON><PERSON><PERSON> requires about the content that is saved in this content-type.</p>\n"}, {"text": "If needed, we can directly edit the file without having to use the Strapi admin panel.", "id": "Su4JoLiGBU_kR47-wyWzP", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>If needed, we can directly edit the file without having to use the Strapi admin panel.</p>\n"}, {"text": "All of the above", "id": "f2gwiv_i1k8HeCNwqPgK7", "correct": true, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>All of the above</p>\n"}], "multipleAnswers": false, "questionTextHtml": "<p>We learned that <PERSON><PERSON><PERSON> automatically generates a folder with the name of the user-created collection type. It also creates some subfolders, including the <code>content-types</code> folder with a file named <code>schema.json</code>.  Based on this, which statement is true?</p>\n"}, {"id": "jKij455CLA2lMcpExK0LG", "questionText": "We learned that <PERSON><PERSON><PERSON> automatically generates a folder with the name of user-created collection type. It also creates some subfolders, including the `routes` folder.  Which option below is one of the default routes? \n", "questionOptions": [{"text": "The `create` route: This is a `POST` route with the path `/api/:pluralApiId`. It creates a new entry in the collection.\n", "id": "uExoH16J-7Q0IdddWZrXr", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>The <code>create</code> route: This is a <code>POST</code> route with the path <code>/api/:pluralApiId</code>. It creates a new entry in the collection.</p>\n"}, {"text": "The `update` route: This is a `PUT` route with the path `/api/:pluralApiId/:id`. It updates the entry that has the ID mentioned in the route.\n", "id": "qowG2aNPDZj4Ov0M6nhM6", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>The <code>update</code> route: This is a <code>PUT</code> route with the path <code>/api/:pluralApiId/:id</code>. It updates the entry that has the ID mentioned in the route.</p>\n"}, {"text": "The `findOne` route: This is a `GET` route, with the path  `/api/:pluralApiId/:id`. It gets the entry that has the ID mention in the route.\n", "id": "yiqgxOb6z3lSxFRNlRt34", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>The <code>findOne</code> route: This is a <code>GET</code> route, with the path  <code>/api/:pluralApiId/:id</code>. It gets the entry that has the ID mention in the route.</p>\n"}, {"text": "The `delete` route: This is a `DELETE` route with the path `/api/:pluralApiId/:id`. It deletes the entry that has the ID mentioned in the route.", "id": "8IeWWTL5YinHN1B4J5lVa", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>The <code>delete</code> route: This is a <code>DELETE</code> route with the path <code>/api/:pluralApiId/:id</code>. It deletes the entry that has the ID mentioned in the route.</p>\n"}, {"text": "All of the above", "id": "s3gJMsI9u5hnfYHwmRcvs", "correct": true, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>All of the above</p>\n"}], "multipleAnswers": false, "questionTextHtml": "<p>We learned that <PERSON><PERSON><PERSON> automatically generates a folder with the name of user-created collection type. It also creates some subfolders, including the <code>routes</code> folder.  Which option below is one of the default routes?</p>\n"}, {"id": "3cc5mnO0xfG3mtxvuIFag", "questionText": "We learned that Strapi automatically generates a folder with the name of user-created collection type. It also creates some subfolders, including the `controllers` folder.  Which statement is true for Strapi controllers?", "questionOptions": [{"text": "Each route is assigned to a controller function, which are called actions.", "id": "iT7nk_AqUEOyvwZNm5PVb", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>Each route is assigned to a controller function, which are called actions.</p>\n"}, {"text": "Strapi automatically generates the controller functions for the automatically generated routes.", "id": "7PO1mbVGmHTC1KLlEx7Wv", "correct": true, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p><PERSON><PERSON><PERSON> automatically generates the controller functions for the automatically generated routes.</p>\n"}, {"text": "We can't override the already existing functions to change the controller functions of the default routes.", "id": "dRXOl1nQL1jqtAmmTqFUL", "correct": false, "explanation": {"mdText": "", "mdHtml": ""}, "mdHtml": "<p>We can’t override the already existing functions to change the controller functions of the default routes.</p>\n"}], "multipleAnswers": false, "questionTextHtml": "<p>We learned that Strapi automatically generates a folder with the name of user-created collection type. It also creates some subfolders, including the <code>controllers</code> folder.  Which statement is true for Strapi controllers?</p>\n"}]}]}, {"id": "95r1sks8k", "title": "Building the Complete Application", "summary": "Follow the process of integrating a Next.js frontend with a Strapi backend for a meal management app.", "pages": [{"id": "6028668829958144", "title": "Application Requirements", "is_preview": false, "slug": "application-requirements", "text": "![The home page of our front-end application](http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755671717322277028.svg)\n![The Content-type Builder page of our Strapi application](http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755671719441990221.svg)", "mdHtml": "<div class='image-component'>![The home page of our front-end application](http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755671717322277028.svg)</div>\n<div class='image-component'>![The Content-type Builder page of our Strapi application](http://nas.corp.jancsitech.net:9000/pointer-center/images/general/202508/1755671719441990221.svg)</div>", "summary": {"titleUpdated": true, "title": "Application Requirements", "description": "Let's discuss what we've created up until now and what we'll do to connect the frontend and the backend to build the complete application.", "tags": []}, "type": "Lesson"}, {"id": "4917979977613312", "title": "Creating the Authentication Frontend", "is_preview": false, "slug": "creating-the-authentication-frontend", "text": "```javascript\nimport { AUTH_TOKEN } from \"./constants\";\n    \nexport const getToken = () => {\n    if (typeof window !== 'undefined') return localStorage.getItem(AUTH_TOKEN);\n    return null;\n};\n\nexport const setToken = (token) => {\n  if (token) {\n    if (typeof window !== 'undefined') localStorage.setItem(AUTH_TOKEN, token);\n  }\n};\n\nexport const removeToken = () => {\n    if (typeof window !== 'undefined') localStorage.removeItem(AUTH_TOKEN);\n};\n\nexport const checkLogin = () => {\n    if (getToken) return true;\n    return false;\n}\n```\n\n\n```javascript\nimport { createContext, useContext } from \"react\";\n    \nexport const AuthContext = createContext({\n    user: undefined,\n    isLoading: false,\n    setUser: () => {},\n});\n\nexport const useAuthContext = () => useContext(AuthContext);\n\n```\n\n\n```javascript\nimport React, { useState } from \"react\";\nimport { AuthContext } from \"../context/AuthContext\";\nimport { API, BEARER } from \"../utils/constants\";\nimport { useEffect } from \"react\";\nimport { getToken } from \"../utils/helpers\";\n\nconst AuthProvider = ({ children }) => {\n    const [userData, setUserData] = useState();\n    const [isLoading, setIsLoading] = useState(false);\n  \n    const authToken = getToken();\n  \n    const fetchLoggedInUser = async (token) => {\n      setIsLoading(true);\n      try {\n        const response = await fetch(`${API}/users/me`, {\n          headers: { Authorization: `${BEARER} ${token}` },\n        });\n        const data = await response.json();\n  \n        setUserData(data);\n      } catch (error) {\n        console.error(error);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n  \n    const handleUser = (user) => {\n      setUserData(user);\n    };\n  \n    useEffect(() => {\n      if (authToken) {\n        fetchLoggedInUser(authToken);\n      }\n    }, [authToken]);\n  \n    return (\n      <AuthContext.Provider\n        value={{ user: userData, setUser: handleUser, isLoading }}\n      >\n        {children}\n      </AuthContext.Provider>\n    );\n};\n  \nexport default AuthProvider;\n```\n\n\n```javascript\nimport '../styles/globals.css'\nimport AuthProvider from '../components/AuthProvider'\nfunction MyApp({ Component, pageProps }) {\n  return <AuthProvider><Component {...pageProps} /></AuthProvider>\n}\n\nexport default MyApp\n\n```\n\n\n```javascript\nimport Head from 'next/head';\nimport React, {useState} from 'react'\nimport styles from '../../styles/Home.module.css'\nimport AuthHeader from '../../components/AuthHeader';\nimport { Button, TextField } from '@mui/material';\n\n\nconst Register = () => {\n    const [username, setUsername] = useState('');\n    const [password, setPassword] = useState('');\n    const [email, setEmail] = useState('');\n\n    const handleRegister = () => {\n        console.log('User email:' + email + 'username:' + username)\n    }\n    return (\n        <>\n            <Head>\n                <title>Register</title>\n            </Head>\n            <AuthHeader page='register' />\n            <main className={styles.main}>\n                <h3>Welcome back, please login to your account</h3>\n                <div className={styles.searchbarDiv}>\n                    <TextField fullWidth\n                    placeholder='Username'\n                    onChange={(e)=> {setUsername(e.target.value)}}\n                    /> <br /> <br />\n\n                    <TextField fullWidth\n                    placeholder='Email'\n                    onChange={(e) => {setEmail(e.target.value)}}\n                    /> <br /> <br />\n\n                    <TextField fullWidth\n                    placeholder='Password'\n                    type='password'\n                    onChange={(e) => {setPassword(e.target.value)}}\n                    /> <br /> <br />\n                </div>\n                <Button variant='contained' onClick={handleRegister}>Register</Button>\n            </main>\n        </>\n    )\n}\n\nexport default Register;\n\n```\n\n\n```javascript\nimport Head from 'next/head';\nimport React, {useState} from 'react'\nimport styles from '../../styles/Home.module.css'\nimport AuthHeader from '../../components/AuthHeader';\nimport { Button, CircularProgress, TextField } from '@mui/material';\nimport { useRouter } from 'next/router';\nimport { useAuthContext } from '../../context/AuthContext';\nimport { API } from '../../utils/constants';\nimport { setToken } from '../../utils/helpers';\n\nconst Register = () => {\n    const router = useRouter();\n    const { setUser } = useAuthContext();\n    \n    const [isLoading, setIsLoading] = useState(false);\n    const [username, setUsername] = useState('');\n    const [password, setPassword] = useState('');\n    const [email, setEmail] = useState('');\n\n    const handleRegister = async () => {\n        const values = {\n            username: username,\n            password: password,\n            email: email,\n        }\n        setIsLoading(true);\n        try {\n          const response = await fetch(`${API}/auth/local/register`, {\n            method: \"POST\",\n            headers: {\n              \"Content-Type\": \"application/json\",\n            },\n            body: JSON.stringify(values),\n          });\n    \n          const data = await response.json();\n          if (data?.error) {\n            throw data?.error;\n          } else {\n            // set the token\n            setToken(data.jwt);\n    \n            // set the user\n            setUser(data.user);\n    \n            router.push('/')\n          }\n        } catch (error) {\n          console.error(error);\n        } finally {\n          setIsLoading(false);\n        }\n    };\n\n    return (\n        <>\n            <Head>\n                <title>Register</title>\n            </Head>\n            <AuthHeader page='register' />\n            <main className={styles.main}>\n                <h3>Welcome back, please login to your account</h3>\n                <div className={styles.searchbarDiv}>\n                    <TextField fullWidth\n                    placeholder='Username'\n                    onChange={(e)=> {setUsername(e.target.value)}}\n                    /> <br /> <br />\n\n                    <TextField fullWidth\n                    placeholder='Email'\n                    onChange={(e) => {setEmail(e.target.value)}}\n                    /> <br /> <br />\n\n                    <TextField fullWidth\n                    placeholder='Password'\n                    type='password'\n                    onChange={(e) => {setPassword(e.target.value)}}\n                    /> <br /> <br />\n                </div>\n                <Button disabled={isLoading} variant='contained' onClick={handleRegister}>Register</Button>\n\n                {\n                    isLoading ? <CircularProgress /> : null\n                }\n            </main>\n        </>\n    )\n}\n\nexport default Register;\n\n```\n\n\n```javascript\nimport Head from 'next/head';\nimport React from 'react'\nimport styles from '../../styles/Home.module.css'\nimport AuthHeader from '../../components/AuthHeader';\nimport { Button, TextField } from '@mui/material';\n\nconst Login = () => {\n  const [email, setEmail] = React.useState('')\n  const [password, setPassword] = React.useState('')\n\n  const loginHandler = async () => {\n    console.log(\"logging in user: \" + email)\n  };\n\n  return (\n    <>\n        <Head>\n          <title>Login</title>\n        </Head>\n        <AuthHeader page='login' />\n        <main className={styles.main}>\n          <h3>Welcome back, please login to your account</h3>\n          <div className={styles.searchbarDiv}>\n            <TextField fullWidth\n              placeholder='Email'\n              onChange={(e) => setEmail(e.target.value)}\n            /> <br /> <br />\n            <TextField fullWidth\n              placeholder='password'\n              type='password'\n              onChange={(e) => setPassword(e.target.value)}\n            /> <br /> <br />      \n            </div>\n            <Button variant='contained' onClick={loginHandler}>Login</Button>\n        </main>\n    </>\n  )\n}\nexport default Login;\n\n```\n\n\n```javascript\nimport Head from 'next/head';\nimport React from 'react'\nimport styles from '../../styles/Home.module.css'\nimport AuthHeader from '../../components/AuthHeader';\nimport { Button, TextField } from '@mui/material';\nimport { useRouter } from 'next/router';\nimport { useAuthContext } from '../../context/AuthContext';\nimport { API } from '../../utils/constants';\nimport { setToken } from '../../utils/helpers';\n\nconst Login = () => {\n  const [email, setEmail] = React.useState('')\n  const [password, setPassword] = React.useState('')\n  const [isLoading, setIsLoading] = React.useState(false);\n  \n  const router = useRouter();\n\n  const { setUser } = useAuthContext();\n\n\n  const loginHandler = async () => {\n    setIsLoading(true);\n    try {\n      const values = {\n        identifier: email,\n        password: password,\n      };\n      const response = await fetch(`${API}/auth/local`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify(values),\n      });\n\n      const data = await response.json();\n      if (data?.error) {\n        throw data?.error;\n      } else {\n        // set the token\n        setToken(data.jwt);\n\n        // set the user\n        setUser(data.user);\n\n        router.push('/')\n      }\n    } catch (error) {\n      console.error(error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <>\n        <Head>\n          <title>Login</title>\n        </Head>\n        <AuthHeader page='login' />\n        <main className={styles.main}>\n          <h3>Welcome back, please login to your account</h3>\n          <div className={styles.searchbarDiv}>\n            <TextField fullWidth\n              placeholder='Email'\n              onChange={(e) => setEmail(e.target.value)}\n            /> <br /> <br />\n            <TextField fullWidth\n              placeholder='password'\n              type='password'\n              onChange={(e) => setPassword(e.target.value)}\n            /> <br /> <br />\n            </div>\n            <Button variant='contained' onClick={loginHandler}>Login</Button>\n            {\n              isLoading ? <CircularProgress /> : null\n            }\n        </main>\n    </>\n  )\n}\nexport default Login;\n\n```", "mdHtml": "<div class='image-component'>\n```javascript\nimport { AUTH_TOKEN } from \"./constants\";\n    \nexport const getToken = () => {\n    if (typeof window !== 'undefined') return localStorage.getItem(AUTH_TOKEN);\n    return null;\n};\n\nexport const setToken = (token) => {\n  if (token) {\n    if (typeof window !== 'undefined') localStorage.setItem(AUTH_TOKEN, token);\n  }\n};\n\nexport const removeToken = () => {\n    if (typeof window !== 'undefined') localStorage.removeItem(AUTH_TOKEN);\n};\n\nexport const checkLogin = () => {\n    if (getToken) return true;\n    return false;\n}\n```\n</div>\n<div class='image-component'>\n```javascript\nimport { createContext, useContext } from \"react\";\n    \nexport const AuthContext = createContext({\n    user: undefined,\n    isLoading: false,\n    setUser: () => {},\n});\n\nexport const useAuthContext = () => useContext(AuthContext);\n\n```\n</div>\n<div class='image-component'>\n```javascript\nimport React, { useState } from \"react\";\nimport { AuthContext } from \"../context/AuthContext\";\nimport { API, BEARER } from \"../utils/constants\";\nimport { useEffect } from \"react\";\nimport { getToken } from \"../utils/helpers\";\n\nconst AuthProvider = ({ children }) => {\n    const [userData, setUserData] = useState();\n    const [isLoading, setIsLoading] = useState(false);\n  \n    const authToken = getToken();\n  \n    const fetchLoggedInUser = async (token) => {\n      setIsLoading(true);\n      try {\n        const response = await fetch(`${API}/users/me`, {\n          headers: { Authorization: `${BEARER} ${token}` },\n        });\n        const data = await response.json();\n  \n        setUserData(data);\n      } catch (error) {\n        console.error(error);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n  \n    const handleUser = (user) => {\n      setUserData(user);\n    };\n  \n    useEffect(() => {\n      if (authToken) {\n        fetchLoggedInUser(authToken);\n      }\n    }, [authToken]);\n  \n    return (\n      <AuthContext.Provider\n        value={{ user: userData, setUser: handleUser, isLoading }}\n      >\n        {children}\n      </AuthContext.Provider>\n    );\n};\n  \nexport default AuthProvider;\n```\n</div>\n<div class='image-component'>\n```javascript\nimport '../styles/globals.css'\nimport AuthProvider from '../components/AuthProvider'\nfunction MyApp({ Component, pageProps }) {\n  return <AuthProvider><Component {...pageProps} /></AuthProvider>\n}\n\nexport default MyApp\n\n```\n</div>\n<div class='image-component'>\n```javascript\nimport Head from 'next/head';\nimport React, {useState} from 'react'\nimport styles from '../../styles/Home.module.css'\nimport AuthHeader from '../../components/AuthHeader';\nimport { Button, TextField } from '@mui/material';\n\n\nconst Register = () => {\n    const [username, setUsername] = useState('');\n    const [password, setPassword] = useState('');\n    const [email, setEmail] = useState('');\n\n    const handleRegister = () => {\n        console.log('User email:' + email + 'username:' + username)\n    }\n    return (\n        <>\n            <Head>\n                <title>Register</title>\n            </Head>\n            <AuthHeader page='register' />\n            <main className={styles.main}>\n                <h3>Welcome back, please login to your account</h3>\n                <div className={styles.searchbarDiv}>\n                    <TextField fullWidth\n                    placeholder='Username'\n                    onChange={(e)=> {setUsername(e.target.value)}}\n                    /> <br /> <br />\n\n                    <TextField fullWidth\n                    placeholder='Email'\n                    onChange={(e) => {setEmail(e.target.value)}}\n                    /> <br /> <br />\n\n                    <TextField fullWidth\n                    placeholder='Password'\n                    type='password'\n                    onChange={(e) => {setPassword(e.target.value)}}\n                    /> <br /> <br />\n                </div>\n                <Button variant='contained' onClick={handleRegister}>Register</Button>\n            </main>\n        </>\n    )\n}\n\nexport default Register;\n\n```\n</div>\n<div class='image-component'>\n```javascript\nimport Head from 'next/head';\nimport React, {useState} from 'react'\nimport styles from '../../styles/Home.module.css'\nimport AuthHeader from '../../components/AuthHeader';\nimport { Button, CircularProgress, TextField } from '@mui/material';\nimport { useRouter } from 'next/router';\nimport { useAuthContext } from '../../context/AuthContext';\nimport { API } from '../../utils/constants';\nimport { setToken } from '../../utils/helpers';\n\nconst Register = () => {\n    const router = useRouter();\n    const { setUser } = useAuthContext();\n    \n    const [isLoading, setIsLoading] = useState(false);\n    const [username, setUsername] = useState('');\n    const [password, setPassword] = useState('');\n    const [email, setEmail] = useState('');\n\n    const handleRegister = async () => {\n        const values = {\n            username: username,\n            password: password,\n            email: email,\n        }\n        setIsLoading(true);\n        try {\n          const response = await fetch(`${API}/auth/local/register`, {\n            method: \"POST\",\n            headers: {\n              \"Content-Type\": \"application/json\",\n            },\n            body: JSON.stringify(values),\n          });\n    \n          const data = await response.json();\n          if (data?.error) {\n            throw data?.error;\n          } else {\n            // set the token\n            setToken(data.jwt);\n    \n            // set the user\n            setUser(data.user);\n    \n            router.push('/')\n          }\n        } catch (error) {\n          console.error(error);\n        } finally {\n          setIsLoading(false);\n        }\n    };\n\n    return (\n        <>\n            <Head>\n                <title>Register</title>\n            </Head>\n            <AuthHeader page='register' />\n            <main className={styles.main}>\n                <h3>Welcome back, please login to your account</h3>\n                <div className={styles.searchbarDiv}>\n                    <TextField fullWidth\n                    placeholder='Username'\n                    onChange={(e)=> {setUsername(e.target.value)}}\n                    /> <br /> <br />\n\n                    <TextField fullWidth\n                    placeholder='Email'\n                    onChange={(e) => {setEmail(e.target.value)}}\n                    /> <br /> <br />\n\n                    <TextField fullWidth\n                    placeholder='Password'\n                    type='password'\n                    onChange={(e) => {setPassword(e.target.value)}}\n                    /> <br /> <br />\n                </div>\n                <Button disabled={isLoading} variant='contained' onClick={handleRegister}>Register</Button>\n\n                {\n                    isLoading ? <CircularProgress /> : null\n                }\n            </main>\n        </>\n    )\n}\n\nexport default Register;\n\n```\n</div>\n<div class='image-component'>\n```javascript\nimport Head from 'next/head';\nimport React from 'react'\nimport styles from '../../styles/Home.module.css'\nimport AuthHeader from '../../components/AuthHeader';\nimport { Button, TextField } from '@mui/material';\n\nconst Login = () => {\n  const [email, setEmail] = React.useState('')\n  const [password, setPassword] = React.useState('')\n\n  const loginHandler = async () => {\n    console.log(\"logging in user: \" + email)\n  };\n\n  return (\n    <>\n        <Head>\n          <title>Login</title>\n        </Head>\n        <AuthHeader page='login' />\n        <main className={styles.main}>\n          <h3>Welcome back, please login to your account</h3>\n          <div className={styles.searchbarDiv}>\n            <TextField fullWidth\n              placeholder='Email'\n              onChange={(e) => setEmail(e.target.value)}\n            /> <br /> <br />\n            <TextField fullWidth\n              placeholder='password'\n              type='password'\n              onChange={(e) => setPassword(e.target.value)}\n            /> <br /> <br />      \n            </div>\n            <Button variant='contained' onClick={loginHandler}>Login</Button>\n        </main>\n    </>\n  )\n}\nexport default Login;\n\n```\n</div>\n<div class='image-component'>\n```javascript\nimport Head from 'next/head';\nimport React from 'react'\nimport styles from '../../styles/Home.module.css'\nimport AuthHeader from '../../components/AuthHeader';\nimport { Button, TextField } from '@mui/material';\nimport { useRouter } from 'next/router';\nimport { useAuthContext } from '../../context/AuthContext';\nimport { API } from '../../utils/constants';\nimport { setToken } from '../../utils/helpers';\n\nconst Login = () => {\n  const [email, setEmail] = React.useState('')\n  const [password, setPassword] = React.useState('')\n  const [isLoading, setIsLoading] = React.useState(false);\n  \n  const router = useRouter();\n\n  const { setUser } = useAuthContext();\n\n\n  const loginHandler = async () => {\n    setIsLoading(true);\n    try {\n      const values = {\n        identifier: email,\n        password: password,\n      };\n      const response = await fetch(`${API}/auth/local`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify(values),\n      });\n\n      const data = await response.json();\n      if (data?.error) {\n        throw data?.error;\n      } else {\n        // set the token\n        setToken(data.jwt);\n\n        // set the user\n        setUser(data.user);\n\n        router.push('/')\n      }\n    } catch (error) {\n      console.error(error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <>\n        <Head>\n          <title>Login</title>\n        </Head>\n        <AuthHeader page='login' />\n        <main className={styles.main}>\n          <h3>Welcome back, please login to your account</h3>\n          <div className={styles.searchbarDiv}>\n            <TextField fullWidth\n              placeholder='Email'\n              onChange={(e) => setEmail(e.target.value)}\n            /> <br /> <br />\n            <TextField fullWidth\n              placeholder='password'\n              type='password'\n              onChange={(e) => setPassword(e.target.value)}\n            /> <br /> <br />\n            </div>\n            <Button variant='contained' onClick={loginHandler}>Login</Button>\n            {\n              isLoading ? <CircularProgress /> : null\n            }\n        </main>\n    </>\n  )\n}\nexport default Login;\n\n```\n</div>", "summary": {"titleUpdated": true, "title": "Creating Authentication Frontend", "description": "Learn how to fetch user details and send requests to the Strapi backend. ", "tags": []}, "type": "Lesson"}, {"id": "****************", "title": "Allowing Users to Add Favorites", "is_preview": false, "slug": "allowing-users-to-add-favorites", "text": "**[CanvasAnimation 图表]** - 交互式图表内容\n\n\n**[CanvasAnimation 图表]** - 交互式图表内容", "mdHtml": "<div class='image-component'>\n**[CanvasAnimation 图表]** - 交互式图表内容\n</div>\n<div class='image-component'>\n**[CanvasAnimation 图表]** - 交互式图表内容\n</div>", "summary": {"titleUpdated": true, "title": "Allowing Users to Add Favourites", "description": "Learn how to update user details with Strapi and how to allow users to add recipes to their favorites.", "tags": []}, "type": "Lesson"}]}, {"id": 4732861359063040, "title": "Project: Notes Application", "summary": "", "pages": []}, {"id": "ywtwnfoh5", "title": "Conclusion", "summary": "Approach building a Jamstack application with Next.js and Strapi CMS while managing content.", "pages": [{"id": "6365572034199552", "title": "Course Summary", "is_preview": false, "slug": "course-summary", "text": "<div class=\"flex justify-center sm:flex-row sm:justify-start -mt-4 flex-col\" style=\"gap: 20px;\">\n<div style='flex: 1; width: 70%;'>\nWe started this course with an introduction to Jamstack. We briefly looked at the components of Jamstack: JavaScript, APIs, and markup.\n</div>\n<div style='flex: 1; width: 30%;'>\n**[DrawIOWidget]**\n</div>\n</div>\n\n\n<div class=\"flex justify-center sm:flex-row sm:justify-start -mt-4 flex-col\" style=\"gap: 20px;\">\n<div style='flex: 1; width: 80%;'>\nWe then moved on to the core part, which was creating static applications with Next.js. We learned about the `getStaticPaths` and `getStaticProps` methods that Next.js uses. These functions allow us to pre-render web pages during build time and have them saved on a CDN for faster content delivery. \n</div>\n<div style='flex: 1; width: 20%;'>\n**[DrawIOWidget]**\n</div>\n</div>\n\n\n<div class=\"flex justify-center sm:flex-row sm:justify-start -mt-4 flex-col\" style=\"gap: 20px;\">\n<div style='flex: 1; width: 70%;'>\nDuring the last part of this course, we learned about Strapi CMS and how to integrate the backend of our application with the frontend. Strapi is a Node.js headless CMS that allows us to easily create backends for applications. \n</div>\n<div style='flex: 1; width: 30%;'>\n**[DrawIOWidget]**\n</div>\n</div>", "mdHtml": "<div class='image-component'>\n<div class=\"flex justify-center sm:flex-row sm:justify-start -mt-4 flex-col\" style=\"gap: 20px;\">\n<div style='flex: 1; width: 70%;'>\nWe started this course with an introduction to Jamstack. We briefly looked at the components of Jamstack: JavaScript, APIs, and markup.\n</div>\n<div style='flex: 1; width: 30%;'>\n**[DrawIOWidget]**\n</div>\n</div>\n</div>\n<div class='image-component'>\n<div class=\"flex justify-center sm:flex-row sm:justify-start -mt-4 flex-col\" style=\"gap: 20px;\">\n<div style='flex: 1; width: 80%;'>\nWe then moved on to the core part, which was creating static applications with Next.js. We learned about the `getStaticPaths` and `getStaticProps` methods that Next.js uses. These functions allow us to pre-render web pages during build time and have them saved on a CDN for faster content delivery. \n</div>\n<div style='flex: 1; width: 20%;'>\n**[DrawIOWidget]**\n</div>\n</div>\n</div>\n<div class='image-component'>\n<div class=\"flex justify-center sm:flex-row sm:justify-start -mt-4 flex-col\" style=\"gap: 20px;\">\n<div style='flex: 1; width: 70%;'>\nDuring the last part of this course, we learned about Strapi CMS and how to integrate the backend of our application with the frontend. Strapi is a Node.js headless CMS that allows us to easily create backends for applications. \n</div>\n<div style='flex: 1; width: 30%;'>\n**[DrawIOWidget]**\n</div>\n</div>\n</div>", "summary": {"titleUpdated": true, "title": "Course Summary [TBD]", "description": "Let's summarize what we learned in this course.", "tags": []}, "type": "Lesson"}, {"id": "6321318838665216", "title": "Wrap Up", "is_preview": false, "slug": "wrap-up", "text": "<div class=\"flex justify-center sm:flex-row sm:justify-start -mt-4 flex-col\" style=\"gap: 20px;\">\n<div style='flex: 1; width: 80%;'>\nCongratulations! You’ve completed this course and learned how to create a complete Jamstack application with Next.js and Strapi CMS. We hope that this was a fun, interactive, and helpful learning experience for you. We also hope to see you again in the other exciting courses Educative has to offer.\n</div>\n<div style='flex: 1; width: 20%;'>\n**[DrawIOWidget]**\n</div>\n</div>\n\n\n<div class=\"flex justify-center sm:flex-row sm:justify-start -mt-4 flex-col\" style=\"gap: 20px;\">\n<div style='flex: 1; width: 80%;'>\nWe encourage you to keep learning and exploring more resources on these technologies. The official documentation of [Next.js](https://nextjs.org/docs) and [Strapi](https://docs.strapi.io/developer-docs/latest/getting-started/introduction.html) are great sources of information for learning more about these technologies.\n</div>\n<div style='flex: 1; width: 20%;'>\n**[DrawIOWidget]**\n</div>\n</div>", "mdHtml": "<div class='image-component'>\n<div class=\"flex justify-center sm:flex-row sm:justify-start -mt-4 flex-col\" style=\"gap: 20px;\">\n<div style='flex: 1; width: 80%;'>\nCongratulations! You’ve completed this course and learned how to create a complete Jamstack application with Next.js and Strapi CMS. We hope that this was a fun, interactive, and helpful learning experience for you. We also hope to see you again in the other exciting courses Educative has to offer.\n</div>\n<div style='flex: 1; width: 20%;'>\n**[DrawIOWidget]**\n</div>\n</div>\n</div>\n<div class='image-component'>\n<div class=\"flex justify-center sm:flex-row sm:justify-start -mt-4 flex-col\" style=\"gap: 20px;\">\n<div style='flex: 1; width: 80%;'>\nWe encourage you to keep learning and exploring more resources on these technologies. The official documentation of [Next.js](https://nextjs.org/docs) and [Strapi](https://docs.strapi.io/developer-docs/latest/getting-started/introduction.html) are great sources of information for learning more about these technologies.\n</div>\n<div style='flex: 1; width: 20%;'>\n**[DrawIOWidget]**\n</div>\n</div>\n</div>", "summary": {"titleUpdated": false, "title": "Wrap Up!", "description": "Conclude your journey and get recommendations for further learning.\n", "tags": []}, "type": "Lesson"}]}]}]}