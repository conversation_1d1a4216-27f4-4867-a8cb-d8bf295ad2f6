<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="701px" height="184px" viewBox="-0.5 -0.5 701 184"><defs/><g><rect x="70" y="93" width="630" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 96px; margin-left: 72px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 22px; font-family: Courier New; color: #0742a2 ; line-height: 1.2; pointer-events: all; white-space: nowrap; "><font style="font-size: 27px">[x for x in range(0,21) if (x % 2!=0)]</font></div></div></div></foreignObject><text x="313" y="26" fill="#0742a2 " text-anchor="middle" font-size="22px" font-family="Courier New">&lt;font style="font-size: 27px"&gt;[x for x in range(0,21) if (x % 2!=0)]&lt;/font&gt;</text></switch></g><rect x="0" y="153" width="240" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 156px; margin-left: 2px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 20px; font-family: Courier New; color: #0742a2 ; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">output expression</div></div></div></foreignObject><text x="118" y="25" fill="#0742a2 " text-anchor="middle" font-size="20px" font-family="Courier New">output expression</text></switch></g><rect x="170" y="43" width="120" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 46px; margin-left: 172px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 20px; font-family: Courier New; color: #0742a2 ; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">variable</div></div></div></foreignObject><text x="58" y="25" fill="#0742a2 " text-anchor="middle" font-size="20px" font-family="Courier New">variable</text></switch></g><rect x="300" y="153" width="220" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 156px; margin-left: 302px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 22px; font-family: Courier New; color: #0742a2 ; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; "><font style="font-size: 19px">reference sequence</font></div></div></div></foreignObject><text x="302" y="178" fill="#0742a2 " font-family="Courier New" font-size="22px">[Not supported by viewer]</text></switch></g><rect x="460" y="43" width="230" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 46px; margin-left: 462px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 22px; font-family: Courier New; color: #0742a2 ; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; "><font style="font-size: 19px">predicate(optional)</font></div></div></div></foreignObject><text x="462" y="68" fill="#0742a2 " font-family="Courier New" font-size="22px">[Not supported by viewer]</text></switch></g><path d="M 575 73 L 575 96.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 575 101.88 L 571.5 94.88 L 575 96.63 L 578.5 94.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 110 163 L 110 129.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 110 124.12 L 113.5 131.12 L 110 129.37 L 106.5 131.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 370 163 L 370 129.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 370 124.12 L 373.5 131.12 L 370 129.37 L 366.5 131.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 230 73 L 230 96.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 230 101.88 L 226.5 94.88 L 230 96.63 L 233.5 94.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><rect x="240" y="0" width="280" height="20" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 3px; margin-left: 242px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; font-weight: bold; white-space: nowrap; "><font face="Courier New" style="font-size: 23px">List of odd numbers</font></div></div></div></foreignObject><text x="138" y="16" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica" font-weight="bold">&lt;font face="Courier New" style="font-size: 23px"&gt;List of odd numbers&lt;/font&gt;</text></switch></g></g></svg>