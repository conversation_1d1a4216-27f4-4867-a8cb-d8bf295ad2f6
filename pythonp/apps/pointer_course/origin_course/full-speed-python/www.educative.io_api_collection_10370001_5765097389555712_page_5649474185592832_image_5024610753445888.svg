<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="523px" height="396px" viewBox="-0.5 -0.5 523 396"><defs><linearGradient x1="100%" y1="0%" x2="0%" y2="0%" id="mx-gradient-ffffff-1-ffffff-1-e-0"><stop offset="0%" style="stop-color:#ffffff"/><stop offset="100%" style="stop-color:#ffffff"/></linearGradient></defs><g><rect x="0" y="30" width="520" height="290" fill="#000000" stroke="#000000" transform="translate(2,3)" opacity="0.25"/><rect x="0" y="30" width="520" height="290" fill="url(#mx-gradient-ffffff-1-ffffff-1-e-0)" stroke="#000000" pointer-events="all"/><rect x="50" y="40" width="160" height="260" fill="none" stroke="#000000" pointer-events="all"/><rect x="250" y="80" width="100" height="30" fill="#ccff99" stroke="#8aadff" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 95px; margin-left: 251px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font face="Courier New" style="font-size: 22px">age</font></div></div></div></foreignObject><text x="300" y="99" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">[Not supported by viewer]</text></switch></g><rect x="250" y="160" width="100" height="30" fill="#ccff99" stroke="#8aadff" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 175px; margin-left: 251px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font face="Courier New" style="font-size: 22px">age</font></div></div></div></foreignObject><text x="300" y="179" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">[Not supported by viewer]</text></switch></g><rect x="70" y="225" width="120" height="50" fill="#8aadff" stroke="#78b2ff" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 250px; margin-left: 71px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font face="Courier New" style="font-size: 22px">"Charles"</font></div></div></div></foreignObject><text x="130" y="254" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">[Not supported by viewer]</text></switch></g><rect x="70" y="80" width="120" height="50" fill="#007fff" stroke="#8aadff" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 105px; margin-left: 71px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 22px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font face="Courier New">"Peter"</font></div></div></div></foreignObject><text x="130" y="112" fill="#000000" font-family="Helvetica" font-size="22px" text-anchor="middle">[Not supported by viewer]</text></switch></g><rect x="70" y="155" width="120" height="45" fill="#007fff" stroke="#8aadff" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 178px; margin-left: 71px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 22px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font face="Courier New">"Anna"</font></div></div></div></foreignObject><text x="130" y="184" fill="#000000" font-family="Helvetica" font-size="22px" text-anchor="middle">[Not supported by viewer]</text></switch></g><rect x="250" y="230" width="100" height="30" fill="#ccff99" stroke="#85d2ff" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 245px; margin-left: 251px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font face="Courier New" style="font-size: 22px">age</font></div></div></div></foreignObject><text x="300" y="249" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">[Not supported by viewer]</text></switch></g><path d="M 190 105 L 243.63 105" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 248.88 105 L 241.88 108.5 L 243.63 105 L 241.88 101.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 190 177 L 243.63 177" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 248.88 177 L 241.88 180.5 L 243.63 177 L 241.88 173.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 190 250 L 243.63 250" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 248.88 250 L 241.88 253.5 L 243.63 250 L 241.88 246.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><rect x="95" y="40" width="70" height="20" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 43px; margin-left: 97px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; "><font face="Courier New" style="font-size: 22px"><b>keys</b></font></div></div></div></foreignObject><text x="97" y="55" fill="#000000" font-family="Helvetica" font-size="12px">[Not supported by viewer]</text></switch></g><rect x="250" y="50" width="110" height="20" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 53px; margin-left: 252px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; "><font face="Courier New" style="font-size: 22px"><b>subkeys</b></font></div></div></div></foreignObject><text x="252" y="65" fill="#000000" font-family="Helvetica" font-size="12px">[Not supported by viewer]</text></switch></g><rect x="0" y="0" width="250" height="20" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 3px; margin-left: 2px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; "><font face="Courier New" style="font-size: 22px"><b>Student dictionary</b></font></div></div></div></foreignObject><text x="123" y="16" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">&lt;font face="Courier New" style="font-size: 22px"&gt;&lt;b&gt;Student dictionary&lt;/b&gt;&lt;/font&gt;</text></switch></g><rect x="250" y="110" width="100" height="30" fill="#ccff99" stroke="#8aadff" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 125px; margin-left: 251px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font face="Courier New" style="font-size: 22px">address</font></div></div></div></foreignObject><text x="300" y="129" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">[Not supported by viewer]</text></switch></g><rect x="250" y="185" width="100" height="30" fill="#ccff99" stroke="#8aadff" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 200px; margin-left: 251px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font face="Courier New" style="font-size: 22px">address</font></div></div></div></foreignObject><text x="300" y="204" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">[Not supported by viewer]</text></switch></g><rect x="250" y="260" width="100" height="30" fill="#ccff99" stroke="#85d2ff" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 275px; margin-left: 251px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font face="Courier New" style="font-size: 22px">address</font></div></div></div></foreignObject><text x="300" y="279" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">[Not supported by viewer]</text></switch></g><rect x="380" y="80" width="100" height="30" fill="#ffe599" stroke="#ccff99" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 95px; margin-left: 381px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font face="Courier New" style="font-size: 22px">21</font></div></div></div></foreignObject><text x="430" y="99" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">[Not supported by viewer]</text></switch></g><rect x="380" y="110" width="100" height="30" fill="#ffe599" stroke="#ccff99" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 125px; margin-left: 381px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font face="Courier New" style="font-size: 22px">"Lisbon"</font></div></div></div></foreignObject><text x="430" y="129" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">[Not supported by viewer]</text></switch></g><rect x="380" y="160" width="100" height="30" fill="#ffe599" stroke="#97ff52" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 175px; margin-left: 381px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font face="Courier New" style="font-size: 22px">22</font></div></div></div></foreignObject><text x="430" y="179" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">[Not supported by viewer]</text></switch></g><rect x="380" y="185" width="100" height="30" fill="#ffe599" stroke="#97ff52" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 200px; margin-left: 381px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font face="Courier New"><span style="font-size: 22px">"Lisbon"</span></font></div></div></div></foreignObject><text x="430" y="204" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">[Not supported by viewer]</text></switch></g><rect x="380" y="230" width="100" height="30" fill="#ffe599" stroke="#ccff99" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 245px; margin-left: 381px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font face="Courier New" style="font-size: 22px">32</font></div></div></div></foreignObject><text x="430" y="249" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">[Not supported by viewer]</text></switch></g><rect x="380" y="260" width="100" height="30" fill="#ffe599" stroke="#ccff99" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 275px; margin-left: 381px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font face="Courier New" style="font-size: 22px">"Turkey"</font></div></div></div></foreignObject><text x="430" y="279" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">[Not supported by viewer]</text></switch></g><path d="M 350 95 L 373.63 95" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 378.88 95 L 371.88 98.5 L 373.63 95 L 371.88 91.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 350 124 L 373.63 124" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 378.88 124 L 371.88 127.5 L 373.63 124 L 371.88 120.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 350 205 L 373.63 205" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 378.88 205 L 371.88 208.5 L 373.63 205 L 371.88 201.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 350 175 L 373.63 175" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 378.88 175 L 371.88 178.5 L 373.63 175 L 371.88 171.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 350 245 L 373.63 245" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 378.88 245 L 371.88 248.5 L 373.63 245 L 371.88 241.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 350 274 L 373.63 274" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 378.88 274 L 371.88 277.5 L 373.63 274 L 371.88 270.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><rect x="365" y="50" width="130" height="20" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 53px; margin-left: 367px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; "><font face="Courier New" style="font-size: 22px"><b>subvalues</b></font></div></div></div></foreignObject><text x="367" y="65" fill="#000000" font-family="Helvetica" font-size="12px">[Not supported by viewer]</text></switch></g><rect x="125" y="340" width="360" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 343px; margin-left: 127px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; "><font face="Courier New" style="font-size: 22px"><b>address = "Lisbon"</b></font><div><font face="Courier New" style="font-size: 22px"><b>Output = ['Anna', 'Peter']</b></font></div></div></div></div></foreignObject><text x="127" y="355" fill="#000000" font-family="Helvetica" font-size="12px">[Not supported by viewer]</text></switch></g></g></svg>