<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="background: transparent; background-color: transparent;" version="1.1" width="920px" height="160px" viewBox="-0.5 -0.5 920 160"><defs/><g><g data-cell-id="0"><g data-cell-id="1"><g data-cell-id="2"><g><rect x="80" y="60" width="830" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 63px; margin-left: 82px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; color: #0742a2 ; "><div style="display: inline-block; font-size: 22px; font-family: &quot;Courier New&quot;; color: light-dark(#0742a2 , #8bbeff); line-height: 1.2; pointer-events: all; white-space: nowrap; "><font style="font-size: 27px;">[x**2 for x in range(0, 21) if x%3!=0 and x%2 ==0]</font></div></div></div></foreignObject><text x="82" y="85" fill="#0742a2 " font-family="&quot;Courier New&quot;" font-size="22px">[x**2 for x in range(0, 21) if x%3!=0 and x%2 ==0]</text></switch></g></g></g><g data-cell-id="3"><g><rect x="10" y="120" width="240" height="30" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 123px; margin-left: 12px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; color: #0742a2 ; background-color: #ffffff; "><div style="display: inline-block; font-size: 20px; font-family: &quot;Courier New&quot;; color: light-dark(#0742a2 , #8bbeff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, #121212); white-space: nowrap; ">output expression</div></div></div></foreignObject><text x="12" y="143" fill="#0742a2 " font-family="&quot;Courier New&quot;" font-size="20px">output expression</text></switch></g></g></g><g data-cell-id="4"><g><rect x="200" y="10" width="120" height="30" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 13px; margin-left: 202px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; color: #0742a2 ; background-color: #ffffff; "><div style="display: inline-block; font-size: 20px; font-family: &quot;Courier New&quot;; color: light-dark(#0742a2 , #8bbeff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, #121212); white-space: nowrap; ">variable</div></div></div></foreignObject><text x="202" y="33" fill="#0742a2 " font-family="&quot;Courier New&quot;" font-size="20px">variable</text></switch></g></g></g><g data-cell-id="5"><g><rect x="310" y="120" width="220" height="30" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 123px; margin-left: 312px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; color: #0742a2 ; background-color: #ffffff; "><div style="display: inline-block; font-size: 22px; font-family: &quot;Courier New&quot;; color: light-dark(#0742a2 , #8bbeff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, #121212); white-space: nowrap; "><font style="font-size: 19px">reference sequence</font></div></div></div></foreignObject><text x="312" y="145" fill="#0742a2 " font-family="&quot;Courier New&quot;" font-size="22px">reference sequence</text></switch></g></g></g><g data-cell-id="6"><g><path d="M 120 130 L 120 96.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 120 91.12 L 123.5 98.12 L 120 96.37 L 116.5 98.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g></g><g data-cell-id="7"><g><path d="M 380 130 L 380 96.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 380 91.12 L 383.5 98.12 L 380 96.37 L 376.5 98.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g></g><g data-cell-id="8"><g><path d="M 250 30 L 250 53.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 250 58.88 L 246.5 51.88 L 250 53.63 L 253.5 51.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g></g><g data-cell-id="11"><g><rect x="650" y="10" width="130" height="30" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 13px; margin-left: 652px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; color: #0742a2 ; background-color: #ffffff; "><div style="display: inline-block; font-size: 20px; font-family: &quot;Courier New&quot;; color: light-dark(#0742a2 , #8bbeff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, #121212); white-space: nowrap; ">predicate</div></div></div></foreignObject><text x="652" y="33" fill="#0742a2 " font-family="&quot;Courier New&quot;" font-size="20px">predicate</text></switch></g></g></g><g data-cell-id="12"><g><path d="M 701 30 L 701 53.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 701 58.88 L 697.5 51.88 L 701 53.63 L 704.5 51.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g></g></g></g></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.drawio.com/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Text is not SVG - cannot display</text></a></switch></svg>