<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="381px" height="226px" viewBox="-0.5 -0.5 381 226"><defs/><g><rect x="158" y="45" width="20" height="20" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 48px; margin-left: 160px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 22px; font-family: Courier New; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; ">2</div></div></div></foreignObject><text x="8" y="21" fill="#000000" text-anchor="middle" font-size="22px" font-family="Courier New">2</text></switch></g><rect x="188" y="45" width="30" height="20" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 48px; margin-left: 190px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 22px; font-family: Courier New; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; ">17</div></div></div></foreignObject><text x="13" y="21" fill="#000000" text-anchor="middle" font-size="22px" font-family="Courier New">17</text></switch></g><path d="M 180 35 L 223 35" fill="none" stroke="#0742a2 " stroke-width="4" stroke-miterlimit="10" pointer-events="all"/><path d="M 165.5 52.5 L 200.5 52.5" fill="none" stroke="#0742a2 " stroke-width="4" stroke-miterlimit="10" transform="rotate(90,183,52.5)" pointer-events="all"/><rect x="203" y="5" width="30" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 8px; margin-left: 205px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 22px; font-family: Courier New; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; ">8</div></div></div></foreignObject><text x="13" y="26" fill="#000000" text-anchor="middle" font-size="22px" font-family="Courier New">8</text></switch></g><rect x="188" y="80" width="40" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 83px; margin-left: 190px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 22px; font-family: Courier New; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; ">16</div></div></div></foreignObject><text x="18" y="26" fill="#000000" text-anchor="middle" font-size="22px" font-family="Courier New">16</text></switch></g><rect x="208" y="120" width="30" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 123px; margin-left: 210px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 22px; font-family: Courier New; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; ">1</div></div></div></foreignObject><text x="13" y="26" fill="#000000" text-anchor="middle" font-size="22px" font-family="Courier New">1</text></switch></g><rect x="15" y="120" width="110" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 123px; margin-left: 17px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 22px; font-family: Courier New; color: #0742a2 ; line-height: 1.2; pointer-events: all; white-space: nowrap; ">modulus</div></div></div></foreignObject><text x="53" y="26" fill="#0742a2 " text-anchor="middle" font-size="22px" font-family="Courier New">modulus</text></switch></g><rect x="15" y="42" width="110" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 45px; margin-left: 17px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 22px; font-family: Courier New; color: #0742a2 ; line-height: 1.2; pointer-events: all; white-space: nowrap; ">divisor</div></div></div></foreignObject><text x="53" y="26" fill="#0742a2 " text-anchor="middle" font-size="22px" font-family="Courier New">divisor</text></switch></g><rect x="260" y="39" width="120" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 42px; margin-left: 262px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 22px; font-family: Courier New; color: #0742a2 ; line-height: 1.2; pointer-events: all; white-space: nowrap; ">dividend</div></div></div></foreignObject><text x="58" y="26" fill="#0742a2 " text-anchor="middle" font-size="22px" font-family="Courier New">dividend</text></switch></g><rect x="260" y="0" width="120" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 3px; margin-left: 262px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 22px; font-family: Courier New; color: #0742a2 ; line-height: 1.2; pointer-events: all; white-space: nowrap; ">quotient</div></div></div></foreignObject><text x="58" y="26" fill="#0742a2 " text-anchor="middle" font-size="22px" font-family="Courier New">quotient</text></switch></g><path d="M 120 52.14 L 138.15 52.14 L 138.15 45 L 150 57.5 L 138.15 70 L 138.15 62.86 L 120 62.86 Z" fill="#0742a2 " stroke="#0742a2 " stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><path d="M 120 128.57 L 138.15 128.57 L 138.15 120 L 150 135 L 138.15 150 L 138.15 141.43 L 120 141.43 Z" fill="#0742a2 " stroke="#0742a2 " stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><path d="M 255 9.71 L 238.66 9.71 L 238.66 2 L 228 15.5 L 238.66 29 L 238.66 21.29 L 255 21.29 Z" fill="#0742a2 " stroke="#0742a2 " stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><path d="M 255 51.14 L 238.66 51.14 L 238.66 44 L 228 56.5 L 238.66 69 L 238.66 61.86 L 255 61.86 Z" fill="#0742a2 " stroke="#0742a2 " stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><path d="M 188 119 L 218 119" fill="none" stroke="#000000" stroke-miterlimit="10" stroke-dasharray="1 4" pointer-events="stroke"/><rect x="0" y="140" width="140" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 143px; margin-left: 2px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 22px; font-family: Courier New; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; "><font color="#0742a2" style="font-size: 19px">(Remainder)</font></div></div></div></foreignObject><text x="2" y="165" fill="#000000" font-family="Courier New" font-size="22px">[Not supported by viewer]</text></switch></g><rect x="150" y="195" width="160" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 198px; margin-left: 152px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 22px; font-family: Courier New; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; "><b>Odd Parity </b></div></div></div></foreignObject><text x="78" y="26" fill="#000000" text-anchor="middle" font-size="22px" font-family="Courier New">&lt;b&gt;Odd Parity &lt;/b&gt;</text></switch></g><path d="M 217 145 L 217 188.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 217 193.88 L 213.5 186.88 L 217 188.63 L 220.5 186.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/></g></svg>