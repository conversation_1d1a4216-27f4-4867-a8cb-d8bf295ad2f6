<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="801px" height="626px" version="1.1"><defs/><g transform="translate(0.5,0.5)"><rect x="50" y="33" width="520" height="517" fill="none" stroke="#000000" transform="translate(2,3)" opacity="0.25"/><rect x="50" y="33" width="520" height="517" fill="none" stroke="#000000" pointer-events="none"/><rect x="86" y="40" width="160" height="500" fill="none" stroke="#000000" pointer-events="none"/><rect x="290" y="80" width="100" height="30" fill="#ccff99" stroke="#8aadff" pointer-events="none"/><g transform="translate(319.5,82.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="40" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 40px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px">age</font></div></div></foreignObject><text x="20" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="290" y="160" width="100" height="30" fill="#ccff99" stroke="#8aadff" pointer-events="none"/><g transform="translate(319.5,162.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="40" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 40px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px">age</font></div></div></foreignObject><text x="20" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="110" y="225" width="120" height="50" fill="#8aadff" stroke="#78b2ff" pointer-events="none"/><g transform="translate(110.5,237.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="118" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 120px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px">"Charles"</font></div></div></foreignObject><text x="59" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="110" y="80" width="120" height="50" fill="#8aadff" stroke="#8aadff" pointer-events="none"/><g transform="translate(123.5,91.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="92" height="26" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 22px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 94px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New">"Peter"</font></div></div></foreignObject><text x="46" y="24" fill="#000000" text-anchor="middle" font-size="22px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="110" y="155" width="120" height="45" fill="#8aadff" stroke="#8aadff" pointer-events="none"/><g transform="translate(123.5,164.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="92" height="26" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 22px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 94px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New">"Susan"</font></div></div></foreignObject><text x="46" y="24" fill="#000000" text-anchor="middle" font-size="22px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="290" y="230" width="100" height="30" fill="#ccff99" stroke="#85d2ff" pointer-events="none"/><g transform="translate(319.5,232.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="40" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 40px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px">age</font></div></div></foreignObject><text x="20" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><path d="M 230 105 L 283.63 105" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 288.88 105 L 281.88 108.5 L 283.63 105 L 281.88 101.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 230 177 L 283.63 177" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 288.88 177 L 281.88 180.5 L 283.63 177 L 281.88 173.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 230 250 L 283.63 250" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 288.88 250 L 281.88 253.5 L 283.63 250 L 281.88 246.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(136.5,42.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="53" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; white-space: nowrap;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px"><b>keys</b></font></div></div></foreignObject><text x="27" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><g transform="translate(291.5,52.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="92" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; white-space: nowrap;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px"><b>subkeys</b></font></div></div></foreignObject><text x="46" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><g transform="translate(41.5,2.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="238" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; white-space: nowrap;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px"><b>Student dictionary</b></font></div></div></foreignObject><text x="119" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">&lt;font face="Courier New" style="font-size: 22px"&gt;&lt;b&gt;Student dictionary&lt;/b&gt;&lt;/font&gt;</text></switch></g><rect x="290" y="110" width="100" height="30" fill="#ccff99" stroke="#8aadff" pointer-events="none"/><g transform="translate(293.5,112.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="92" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 94px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px">address</font></div></div></foreignObject><text x="46" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="290" y="185" width="100" height="30" fill="#ccff99" stroke="#8aadff" pointer-events="none"/><g transform="translate(293.5,187.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="92" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 94px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px">address</font></div></div></foreignObject><text x="46" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="290" y="260" width="100" height="30" fill="#ccff99" stroke="#85d2ff" pointer-events="none"/><g transform="translate(293.5,262.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="92" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 94px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px">address</font></div></div></foreignObject><text x="46" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="420" y="80" width="100" height="30" fill="#ffe599" stroke="#ccff99" pointer-events="none"/><g transform="translate(456.5,82.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="26" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 28px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px">10</font></div></div></foreignObject><text x="13" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="420" y="110" width="100" height="30" fill="#ffe599" stroke="#ccff99" pointer-events="none"/><g transform="translate(417.5,112.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="105" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 107px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px">"Lisbon"</font></div></div></foreignObject><text x="53" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="420" y="160" width="100" height="30" fill="#ffe599" stroke="#97ff52" pointer-events="none"/><g transform="translate(456.5,162.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="26" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 28px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px">11</font></div></div></foreignObject><text x="13" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="420" y="185" width="100" height="30" fill="#ffe599" stroke="#97ff52" pointer-events="none"/><g transform="translate(417.5,187.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="105" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 107px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New"><span style="font-size: 22px">"Sweden"</span></font></div></div></foreignObject><text x="53" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="420" y="230" width="100" height="30" fill="#ffe599" stroke="#ccff99" pointer-events="none"/><g transform="translate(462.5,232.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="14" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 14px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px">9</font></div></div></foreignObject><text x="7" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="420" y="260" width="100" height="30" fill="#ffe599" stroke="#ccff99" pointer-events="none"/><g transform="translate(417.5,262.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="105" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 107px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px">"Turkey"</font></div></div></foreignObject><text x="53" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><path d="M 390 95 L 413.63 95" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 418.88 95 L 411.88 98.5 L 413.63 95 L 411.88 91.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 390 124 L 413.63 124" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 418.88 124 L 411.88 127.5 L 413.63 124 L 411.88 120.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 390 205 L 413.63 205" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 418.88 205 L 411.88 208.5 L 413.63 205 L 411.88 201.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 390 175 L 413.63 175" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 418.88 175 L 411.88 178.5 L 413.63 175 L 411.88 171.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 390 245 L 413.63 245" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 418.88 245 L 411.88 248.5 L 413.63 245 L 411.88 241.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 390 274 L 413.63 274" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 418.88 274 L 411.88 277.5 L 413.63 274 L 411.88 270.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(406.5,52.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="119" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; white-space: nowrap;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px"><b>subvalues</b></font></div></div></foreignObject><text x="60" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="110" y="310" width="120" height="50" fill="#8aadff" stroke="#78b2ff" pointer-events="none"/><g transform="translate(116.5,322.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="106" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 106px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px">"Thomas"</font></div></div></foreignObject><text x="53" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="110" y="380" width="120" height="50" fill="#8aadff" stroke="#78b2ff" pointer-events="none"/><g transform="translate(136.5,392.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="66" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 68px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px">"Bob"</font></div></div></foreignObject><text x="33" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="110" y="450" width="120" height="50" fill="#8aadff" stroke="#78b2ff" pointer-events="none"/><g transform="translate(116.5,462.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="106" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 106px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px">"Joseph"</font></div></div></foreignObject><text x="53" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><path d="M 230 335 L 283.63 335" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 288.88 335 L 281.88 338.5 L 283.63 335 L 281.88 331.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><rect x="290" y="308" width="100" height="30" fill="#ccff99" stroke="#8aadff" pointer-events="none"/><g transform="translate(319.5,310.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="40" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 40px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px">age</font></div></div></foreignObject><text x="20" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="290" y="388" width="100" height="30" fill="#ccff99" stroke="#8aadff" pointer-events="none"/><g transform="translate(319.5,390.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="40" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 40px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px">age</font></div></div></foreignObject><text x="20" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="290" y="458" width="100" height="30" fill="#ccff99" stroke="#85d2ff" pointer-events="none"/><g transform="translate(319.5,460.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="40" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 40px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px">age</font></div></div></foreignObject><text x="20" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="290" y="338" width="100" height="30" fill="#ccff99" stroke="#8aadff" pointer-events="none"/><g transform="translate(293.5,340.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="92" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 94px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px">address</font></div></div></foreignObject><text x="46" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="290" y="413" width="100" height="30" fill="#ccff99" stroke="#8aadff" pointer-events="none"/><g transform="translate(293.5,415.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="92" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 94px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px">address</font></div></div></foreignObject><text x="46" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="290" y="488" width="100" height="30" fill="#ccff99" stroke="#85d2ff" pointer-events="none"/><g transform="translate(293.5,490.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="92" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 94px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px">address</font></div></div></foreignObject><text x="46" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="420" y="308" width="100" height="30" fill="#ffe599" stroke="#ccff99" pointer-events="none"/><g transform="translate(456.5,310.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="26" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 28px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px">10</font></div></div></foreignObject><text x="13" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="420" y="338" width="100" height="30" fill="#ffe599" stroke="#ccff99" pointer-events="none"/><g transform="translate(417.5,340.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="105" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 107px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px">"Lisbon"</font></div></div></foreignObject><text x="53" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="420" y="388" width="100" height="30" fill="#ffe599" stroke="#97ff52" pointer-events="none"/><g transform="translate(456.5,390.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="26" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 28px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px">11</font></div></div></foreignObject><text x="13" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="420" y="413" width="100" height="30" fill="#ffe599" stroke="#97ff52" pointer-events="none"/><g transform="translate(417.5,415.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="105" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 107px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New"><span style="font-size: 22px">"Sweden"</span></font></div></div></foreignObject><text x="53" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="420" y="458" width="100" height="30" fill="#ffe599" stroke="#ccff99" pointer-events="none"/><g transform="translate(462.5,460.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="14" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 14px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px">9</font></div></div></foreignObject><text x="7" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="420" y="488" width="100" height="30" fill="#ffe599" stroke="#ccff99" pointer-events="none"/><g transform="translate(417.5,490.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="105" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 107px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px">"Turkey"</font></div></div></foreignObject><text x="53" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><path d="M 390 323 L 413.63 323" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 418.88 323 L 411.88 326.5 L 413.63 323 L 411.88 319.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 390 352 L 413.63 352" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 418.88 352 L 411.88 355.5 L 413.63 352 L 411.88 348.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 390 433 L 413.63 433" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 418.88 433 L 411.88 436.5 L 413.63 433 L 411.88 429.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 390 403 L 413.63 403" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 418.88 403 L 411.88 406.5 L 413.63 403 L 411.88 399.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 390 473 L 413.63 473" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 418.88 473 L 411.88 476.5 L 413.63 473 L 411.88 469.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 390 502 L 413.63 502" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 418.88 502 L 411.88 505.5 L 413.63 502 L 411.88 498.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 230 408 L 283.63 408" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 288.88 408 L 281.88 411.5 L 283.63 408 L 281.88 404.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 230 481 L 283.63 481" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 288.88 481 L 281.88 484.5 L 283.63 481 L 281.88 477.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(1.5,572.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="787" height="52" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; white-space: nowrap;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><b><font face="Courier New" style="font-size: 23px">Average age of Students = (10 + 11 + 9 + 10 + 11 + 9) / 6</font></b><div><b><font face="Courier New" style="font-size: 23px">                        =  10 </font></b></div></div></div></foreignObject><text x="394" y="32" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">&lt;b&gt;&lt;font face="Courier New" style="font-size: 23px"&gt;Average age of Students = (10 + 11 + 9 + 10 + 11 + 9) / 6&lt;/font&gt;&lt;/b&gt;&lt;div&gt;&lt;b&gt;&lt;font face="Courier New" style="font-size: 23px"&gt;&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; =&amp;nbsp; 10&amp;nbsp;&lt;/font&gt;&lt;/b&gt;&lt;/div&gt;</text></switch></g></g></svg>