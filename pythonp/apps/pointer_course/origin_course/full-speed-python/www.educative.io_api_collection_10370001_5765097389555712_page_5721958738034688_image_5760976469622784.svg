<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="403px" height="354px" viewBox="-0.5 -0.5 403 354"><defs/><g><rect x="0" y="30" width="400" height="320" fill="#000000" stroke="#000000" transform="translate(2,3)" opacity="0.25"/><rect x="0" y="30" width="400" height="320" fill="#ffffff" stroke="#000000" pointer-events="all"/><rect x="50" y="70" width="160" height="230" fill="#ffffff" stroke="#000000" pointer-events="all"/><rect x="250" y="90" width="100" height="30" fill="#ffffff" stroke="#ffb259" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 105px; margin-left: 251px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font face="Courier New" style="font-size: 22px">21</font></div></div></div></foreignObject><text x="300" y="109" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">[Not supported by viewer]</text></switch></g><rect x="250" y="170" width="100" height="30" fill="#ffffff" stroke="#97ff52" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 185px; margin-left: 251px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font face="Courier New" style="font-size: 22px">23</font></div></div></div></foreignObject><text x="300" y="189" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">[Not supported by viewer]</text></switch></g><rect x="70" y="230" width="120" height="50" fill="#8aadff" stroke="#78b2ff" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 255px; margin-left: 71px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font face="Courier New" style="font-size: 22px">"Charles"</font></div></div></div></foreignObject><text x="130" y="259" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">[Not supported by viewer]</text></switch></g><rect x="70" y="80" width="120" height="50" fill="#ffbb94" stroke="#ffb259" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 105px; margin-left: 71px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 22px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font face="Courier New">"Peter"</font></div></div></div></foreignObject><text x="130" y="112" fill="#000000" font-family="Helvetica" font-size="22px" text-anchor="middle">[Not supported by viewer]</text></switch></g><rect x="70" y="155" width="120" height="45" fill="#d0ffb0" stroke="#97ff52" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 178px; margin-left: 71px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 22px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font face="Courier New">"Susan"</font></div></div></div></foreignObject><text x="130" y="184" fill="#000000" font-family="Helvetica" font-size="22px" text-anchor="middle">[Not supported by viewer]</text></switch></g><rect x="250" y="240" width="100" height="30" fill="#ffffff" stroke="#85d2ff" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 255px; margin-left: 251px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font face="Courier New" style="font-size: 22px">32</font></div></div></div></foreignObject><text x="300" y="259" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">[Not supported by viewer]</text></switch></g><path d="M 190 105 L 243.63 105" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 248.88 105 L 241.88 108.5 L 243.63 105 L 241.88 101.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 190 185 L 243.63 185" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 248.88 185 L 241.88 188.5 L 243.63 185 L 241.88 181.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 190 255 L 243.63 255" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 248.88 255 L 241.88 258.5 L 243.63 255 L 241.88 251.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><rect x="95" y="40" width="70" height="20" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 43px; margin-left: 97px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; "><font face="Courier New" style="font-size: 22px"><b>keys</b></font></div></div></div></foreignObject><text x="97" y="55" fill="#000000" font-family="Helvetica" font-size="12px">[Not supported by viewer]</text></switch></g><rect x="250" y="50" width="90" height="20" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 53px; margin-left: 252px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; "><font face="Courier New" style="font-size: 22px"><b>values</b></font></div></div></div></foreignObject><text x="252" y="65" fill="#000000" font-family="Helvetica" font-size="12px">[Not supported by viewer]</text></switch></g><rect x="0" y="0" width="250" height="20" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 3px; margin-left: 2px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; "><font face="Courier New" style="font-size: 22px"><b>Student dictionary</b></font></div></div></div></foreignObject><text x="123" y="16" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">&lt;font face="Courier New" style="font-size: 22px"&gt;&lt;b&gt;Student dictionary&lt;/b&gt;&lt;/font&gt;</text></switch></g></g></svg>