<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="731px" height="192px" viewBox="-0.5 -0.5 731 192"><defs/><g><rect x="100" y="50" width="630" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 53px; margin-left: 102px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 22px; font-family: Courier New; color: #0742a2 ; line-height: 1.2; pointer-events: all; white-space: nowrap; "><font style="font-size: 27px">[x*x for x in [0,1,2,3] if (x % 2==0)]</font></div></div></div></foreignObject><text x="313" y="26" fill="#0742a2 " text-anchor="middle" font-size="22px" font-family="Courier New">&lt;font style="font-size: 27px"&gt;[x*x for x in [0,1,2,3] if (x % 2==0)]&lt;/font&gt;</text></switch></g><rect x="30" y="111" width="240" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 114px; margin-left: 32px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 20px; font-family: Courier New; color: #0742a2 ; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">output expression</div></div></div></foreignObject><text x="118" y="25" fill="#0742a2 " text-anchor="middle" font-size="20px" font-family="Courier New">output expression</text></switch></g><rect x="220" y="0" width="120" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 3px; margin-left: 222px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 20px; font-family: Courier New; color: #0742a2 ; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">variable</div></div></div></foreignObject><text x="58" y="25" fill="#0742a2 " text-anchor="middle" font-size="20px" font-family="Courier New">variable</text></switch></g><rect x="330" y="110" width="220" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 113px; margin-left: 332px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 22px; font-family: Courier New; color: #0742a2 ; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; "><font style="font-size: 19px">reference sequence</font></div></div></div></foreignObject><text x="332" y="135" fill="#0742a2 " font-family="Courier New" font-size="22px">[Not supported by viewer]</text></switch></g><rect x="490" y="0" width="230" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 3px; margin-left: 492px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 22px; font-family: Courier New; color: #0742a2 ; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; "><font style="font-size: 19px">predicate(optional)</font></div></div></div></foreignObject><text x="492" y="25" fill="#0742a2 " font-family="Courier New" font-size="22px">[Not supported by viewer]</text></switch></g><path d="M 605 30 L 605 53.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 605 58.88 L 601.5 51.88 L 605 53.63 L 608.5 51.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 140 120 L 140 86.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 140 81.12 L 143.5 88.12 L 140 86.37 L 136.5 88.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 400 120 L 400 86.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 400 81.12 L 403.5 88.12 L 400 86.37 L 396.5 88.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 260 30 L 260 53.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 260 58.88 L 256.5 51.88 L 260 53.63 L 263.5 51.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><rect x="0" y="140" width="270" height="50" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 143px; margin-left: 2px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 20px; font-family: Courier New; color: #0742a2 ; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; "><div style="text-align: center"><b style="font-size: 12px ; color: rgb(0 , 102 , 204)">In this case,</b></div><div style="text-align: center"><font color="#0066cc" style="font-size: 12px"><b> the list of square of elements in x</b></font></div></div></div></div></foreignObject><text x="2" y="163" fill="#0742a2 " font-family="Courier New" font-size="20px">[Not supported by viewer]</text></switch></g><rect x="380" y="130" width="70" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 133px; margin-left: 382px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 20px; font-family: Courier New; color: #0742a2 ; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; "><font color="#0066cc" style="font-size: 12px"><b>The list</b></font></div></div></div></foreignObject><text x="382" y="153" fill="#0742a2 " font-family="Courier New" font-size="20px">[Not supported by viewer]</text></switch></g></g></svg>