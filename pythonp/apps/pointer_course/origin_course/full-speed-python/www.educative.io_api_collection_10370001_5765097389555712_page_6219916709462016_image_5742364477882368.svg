<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="523px" height="368px" version="1.1"><defs><linearGradient x1="100%" y1="0%" x2="0%" y2="0%" id="mx-gradient-ffffff-1-ffffff-1-e-0"><stop offset="0%" style="stop-color:#ffffff"/><stop offset="100%" style="stop-color:#ffffff"/></linearGradient></defs><g transform="translate(0.5,0.5)"><rect x="0" y="30" width="520" height="290" fill="#000000" stroke="#000000" transform="translate(2,3)" opacity="0.25"/><rect x="0" y="30" width="520" height="290" fill="url(#mx-gradient-ffffff-1-ffffff-1-e-0)" stroke="#000000" pointer-events="none"/><rect x="50" y="40" width="160" height="260" fill="none" stroke="#000000" pointer-events="none"/><rect x="250" y="80" width="100" height="30" fill="#ccff99" stroke="#8aadff" pointer-events="none"/><g transform="translate(279.5,82.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="40" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 40px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px">age</font></div></div></foreignObject><text x="20" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="250" y="160" width="100" height="30" fill="#ccff99" stroke="#8aadff" pointer-events="none"/><g transform="translate(279.5,162.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="40" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 40px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px">age</font></div></div></foreignObject><text x="20" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="70" y="225" width="120" height="50" fill="#8aadff" stroke="#78b2ff" pointer-events="none"/><g transform="translate(70.5,237.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="118" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 120px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px">"Charles"</font></div></div></foreignObject><text x="59" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="70" y="80" width="120" height="50" fill="#8aadff" stroke="#8aadff" pointer-events="none"/><g transform="translate(83.5,91.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="92" height="26" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 22px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 94px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New">"Peter"</font></div></div></foreignObject><text x="46" y="24" fill="#000000" text-anchor="middle" font-size="22px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="70" y="155" width="120" height="45" fill="#8aadff" stroke="#8aadff" pointer-events="none"/><g transform="translate(83.5,164.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="92" height="26" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 22px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 94px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New">"Susan"</font></div></div></foreignObject><text x="46" y="24" fill="#000000" text-anchor="middle" font-size="22px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="250" y="230" width="100" height="30" fill="#ccff99" stroke="#85d2ff" pointer-events="none"/><g transform="translate(279.5,232.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="40" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 40px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px">age</font></div></div></foreignObject><text x="20" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><path d="M 190 105 L 243.63 105" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 248.88 105 L 241.88 108.5 L 243.63 105 L 241.88 101.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 190 177 L 243.63 177" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 248.88 177 L 241.88 180.5 L 243.63 177 L 241.88 173.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 190 250 L 243.63 250" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 248.88 250 L 241.88 253.5 L 243.63 250 L 241.88 246.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(96.5,42.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="53" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; white-space: nowrap;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px"><b>keys</b></font></div></div></foreignObject><text x="27" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><g transform="translate(251.5,52.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="92" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; white-space: nowrap;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px"><b>subkeys</b></font></div></div></foreignObject><text x="46" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><g transform="translate(1.5,2.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="238" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; white-space: nowrap;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px"><b>Student dictionary</b></font></div></div></foreignObject><text x="119" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">&lt;font face="Courier New" style="font-size: 22px"&gt;&lt;b&gt;Student dictionary&lt;/b&gt;&lt;/font&gt;</text></switch></g><rect x="250" y="110" width="100" height="30" fill="#ccff99" stroke="#8aadff" pointer-events="none"/><g transform="translate(253.5,112.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="92" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 94px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px">address</font></div></div></foreignObject><text x="46" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="250" y="185" width="100" height="30" fill="#ccff99" stroke="#8aadff" pointer-events="none"/><g transform="translate(253.5,187.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="92" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 94px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px">address</font></div></div></foreignObject><text x="46" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="250" y="260" width="100" height="30" fill="#ccff99" stroke="#85d2ff" pointer-events="none"/><g transform="translate(253.5,262.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="92" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 94px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px">address</font></div></div></foreignObject><text x="46" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="380" y="80" width="100" height="30" fill="#ffe599" stroke="#ccff99" pointer-events="none"/><g transform="translate(416.5,82.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="26" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 28px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px">21</font></div></div></foreignObject><text x="13" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="380" y="110" width="100" height="30" fill="#ffe599" stroke="#ccff99" pointer-events="none"/><g transform="translate(377.5,112.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="105" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 107px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px">"Lisbon"</font></div></div></foreignObject><text x="53" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="380" y="160" width="100" height="30" fill="#ffe599" stroke="#97ff52" pointer-events="none"/><g transform="translate(416.5,162.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="26" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 28px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px">22</font></div></div></foreignObject><text x="13" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="380" y="185" width="100" height="30" fill="#ffe599" stroke="#97ff52" pointer-events="none"/><g transform="translate(377.5,187.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="105" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 107px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New"><span style="font-size: 22px">"Sweden"</span></font></div></div></foreignObject><text x="53" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="380" y="230" width="100" height="30" fill="#ffe599" stroke="#ccff99" pointer-events="none"/><g transform="translate(416.5,232.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="26" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 28px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px">32</font></div></div></foreignObject><text x="13" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="380" y="260" width="100" height="30" fill="#ffe599" stroke="#ccff99" pointer-events="none"/><g transform="translate(377.5,262.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="105" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 107px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px">"Turkey"</font></div></div></foreignObject><text x="53" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><path d="M 350 95 L 373.63 95" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 378.88 95 L 371.88 98.5 L 373.63 95 L 371.88 91.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 350 124 L 373.63 124" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 378.88 124 L 371.88 127.5 L 373.63 124 L 371.88 120.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 350 205 L 373.63 205" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 378.88 205 L 371.88 208.5 L 373.63 205 L 371.88 201.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 350 175 L 373.63 175" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 378.88 175 L 371.88 178.5 L 373.63 175 L 371.88 171.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 350 245 L 373.63 245" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 378.88 245 L 371.88 248.5 L 373.63 245 L 371.88 241.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 350 274 L 373.63 274" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 378.88 274 L 371.88 277.5 L 373.63 274 L 371.88 270.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(366.5,52.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="119" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; white-space: nowrap;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px"><b>subvalues</b></font></div></div></foreignObject><text x="60" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><g transform="translate(126.5,342.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="251" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; white-space: nowrap;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px"><b>Size of Student = 3</b></font></div></div></foreignObject><text x="126" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">&lt;font face="Courier New" style="font-size: 22px"&gt;&lt;b&gt;Size of Student = 3&lt;/b&gt;&lt;/font&gt;</text></switch></g></g></svg>