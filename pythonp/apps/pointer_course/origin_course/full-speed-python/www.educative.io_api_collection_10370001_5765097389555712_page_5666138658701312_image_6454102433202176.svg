<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="731px" height="684px" version="1.1"><defs/><g transform="translate(0.5,0.5)"><rect x="0" y="30" width="400" height="650" fill="#000000" stroke="#000000" transform="translate(2,3)" opacity="0.25"/><rect x="0" y="30" width="400" height="650" fill="#ffffff" stroke="#000000" pointer-events="none"/><rect x="50" y="70" width="160" height="570" fill="#ffffff" stroke="#000000" pointer-events="none"/><rect x="250" y="90" width="100" height="30" fill="#baffa8" stroke="#8aadff" pointer-events="none"/><g transform="translate(286.5,92.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="26" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 28px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px">10</font></div></div></foreignObject><text x="13" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="250" y="170" width="100" height="30" fill="#baffa8" stroke="#8aadff" pointer-events="none"/><g transform="translate(286.5,172.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="26" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 28px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px">11</font></div></div></foreignObject><text x="13" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="70" y="230" width="120" height="50" fill="#8aadff" stroke="#78b2ff" pointer-events="none"/><g transform="translate(89.5,242.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="80" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 80px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px">"Anna"</font></div></div></foreignObject><text x="40" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="70" y="80" width="120" height="50" fill="#8aadff" stroke="#8aadff" pointer-events="none"/><g transform="translate(83.5,91.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="92" height="26" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 22px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 94px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New">"Peter"</font></div></div></foreignObject><text x="46" y="24" fill="#000000" text-anchor="middle" font-size="22px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="70" y="155" width="120" height="45" fill="#8aadff" stroke="#8aadff" pointer-events="none"/><g transform="translate(76.5,164.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="106" height="26" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 22px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 106px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New">"Isabel"</font></div></div></foreignObject><text x="53" y="24" fill="#000000" text-anchor="middle" font-size="22px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="250" y="240" width="100" height="30" fill="#baffa8" stroke="#8aadff" pointer-events="none"/><g transform="translate(292.5,242.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="14" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 14px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px">9</font></div></div></foreignObject><text x="7" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><path d="M 190 105 L 243.63 105" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 248.88 105 L 241.88 108.5 L 243.63 105 L 241.88 101.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 190 185 L 243.63 185" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 248.88 185 L 241.88 188.5 L 243.63 185 L 241.88 181.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 190 255 L 243.63 255" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 248.88 255 L 241.88 258.5 L 243.63 255 L 241.88 251.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(96.5,42.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="53" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; white-space: nowrap;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px"><b>keys</b></font></div></div></foreignObject><text x="27" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><g transform="translate(251.5,52.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="79" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; white-space: nowrap;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px"><b>values</b></font></div></div></foreignObject><text x="40" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><g transform="translate(1.5,2.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="238" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; white-space: nowrap;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px"><b>Student dictionary</b></font></div></div></foreignObject><text x="119" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">&lt;font face="Courier New" style="font-size: 22px"&gt;&lt;b&gt;Student dictionary&lt;/b&gt;&lt;/font&gt;</text></switch></g><rect x="70" y="295" width="120" height="50" fill="#8aadff" stroke="#78b2ff" pointer-events="none"/><g transform="translate(76.5,307.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="106" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 106px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px">"Thomas"</font></div></div></foreignObject><text x="53" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="70" y="363" width="120" height="50" fill="#8aadff" stroke="#78b2ff" pointer-events="none"/><g transform="translate(96.5,375.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="66" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 68px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px">"Bob"</font></div></div></foreignObject><text x="33" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="70" y="430" width="120" height="50" fill="#8aadff" stroke="#78b2ff" pointer-events="none"/><g transform="translate(76.5,442.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="106" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 106px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px">"Joseph"</font></div></div></foreignObject><text x="53" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="70" y="500" width="120" height="50" fill="#6666ff" stroke="#78b2ff" pointer-events="none"/><g transform="translate(83.5,512.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="92" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 94px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px">"Maria"</font></div></div></foreignObject><text x="46" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="70" y="570" width="120" height="50" fill="#8aadff" stroke="#78b2ff" pointer-events="none"/><g transform="translate(70.5,582.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="118" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 120px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px">"Gabriel"</font></div></div></foreignObject><text x="59" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><path d="M 190 320 L 243.63 320" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 248.88 320 L 241.88 323.5 L 243.63 320 L 241.88 316.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 190 388 L 243.63 388" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 248.88 388 L 241.88 391.5 L 243.63 388 L 241.88 384.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 190 455 L 243.63 455" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 248.88 455 L 241.88 458.5 L 243.63 455 L 241.88 451.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 190 529 L 243.63 529" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 248.88 529 L 241.88 532.5 L 243.63 529 L 241.88 525.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 190 595 L 243.63 595" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 248.88 595 L 241.88 598.5 L 243.63 595 L 241.88 591.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><rect x="250" y="307" width="100" height="30" fill="#baffa8" stroke="#85d2ff" pointer-events="none"/><g transform="translate(286.5,309.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="26" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 28px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px">10</font></div></div></foreignObject><text x="13" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="250" y="375" width="100" height="30" fill="#baffa8" stroke="#85d2ff" pointer-events="none"/><g transform="translate(286.5,377.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="26" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 28px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px">10</font></div></div></foreignObject><text x="13" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="250" y="440" width="100" height="30" fill="#baffa8" stroke="#85d2ff" pointer-events="none"/><g transform="translate(286.5,442.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="26" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 28px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px">11</font></div></div></foreignObject><text x="13" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="250" y="515" width="100" height="30" fill="#67ab9f" stroke="#85d2ff" pointer-events="none"/><g transform="translate(286.5,517.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="26" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 28px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px">12</font></div></div></foreignObject><text x="13" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><rect x="250" y="580" width="100" height="30" fill="#baffa8" stroke="#85d2ff" pointer-events="none"/><g transform="translate(286.5,582.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="26" height="24" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 28px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><font face="Courier New" style="font-size: 22px">10</font></div></div></foreignObject><text x="13" y="18" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><g transform="translate(441.5,370.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="277" height="44" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; white-space: nowrap;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><b><font style="font-size: 21px" face="Courier New">Key with maximum value</font></b><div><b><font face="Courier New" size="4">=&gt; Maria </font></b></div></div></div></foreignObject><text x="139" y="28" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g></g></svg>