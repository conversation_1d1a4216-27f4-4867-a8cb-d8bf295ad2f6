<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="961px" height="685px" viewBox="-0.5 -0.5 961 685"><defs/><g><rect x="0" y="31" width="400" height="650" fill="#000000" stroke="#000000" transform="translate(2,3)" opacity="0.25"/><rect x="0" y="31" width="400" height="650" fill="#ffffff" stroke="#000000" pointer-events="all"/><rect x="50" y="71" width="160" height="570" fill="#ffffff" stroke="#000000" pointer-events="all"/><rect x="250" y="91" width="100" height="30" fill="#baffa8" stroke="#8aadff" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 106px; margin-left: 251px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font face="Courier New" style="font-size: 22px">10</font></div></div></div></foreignObject><text x="300" y="110" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">10</text></switch></g><rect x="250" y="171" width="100" height="30" fill="#baffa8" stroke="#8aadff" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 186px; margin-left: 251px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font face="Courier New" style="font-size: 22px">11</font></div></div></div></foreignObject><text x="300" y="190" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">11</text></switch></g><rect x="70" y="231" width="120" height="50" fill="#8aadff" stroke="#78b2ff" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 256px; margin-left: 71px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font face="Courier New" style="font-size: 22px">"Anna"</font></div></div></div></foreignObject><text x="130" y="260" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">"Anna"</text></switch></g><rect x="70" y="81" width="120" height="50" fill="#8aadff" stroke="#8aadff" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 106px; margin-left: 71px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 22px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font face="Courier New">"Peter"</font></div></div></div></foreignObject><text x="130" y="113" fill="#000000" font-family="Helvetica" font-size="22px" text-anchor="middle">"Peter"</text></switch></g><rect x="70" y="156" width="120" height="45" fill="#8aadff" stroke="#8aadff" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 179px; margin-left: 71px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 22px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font face="Courier New">"Isabel"</font></div></div></div></foreignObject><text x="130" y="185" fill="#000000" font-family="Helvetica" font-size="22px" text-anchor="middle">"Isabel"</text></switch></g><rect x="250" y="241" width="100" height="30" fill="#baffa8" stroke="#8aadff" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 256px; margin-left: 251px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font face="Courier New" style="font-size: 22px">9</font></div></div></div></foreignObject><text x="300" y="260" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">9</text></switch></g><path d="M 190 106 L 243.63 106" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 248.88 106 L 241.88 109.5 L 243.63 106 L 241.88 102.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 190 186 L 243.63 186" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 248.88 186 L 241.88 189.5 L 243.63 186 L 241.88 182.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 190 256 L 243.63 256" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 248.88 256 L 241.88 259.5 L 243.63 256 L 241.88 252.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><rect x="95" y="41" width="70" height="20" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 44px; margin-left: 97px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; "><font face="Courier New" style="font-size: 22px"><b>keys</b></font></div></div></div></foreignObject><text x="97" y="56" fill="#000000" font-family="Helvetica" font-size="12px">keys</text></switch></g><rect x="250" y="51" width="90" height="20" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 54px; margin-left: 252px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; "><font face="Courier New" style="font-size: 22px"><b>values</b></font></div></div></div></foreignObject><text x="252" y="66" fill="#000000" font-family="Helvetica" font-size="12px">values</text></switch></g><rect x="0" y="1" width="250" height="20" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 4px; margin-left: 2px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; "><font face="Courier New" style="font-size: 22px"><b>Student dictionary</b></font></div></div></div></foreignObject><text x="2" y="16" fill="#000000" font-family="Helvetica" font-size="12px">Student dictionary</text></switch></g><rect x="70" y="296" width="120" height="50" fill="#8aadff" stroke="#78b2ff" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 321px; margin-left: 71px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font face="Courier New" style="font-size: 22px">"Thomas"</font></div></div></div></foreignObject><text x="130" y="325" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">"Thomas"</text></switch></g><rect x="70" y="364" width="120" height="50" fill="#8aadff" stroke="#78b2ff" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 389px; margin-left: 71px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font face="Courier New" style="font-size: 22px">"Bob"</font></div></div></div></foreignObject><text x="130" y="393" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">"Bob"</text></switch></g><rect x="70" y="431" width="120" height="50" fill="#8aadff" stroke="#78b2ff" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 456px; margin-left: 71px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font face="Courier New" style="font-size: 22px">"Joseph"</font></div></div></div></foreignObject><text x="130" y="460" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">"Joseph"</text></switch></g><rect x="70" y="501" width="120" height="50" fill="#8aadff" stroke="#78b2ff" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 526px; margin-left: 71px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font face="Courier New" style="font-size: 22px">"Maria"</font></div></div></div></foreignObject><text x="130" y="530" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">"Maria"</text></switch></g><rect x="70" y="571" width="120" height="50" fill="#8aadff" stroke="#78b2ff" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 596px; margin-left: 71px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font face="Courier New" style="font-size: 22px">"Gabriel"</font></div></div></div></foreignObject><text x="130" y="600" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">"Gabriel"</text></switch></g><path d="M 190 321 L 243.63 321" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 248.88 321 L 241.88 324.5 L 243.63 321 L 241.88 317.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 190 389 L 243.63 389" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 248.88 389 L 241.88 392.5 L 243.63 389 L 241.88 385.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 190 456 L 243.63 456" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 248.88 456 L 241.88 459.5 L 243.63 456 L 241.88 452.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 190 530 L 243.63 530" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 248.88 530 L 241.88 533.5 L 243.63 530 L 241.88 526.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 190 596 L 243.63 596" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 248.88 596 L 241.88 599.5 L 243.63 596 L 241.88 592.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><rect x="250" y="308" width="100" height="30" fill="#baffa8" stroke="#85d2ff" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 323px; margin-left: 251px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font face="Courier New" style="font-size: 22px">10</font></div></div></div></foreignObject><text x="300" y="327" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">10</text></switch></g><rect x="250" y="376" width="100" height="30" fill="#baffa8" stroke="#85d2ff" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 391px; margin-left: 251px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font face="Courier New" style="font-size: 22px">10</font></div></div></div></foreignObject><text x="300" y="395" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">10</text></switch></g><rect x="250" y="441" width="100" height="30" fill="#baffa8" stroke="#85d2ff" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 456px; margin-left: 251px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font face="Courier New" style="font-size: 22px">11</font></div></div></div></foreignObject><text x="300" y="460" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">11</text></switch></g><rect x="250" y="516" width="100" height="30" fill="#baffa8" stroke="#85d2ff" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 531px; margin-left: 251px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font face="Courier New" style="font-size: 22px">12</font></div></div></div></foreignObject><text x="300" y="535" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">12</text></switch></g><rect x="250" y="581" width="100" height="30" fill="#baffa8" stroke="#85d2ff" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 596px; margin-left: 251px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font face="Courier New" style="font-size: 22px">10</font></div></div></div></foreignObject><text x="300" y="600" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">10</text></switch></g><rect x="558" y="30" width="400" height="650" fill="#000000" stroke="#000000" transform="translate(2,3)" opacity="0.25"/><rect x="558" y="30" width="400" height="650" fill="#ffffff" stroke="#000000" pointer-events="all"/><rect x="608" y="70" width="160" height="570" fill="#ffffff" stroke="#000000" pointer-events="all"/><rect x="808" y="90" width="100" height="30" fill="#9ac7bf" stroke="#8aadff" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 105px; margin-left: 809px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font face="Courier New" style="font-size: 22px">11</font></div></div></div></foreignObject><text x="858" y="109" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">11</text></switch></g><rect x="808" y="170" width="100" height="30" fill="#9ac7bf" stroke="#8aadff" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 185px; margin-left: 809px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font face="Courier New" style="font-size: 22px">12</font></div></div></div></foreignObject><text x="858" y="189" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">12</text></switch></g><rect x="628" y="230" width="120" height="50" fill="#8aadff" stroke="#78b2ff" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 255px; margin-left: 629px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font face="Courier New" style="font-size: 22px">"Anna"</font></div></div></div></foreignObject><text x="688" y="259" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">"Anna"</text></switch></g><rect x="628" y="80" width="120" height="50" fill="#8aadff" stroke="#8aadff" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 105px; margin-left: 629px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 22px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font face="Courier New">"Peter"</font></div></div></div></foreignObject><text x="688" y="112" fill="#000000" font-family="Helvetica" font-size="22px" text-anchor="middle">"Peter"</text></switch></g><rect x="628" y="155" width="120" height="45" fill="#8aadff" stroke="#8aadff" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 178px; margin-left: 629px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 22px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font face="Courier New">"Isabel"</font></div></div></div></foreignObject><text x="688" y="184" fill="#000000" font-family="Helvetica" font-size="22px" text-anchor="middle">"Isabel"</text></switch></g><rect x="808" y="240" width="100" height="30" fill="#9ac7bf" stroke="#8aadff" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 255px; margin-left: 809px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font face="Courier New"><span style="font-size: 22px">10</span></font></div></div></div></foreignObject><text x="858" y="259" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">10</text></switch></g><path d="M 748 105 L 801.63 105" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 806.88 105 L 799.88 108.5 L 801.63 105 L 799.88 101.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 748 185 L 801.63 185" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 806.88 185 L 799.88 188.5 L 801.63 185 L 799.88 181.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 748 255 L 801.63 255" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 806.88 255 L 799.88 258.5 L 801.63 255 L 799.88 251.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><rect x="653" y="40" width="70" height="20" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 43px; margin-left: 655px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; "><font face="Courier New" style="font-size: 22px"><b>keys</b></font></div></div></div></foreignObject><text x="655" y="55" fill="#000000" font-family="Helvetica" font-size="12px">keys</text></switch></g><rect x="808" y="50" width="90" height="20" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 53px; margin-left: 810px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; "><font face="Courier New" style="font-size: 22px"><b>values</b></font></div></div></div></foreignObject><text x="810" y="65" fill="#000000" font-family="Helvetica" font-size="12px">values</text></switch></g><rect x="558" y="0" width="250" height="20" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 3px; margin-left: 560px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; "><font face="Courier New" style="font-size: 22px"><b>Student dictionary</b></font></div></div></div></foreignObject><text x="560" y="15" fill="#000000" font-family="Helvetica" font-size="12px">Student dictionary</text></switch></g><rect x="628" y="295" width="120" height="50" fill="#8aadff" stroke="#78b2ff" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 320px; margin-left: 629px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font face="Courier New" style="font-size: 22px">"Thomas"</font></div></div></div></foreignObject><text x="688" y="324" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">"Thomas"</text></switch></g><rect x="628" y="363" width="120" height="50" fill="#8aadff" stroke="#78b2ff" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 388px; margin-left: 629px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font face="Courier New" style="font-size: 22px">"Bob"</font></div></div></div></foreignObject><text x="688" y="392" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">"Bob"</text></switch></g><rect x="628" y="430" width="120" height="50" fill="#8aadff" stroke="#78b2ff" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 455px; margin-left: 629px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font face="Courier New" style="font-size: 22px">"Joseph"</font></div></div></div></foreignObject><text x="688" y="459" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">"Joseph"</text></switch></g><rect x="628" y="500" width="120" height="50" fill="#8aadff" stroke="#78b2ff" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 525px; margin-left: 629px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font face="Courier New" style="font-size: 22px">"Maria"</font></div></div></div></foreignObject><text x="688" y="529" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">"Maria"</text></switch></g><rect x="628" y="570" width="120" height="50" fill="#8aadff" stroke="#78b2ff" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 595px; margin-left: 629px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font face="Courier New" style="font-size: 22px">"Gabriel"</font></div></div></div></foreignObject><text x="688" y="599" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">"Gabriel"</text></switch></g><path d="M 748 320 L 801.63 320" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 806.88 320 L 799.88 323.5 L 801.63 320 L 799.88 316.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 748 388 L 801.63 388" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 806.88 388 L 799.88 391.5 L 801.63 388 L 799.88 384.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 748 455 L 801.63 455" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 806.88 455 L 799.88 458.5 L 801.63 455 L 799.88 451.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 748 529 L 801.63 529" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 806.88 529 L 799.88 532.5 L 801.63 529 L 799.88 525.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 748 595 L 801.63 595" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 806.88 595 L 799.88 598.5 L 801.63 595 L 799.88 591.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><rect x="808" y="307" width="100" height="30" fill="#9ac7bf" stroke="#85d2ff" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 322px; margin-left: 809px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font face="Courier New" style="font-size: 22px">11</font></div></div></div></foreignObject><text x="858" y="326" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">11</text></switch></g><rect x="808" y="375" width="100" height="30" fill="#9ac7bf" stroke="#85d2ff" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 390px; margin-left: 809px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font face="Courier New" style="font-size: 22px">11</font></div></div></div></foreignObject><text x="858" y="394" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">11</text></switch></g><rect x="808" y="440" width="100" height="30" fill="#9ac7bf" stroke="#85d2ff" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 455px; margin-left: 809px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font face="Courier New" style="font-size: 22px">12</font></div></div></div></foreignObject><text x="858" y="459" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">12</text></switch></g><rect x="808" y="515" width="100" height="30" fill="#9ac7bf" stroke="#85d2ff" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 530px; margin-left: 809px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font face="Courier New" style="font-size: 22px">13<br /></font></div></div></div></foreignObject><text x="858" y="534" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">13&#xa;</text></switch></g><rect x="808" y="580" width="100" height="30" fill="#9ac7bf" stroke="#85d2ff" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 595px; margin-left: 809px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><font face="Courier New" style="font-size: 22px">11</font></div></div></div></foreignObject><text x="858" y="599" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">11</text></switch></g><path d="M 430.5 342 L 430.5 332 L 510.5 332 L 510.5 321.5 L 529.5 337 L 510.5 352.5 L 510.5 342 Z" fill="#999999" stroke="#666666" stroke-linejoin="round" stroke-miterlimit="10" pointer-events="all"/><rect x="420" y="363" width="140" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 366px; margin-left: 422px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; "><font face="Courier New"><b>increment age </b></font><font face="Courier New"><b>of </b></font><div><font face="Courier New"><b>all students </b></font><b style="font-family: &quot;courier new&quot;">by</b><b style="font-family: &quot;courier new&quot;"> 1</b></div></div></div></div></foreignObject><text x="422" y="378" fill="#000000" font-family="Helvetica" font-size="12px">increment age of...</text></switch></g><rect x="450" y="298" width="50" height="20" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 301px; margin-left: 452px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; "><font face="Courier New"><b>n = 1</b></font></div></div></div></foreignObject><text x="452" y="313" fill="#000000" font-family="Helvetica" font-size="12px">n = 1</text></switch></g></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://desk.draw.io/support/solutions/articles/16000042487" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Viewer does not support full SVG 1.1</text></a></switch></svg>