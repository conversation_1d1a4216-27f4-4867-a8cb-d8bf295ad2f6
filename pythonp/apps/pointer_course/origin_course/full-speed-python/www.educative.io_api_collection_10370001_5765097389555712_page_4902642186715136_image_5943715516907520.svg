<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="531px" height="194px" viewBox="-0.5 -0.5 531 194"><defs/><g><rect x="80" y="70" width="400" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 73px; margin-left: 82px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 22px; font-family: Courier New; color: #0742a2 ; line-height: 1.2; pointer-events: all; white-space: nowrap; "><font style="font-size: 27px">[x*x for x in [0,1,2,3]]</font></div></div></div></foreignObject><text x="198" y="26" fill="#0742a2 " text-anchor="middle" font-size="22px" font-family="Courier New">&lt;font style="font-size: 27px"&gt;[x*x for x in [0,1,2,3]]&lt;/font&gt;</text></switch></g><rect x="10" y="130" width="240" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 133px; margin-left: 12px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 20px; font-family: Courier New; color: #0742a2 ; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">output expression</div></div></div></foreignObject><text x="118" y="25" fill="#0742a2 " text-anchor="middle" font-size="20px" font-family="Courier New">output expression</text></switch></g><rect x="180" y="20" width="120" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 23px; margin-left: 182px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 20px; font-family: Courier New; color: #0742a2 ; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">variable</div></div></div></foreignObject><text x="58" y="25" fill="#0742a2 " text-anchor="middle" font-size="20px" font-family="Courier New">variable</text></switch></g><rect x="310" y="130" width="220" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 133px; margin-left: 312px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 22px; font-family: Courier New; color: #0742a2 ; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; "><font style="font-size: 19px">reference sequence</font></div></div></div></foreignObject><text x="312" y="155" fill="#0742a2 " font-family="Courier New" font-size="22px">[Not supported by viewer]</text></switch></g><path d="M 120 140 L 120 106.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 120 101.12 L 123.5 108.12 L 120 106.37 L 116.5 108.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 380 140 L 380 106.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 380 101.12 L 383.5 108.12 L 380 106.37 L 376.5 108.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 240 50 L 240 73.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 240 78.88 L 236.5 71.88 L 240 73.63 L 243.5 71.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><rect x="0" y="160" width="270" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 163px; margin-left: 2px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; "><div style="text-align: center"><b><font face="Courier New">In this case,</font></b></div><div style="text-align: center"><font color="#0066cc" face="Courier New"><b> the list of square of elements in x</b></font></div></div></div></div></foreignObject><text x="2" y="175" fill="#000000" font-family="Helvetica" font-size="12px">[Not supported by viewer]</text></switch></g><rect x="360" y="155" width="70" height="20" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 158px; margin-left: 362px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; "><div style="text-align: center"><font face="Courier New" color="#0066cc"><b>The list</b></font></div></div></div></div></foreignObject><text x="362" y="170" fill="#000000" font-family="Helvetica" font-size="12px">[Not supported by viewer]</text></switch></g><rect x="200" y="0" width="70" height="20" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 3px; margin-left: 202px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; "><div style="text-align: center"><font face="Courier New" color="#0066cc"><b>iterator</b></font></div></div></div></div></foreignObject><text x="202" y="15" fill="#000000" font-family="Helvetica" font-size="12px">[Not supported by viewer]</text></switch></g></g></svg>