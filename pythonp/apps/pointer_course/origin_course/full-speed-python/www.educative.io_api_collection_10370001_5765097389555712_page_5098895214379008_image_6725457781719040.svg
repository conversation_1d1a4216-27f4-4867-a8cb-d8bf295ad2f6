<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="521px" height="141px" viewBox="-0.5 -0.5 521 141"><defs/><g><rect x="70" y="50" width="440" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 53px; margin-left: 72px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 22px; font-family: Courier New; color: #0742a2 ; line-height: 1.2; pointer-events: all; white-space: nowrap; "><font style="font-size: 27px">[x*x for x in range(1,11)]</font></div></div></div></foreignObject><text x="218" y="26" fill="#0742a2 " text-anchor="middle" font-size="22px" font-family="Courier New">&lt;font style="font-size: 27px"&gt;[x*x for x in range(1,11)]&lt;/font&gt;</text></switch></g><rect x="0" y="110" width="240" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 113px; margin-left: 2px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 20px; font-family: Courier New; color: #0742a2 ; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">output expression</div></div></div></foreignObject><text x="118" y="25" fill="#0742a2 " text-anchor="middle" font-size="20px" font-family="Courier New">output expression</text></switch></g><rect x="170" y="0" width="120" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 3px; margin-left: 172px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 20px; font-family: Courier New; color: #0742a2 ; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">variable</div></div></div></foreignObject><text x="58" y="25" fill="#0742a2 " text-anchor="middle" font-size="20px" font-family="Courier New">variable</text></switch></g><rect x="300" y="110" width="220" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 113px; margin-left: 302px;"><div style="box-sizing: border-box; font-size: 0; text-align: left; "><div style="display: inline-block; font-size: 22px; font-family: Courier New; color: #0742a2 ; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; "><font style="font-size: 19px">reference sequence</font></div></div></div></foreignObject><text x="302" y="135" fill="#0742a2 " font-family="Courier New" font-size="22px">[Not supported by viewer]</text></switch></g><path d="M 110 120 L 110 86.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 110 81.12 L 113.5 88.12 L 110 86.37 L 106.5 88.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 370 120 L 370 86.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 370 81.12 L 373.5 88.12 L 370 86.37 L 366.5 88.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><path d="M 230 30 L 230 53.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 230 58.88 L 226.5 51.88 L 230 53.63 L 233.5 51.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/></g></svg>