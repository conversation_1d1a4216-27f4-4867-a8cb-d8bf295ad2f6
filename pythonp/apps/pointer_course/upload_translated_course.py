# 基础配置与依赖
import json
import requests
import os
import glob
from vllm import LLM, SamplingParams

SERVER_IP = "************:8012"
BASE = f"http://{SERVER_IP}/api/v1"
LOGIN = f"{BASE}/auth/login"
login_req = {
    "email": "<EMAIL>",
    "password": "chen4212"
}
login_resp = requests.post(LOGIN, json=login_req)
TOKEN = login_resp.json().get('data', {}).get("access_token")

HEADERS = {
    "Authorization": f"Bearer {TOKEN}",
    "Content-Type": "application/json",
}

# vLLM: 加载 Seed-X 模型与推理参数
model = LLM(
    model="ByteDance-Seed/Seed-X-PPO-7B",
    max_num_seqs=64,
    tensor_parallel_size=1,
    enable_prefix_caching=True,
    gpu_memory_utilization=0.8,
    swap_space=2.0,
)
decoding_params = SamplingParams(
    temperature=0,
    max_tokens=512,
    skip_special_tokens=True,
)

def clean_llm_output(text: str) -> str:
    """移除可能出现的 COT 内容，并做轻量清洗。"""
    if not isinstance(text, str):
        return ""
    t = text.strip()
    # 若误返回 JSON 列表，尝试解析第一个元素
    try:
        parsed = json.loads(t)
        if isinstance(parsed, list) and parsed:
            t = str(parsed[0]).strip()
    except Exception:
        pass
    # 截断 [COT] 及其后续内容（大小写/换行容错）
    for marker in ["[COT]", "\n[COT]", "[CoT]", "\n[CoT]", "[Cot]", "\n[Cot]"]:
        idx = t.find(marker)
        if idx != -1:
            t = t[:idx].strip()
    # 去掉成对引号包裹
    if (t.startswith('"') and t.endswith('"')) or (t.startswith("'") and t.endswith("'")):
        t = t[1:-1].strip()
    # 去掉全角引号
    if (t.startswith('“') and t.endswith('”')) or (t.startswith('‘') and t.endswith('’')):
        t = t[1:-1].strip()
    return t

# 新增：构造统一 prompt 与批量翻译
def build_prompt(text: str) -> str:
    return f"Translate the following English sentence into Chinese:\n{text} <zh>"

def translate_batch(texts):
    uniq = []
    seen = set()
    for t in texts:
        if not t:
            continue
        if t not in seen:
            uniq.append(t)
            seen.add(t)
    if not uniq:
        return {}
    prompts = [build_prompt(t) for t in uniq]
    results = model.generate(prompts, decoding_params)
    outs = []
    for res in results:
        o = clean_llm_output(res.outputs[0].text) if res and res.outputs else ""
        outs.append(o)
    return dict(zip(uniq, outs))

# 批量上传目录
upload_dir = "course"
# 新增：翻译结果输出目录
translated_output_dir = os.path.join(upload_dir, "translated")
os.makedirs(translated_output_dir, exist_ok=True)

# 新增：文件名清洗
def sanitize_filename(name: str, default="course"):
    name = (name or default).strip()
    safe = "".join(c if (c.isalnum() or c in "._-（）()[] ") else "_" for c in name)
    safe = "_".join(safe.split())  # 空白转下划线
    return (safe or default)[:80]

# 读取 upload_nodes 中的 JSON，并收集课程
files = sorted(glob.glob(os.path.join(upload_dir, "*.json")))[:3]
course_items = []
for path in files:
    with open(path, encoding="utf-8") as f:
        raw = json.load(f)
    courses = raw.get("courses")
    if isinstance(courses, list):
        iterable = courses
    elif isinstance(courses, dict):
        iterable = [courses]
    else:
        iterable = []
    for course in iterable:
        # 先缓存原文，稍后统一批量翻译后再组装 node_req
        node_title_src = course.get("title", "")
        node_desc_src = course.get("summary", "")
        course_items.append({
            "file": path,
            "course": course,
            "node_title_src": node_title_src,
            "node_desc_src": node_desc_src
        })

print(f"发现 {len(files)} 个文件，待上传课程 {len(course_items)} 个")

# 新增：对所有节点标题与描述批量翻译并构建 node_req
node_texts = []
for item in course_items:
    node_texts.append(item["node_title_src"])
    node_texts.append(item["node_desc_src"])
node_map = translate_batch(node_texts)
for item in course_items:
    item["node_req"] = {
        "title": node_map.get(item["node_title_src"], item["node_title_src"]),
        "description": node_map.get(item["node_desc_src"], item["node_desc_src"]),
        "estimated_hours": 6,
        "difficulty": 2
    }

created = []
for item in course_items:
    node_resp = requests.post(f"{BASE}/learning-nodes", json=item["node_req"], headers=HEADERS)
    if node_resp.status_code >= 400:
        print(f"创建节点失败: {item['node_req'].get('title','')} -> {getattr(node_resp, 'text', '')}")
        continue
    node = node_resp.json().get("data", {})
    item["node_id"] = node.get("id")
    created.append(item)
    print("已创建节点:", item["node_id"], "标题:", item["node_req"]["title"])

print(f"成功创建 {len(created)}/{len(course_items)} 个节点")

# 遍历每个节点对应课程分类和页面，创建 lessons，并校验
total_success = 0
for item in created:
    course = item["course"]
    node_id = item["node_id"]
    success = 0

    # 新增：为该课程收集需要翻译的所有文本（lesson 标题/描述、text_explanation 的 title/body、quiz 的 question）
    to_translate = []
    lessons = course.get("categories", [])
    for lesson in lessons:
        to_translate.append(lesson.get("title", ""))
        to_translate.append(lesson.get("summary", ""))
        for block in lesson.get("pages", []):
            if block.get('type') == 'Lesson':
                to_translate.append(block.get("title", "text"))
                to_translate.append(block.get("text", block.get("mdHtml", "")))
            elif block.get('type') == 'Quiz':
                for quiz in block.get("questions", []):
                    to_translate.append(quiz.get("questionText", ""))

    trans_map = translate_batch(to_translate)

    # 新增：构建“翻译后的课程结构”
    translated_categories = []

    for lesson_order, lesson in enumerate(lessons):
        # 使用批量翻译结果
        lesson_title = trans_map.get(lesson.get("title", ""), lesson.get("title", ""))
        lesson_description = trans_map.get(lesson.get("summary", ""), lesson.get("summary", ""))
        lesson_type = "text"
        estimated_minutes = 30
        difficulty = 2
        content_flow = []
        index = 0

        # 新增：构造翻译后的 lesson 副本
        translated_lesson = dict(lesson)
        translated_lesson["title"] = lesson_title
        translated_lesson["summary"] = lesson_description
        translated_pages = []

        for block in lesson.get("pages", []):
            if block.get('type') == 'Lesson':
                title_src = block.get("title", "text")
                body_src = block.get("text", block.get("mdHtml", ""))

                content_flow.append({
                    "type": "text_explanation",
                    "order": index,
                    "data": {
                        "title": trans_map.get(title_src, title_src),
                        "body": trans_map.get(body_src, body_src)
                    }
                })
                index += 1

                # 翻译后的页面副本
                nb = dict(block)
                nb["title"] = trans_map.get(title_src, title_src)
                if "text" in nb:
                    nb["text"] = trans_map.get(body_src, body_src)
                elif "mdHtml" in nb:
                    nb["mdHtml"] = trans_map.get(body_src, body_src)
                translated_pages.append(nb)

            elif block.get('type') == 'Quiz':
                for quiz in block.get("questions", []):
                    question = {
                        "type": "multiple_choice_quiz",
                        "order": index,
                        "data": {
                            "difficulty": 2,
                            "question": trans_map.get(quiz.get("questionText", ""), quiz.get("questionText", "")),
                            "options": [],
                            "correct_answer_indices": []
                        }
                    }
                    for op_i, option in enumerate(quiz.get("questionOptions", [])):
                        question["data"]["options"].append(option.get("text", ""))
                        if option.get("correct", False):
                            question["data"]["correct_answer_index"] = op_i
                            question["data"]["correct_answer_indices"].append(op_i)
                    question['data']['single_answer'] = len(question['data']['correct_answer_indices']) == 1
                    content_flow.append(question)
                    index += 1

                # 翻译后的页面副本（仅问题文本）
                nb = dict(block)
                new_questions = []
                for q in block.get("questions", []):
                    nq = dict(q)
                    q_src = q.get("questionText", "")
                    nq["questionText"] = trans_map.get(q_src, q_src)
                    new_questions.append(nq)
                nb["questions"] = new_questions
                translated_pages.append(nb)

            else:
                # 其它类型页面：直接拷贝
                translated_pages.append(dict(block))

        translated_lesson["pages"] = translated_pages
        translated_categories.append(translated_lesson)

        lesson_req = {
            "title": lesson_title,
            "description": lesson_description,
            "type": lesson_type,
            "estimated_minutes": estimated_minutes,
            "difficulty": difficulty,
            "content_flow": content_flow,
            "node_id": node_id,
            "order": lesson_order
        }
        response = requests.post(f"{BASE}/learning-lessons", headers=HEADERS, json=lesson_req)
        if response.status_code == 201:
            success += 1
        else:
            print("创建 lesson 失败：", response.text)

    # 新增：写出翻译后的课程 JSON
    translated_course = dict(course)
    translated_course["title"] = item["node_req"]["title"]
    translated_course["summary"] = item["node_req"]["description"]
    translated_course["categories"] = translated_categories

    src_base = os.path.splitext(os.path.basename(item["file"]))[0]
    safe_title = sanitize_filename(item["node_req"]["title"])
    out_path = os.path.join(translated_output_dir, f"{src_base}__{safe_title}__{node_id}.json")
    with open(out_path, "w", encoding="utf-8") as f:
        json.dump({"courses": translated_course}, f, ensure_ascii=False, indent=2)

    total_success += success
    print(f"节点 {node_id} 创建 lessons: {success}")
    # 校验该节点下的课程 ID 列表
    ids_resp = requests.get(f"{BASE}/learning-nodes/{node_id}/lesson-ids", headers=HEADERS)
    if ids_resp.status_code < 400:
        print("节点下 lesson 数量:", len(ids_resp.json().get("data", [])))
    else:
        print("查询节点下课程失败：", getattr(ids_resp, 'text', ''))

print(f"所有节点共创建 lessons: {total_success}")