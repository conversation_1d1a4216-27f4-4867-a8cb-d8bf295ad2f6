#!/usr/bin/env python3
"""
Course to SQL Script
Converts course JSON files to learning nodes and lessons via API calls.

Supported course JSON fields:
- title: Course title (required)
- summary: Course description
- read_time: Estimated time in seconds
- price: Course price in cents (optional, null for free)
- certificate_price: Certificate price in cents (optional, null for free)
- skills: Array of skill tags (optional)
- tags: Array of general tags (optional)
- learner_tags: Array of learner-specific tags (optional)
- whatYouWillLearn: Array of learning outcomes (optional)
- categories: Array of lessons/categories
- target_audience: Target audience description
"""

import json
import requests
import os
import glob
import sys
from typing import List, Dict, Any, Optional


class CourseUploader:
    """Handles uploading courses to the learning platform."""
    
    def __init__(self, server_ip: str = "************:8012", 
                 email: str = "<EMAIL>", 
                 password: str = "chen4212"):
        self.server_ip = server_ip
        self.base_url = f"http://{server_ip}/api/v1"
        self.email = email
        self.password = password
        self.token = None
        self.headers = {}
        
    def login(self) -> bool:
        """Authenticate with the API and get access token."""
        login_url = f"{self.base_url}/auth/login"
        login_req = {
            "email": self.email,
            "password": self.password
        }
        
        try:
            response = requests.post(login_url, json=login_req)
            response.raise_for_status()
            
            data = response.json()
            self.token = data.get('data', {}).get("access_token")
            
            if not self.token:
                print("Failed to get access token from login response")
                return False
                
            self.headers = {
                "Authorization": f"Bearer {self.token}",
                "Content-Type": "application/json",
            }
            print("Successfully logged in and obtained access token")
            return True
            
        except requests.exceptions.RequestException as e:
            print(f"Login failed: {e}")
            return False
    
    def load_course_files(self, upload_dir: str = "course") -> List[Dict[str, Any]]:
        """Load course JSON files and prepare course items."""
        files = sorted(glob.glob(os.path.join(upload_dir, "*.json")))
        course_items = []
        
        for path in files:
            try:
                with open(path, encoding="utf-8") as f:
                    raw = json.load(f)
                
                courses = raw.get("courses")
                if isinstance(courses, list):
                    iterable = courses
                elif isinstance(courses, dict):
                    iterable = [courses]
                else:
                    iterable = []
                
                for course in iterable:
                    # Convert read_time from seconds to hours
                    read_time_seconds = raw.get("read_time", 21600)  #
                    print(f"  🕒 Original read_time: {read_time_seconds} seconds")

                    # Extract new fields from raw data (top level, not from course object)
                    price = raw.get("price")  # Can be None for free courses
                    certificate_price = raw.get("certificate_price")  # Can be None for free certificates
                    skills = raw.get("skills", [])  # Default to empty list
                    tags = raw.get("tags", [])  # Default to empty list
                    learner_tags = raw.get("learner_tags", [])  # Default to empty list

                    # Extract whatYouWillLearn from course object
                    what_you_will_learn = course.get("whatYouWillLearn", [])  # Default to empty list

                    # Ensure arrays are lists and filter out empty strings
                    if not isinstance(skills, list):
                        skills = []
                    else:
                        skills = [skill.strip() for skill in skills if skill and skill.strip()]

                    if not isinstance(tags, list):
                        tags = []
                    else:
                        tags = [tag.strip() for tag in tags if tag and tag.strip()]

                    if not isinstance(learner_tags, list):
                        learner_tags = []
                    else:
                        learner_tags = [tag.strip() for tag in learner_tags if tag and tag.strip()]

                    # Ensure what_you_will_learn is a list and filter out empty strings
                    if not isinstance(what_you_will_learn, list):
                        what_you_will_learn = []
                    else:
                        what_you_will_learn = [item.strip() for item in what_you_will_learn if item and item.strip()]

                    node_req = {
                        "title": course.get("title", ""),
                        "description": course.get("summary", ""),
                        "estimated_times": int(read_time_seconds),
                        "difficulty": 2,
                        "skills": skills,
                        "tags": tags,
                        "learner_tags": learner_tags,
                        "what_you_will_learn": what_you_will_learn
                    }

                    # Add pricing fields only if they are provided and valid
                    if price is not None and isinstance(price, (int, float)) and price >= 0:
                        node_req["price"] = int(price)

                    if certificate_price is not None and isinstance(certificate_price, (int, float)) and certificate_price >= 0:
                        node_req["certificate_price"] = int(certificate_price)
                    course_items.append({
                        "file": path, 
                        "course": course, 
                        "node_req": node_req
                    })
                    
            except (json.JSONDecodeError, IOError) as e:
                print(f"Error loading file {path}: {e}")
                continue
        
        print(f"Found {len(files)} files, prepared {len(course_items)} courses for upload")
        return course_items
    
    def create_learning_nodes(self, course_items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Create learning nodes for each course."""
        created = []
        
        for item in course_items:
            try:
                response = requests.post(
                    f"{self.base_url}/learning-nodes", 
                    json=item["node_req"], 
                    headers=self.headers
                )
                
                if response.status_code >= 400:
                    print(f"Failed to create node: {item['node_req'].get('title', '')} -> {response.text}")
                    continue
                
                node = response.json().get("data", {})
                item["node_id"] = node.get("id")
                created.append(item)
                print(f"Created node: {item['node_id']} Title: {item['node_req']['title']}")
                
            except requests.exceptions.RequestException as e:
                print(f"Error creating node for {item['node_req'].get('title', '')}: {e}")
                continue
        
        print(f"Successfully created {len(created)}/{len(course_items)} nodes")
        return created
    
    def create_content_flow(self, lesson: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Create content flow from lesson pages."""
        content_flow = []
        index = 0

        for block in lesson.get("pages", []):
            block_type = block.get('type')
            summary = block.get("summary", {})

            if block_type == 'Lesson':
                content_flow.append({
                    "type": "text_explanation",
                    "order": index,
                    "data": {
                        "title": block.get("title", ""),
                        "description": summary.get("description", ""),
                        "body": block.get("text", block.get("mdHtml", ""))
                    }
                })
                index += 1

            elif block_type == 'Quiz':
                quiz_data = []  # 收集所有问题数据

                for quiz in block.get("questions", []):
                    question_data = {
                        "difficulty": 2,
                        "question": quiz.get("questionText", ""),
                        "options": [],
                        "correct_answer_indices": []
                    }

                    for op_i, option in enumerate(quiz.get("questionOptions", [])):
                        question_data["options"].append(option.get("text", ""))
                        if option.get("correct", False):
                            question_data["correct_answer_index"] = op_i
                            question_data["correct_answer_indices"].append(op_i)

                    question_data['single_answer'] = len(question_data['correct_answer_indices']) == 1
                    quiz_data.append(question_data)

                # 只添加一次到 content_flow
                question = {
                    "type": "multiple_choice_quiz",
                    "order": index,
                    "data": {
                        "questions": quiz_data,
                        "title": block.get("title", ""),
                        "description": summary.get("description", ""),
                    } 
                }

                content_flow.append(question)
                index += 1

            else:
                # Handle unknown block types
                if block_type:
                    print(f"⚠️  Warning: Unknown block type '{block_type}' encountered in lesson '{lesson.get('title', 'Unknown')}'")
                    print(f"   Block ID: {block.get('id', 'N/A')}, Title: {block.get('title', 'N/A')}")
                else:
                    print(f"⚠️  Warning: Block with missing 'type' field in lesson '{lesson.get('title', 'Unknown')}'")
                    print(f"   Block ID: {block.get('id', 'N/A')}, Title: {block.get('title', 'N/A')}")

        return content_flow

    def create_lessons(self, created_nodes: List[Dict[str, Any]]) -> int:
        """Create lessons for each node and return total success count."""
        total_success = 0

        for item in created_nodes:
            course = item["course"]
            node_id = item["node_id"]
            success = 0

            for lesson_order, lesson in enumerate(course.get("categories", [])):
                lesson_title = lesson.get("title", "")
                lesson_description = lesson.get("summary", "")
                lesson_type = "text"
                estimated_times = 30 * 60  # 30 minutes in seconds
                difficulty = 2

                content_flow = self.create_content_flow(lesson)

                lesson_req = {
                    "title": lesson_title,
                    "description": lesson_description,
                    "type": lesson_type,
                    "estimated_times": estimated_times,
                    "difficulty": difficulty,
                    "content_flow": content_flow,
                    "node_id": node_id,
                    "order": lesson_order
                }

                try:
                    response = requests.post(
                        f"{self.base_url}/learning-lessons",
                        headers=self.headers,
                        json=lesson_req
                    )

                    if response.status_code == 201:
                        success += 1
                    else:
                        print(f"Failed to create lesson: {response.text}")

                except requests.exceptions.RequestException as e:
                    print(f"Error creating lesson '{lesson_title}': {e}")
                    continue

            total_success += success
            print(f"Node {node_id} created lessons: {success}")

            # Verify lessons created for this node
            self.verify_node_lessons(node_id)

        print(f"All nodes created lessons: {total_success}")
        return total_success

    def verify_node_lessons(self, node_id: str) -> Optional[int]:
        """Verify the number of lessons created for a node."""
        try:
            response = requests.get(
                f"{self.base_url}/learning-nodes/{node_id}/lesson-ids",
                headers=self.headers
            )

            if response.status_code < 400:
                lesson_count = len(response.json().get("data", []))
                print(f"Node lessons count: {lesson_count}")
                return lesson_count
            else:
                print(f"Failed to query node lessons: {response.text}")
                return None

        except requests.exceptions.RequestException as e:
            print(f"Error verifying node lessons: {e}")
            return None

    def print_upload_data(self, upload_dir: str = "course") -> bool:
        """Print all upload data without actually uploading."""
        print("=" * 80)
        print("COURSE UPLOAD DATA PREVIEW")
        print("=" * 80)

        # Step 1: Load course files
        course_items = self.load_course_files(upload_dir)
        if not course_items:
            print("No courses found to upload")
            return False

        # Step 2: Print detailed data for each course
        for i, item in enumerate(course_items, 1):
            print(f"\n{'='*60}")
            print(f"COURSE {i}: {item['node_req']['title']}")
            print(f"{'='*60}")

            # Print node request data
            print("\n📋 NODE REQUEST DATA:")
            print("-" * 40)

            # Show original read_time and calculated estimated_times
            with open(item['file'], encoding="utf-8") as f:
                raw_data = json.load(f)
            read_time_seconds = raw_data.get("read_time", 0)
            read_time_hours = read_time_seconds / 3600 if read_time_seconds else 0

            print(f"  📖 Original read_time: {read_time_seconds} seconds ({read_time_hours:.1f} hours)")

            for key, value in item['node_req'].items():
                if key == "estimated_times":
                    print(f"  ⏱️  {key}: {value} (calculated from read_time)")
                elif key == "price":
                    if value is not None:
                        print(f"  💰 {key}: {value} cents (${value/100:.2f})")
                    else:
                        print(f"  💰 {key}: Free")
                elif key == "certificate_price":
                    if value is not None:
                        print(f"  🏆 {key}: {value} cents (${value/100:.2f})")
                    else:
                        print(f"  🏆 {key}: Free")
                elif key == "skills":
                    print(f"  🛠️  {key}: {value} ({len(value)} skills)")
                elif key == "tags":
                    print(f"  🏷️  {key}: {value} ({len(value)} tags)")
                elif key == "learner_tags":
                    print(f"  👥 {key}: {value} ({len(value)} learner tags)")
                elif key == "what_you_will_learn":
                    print(f"  📚 {key}: {value} ({len(value)} learning outcomes)")
                else:
                    print(f"  {key}: {value}")

            # Print course structure
            course = item['course']
            print(f"\n📚 COURSE STRUCTURE:")
            print("-" * 40)
            print(f"  Title: {course.get('title', 'N/A')}")
            print(f"  Summary: {course.get('summary', 'N/A')[:200]}...")
            print(f"  Target Audience: {course.get('target_audience', 'N/A')}")
            print(f"  Categories (Lessons): {len(course.get('categories', []))}")

            # Print new fields if they exist in source data (from raw data, not course)
            with open(item['file'], encoding="utf-8") as f:
                raw_data = json.load(f)

            if 'price' in raw_data:
                price_val = raw_data.get('price')
                if price_val is not None:
                    print(f"  💰 Price: {price_val} cents (${price_val/100:.2f})")
                else:
                    print(f"  💰 Price: Free")

            if 'certificate_price' in raw_data:
                cert_price_val = raw_data.get('certificate_price')
                if cert_price_val is not None:
                    print(f"  🏆 Certificate Price: {cert_price_val} cents (${cert_price_val/100:.2f})")
                else:
                    print(f"  🏆 Certificate Price: Free")

            if 'skills' in raw_data:
                skills_val = raw_data.get('skills', [])
                print(f"  🛠️  Skills: {skills_val} ({len(skills_val)} skills)")

            if 'tags' in raw_data:
                tags_val = raw_data.get('tags', [])
                print(f"  🏷️  Tags: {tags_val} ({len(tags_val)} tags)")

            if 'learner_tags' in raw_data:
                learner_tags_val = raw_data.get('learner_tags', [])
                print(f"  👥 Learner Tags: {learner_tags_val} ({len(learner_tags_val)} learner tags)")

            if 'whatYouWillLearn' in course:
                what_you_will_learn_val = course.get('whatYouWillLearn', [])
                print(f"  📚 What You Will Learn: {what_you_will_learn_val} ({len(what_you_will_learn_val)} learning outcomes)")

            # Print lessons details
            categories = course.get('categories', [])
            if categories:
                print(f"\n📖 LESSONS DETAILS:")
                print("-" * 40)

                for j, lesson in enumerate(categories, 1):
                    print(f"\n  Lesson {j}: {lesson.get('title', 'Untitled')}")
                    print(f"    Summary: {lesson.get('summary', 'No summary')[:100]}...")

                    # Count content types
                    pages = lesson.get('pages', [])
                    lesson_count = sum(1 for p in pages if p.get('type') == 'Lesson')
                    quiz_count = sum(len(p.get('questions', [])) for p in pages if p.get('type') == 'Quiz')

                    print(f"    Pages: {len(pages)} (Lessons: {lesson_count}, Quiz Questions: {quiz_count})")

                    # Create and print content flow
                    content_flow = self.create_content_flow(lesson)
                    print(f"    Content Flow Items: {len(content_flow)}")

                    # Print content flow details
                    if content_flow:
                        print(f"    Content Flow Preview:")
                        for k, content in enumerate(content_flow[:3], 1):  # Show first 3 items
                            content_type = content.get('type', 'unknown')
                            if content_type == 'text_explanation':
                                title = content.get('data', {}).get('title', 'No title')
                                body_preview = content.get('data', {}).get('body', '')[:50]
                                print(f"      {k}. {content_type}: {title} - {body_preview}...")
                            elif content_type == 'multiple_choice_quiz':
                                question = content.get('data', {}).get('question', '')[:50]
                                options_count = len(content.get('data', {}).get('options', []))
                                print(f"      {k}. {content_type}: {question}... ({options_count} options)")

                        if len(content_flow) > 3:
                            print(f"      ... and {len(content_flow) - 3} more items")

                    # Print lesson request that would be sent
                    lesson_req = {
                        "title": lesson.get('title', ''),
                        "description": lesson.get('summary', ''),
                        "type": "text",
                        "estimated_times": 30 * 60,
                        "difficulty": 2,
                        "content_flow": content_flow,
                        "node_id": "PLACEHOLDER_NODE_ID",
                        "order": j - 1
                    }

                    print(f"\n    📤 LESSON REQUEST DATA:")
                    print(f"      Title: {lesson_req['title']}")
                    print(f"      Description: {lesson_req['description'][:100]}...")
                    print(f"      Type: {lesson_req['type']}")
                    print(f"      Estimated Times: {lesson_req['estimated_times']}")
                    print(f"      Difficulty: {lesson_req['difficulty']}")
                    print(f"      Content Flow Items: {len(lesson_req['content_flow'])}")
                    print(f"      Order: {lesson_req['order']}")

        print(f"\n{'='*80}")
        print(f"SUMMARY: Found {len(course_items)} courses ready for upload")
        print(f"{'='*80}")
        return True

    def upload_courses(self, upload_dir: str = "course") -> bool:
        """Main method to upload all courses."""
        print("Starting course upload process...")

        # Step 1: Login
        if not self.login():
            return False

        # Step 2: Load course files
        course_items = self.load_course_files(upload_dir)
        if not course_items:
            print("No courses found to upload")
            return False

        # Step 3: Create learning nodes
        created_nodes = self.create_learning_nodes(course_items)
        if not created_nodes:
            print("No nodes were created successfully")
            return False

        # Step 4: Create lessons
        total_lessons = self.create_lessons(created_nodes)

        print(f"Upload process completed. Total lessons created: {total_lessons}")
        return total_lessons > 0


def main():
    """Main entry point for the script."""
    # Fixed configuration - no command line arguments needed
    upload_dir = "course"  # Course directory
    server_ip = "127.0.0.1:8012"
    email = "<EMAIL>"
    password = "123123123"

    # Create uploader instance
    uploader = CourseUploader(
        server_ip=server_ip,
        email=email,
        password=password
    )

    print("🔍 PREVIEW MODE: Printing upload data without actually uploading")
    print("To enable actual upload, modify the main() function\n")

    # Print upload data instead of uploading
    # success = uploader.print_upload_data(upload_direstimated_times)
    success = uploader.upload_courses(upload_dir)

    if success:
        print("\n✅ Data preview completed successfully!")
        print("📝 Review the data above before enabling actual upload")
        sys.exit(0)
    else:
        print("\n❌ Data preview failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
